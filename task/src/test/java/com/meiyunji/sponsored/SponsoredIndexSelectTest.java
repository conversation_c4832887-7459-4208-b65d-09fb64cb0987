package com.meiyunji.sponsored;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportType;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.Region;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.cron.CronjobService;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.feign.param.AdvertisingCampaignWarningRequest;
import com.meiyunji.sponsored.service.feign.param.AdvertisingCampaignWarningResponse;
import com.meiyunji.sponsored.service.feign.service.KeywordRankMonitorFeignService;
import com.meiyunji.sponsored.service.index.dto.BudgetCampaignWarningMessage;
import com.meiyunji.sponsored.service.kafka.BudgetWarningIndexKafkaProducer;
import com.meiyunji.sponsored.service.kafka.SponsoredIndexSelectProducer;
import com.meiyunji.sponsored.service.kafka.message.SponsoredIndexObject;
import com.meiyunji.sponsored.service.kafka.message.SponsoredIndexSelectMessage;
import com.meiyunji.sponsored.service.kafka.message.SponsoredIndexTarget;
import com.meiyunji.sponsored.service.syncTask.AdvertiseSyncProcessFactory;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductAdvertisedProduct;
import com.meiyunji.sponsored.service.util.GZipUtils;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IAmazonAdWordRootKeywordSpDao;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootCalculateServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SponsoredIndexSelectTest {

//    @Autowired
//    @Qualifier("sponsoredIndexSelectGroupStrategy")
//    private SponsoredIndexSelectProcessStrategy groupStrategy;
//
//    @Autowired
//    @Qualifier("sponsoredIndexSelectCampaignStrategy")
//    private SponsoredIndexSelectProcessStrategy campaignStrategy;
//
//    @Autowired
//    @Qualifier("sponsoredIndexSelectPortfolioStrategy")
//    private SponsoredIndexSelectProcessStrategy portfolioStrategy;

    @Autowired
    private KeywordRankMonitorFeignService keywordRankMonitorFeignService;

    @Autowired
    private CronjobService cronjobService;

    @Autowired
    private AdvertiseSyncProcessFactory advertiseSyncProcessFactory;
    @Autowired
    private CosBucketClient dataBucketClient;

    @Test
    public void testKeywordRankMonitorFeignService() throws Exception {
        AdvertisingCampaignWarningRequest request = new AdvertisingCampaignWarningRequest();
        request.setIndex(0);
        request.setSize(10);
        Result<AdvertisingCampaignWarningResponse> result = keywordRankMonitorFeignService.selectAdvertisingCampaignWarningObject(request);
        log.info(String.valueOf(result));
    }

    @Test
    public void testGroupStrategy() throws Exception {
        cronjobService.updateSbAdTargetType();
//        SponsoredIndexSelectMessage message = new SponsoredIndexSelectMessage();
//        message.setPuid(100);
//        message.setTaskId(1L);
//        message.setMt("mt");
//        message.setMv(getMvList());
//        message.setTargetMap(getTargetMap());
//        groupStrategy.processSponsoredIndexSelect(getMessage());
    }

    @Test
    public void testCampaignStrategy() throws Exception {
//        SponsoredIndexSelectMessage message = new SponsoredIndexSelectMessage();
//        message.setPuid(100);
//        message.setTaskId(1L);
//        message.setMt("mt");
//        message.setMv(getMvList());
//        message.setTargetMap(getTargetMap());
//        campaignStrategy.processSponsoredIndexSelect(getMessage());
    }

    @Test
    public void testPortfolioStrategy() throws Exception {
//        SponsoredIndexSelectMessage message = new SponsoredIndexSelectMessage();
//        message.setPuid(100);
//        message.setTaskId(1L);
//        message.setMt("mt");
//        message.setMv(getMvList());
//        message.setTargetMap(getTargetMap());
//        portfolioStrategy.processSponsoredIndexSelect(message);
//        portfolioStrategy.processSponsoredIndexSelect(getMessage());
    }

    private SponsoredIndexSelectMessage getMessage() {
        String jsonStr = "{\"puid\":100,\"taskId\":null,\"mt\":\"adTeam\",\"uidList\":[],\"mv\":[{\"id\":1,\"v\":\"106189736918091\",\"shopId\":4636,\"productId\":null,\"marketplaceId\":\"ATVPDKIKX0DER\",\"shareKey\":null,\"fnSku\":null,\"warehouseId\":null,\"warehouseName\":null}],\"noAuthShopList\":null,\"ver\":\"2024061309\",\"deadline\":\"2024061318\",\"targetMap\":{\"ATVPDKIKX0DER\":[{\"factor\":\"day_0_0_0_cost_latest\",\"id\":2,\"k\":\"cost\",\"st\":\"2024-06-12\",\"et\":\"2024-06-12\",\"type\":\"day\",\"census\":\"latest\",\"cId\":null,\"cid\":null},{\"factor\":\"day_0_2_0_adAcos\",\"id\":1,\"k\":\"adAcos\",\"st\":\"2024-06-09\",\"et\":\"2024-06-11\",\"type\":\"day\",\"census\":null,\"cId\":null,\"cid\":null}]}}";
        return JSONUtil.jsonToObject(jsonStr, SponsoredIndexSelectMessage.class);
    }

    private List<SponsoredIndexObject> getMvList() {
        List<SponsoredIndexObject> mvList = Lists.newArrayList();
        // 只调整2 4参数

        // 广告组合 v -> portfolioId
//        mvList.add(new SponsoredIndexObject(1L, "************", 4636, "ATVPDKIKX0DER"));
//        mvList.add(new SponsoredIndexObject(2L, "65954558072333", 4636, "ATVPDKIKX0DER"));

        // 广告活动 v -> campaignId
//        mvList.add(new SponsoredIndexObject(1L, "420195853706141", 4636, "ATVPDKIKX0DER"));// sp
//        mvList.add(new SponsoredIndexObject(2L, "317999465739137", 4636, "ATVPDKIKX0DER"));// sp
//        mvList.add(new SponsoredIndexObject(3L, "115290763919965", 4636, "ATVPDKIKX0DER"));// sb
//        mvList.add(new SponsoredIndexObject(4L, "144177317904457829", 4636, "ATVPDKIKX0DER"));// sb
//        mvList.add(new SponsoredIndexObject(5L, "43732747190707", 4636, "ATVPDKIKX0DER"));// sd
//        mvList.add(new SponsoredIndexObject(6L, "301507561722394", 4636, "ATVPDKIKX0DER"));// sd

        // 广告组 v -> adGroupId
//        mvList.add(new SponsoredIndexObject(1L, "307770996892554", 4636, "ATVPDKIKX0DER"));// sp
//        mvList.add(new SponsoredIndexObject(2L, "511861890463778", 4636, "ATVPDKIKX0DER"));// sp
//        mvList.add(new SponsoredIndexObject(3L, "533604384564182", 4636, "ATVPDKIKX0DER"));// sb
//        mvList.add(new SponsoredIndexObject(4L, "144270133940522775", 4636, "ATVPDKIKX0DER"));// sb
//        mvList.add(new SponsoredIndexObject(5L, "462023813368285", 4636, "ATVPDKIKX0DER"));// sd
//        mvList.add(new SponsoredIndexObject(6L, "312166753613846", 4636, "ATVPDKIKX0DER"));// sd

        return mvList;
    }

    private Map<String, List<SponsoredIndexTarget>> getTargetMap() {
        Map<String, List<SponsoredIndexTarget>> map = Maps.newHashMap();

        // 广告组合
        map.put("ATVPDKIKX0DER", getTargetList());

        return map;
    }

    private List<SponsoredIndexTarget> getTargetList() {
        List<SponsoredIndexTarget> targetList = Lists.newArrayList();
//        targetList.add(new SponsoredIndexTarget(1L, "k", "st", "et", "type", "census", "cId")); k st et census

//        targetList.add(new SponsoredIndexTarget(1L, "roas", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(2L, "cvr", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(3L, "ctr", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(4L, "adAcos", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//
//        targetList.add(new SponsoredIndexTarget(5L, "acots", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(6L, "asots", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//
//        targetList.add(new SponsoredIndexTarget(7L, "adCpa", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(8L, "adCpc", "2024-03-21", "2024-05-20", "type", "total or avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(9L, "adSalesNum", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(10L, "adSalesNum", "2024-03-21", "2024-05-20", "type", "total", "cId"));
//        targetList.add(new SponsoredIndexTarget(11L, "impressions", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(12L, "impressions", "2024-03-21", "2024-05-20", "type", "total", "cId"));
//        targetList.add(new SponsoredIndexTarget(13L, "clicks", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(14L, "clicks", "2024-03-21", "2024-05-20", "type", "total", "cId"));
//        targetList.add(new SponsoredIndexTarget(15L, "cost", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(16L, "cost", "2024-03-21", "2024-05-20", "type", "total", "cId"));
//        targetList.add(new SponsoredIndexTarget(17L, "adOrderNums", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(18L, "adOrderNums", "2024-03-21", "2024-05-20", "type", "total", "cId"));
//        targetList.add(new SponsoredIndexTarget(19L, "adSales", "2024-03-21", "2024-05-20", "type", "avg", "cId"));
//        targetList.add(new SponsoredIndexTarget(20L, "adSales", "2024-03-21", "2024-05-20", "type", "total", "cId"));

//        targetList.add(new SponsoredIndexTarget(1L, "adCpc", "2024-05-28", "2024-05-28", "day", "latest", null));
//        targetList.add(new SponsoredIndexTarget(2L, "acots", "2024-05-28", "2024-05-20", "day", "latest", null));


        return targetList;

        // 16 总花费
//        put("adSalesTotal", "sum(total_sales) adSalesTotal"); // 广告销售额 20
//        put("adSalesAvg", "avg(total_sales) adSalesAvg"); 19
//        put("adOrderNumTotal", "sum(sale_num) adOrderNumTotal"); // 广告订单量 18
//        put("adOrderNumAvg", "avg(sale_num) adOrderNumAvg"); 17
//        put("adSalesNumTotal", "sum(order_num) adSalesNumTotal"); // 广告销量 10
//        put("adSalesNumAvg", "avg(order_num) adSalesNumAvg"); 9

    }


    @Autowired
    private SponsoredIndexSelectProducer sponsoredIndexSelectProducer;

    @Test
    public void testKafkaSend() throws Exception {
////        String jsonStr = "{\"puid\":100,\"taskId\":16573,\"list\":[{\"k\":3,\"v\":[{\"id\":2,\"v\":\"0.00\"},{\"id\":1,\"v\":\"0\"}]},{\"k\":4,\"v\":[{\"id\":2,\"v\":\"0.00\"},{\"id\":1,\"v\":\"0\"}]},{\"k\":1,\"v\":[{\"id\":2,\"v\":\"0.00\"},{\"id\":1,\"v\":\"0\"}]},{\"k\":2,\"v\":[{\"id\":2,\"v\":\"0.00\"},{\"id\":1,\"v\":\"0\"}]}]}";
////        SponsoredIndexResponse response = JSONUtil.jsonToObject(jsonStr, SponsoredIndexResponse.class);
//        sponsoredIndexSelectProducer.send(getMessage());


            byte[] objectToBytes = dataBucketClient.getObjectToBytes("NorthAmerica/A2380AM273CQ9I/USA/reportV3/sp_campaigns/2025-01-12_2025-01-15.json.gz");
            byte[] bytes = GZipUtils.decompressData(objectToBytes);
            String str = new String(bytes);
            System.out.println(str);
    }

    @Autowired
    private BudgetWarningIndexKafkaProducer budgetWarningIndexKafkaProducer;

    @Test
    public void testKafkaSend2() throws Exception {
        BudgetCampaignWarningMessage budgetCampaignWarningMessage = new BudgetCampaignWarningMessage();
        budgetCampaignWarningMessage.setPuid(100);
        budgetCampaignWarningMessage.setShopId(4636);
        budgetCampaignWarningMessage.setV("420195853706141");
        budgetCampaignWarningMessage.setBudgetUsagePercentage(new BigDecimal("20")); // 20 30
        budgetCampaignWarningMessage.setRuleId(Long.valueOf("263"));
        budgetWarningIndexKafkaProducer.send(budgetCampaignWarningMessage);
    }

    @Test
    public void spPurchaseProductTest() throws Exception {
        ReportReadyNotification readyNotification = new ReportReadyNotification();
        readyNotification.setMarketplace(Marketplace.Japan);
        readyNotification.setVersion(3);
        readyNotification.setV3StartDate(LocalDate.parse(
                "2024-08-08"));
        readyNotification.setV3EndDate(LocalDate.parse(
                "2024-08-11"));
        readyNotification.setPath("FarEast/A3S3Z7V80RM10W/Japan/reportV3/sp_asin_purchase/2024-08-08_2024-08-11.json.gz");
        readyNotification.setProfileId(2189746415424872L);
        readyNotification.setSellerIdentifier(100);
        readyNotification.setMarketplaceIdentifier(4636);
        readyNotification.setSellerId("A3TYT3AGC5P9UV");
        readyNotification.setRegion(Region.NorthAmerica);
        readyNotification.setV3Type(AmazonReportV3Type.sp_asin_purchase);
        readyNotification.setReportId("b6ab0d71-b4d7-4c81-8e7c-07a3f6a0f36f");
        readyNotification.setType(AmazonReportType.sd_asins_t00020);
        advertiseSyncProcessFactory.getReportStrategy(readyNotification).processReport(readyNotification);
    }

    @Test
    public void sdPurchaseProductTest() throws Exception {
        ReportReadyNotification readyNotification = new ReportReadyNotification();
        readyNotification.setMarketplace(Marketplace.USA);
        readyNotification.setVersion(3);
        readyNotification.setV3StartDate(LocalDate.parse(
                "2024-08-07"));
        readyNotification.setV3EndDate(LocalDate.parse(
                "2024-08-10"));
        readyNotification.setPath("NorthAmerica/A3TYT3AGC5P9UV/USA/reportV3/sd_asin_purchase/2024-08-07_2024-08-10.json.gz");
        readyNotification.setProfileId(790436899648724L);
        readyNotification.setSellerIdentifier(100);
        readyNotification.setMarketplaceIdentifier(4636);
        readyNotification.setSellerId("A3S3Z7V80RM10W");
        readyNotification.setRegion(Region.NorthAmerica);
        readyNotification.setV3Type(AmazonReportV3Type.sd_asin_purchase);
        readyNotification.setReportId("5efa47b3-78d8-4389-a335-6e383c64d5f8");
        readyNotification.setType(AmazonReportType.sd_asins_t00020);
        advertiseSyncProcessFactory.getReportStrategy(readyNotification).processReport(readyNotification);
    }


    @Test
    public void download() {

        //dataBucketClient.getObjectToBytes("2024-12-25/A2ISMECGPXRJ8BNorthAmerica/None/getAccessToken/6541927/"));

        try {
//            byte[] objectToBytes = dataBucketClient.getObjectToBytes("NorthAmerica/A31LSFQRG3AHX3/USA/reportV3/sp_ad_product/2024-10-01_2024-10-17.json.gz");
//            byte[] bytes = GZipUtils.decompressData(objectToBytes);
//            String str = new String(bytes);
//            System.out.println(str);

            InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes("NorthAmerica/A31LSFQRG3AHX3/USA/reportV3/sp_ad_product/2024-10-01_2024-10-17.json.gz"))));
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<SponsoredProductAdvertisedProduct> reports = Lists.newArrayListWithExpectedSize(10000000);

            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductAdvertisedProduct report = new SponsoredProductAdvertisedProduct();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() == 0) {
                    System.out.println("1");
                }
                reports.add(report);

            }
            jsonReader.endArray();
            System.out.println(reports.stream().map(SponsoredProductAdvertisedProduct::getCost).reduce(BigDecimal::add).get());


            inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes("NorthAmerica/A31LSFQRG3AHX3/USA/reportV3/sp_ad_product/2024-10-18_2024-11-01.json.gz"))));
            jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            reports = Lists.newArrayListWithExpectedSize(10000000);

            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductAdvertisedProduct report = new SponsoredProductAdvertisedProduct();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (!report.getDate().isEqual(LocalDate.parse("2024-11-01"))){
                    reports.add(report);
                    if ( report.getImpressions() == 0) {
                        System.out.println("1");
                    }
                }

            }
            jsonReader.endArray();
            System.out.println(reports.stream().map(SponsoredProductAdvertisedProduct::getCost).reduce(BigDecimal::add).get());


        } catch (IOException ioException) {
            ioException.printStackTrace();
        }
    }

}
