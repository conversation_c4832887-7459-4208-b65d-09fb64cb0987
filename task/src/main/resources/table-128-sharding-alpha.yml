_00_01: 0x00..0x01
_02_03: 0x02..0x03
_04_05: 0x04..0x05
_06_07: 0x06..0x07
_08_09: 0x08..0x09
_0a_0b: 0x0a..0x0b
_0c_0d: 0x0c..0x0d
_0e_0f: 0x0e..0x0f
_10_11: 0x10..0x11
_12_13: 0x12..0x13
_14_15: 0x14..0x15
_16_17: 0x16..0x17
_18_19: 0x18..0x19
_1a_1b: 0x1a..0x1b
_1c_1d: 0x1c..0x1d
_1e_1f: 0x1e..0x1f
_20_21: 0x20..0x21
_22_23: 0x22..0x23
_24_25: 0x24..0x25
_26_27: 0x26..0x27
_28_29: 0x28..0x29
_2a_2b: 0x2a..0x2b
_2c_2d: 0x2c..0x2d
_2e_2f: 0x2e..0x2f
_30_31: 0x30..0x31
_32_33: 0x32..0x33
_34_35: 0x34..0x35
_36_37: 0x36..0x37
_38_39: 0x38..0x39
_3a_3b: 0x3a..0x3b
_3c_3d: 0x3c..0x3d
_3e_3f: 0x3e..0x3f
_40_41: 0x40..0x41
_42_43: 0x42..0x43
_44_45: 0x44..0x45
_46_47: 0x46..0x47
_48_49: 0x48..0x49
_4a_4b: 0x4a..0x4b
_4c_4d: 0x4c..0x4d
_4e_4f: 0x4e..0x4f
_50_51: 0x50..0x51
_52_53: 0x52..0x53
_54_55: 0x54..0x55
_56_57: 0x56..0x57
_58_59: 0x58..0x59
_5a_5b: 0x5a..0x5b
_5c_5d: 0x5c..0x5d
_5e_5f: 0x5e..0x5f
_60_61: 0x60..0x61
_62_63: 0x62..0x63
_64_65: 0x64..0x65
_66_67: 0x66..0x67
_68_69: 0x68..0x69
_6a_6b: 0x6a..0x6b
_6c_6d: 0x6c..0x6d
_6e_6f: 0x6e..0x6f
_70_71: 0x70..0x71
_72_73: 0x72..0x73
_74_75: 0x74..0x75
_76_77: 0x76..0x77
_78_79: 0x78..0x79
_7a_7b: 0x7a..0x7b
_7c_7d: 0x7c..0x7d
_7e_7f: 0x7e..0x7f
_80_81: 0x80..0x81
_82_83: 0x82..0x83
_84_85: 0x84..0x85
_86_87: 0x86..0x87
_88_89: 0x88..0x89
_8a_8b: 0x8a..0x8b
_8c_8d: 0x8c..0x8d
_8e_8f: 0x8e..0x8f
_90_91: 0x90..0x91
_92_93: 0x92..0x93
_94_95: 0x94..0x95
_96_97: 0x96..0x97
_98_99: 0x98..0x99
_9a_9b: 0x9a..0x9b
_9c_9d: 0x9c..0x9d
_9e_9f: 0x9e..0x9f
_a0_a1: 0xa0..0xa1
_a2_a3: 0xa2..0xa3
_a4_a5: 0xa4..0xa5
_a6_a7: 0xa6..0xa7
_a8_a9: 0xa8..0xa9
_aa_ab: 0xaa..0xab
_ac_ad: 0xac..0xad
_ae_af: 0xae..0xaf
_b0_b1: 0xb0..0xb1
_b2_b3: 0xb2..0xb3
_b4_b5: 0xb4..0xb5
_b6_b7: 0xb6..0xb7
_b8_b9: 0xb8..0xb9
_ba_bb: 0xba..0xbb
_bc_bd: 0xbc..0xbd
_be_bf: 0xbe..0xbf
_c0_c1: 0xc0..0xc1
_c2_c3: 0xc2..0xc3
_c4_c5: 0xc4..0xc5
_c6_c7: 0xc6..0xc7
_c8_c9: 0xc8..0xc9
_ca_cb: 0xca..0xcb
_cc_cd: 0xcc..0xcd
_ce_cf: 0xce..0xcf
_d0_d1: 0xd0..0xd1
_d2_d3: 0xd2..0xd3
_d4_d5: 0xd4..0xd5
_d6_d7: 0xd6..0xd7
_d8_d9: 0xd8..0xd9
_da_db: 0xda..0xdb
_dc_dd: 0xdc..0xdd
_de_df: 0xde..0xdf
_e0_e1: 0xe0..0xe1
_e2_e3: 0xe2..0xe3
_e4_e5: 0xe4..0xe5
_e6_e7: 0xe6..0xe7
_e8_e9: 0xe8..0xe9
_ea_eb: 0xea..0xeb
_ec_ed: 0xec..0xed
_ee_ef: 0xee..0xef
_f0_f1: 0xf0..0xf1
_f2_f3: 0xf2..0xf3
_f4_f5: 0xf4..0xf5
_f6_f7: 0xf6..0xf7
_f8_f9: 0xf8..0xf9
_fa_fb: 0xfa..0xfb
_fc_fd: 0xfc..0xfd
_fe_ff: 0xfe..0xff
