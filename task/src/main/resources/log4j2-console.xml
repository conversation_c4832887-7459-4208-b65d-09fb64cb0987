<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status,这个用于设置log4j2自身内部的信息输出,可以不设置,当设置成trace时,你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身,设置间隔秒数-->
<Configuration status="off" monitorInterval="1800">
    <Appenders>
        <!-- =======================================用来定义输出到控制台的配置======================================= -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %-5p [%t] %c (%F/:%L) - %m%n" />
        </Console>
        <Sentry name="Sentry"/>
    </Appenders>

    <!--定义logger,只有定义了logger并引入的appender,appender才会生效-->
    <Loggers>
        <!-- 本地默认日志级别是DEBUG -->
        <root level="INFO" includeLocation="true">
            <appender-ref ref="Console"/>
            <appender-ref ref="Sentry" level="ERROR"/>
        </root>
    </Loggers>
</Configuration>