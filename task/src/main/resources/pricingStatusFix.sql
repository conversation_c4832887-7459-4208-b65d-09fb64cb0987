 UPDATE t_amazon_ad_keyword_00_01 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_00_01 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_02_03 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_02_03 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_04_05 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_04_05 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_06_07 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_06_07 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_08_09 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_08_09 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_0a_0b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_0a_0b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_0c_0d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_0c_0d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_0e_0f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_0e_0f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_10_11 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_10_11 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_12_13 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_12_13 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_14_15 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_14_15 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_16_17 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_16_17 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_18_19 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_18_19 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_1a_1b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_1a_1b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_1c_1d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_1c_1d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_1e_1f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_1e_1f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_20_21 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_20_21 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_22_23 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_22_23 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_24_25 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_24_25 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_26_27 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_26_27 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_28_29 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_28_29 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_2a_2b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_2a_2b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_2c_2d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_2c_2d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_2e_2f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_2e_2f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_30_31 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_30_31 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_32_33 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_32_33 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_34_35 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_34_35 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_36_37 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_36_37 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_38_39 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_38_39 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_3a_3b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_3a_3b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_3c_3d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_3c_3d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_3e_3f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_3e_3f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_40_41 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_40_41 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_42_43 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_42_43 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_44_45 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_44_45 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_46_47 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_46_47 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_48_49 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_48_49 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_4a_4b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_4a_4b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_4c_4d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_4c_4d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_4e_4f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_4e_4f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_50_51 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_50_51 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_52_53 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_52_53 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_54_55 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_54_55 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_56_57 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_56_57 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_58_59 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_58_59 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_5a_5b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_5a_5b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_5c_5d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_5c_5d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_5e_5f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_5e_5f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_60_61 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_60_61 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_62_63 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_62_63 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_64_65 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_64_65 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_66_67 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_66_67 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_68_69 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_68_69 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_6a_6b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_6a_6b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_6c_6d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_6c_6d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_6e_6f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_6e_6f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_70_71 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_70_71 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_72_73 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_72_73 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_74_75 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_74_75 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_76_77 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_76_77 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_78_79 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_78_79 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_7a_7b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_7a_7b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_7c_7d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_7c_7d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_7e_7f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_7e_7f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_80_81 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_80_81 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_82_83 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_82_83 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_84_85 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_84_85 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_86_87 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_86_87 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_88_89 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_88_89 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_8a_8b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_8a_8b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_8c_8d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_8c_8d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_8e_8f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_8e_8f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_90_91 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_90_91 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_92_93 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_92_93 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_94_95 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_94_95 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_96_97 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_96_97 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_98_99 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_98_99 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_9a_9b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_9a_9b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_9c_9d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_9c_9d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_9e_9f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_9e_9f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_a0_a1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_a0_a1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_a2_a3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_a2_a3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_a4_a5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_a4_a5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_a6_a7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_a6_a7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_a8_a9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_a8_a9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_aa_ab SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_aa_ab t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ac_ad SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ac_ad t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ae_af SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ae_af t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_b0_b1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_b0_b1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_b2_b3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_b2_b3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_b4_b5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_b4_b5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_b6_b7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_b6_b7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_b8_b9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_b8_b9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ba_bb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ba_bb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_bc_bd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_bc_bd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_be_bf SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_be_bf t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_c0_c1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_c0_c1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_c2_c3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_c2_c3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_c4_c5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_c4_c5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_c6_c7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_c6_c7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_c8_c9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_c8_c9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ca_cb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ca_cb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_cc_cd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_cc_cd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ce_cf SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ce_cf t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_d0_d1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_d0_d1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_d2_d3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_d2_d3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_d4_d5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_d4_d5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_d6_d7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_d6_d7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_d8_d9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_d8_d9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_da_db SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_da_db t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_dc_dd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_dc_dd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_de_df SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_de_df t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_e0_e1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_e0_e1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_e2_e3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_e2_e3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_e4_e5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_e4_e5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_e6_e7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_e6_e7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_e8_e9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_e8_e9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ea_eb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ea_eb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ec_ed SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ec_ed t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_ee_ef SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_ee_ef t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_f0_f1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_f0_f1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_f2_f3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_f2_f3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_f4_f5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_f4_f5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_f6_f7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_f6_f7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_f8_f9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_f8_f9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_fa_fb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_fa_fb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_fc_fd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_fc_fd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_keyword_fe_ff SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_fe_ff t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 
 




 UPDATE t_amazon_ad_targeting_00_01 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_00_01 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_02_03 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_02_03 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_04_05 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_04_05 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_06_07 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_06_07 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_08_09 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_08_09 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_0a_0b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_0a_0b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_0c_0d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_0c_0d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_0e_0f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_0e_0f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_10_11 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_10_11 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_12_13 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_12_13 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_14_15 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_14_15 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_16_17 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_16_17 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_18_19 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_18_19 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_1a_1b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_1a_1b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_1c_1d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_1c_1d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_1e_1f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_1e_1f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_20_21 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_20_21 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_22_23 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_22_23 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_24_25 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_24_25 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_26_27 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_26_27 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_28_29 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_28_29 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_2a_2b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_2a_2b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_2c_2d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_2c_2d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_2e_2f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_2e_2f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_30_31 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_30_31 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_32_33 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_32_33 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_34_35 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_34_35 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_36_37 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_36_37 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_38_39 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_38_39 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_3a_3b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_3a_3b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_3c_3d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_3c_3d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_3e_3f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_3e_3f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_40_41 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_40_41 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_42_43 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_42_43 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_44_45 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_44_45 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_46_47 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_46_47 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_48_49 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_48_49 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_4a_4b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_4a_4b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_4c_4d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_4c_4d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_4e_4f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_4e_4f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_50_51 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_50_51 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_52_53 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_52_53 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_54_55 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_54_55 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_56_57 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_56_57 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_58_59 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_58_59 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_5a_5b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_5a_5b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_5c_5d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_5c_5d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_5e_5f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_5e_5f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_60_61 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_60_61 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_62_63 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_62_63 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_64_65 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_64_65 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_66_67 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_66_67 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_68_69 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_68_69 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_6a_6b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_6a_6b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_6c_6d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_6c_6d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_6e_6f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_6e_6f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_70_71 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_70_71 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_72_73 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_72_73 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_74_75 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_74_75 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_76_77 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_76_77 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_78_79 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_78_79 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_7a_7b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_7a_7b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_7c_7d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_7c_7d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_7e_7f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_7e_7f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_80_81 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_80_81 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_82_83 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_82_83 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_84_85 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_84_85 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_86_87 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_86_87 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_88_89 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_88_89 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_8a_8b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_8a_8b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_8c_8d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_8c_8d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_8e_8f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_8e_8f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_90_91 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_90_91 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_92_93 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_92_93 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_94_95 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_94_95 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_96_97 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_96_97 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_98_99 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_98_99 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_9a_9b SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_9a_9b t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_9c_9d SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_9c_9d t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_9e_9f SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_9e_9f t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_a0_a1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_a0_a1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_a2_a3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_a2_a3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_a4_a5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_a4_a5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_a6_a7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_a6_a7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_a8_a9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_a8_a9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_aa_ab SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_aa_ab t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ac_ad SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ac_ad t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ae_af SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ae_af t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_b0_b1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_b0_b1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_b2_b3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_b2_b3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_b4_b5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_b4_b5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_b6_b7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_b6_b7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_b8_b9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_b8_b9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ba_bb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ba_bb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_bc_bd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_bc_bd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_be_bf SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_be_bf t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_c0_c1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_c0_c1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_c2_c3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_c2_c3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_c4_c5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_c4_c5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_c6_c7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_c6_c7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_c8_c9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_c8_c9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ca_cb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ca_cb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_cc_cd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_cc_cd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ce_cf SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ce_cf t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_d0_d1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_d0_d1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_d2_d3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_d2_d3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_d4_d5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_d4_d5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_d6_d7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_d6_d7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_d8_d9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_d8_d9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_da_db SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_da_db t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_dc_dd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_dc_dd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_de_df SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_de_df t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_e0_e1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_e0_e1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_e2_e3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_e2_e3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_e4_e5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_e4_e5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_e6_e7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_e6_e7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_e8_e9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_e8_e9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ea_eb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ea_eb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ec_ed SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ec_ed t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_ee_ef SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_ee_ef t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_f0_f1 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_f0_f1 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_f2_f3 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_f2_f3 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_f4_f5 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_f4_f5 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_f6_f7 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_f6_f7 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_f8_f9 SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_f8_f9 t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_fa_fb SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_fa_fb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_fc_fd SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_fc_fd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 UPDATE t_amazon_ad_targeting_fe_ff SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_fe_ff t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 
 
 
 
  
 
 UPDATE `t_amazon_ad_targeting_sb` SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_sb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 





UPDATE t_amazon_ad_campaign_all SET pricing_budget_state = 0  WHERE id IN (SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_campaign_all t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.campaign_id = t1.item_id
 AND t1.item_type IN ('CAMPAIGN') AND t.pricing_budget_state = 1
 WHERE t.pricing_budget_state = 1 AND t1.id IS NULL ) s);
 



UPDATE  t_amazon_ad_campaign_all SET is_space_pricing = 0   WHERE id IN (SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_campaign_all t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.campaign_id = t1.item_id
 AND t1.item_type IN ('CAMPAIGN_PLACEMENT') AND t.is_space_pricing = 1
 WHERE t.is_space_pricing = 1 AND t1.id IS NULL ) s);
 
 
UPDATE t_amazon_ad_campaign_all SET is_state_pricing = 0  WHERE id IN (SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_campaign_all t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.campaign_id = t1.item_id
 AND t1.item_type IN ('START_STOP') AND t.is_state_pricing = 1
 WHERE t.is_state_pricing = 1 AND t1.id IS NULL ) s);
 
 
  
 
 UPDATE `t_amazon_ad_targeting_sd` SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_targeting_sd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.target_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 



 
 
 UPDATE `t_amazon_ad_product` SET is_state_pricing = 0   WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_product t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.ad_id = t1.item_id
 AND t1.item_type = 'START_STOP' AND t.is_state_pricing = 1
 WHERE t.is_state_pricing = 1 AND t1.id IS NULL ) s ); 
 
 
 
 UPDATE `t_amazon_ad_product_sd` SET is_state_pricing = 0   WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_product_sd t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.ad_id = t1.item_id
 AND t1.item_type = 'START_STOP' AND t.is_state_pricing = 1
 WHERE t.is_state_pricing = 1 AND t1.id IS NULL ) s ); 
 
 
 
  
 
 UPDATE `t_amazon_ad_keyword_sb` SET is_pricing = 0  WHERE 
 id IN ( SELECT s.id FROM (SELECT t.id id FROM t_amazon_ad_keyword_sb t LEFT JOIN t_advertise_strategy_status t1 
 ON t.puid = t1.puid AND t.shop_id = t1.shop_id AND t.keyword_id = t1.item_id
 AND t1.item_type = 'TARGET' AND t.is_pricing = 1
 WHERE t.is_pricing = 1 AND t1.id IS NULL ) s ); 
