package com.meiyunji.sponsored.ApplicationTest;

import com.alibaba.fastjson.JSON;
import com.meiyunji.sponsored.service.config.CosProperties;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.dbcompare.dto.ColumnDto;
import com.meiyunji.sponsored.service.dbcompare.dto.CompareResultDto;
import com.meiyunji.sponsored.service.dbcompare.service.IDbCompareService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.CannedAccessControlList;
import com.qcloud.cos.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DbCompareTest {

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;

    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;

    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;

    @Autowired
    private IDbCompareService dbCompareService;

    @Autowired
    private CosProperties cosProperties;

    @Autowired
    private COSClient nativeCosClient;

    @Test
    public void testDbCompare() throws Exception {
        CompareResultDto compareResultDto = new CompareResultDto();
        List<DataSource> dsList = dbCompareService.getDsList();
        dbCompareService.dbCompare(compareResultDto, dsList);
    }

    @Test
    public void testGetDbColumn() throws Exception {
        JdbcTemplate jdbcTemplate = cpcSbQueryKeywordReportDao.getJdbcTemplate(1612);
        DataSource dataSource = jdbcTemplate.getDataSource();
        Connection connection = dataSource.getConnection();
        DatabaseMetaData databaseMetaData = connection.getMetaData();
        ResultSet columnRS = databaseMetaData.getColumns(connection.getCatalog(), databaseMetaData.getUserName(), "t_amazon_ad_product_aggregation_report_00_7f", null);
        List<ColumnDto> columnDtoList = new ArrayList<>();
        while (columnRS.next()) {
            ColumnDto columnDto = new ColumnDto();
            //所属表
            columnDto.setTableName("t_amazon_ad_product_aggregation_report_00_7f");
            //字段名
            columnDto.setColumnName(columnRS.getString("COLUMN_NAME"));
            //字段类型
            columnDto.setColumnType(columnRS.getString("TYPE_NAME"));
            //字段长度
            columnDto.setColumnSize(columnRS.getInt("COLUMN_SIZE"));
            //小数数字个数
            columnDto.setDecimalDigits(columnRS.getInt("DECIMAL_DIGITS"));
            //默认值
            columnDto.setDefaultValue(columnRS.getString("COLUMN_DEF"));
            //是否非空
            columnDto.setIsAllowNull(columnRS.getString("NULLABLE"));
            //是否自增
            columnDto.setIsAutoIncrement(columnRS.getString("IS_AUTOINCREMENT"));
            //字段注释
            columnDto.setColumnRemark(columnRS.getString("REMARKS"));
            columnDtoList.add(columnDto);
        }

        System.out.println(JSON.toJSONString(columnDtoList));
    }

    @Test
    public void testUploadCos() throws IOException {
        //拼接objectKey
        StringBuilder fileId = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        fileId.append("dbcompare/").append(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YMD_DATE_FORMAT));
        fileId.append("/dbcompare").append(LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YYYYMMDDHHMMSS_DATE_FORMATE));
        fileId.append(".txt");
        String content = "12345";
        String key = fileId.toString();
        //获取bucketName
        String bucketName = cosProperties.getBuckets().get("temp").getBucket() + "-" + cosProperties.getAppId();
        //上传对象
        ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes());
        nativeCosClient.putObject(bucketName, key, inputStream, new ObjectMetadata());
        //设置私有读写
        nativeCosClient.setObjectAcl(bucketName, key, CannedAccessControlList.Private);
        //生成下载链接，半个小时后过期
        java.util.Date expirationDate = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        URL downloadUrl = nativeCosClient.generatePresignedUrl(bucketName, key, expirationDate, HttpMethodName.GET);
        inputStream.close();
        System.out.println(downloadUrl.toString());
    }

}
