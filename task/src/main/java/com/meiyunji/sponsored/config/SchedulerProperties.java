package com.meiyunji.sponsored.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "aadas.scheduler")
@Setter
@Getter
public class SchedulerProperties {
    @Setter
    @Getter
    public static class ExecutorProperties {
        private Boolean enabled = false;
        private Integer maxTasks = 1;
        private Integer maxRequestParallelism = 1;
    }

    private Map<String, ExecutorProperties> executors;
}
