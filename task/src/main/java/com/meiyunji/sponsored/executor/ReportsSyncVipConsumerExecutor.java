package com.meiyunji.sponsored.executor;

import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.service.syncTask.ReportMessageConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;

@Slf4j
public class ReportsSyncVipConsumerExecutor extends SyncTaskExecutor<ReportReadyNotification> {

    private final ReportMessageConsumer reportMessageConsumer;

    public ReportsSyncVipConsumerExecutor(
            Integer poolSize, Consumer<ReportReadyNotification> reportsCheckConsumer,
            ReportMessageConsumer reportMessageConsumer) {
        super(poolSize, reportsCheckConsumer);
        this.reportMessageConsumer = reportMessageConsumer;
    }

    @Override
    public void exec(ReportReadyNotification readyNotification) throws Exception {
        log.info("wade 线程数据 active: {}  pool-size: {}  queue-size: {} complete-size: {}", executor.getActiveCount(), executor.getPoolSize(),
                executor.getQueue().size(), executor.getCompletedTaskCount());
        reportMessageConsumer.process(readyNotification);
    }
}
