package com.meiyunji.sponsored.executor;

import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;
import org.apache.pulsar.shade.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.pulsar.shade.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.pulsar.shade.com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class AutoRuleExecutor<T> extends TaskExecutor {

    private static ObjectMapper objectMapper = getObjectMapper();

    private final Consumer<byte[]> consumer;

    public AutoRuleExecutor(Integer poolSize, Consumer<byte[]> consumer) {
        super(poolSize);
        this.consumer = consumer;
    }


    private static ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        return objectMapper;
    }

    @Override
    public Void call() throws InterruptedException {
        while (true) {
            acquire();
            try {
                Message<byte[]> message = receiveMessage();
                if (message != null) {
                    try {
                        submit(() -> {
                            try {
                                T value = objectMapper.readValue(new String(message.getValue()),
                                        getTypeReference());
                                exec(value);
                                ackMessage(message);
                            } catch (Exception exception) {
                                nackMessage(message);
                                log.error("Auto rule task failed with an error: ", exception);
                            } finally {
                                release();
                            }
                            return null;
                        });
                    } catch (Exception e) {
                        nackMessage(message);
                        release();
                        log.error("wade executor task submit failed: ", e);
                    }
                } else {
                    release();
                }
            } catch (PulsarClientException exception) {
                log.error("Auto rule message from pulsar failed: ", exception);
                release();
            } catch (Exception exception) {
                log.error("Auto rule message failed: ", exception);
                release();
            }
        }
    }

    protected abstract TypeReference<T> getTypeReference();


    private Messages<byte[]> batchReceive() throws PulsarClientException {
        return consumer.batchReceive();
    }

    private Message<byte[]> receiveMessage() throws PulsarClientException {
        return consumer.receive(3, TimeUnit.SECONDS);
    }

    private void ackMessage(MessageId messageId) throws PulsarClientException {
        consumer.acknowledge(messageId);
    }

    private void ackMessage(Message<byte[]> message) throws PulsarClientException {
        consumer.acknowledge(message);
    }

    private void ackMessage(Messages<byte[]> messages) throws PulsarClientException {
        consumer.acknowledge(messages);
    }

    private void nackMessage(MessageId messageId) {
        consumer.negativeAcknowledge(messageId);
    }

    private void nackMessage(Message<byte[]> message, Long delaySeconds) throws PulsarClientException {
        consumer.reconsumeLater(message, delaySeconds, TimeUnit.SECONDS);
    }

    private void nackMessage(Message<byte[]> message) {
        consumer.negativeAcknowledge(message);
    }

    private void nackMessage(Messages<byte[]> messages) {
        consumer.negativeAcknowledge(messages);
    }

    public abstract void exec(T messages) throws Exception;
}
