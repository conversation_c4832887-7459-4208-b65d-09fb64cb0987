package com.meiyunji.sponsored.executor;

import com.meiyunji.sponsored.service.syncTask.ManagementTargetConsumer;
import com.meiyunji.sponsored.service.syncTask.message.ManagementTargetStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.shade.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.pulsar.shade.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.pulsar.shade.com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.Callable;

@Slf4j
public class ManagementSpTargetConsumerExecutor extends SimpleStreamTranspondTaskExecutor<ManagementTargetStreamMessage> {
    private static TypeReference<ManagementTargetStreamMessage> typeReference =
            new TypeReference<ManagementTargetStreamMessage>() {
            };
    private final ManagementTargetConsumer managementTargetConsumer;

    public ManagementSpTargetConsumerExecutor(
            Integer poolSize, Consumer<byte[]> budgetUsageConsumer,
            ManagementTargetConsumer managementTargetConsumer) {
        super(poolSize, budgetUsageConsumer);
        this.managementTargetConsumer = managementTargetConsumer;
    }

    @Override
    protected List<ManagementTargetStreamMessage> getMessageEntry(ObjectMapper objectMapper, String message) throws JsonProcessingException {
        return objectMapper.readValue(message, new TypeReference<List<ManagementTargetStreamMessage>>() {
        });
    }
    @Override
    public Callable<Void> genCallable(List<ManagementTargetStreamMessage> messages, String messageId) throws Exception {
        log.info("wade 线程数据 active: {}  pool-size: {}  queue-size: {} complete-size: {}", executor.getActiveCount(), executor.getPoolSize(),
                executor.getQueue().size(), executor.getCompletedTaskCount());
        printMessageDelayTime(messages, "ad targeting");
        return () -> {
            Instant start = Instant.now();
            managementTargetConsumer.process(messages);
            log.info("Process traffic ad management targeting stream data seconds: {}", Duration.between(start, Instant.now()).getSeconds());

            return null;
        };
    }
}
