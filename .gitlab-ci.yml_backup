variables:
  #DOCKER_REPO_URL: "docker.meiyunji.net/ops"
  DOCKER_REPO_URL: "docker.meiyunji.net/svc"

stages:
  - release

docker-build:
  stage: release
  tags:
    - runner4
  only:
    refs:
      - tags
  script:
    - mvn clean package -DskipTests
    - export DOCKER_TAG=${CI_COMMIT_TAG}
    - cd grpc/
    - docker build -t $CI_PROJECT_NAME:${DOCKER_TAG} .
    - docker tag $CI_PROJECT_NAME:${DOCKER_TAG} $DOCKER_REPO_URL/$CI_PROJECT_NAME:${DOCKER_TAG}
    - docker push $DOCKER_REPO_URL/$CI_PROJECT_NAME:${DOCKER_TAG}
    - docker rmi $CI_PROJECT_NAME:${DOCKER_TAG} $DOCKER_REPO_URL/$CI_PROJECT_NAME:${DOCKER_TAG}
    - cd ../task/
    - docker build -t sellfox-sponsored-task:${DOCKER_TAG} .
    - docker tag sellfox-sponsored-task:${DOCKER_TAG} $DOCKER_REPO_URL/sellfox-sponsored-task:${DOCKER_TAG}
    - docker push $DOCKER_REPO_URL/sellfox-sponsored-task:${DOCKER_TAG}
    - docker rmi sellfox-sponsored-task:${DOCKER_TAG} $DOCKER_REPO_URL/sellfox-sponsored-task:${DOCKER_TAG}

