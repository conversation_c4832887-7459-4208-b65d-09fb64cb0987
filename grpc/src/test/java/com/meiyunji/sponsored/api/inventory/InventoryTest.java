package com.meiyunji.sponsored.api.inventory;

import com.meiyunji.sellfox.aadras.api.enumeration.RuleIndexTypePb;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.sellfoxApi.IFbaInventoryManageApi;
import com.meiyunji.sponsored.service.sellfoxApi.qo.AsinInventoryInfoReq;
import com.meiyunji.sponsored.service.sellfoxApi.vo.AsinInventoryInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-05-21 09:16
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class InventoryTest {
    @Resource
    private IFbaInventoryManageApi fbaInventoryManageApi;

    @Test
    public void testGetAsinFbaInventory() {
//        AsinInventoryInfoReq asinInventoryInfoReq = new AsinInventoryInfoReq();
//        asinInventoryInfoReq.setPuid(100);
//        asinInventoryInfoReq.setShopId(4636);
//        asinInventoryInfoReq.setAsins(Arrays.asList("CZWQBVZUY","B0CMX9C21R","CZWORDKKQ"));
//        AsinInventoryInfo asinInventoryInfo = fbaInventoryManageApi.getAsinInventoryInfo(asinInventoryInfoReq);
//        log.info(JSONUtil.objectToJson2(asinInventoryInfo));
        System.out.println(RuleIndexTypePb.RuleIndexType.valueOf("fbaSellableInventory"));

    }
}
