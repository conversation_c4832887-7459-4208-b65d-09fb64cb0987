package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * seller_id 过滤功能演示类
 * 用于演示和验证新的 seller_id 有效值检查功能
 */
public class SellerIdFilterDemo {

    public static void main(String[] args) {
        System.out.println("=== seller_id 过滤功能演示 ===\n");

        // 设置目标表
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        targetTables.add("products");

        // 测试用例
        String[] testCases = {
            // 有效的查询
            "SELECT * FROM users WHERE seller_id = 'valid_seller_123'",
            "SELECT * FROM users WHERE seller_id = ?",
            "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')",
            "SELECT * FROM users WHERE seller_id != NULL",
            "SELECT * FROM users WHERE seller_id <> ''",
            
            // 无效的查询
            "SELECT * FROM users WHERE seller_id = NULL",
            "SELECT * FROM users WHERE seller_id IS NULL",
            "SELECT * FROM users WHERE seller_id = ''",
            "SELECT * FROM users WHERE seller_id IS NOT NULL",
            "SELECT * FROM users WHERE seller_id IN (NULL, '')",
            
            // 混合查询
            "SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid')",
            "SELECT * FROM users WHERE seller_id = 'valid' OR seller_id IS NULL",
            
            // 非目标表查询
            "SELECT * FROM logs WHERE seller_id IS NULL",
            
            // 复杂查询
            "SELECT u.*, o.order_id FROM users u JOIN orders o ON u.id = o.user_id WHERE u.seller_id = 'valid_seller'",
            "SELECT u.*, o.order_id FROM users u JOIN orders o ON u.id = o.user_id WHERE u.seller_id IS NULL"
        };

        for (String sql : testCases) {
            testSql(sql, targetTables);
        }
    }

    private static void testSql(String sql, Set<String> targetTables) {
        System.out.println("测试 SQL: " + sql);
        
        try {
            // 解析 SQL
            List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (statements == null || statements.isEmpty()) {
                System.out.println("  结果: SQL 解析失败");
                System.out.println();
                return;
            }

            SQLStatement statement = statements.get(0);

            // 检查是否是 SELECT 语句
            if (!SqlTypeParser.isSelectSql(statement)) {
                System.out.println("  结果: 非 SELECT 语句，跳过检查");
                System.out.println();
                return;
            }

            // 检查表名
            Set<String> tableNames = SqlTypeParser.extractTableNames(statement);
            System.out.println("  涉及表: " + tableNames);

            // 检查是否涉及目标表
            boolean involvesTargetTable = tableNames.stream()
                    .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));
            
            if (!involvesTargetTable) {
                System.out.println("  结果: ✅ 通过 - 不涉及目标表");
                System.out.println();
                return;
            }

            // 检查字段是否存在
            boolean containsField = SqlTypeParser.containsFieldInWhere(statement, "seller_id");
            System.out.println("  包含 seller_id 字段: " + containsField);

            // 检查字段值是否有效
            boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
            System.out.println("  seller_id 值有效: " + hasValidValue);

            // 检查是否需要有效字段
            boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
            System.out.println("  需要有效字段: " + needsValidField);

            // 最终结果
            if (needsValidField) {
                System.out.println("  结果: ❌ 拦截 - seller_id 字段缺失或值无效");
            } else {
                System.out.println("  结果: ✅ 通过 - seller_id 检查通过");
            }

        } catch (Exception e) {
            System.out.println("  结果: ⚠️  解析异常 - " + e.getMessage());
        }
        
        System.out.println();
    }
}
