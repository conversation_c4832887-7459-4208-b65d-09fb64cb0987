package com.meiyunji.sponsored.filter;

import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.config.SqlConfigUtil;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * SfSqlFilter 测试类
 * 专门测试 seller_id 字段的有效值拦截功能
 */
public class SfSqlFilterTest {

    @Mock
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Mock
    private SqlConfigUtil sqlConfigUtil;

    private SfSqlFilter sqlFilter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置模拟对象的行为
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        targetTables.add("products");
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);

        // 创建 SfSqlFilter 实例并注入依赖
        sqlFilter = new SfSqlFilter();
        // 通过反射设置私有字段
        try {
            java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
            configField.setAccessible(true);
            configField.set(sqlFilter, dynamicRefreshNacosConfiguration);

            java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
            utilField.setAccessible(true);
            utilField.set(sqlFilter, sqlConfigUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    @Test
    public void testValidSellerIdQueries() {
        // 测试有效的 seller_id 查询 - 应该通过
        String[] validQueries = {
            "SELECT * FROM users WHERE seller_id = 'valid_seller_123'",
            "SELECT * FROM users WHERE seller_id = ?",
            "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')",
            "SELECT * FROM users WHERE seller_id != NULL",
            "SELECT * FROM users WHERE seller_id <> ''",
            "SELECT * FROM users WHERE seller_id = 'valid' AND status = 'active'"
        };

        for (String sql : validQueries) {
            assertDoesNotThrow(() -> {
                sqlFilter.doFilter(null, null, sql);
            }, "Valid SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testInvalidSellerIdQueries() {
        // 测试无效的 seller_id 查询 - 应该被拦截
        String[] invalidQueries = {
            "SELECT * FROM users WHERE seller_id = NULL",
            "SELECT * FROM users WHERE seller_id IS NULL",
            "SELECT * FROM users WHERE seller_id = ''",
            "SELECT * FROM users WHERE seller_id = '   '",
            "SELECT * FROM users WHERE seller_id IS NOT NULL", // 仅仅不为null还不够
            "SELECT * FROM users WHERE seller_id IN (NULL, '')" // 全部为无效值
        };

        for (String sql : invalidQueries) {
            BizServiceException exception = assertThrows(BizServiceException.class, () -> {
                sqlFilter.doFilter(null, null, sql);
            }, "Invalid SQL should throw exception: " + sql);
            
            assertTrue(exception.getMessage().contains("sellerId"), 
                "Exception message should mention sellerId: " + exception.getMessage());
        }
    }

    @Test
    public void testMixedValidInvalidQueries() {
        // 测试混合有效/无效值的查询
        String[] mixedQueries = {
            "SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid')", // 包含有效值，应该通过
            "SELECT * FROM users WHERE seller_id = 'valid' OR seller_id IS NULL", // 包含有效值，应该通过
            "SELECT * FROM users WHERE (seller_id = 'valid' OR status = 'active') AND seller_id != ''" // 复杂条件，应该通过
        };

        for (String sql : mixedQueries) {
            assertDoesNotThrow(() -> {
                sqlFilter.doFilter(null, null, sql);
            }, "Mixed valid SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testNonTargetTableQueries() {
        // 测试不涉及目标表的查询 - 应该通过（不需要检查）
        String[] nonTargetQueries = {
            "SELECT * FROM logs WHERE seller_id IS NULL",
            "SELECT * FROM config WHERE seller_id = ''",
            "SELECT * FROM temp_table WHERE seller_id = NULL"
        };

        for (String sql : nonTargetQueries) {
            assertDoesNotThrow(() -> {
                sqlFilter.doFilter(null, null, sql);
            }, "Non-target table SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testComplexQueries() {
        // 测试复杂查询
        String validComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id = 'valid_seller' AND o.status = 'completed'";
        
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, validComplexSql);
        }, "Valid complex SQL should not throw exception");

        String invalidComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id IS NULL AND o.status = 'completed'";
        
        assertThrows(BizServiceException.class, () -> {
            sqlFilter.doFilter(null, null, invalidComplexSql);
        }, "Invalid complex SQL should throw exception");
    }

    @Test
    public void testSubqueryWithSellerIdCheck() {
        // 测试子查询中的 seller_id 检查
        String validSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id = 'valid_seller'" +
                ")";
        
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, validSubquerySql);
        }, "Valid subquery SQL should not throw exception");

        String invalidSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id IS NULL" +
                ")";
        
        assertThrows(BizServiceException.class, () -> {
            sqlFilter.doFilter(null, null, invalidSubquerySql);
        }, "Invalid subquery SQL should throw exception");
    }

    @Test
    public void testUpdateDeleteStatements() {
        // 测试 UPDATE 和 DELETE 语句（这些主要检查 puid，但也可能涉及 seller_id）
        String updateSql = "UPDATE users SET status = 'inactive' WHERE puid = 123 AND seller_id = 'valid'";
        String deleteSql = "DELETE FROM users WHERE puid = 123 AND seller_id = 'valid'";

        // 这些语句主要由 PUID_CHECK 策略处理，但不应该因为 seller_id 检查而失败
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, updateSql);
        }, "UPDATE with valid seller_id should not throw exception");

        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, deleteSql);
        }, "DELETE with valid seller_id should not throw exception");
    }

    @Test
    public void testWhitelistQueries() {
        // 测试白名单查询
        String whitelistSql = "SELECT * FROM users WHERE seller_id IS NULL";
        
        // 将查询添加到白名单
        Set<String> whitelist = new HashSet<>();
        whitelist.add(whitelistSql);
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(whitelist);

        // 白名单查询应该通过，即使 seller_id 无效
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, whitelistSql);
        }, "Whitelisted SQL should not throw exception");
    }

    @Test
    public void testEmptyTargetTables() {
        // 测试空的目标表配置
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(new HashSet<>());

        String sql = "SELECT * FROM users WHERE seller_id IS NULL";
        
        // 如果没有配置目标表，应该跳过检查
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, sql);
        }, "SQL should not throw exception when no target tables configured");
    }

    @Test
    public void testInvalidSqlSyntax() {
        // 测试语法错误的 SQL
        String invalidSyntaxSql = "SELECT * FROM WHERE seller_id = 'valid'";
        
        // 语法错误的 SQL 应该被跳过（不抛出异常）
        assertDoesNotThrow(() -> {
            sqlFilter.doFilter(null, null, invalidSyntaxSql);
        }, "Invalid syntax SQL should be skipped");
    }

    @Test
    public void testCaseInsensitiveFieldNames() {
        // 测试字段名大小写不敏感
        String[] caseVariations = {
            "SELECT * FROM users WHERE SELLER_ID = 'valid'",
            "SELECT * FROM users WHERE Seller_Id = 'valid'",
            "SELECT * FROM users WHERE seller_ID = 'valid'",
            "SELECT * FROM users WHERE u.seller_id = 'valid'"
        };

        for (String sql : caseVariations) {
            assertDoesNotThrow(() -> {
                sqlFilter.doFilter(null, null, sql);
            }, "Case variation should not throw exception: " + sql);
        }
    }
}
