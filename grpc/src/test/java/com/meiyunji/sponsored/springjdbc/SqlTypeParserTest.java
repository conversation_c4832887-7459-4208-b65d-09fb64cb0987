package com.meiyunji.sponsored.common.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import org.junit.Test;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import static org.junit.Assert.*;

/**
 * SqlTypeParser 测试类
 */
public class SqlTypeParserTest {

    @Test
    public void testIsSelectStatement() {
        String selectSql = "SELECT * FROM users WHERE id = 1";
        assertTrue(SqlTypeParser.isSelectStatement(selectSql));

        String updateSql = "UPDATE users SET name = 'test' WHERE id = 1";
        assertFalse(SqlTypeParser.isSelectStatement(updateSql));
    }

    @Test
    public void testExtractTableNames() {
        String sql = "SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id WHERE u.sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("posts"));
    }

    @Test
    public void testContainsFieldInWhereForSelect() {
        String sqlWithsell_id = "SELECT * FROM users WHERE sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithsell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));

        String sqlWithoutsell_id = "SELECT * FROM users WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlWithoutsell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testNeedsFieldInWhere() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");

        // 测试涉及目标表但缺少sell_id字段的情况
        String sqlNeedssell_id = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlNeedssell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试涉及目标表且包含sell_id字段的情况
        String sqlHassell_id = "SELECT * FROM users WHERE sell_id = 123";
        statements = SqlTypeParser.parseSql(sqlHassell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试不涉及目标表的情况
        String sqlOtherTable = "SELECT * FROM products WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlOtherTable, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testContainsFieldInWhereWithAlias() {
        String sqlWithAlias = "SELECT u.name FROM users u WHERE u.sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithAlias, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testComplexJoinQuery() {
        String complexSql = "SELECT u.name, o.total FROM users u " +
                "LEFT JOIN orders o ON u.id = o.user_id " +
                "WHERE u.sell_id = 123 AND o.status = 'active'";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(complexSql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testEmptyTargetTables() {
        String sql = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> emptyTables = new HashSet<>();
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), emptyTables, "sell_id"));
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), null, "sell_id"));
    }
}
