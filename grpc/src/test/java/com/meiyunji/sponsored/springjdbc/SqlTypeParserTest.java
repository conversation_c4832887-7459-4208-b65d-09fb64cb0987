package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import org.junit.Test;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import static org.junit.Assert.*;

/**
 * SqlTypeParser 测试类
 */
public class SqlTypeParserTest {

    @Test
    public void testIsSelectStatement() {
        String selectSql = "SELECT * FROM users WHERE id = 1";
        assertTrue(SqlTypeParser.isSelectStatement(selectSql));


        String updateSql = "UPDATE users SET name = 'test' WHERE id = 1";
        assertFalse(SqlTypeParser.isSelectStatement(updateSql));
    }

    @Test
    public void testExtractTableNames() {
        String sql = "SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id WHERE u.sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("posts"));
    }

    @Test
    public void testContainsFieldInWhereForSelect() {
        String sqlWithSellId = "SELECT * FROM T_AMAZON_MARKETING_STREAM_DATA WHERE sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithSellId, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "SELL_ID"));

        String sqlWithoutsell_id = "SELECT * FROM users WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlWithoutsell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.containsFieldInWhere(statements.get(0), "SELL_ID"));
    }

    @Test
    public void testNeedsFieldInWhere() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");

        // 测试涉及目标表但缺少sell_id字段的情况
        String sqlNeedssell_id = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlNeedssell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试涉及目标表且包含sell_id字段的情况
        String sqlHassell_id = "SELECT * FROM users WHERE sell_id = 123";
        statements = SqlTypeParser.parseSql(sqlHassell_id, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试不涉及目标表的情况
        String sqlOtherTable = "SELECT * FROM products WHERE name = 'test'";
        statements = SqlTypeParser.parseSql(sqlOtherTable, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testContainsFieldInWhereWithAlias() {
        String sqlWithAlias = "SELECT u.name FROM users u WHERE u.sell_id = 123";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithAlias, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testComplexJoinQuery() {
        String complexSql = "SELECT u.name, o.total FROM users u " +
                "LEFT JOIN orders o ON u.id = o.user_id " +
                "WHERE u.sell_id = 123 AND o.status = 'active'";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(complexSql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testEmptyTargetTables() {
        String sql = "SELECT * FROM users WHERE name = 'test'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        Set<String> emptyTables = new HashSet<>();
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), emptyTables, "sell_id"));
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), null, "sell_id"));
    }

    // ==================== 子查询测试用例 ====================

    @Test
    public void testSimpleSubqueryInWhere() {
        String sql = "SELECT * FROM t_amazon_marketing_stream_data WHERE id IN (SELECT user_id FROM orders WHERE sell_id = 123)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("t_amazon_marketing_stream_data"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - 子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithExistsClause() {
        String sql = "SELECT * FROM users u WHERE EXISTS (SELECT 1 FROM orders o WHERE o.user_id = u.id AND o.sell_id = 456)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - EXISTS子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithNotExistsClause() {
        String sql = "SELECT * FROM users u WHERE NOT EXISTS (SELECT 1 FROM orders o WHERE o.user_id = u.id AND o.sell_id = 789)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - NOT EXISTS子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testNestedSubqueries() {
        String sql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE product_id IN (" +
                "SELECT id FROM products WHERE sell_id = 999" +
                "))";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取 - 嵌套子查询中的所有表
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(3, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));
        assertTrue(tableNames.contains("products"));

        // 测试字段检查 - 最内层子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryInSelectClause() {
        String sql = "SELECT u.name, (SELECT COUNT(*) FROM orders o WHERE o.user_id = u.id AND o.sell_id = 111) as order_count " +
                "FROM users u WHERE u.active = 1";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 注意：SELECT子句中的子查询可能不会被WHERE条件检查器检测到
        // 这取决于具体的实现逻辑
    }

    @Test
    public void testSubqueryWithJoin() {
        String sql = "SELECT u.name FROM users u " +
                "JOIN (SELECT user_id FROM orders WHERE sell_id = 222) o ON u.id = o.user_id " +
                "WHERE u.status = 'active'";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - JOIN子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithUnion() {
        String sql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE sell_id = 333 " +
                "UNION " +
                "SELECT customer_id FROM purchases WHERE sell_id = 444" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(3, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));
        assertTrue(tableNames.contains("purchases"));

        // 测试字段检查 - UNION子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithCorrelatedQuery() {
        String sql = "SELECT * FROM users u WHERE u.id = (" +
                "SELECT o.user_id FROM orders o WHERE o.sell_id = 555 AND o.user_id = u.id LIMIT 1" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - 关联子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithAnyAllOperators() {
        String sql = "SELECT * FROM users WHERE age > ANY (SELECT age FROM employees WHERE sell_id = 666)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("employees"));

        // 测试字段检查 - ANY子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithAllOperator() {
        String sql = "SELECT * FROM users WHERE age > ALL (SELECT age FROM employees WHERE sell_id = 777)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("employees"));

        // 测试字段检查 - ALL子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithoutTargetField() {
        String sql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE status = 'completed')";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 测试字段检查 - 子查询中不包含 sell_id
        assertFalse(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testComplexSubqueryWithMultipleConditions() {
        String sql = "SELECT u.*, " +
                "(SELECT COUNT(*) FROM orders o WHERE o.user_id = u.id AND o.sell_id = 888) as order_count " +
                "FROM users u " +
                "WHERE u.created_date > '2023-01-01' " +
                "AND EXISTS (SELECT 1 FROM profiles p WHERE p.user_id = u.id AND p.sell_id = 999) " +
                "AND u.id NOT IN (SELECT user_id FROM blacklist WHERE sell_id = 1000)";

        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(4, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));
        assertTrue(tableNames.contains("profiles"));
        assertTrue(tableNames.contains("blacklist"));

        // 测试字段检查 - 多个子查询中都包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryInHavingClause() {
        String sql = "SELECT user_id, COUNT(*) as order_count " +
                "FROM orders " +
                "GROUP BY user_id " +
                "HAVING COUNT(*) > (SELECT AVG(order_count) FROM user_stats WHERE sell_id = 1111)";

        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("orders"));
        assertTrue(tableNames.contains("user_stats"));

        // 测试字段检查 - HAVING子查询中包含 sell_id
        // 注意：HAVING子句的检查可能需要特殊处理
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithCaseWhen() {
        String sql = "SELECT *, " +
                "CASE " +
                "  WHEN id IN (SELECT user_id FROM vip_users WHERE sell_id = 1222) THEN 'VIP' " +
                "  WHEN id IN (SELECT user_id FROM premium_users WHERE sell_id = 1333) THEN 'Premium' " +
                "  ELSE 'Regular' " +
                "END as user_type " +
                "FROM users WHERE status = 'active'";

        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(3, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("vip_users"));
        assertTrue(tableNames.contains("premium_users"));

        // 测试字段检查 - CASE WHEN子查询中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    // ==================== needsFieldInWhere 子查询测试用例 ====================

    @Test
    public void testNeedsFieldInWhereWithSubquery() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");

        // 测试：涉及目标表且子查询中包含 sell_id 字段
        String sqlWithField = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE sell_id = 123)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithField, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 因为包含了 sell_id 字段，所以不需要额外的字段检查
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试：涉及目标表但子查询中不包含 sell_id 字段
        String sqlWithoutField = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE status = 'active')";
        statements = SqlTypeParser.parseSql(sqlWithoutField, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 因为不包含 sell_id 字段，所以需要额外的字段检查
        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testNeedsFieldInWhereWithExistsSubquery() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("profiles");

        // 测试：EXISTS子查询中包含 sell_id
        String sqlWithExists = "SELECT * FROM users u WHERE EXISTS (SELECT 1 FROM profiles p WHERE p.user_id = u.id AND p.sell_id = 456)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlWithExists, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试：EXISTS子查询中不包含 sell_id
        String sqlWithoutExists = "SELECT * FROM users u WHERE EXISTS (SELECT 1 FROM profiles p WHERE p.user_id = u.id AND p.active = 1)";
        statements = SqlTypeParser.parseSql(sqlWithoutExists, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testNeedsFieldInWhereWithNestedSubquery() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        targetTables.add("products");

        // 测试：嵌套子查询中包含 sell_id
        String sqlNested = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE product_id IN (" +
                "SELECT id FROM products WHERE sell_id = 789" +
                "))";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlNested, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试：嵌套子查询中不包含 sell_id
        String sqlNestedWithout = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE product_id IN (" +
                "SELECT id FROM products WHERE category = 'electronics'" +
                "))";
        statements = SqlTypeParser.parseSql(sqlNestedWithout, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testNeedsFieldInWhereWithMixedConditions() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");

        // 测试：主查询中包含 sell_id，子查询中不包含
        String sqlMainHasField = "SELECT * FROM users WHERE sell_id = 100 AND id IN (SELECT user_id FROM orders WHERE status = 'completed')";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlMainHasField, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试：主查询中不包含 sell_id，子查询中包含
        String sqlSubHasField = "SELECT * FROM users WHERE status = 'active' AND id IN (SELECT user_id FROM orders WHERE sell_id = 200)";
        statements = SqlTypeParser.parseSql(sqlSubHasField, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));

        // 测试：主查询和子查询都不包含 sell_id
        String sqlNeitherHasField = "SELECT * FROM users WHERE status = 'active' AND id IN (SELECT user_id FROM orders WHERE status = 'completed')";
        statements = SqlTypeParser.parseSql(sqlNeitherHasField, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        assertTrue(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    @Test
    public void testNeedsFieldInWhereWithNonTargetTables() {
        Set<String> targetTables = new HashSet<>();
        targetTables.add("customers");
        targetTables.add("invoices");

        // 测试：查询的表不在目标表列表中
        String sqlNonTarget = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE sell_id = 300)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sqlNonTarget, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 因为不涉及目标表，所以不需要字段检查
        assertFalse(SqlTypeParser.needsFieldInWhere(statements.get(0), targetTables, "sell_id"));
    }

    // ==================== 边界情况和错误处理测试 ====================

    @Test
    public void testSubqueryWithEmptyResult() {
        String sql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM orders WHERE 1=0)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(2, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));

        // 即使子查询结果为空，字段检查逻辑应该正常工作
        assertFalse(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithComplexExpressions() {
        String sql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE " +
                "sell_id = 123 AND " +
                "created_date BETWEEN '2023-01-01' AND '2023-12-31' AND " +
                "amount > (SELECT AVG(amount) FROM orders WHERE sell_id = 456)" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试字段检查 - 复杂表达式中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithFunctions() {
        String sql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE " +
                "DATE(created_date) = CURDATE() AND " +
                "UPPER(status) = 'COMPLETED' AND " +
                "sell_id = 789" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试字段检查 - 函数调用中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithAliasedFields() {
        String sql = "SELECT * FROM users u WHERE u.id IN (" +
                "SELECT o.user_id FROM orders o WHERE o.sell_id = 999 AND o.status = 'active'" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试字段检查 - 带别名的字段
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithMultipleInClauses() {
        String sql = "SELECT * FROM users WHERE " +
                "id IN (SELECT user_id FROM orders WHERE sell_id = 111) AND " +
                "department_id IN (SELECT id FROM departments WHERE sell_id = 222) AND " +
                "role_id IN (SELECT id FROM roles WHERE active = 1)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试表名提取
        Set<String> tableNames = SqlTypeParser.extractTableNames(statements.get(0));
        assertEquals(4, tableNames.size());
        assertTrue(tableNames.contains("users"));
        assertTrue(tableNames.contains("orders"));
        assertTrue(tableNames.contains("departments"));
        assertTrue(tableNames.contains("roles"));

        // 测试字段检查 - 多个IN子句中包含 sell_id
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithUpdateStatement() {
        String sql = "UPDATE users SET status = 'inactive' WHERE id IN (" +
                "SELECT user_id FROM orders WHERE sell_id = 333 AND status = 'cancelled'" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试UPDATE语句的子查询
        assertTrue(SqlTypeParser.isUpdateOrDeleteSql(statements.get(0)));
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testSubqueryWithDeleteStatement() {
        String sql = "DELETE FROM users WHERE id IN (" +
                "SELECT user_id FROM inactive_users WHERE sell_id = 444 AND last_login < '2023-01-01'" +
                ")";
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 测试DELETE语句的子查询
        assertTrue(SqlTypeParser.isUpdateOrDeleteSql(statements.get(0)));
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }

    @Test
    public void testInvalidSubquerySyntax() {
        // 测试语法错误的子查询
        String invalidSql = "SELECT * FROM users WHERE id IN (SELECT user_id FROM WHERE sell_id = 555)";
        List<SQLStatement> statements = SqlTypeParser.parseSql(invalidSql, JdbcConstants.MYSQL.name());

        // 解析失败时应该返回null或空列表
        assertTrue(statements == null || statements.isEmpty());
    }

    @Test
    public void testSubqueryPerformanceWithLargeIn() {
        // 测试大量IN条件的性能
        StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM users WHERE id IN (");
        for (int i = 1; i <= 1000; i++) {
            if (i > 1) sqlBuilder.append(", ");
            sqlBuilder.append(i);
        }
        sqlBuilder.append(") AND sell_id = 777");

        String sql = sqlBuilder.toString();
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        // 即使有大量IN条件，字段检查也应该正常工作
        assertTrue(SqlTypeParser.containsFieldInWhere(statements.get(0), "sell_id"));
    }
}
