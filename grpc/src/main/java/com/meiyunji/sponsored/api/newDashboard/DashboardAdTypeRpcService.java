package com.meiyunji.sponsored.api.newDashboard;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.*;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTypeRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTypeResponse;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTypeResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.RpcDashboardAdTypeServiceGrpc;
import com.meiyunji.sponsored.rpc.newDashboard.vo.UrlResponse;
import com.meiyunji.sponsored.service.batchCreate.adStructure.AdStructure;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureEnum;
import com.meiyunji.sponsored.service.batchCreate.service.ISpBatchCreateService;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardCurrencyEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardModuleEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdTypeService;
import com.meiyunji.sponsored.service.newDashboard.util.DateTimeUtil;
import com.meiyunji.sponsored.service.newDashboard.util.YoyDateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdTypeReqVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-08-31  19:21
 */

@GRpcService
@Slf4j
public class DashboardAdTypeRpcService extends RpcDashboardAdTypeServiceGrpc.RpcDashboardAdTypeServiceImplBase {

    @Autowired
    private IDashboardAdTypeService dashboardAdTypeService;

    @Override
    public void queryAdTypeCharts(DashboardAdTypeRequest request, StreamObserver<DashboardAdTypeResponse> responseObserver) {
        log.info("dashboard query adtype charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdTypeResponse.Builder builder = DashboardAdTypeResponse.newBuilder();

        DashboardAdTypeReqVo reqVo = processParam(request);

        log.info("dashboard query adtype charts, process request data: {}", request);

        if (Objects.isNull(reqVo)) {
            log.error("sdashboard query adtype charts, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            List<DashboardAdTypeResponseVo> voList = dashboardAdTypeService.queryAdTypeCharts(reqVo);
            builder.addAllData(voList);
            builder.setCode(Result.SUCCESS);

            sw.stop();
            log.info("dashboard query adtype charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    @Override
    public void exportAdTypeCharts(DashboardAdTypeRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("dashboard export adtype charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        UrlResponse.Builder builder = UrlResponse.newBuilder();

        try {
            DashboardAdTypeReqVo reqVo = processParam(request);

            if (Objects.isNull(reqVo)) {
                log.error("dashboard export adtype charts, check param error");
                builder.setCode(Int32Value.of(Result.ERROR));
                builder.setMsg("请求参数错误");
            } else {
                List<String> urlList = dashboardAdTypeService.exportAdTypeCharts(reqVo);
                if (CollectionUtils.isNotEmpty(urlList)) {
                    builder.addAllUrls(urlList);
                    builder.setCode(Int32Value.of(Result.SUCCESS));
                } else {
                    builder.setCode(Int32Value.of(Result.ERROR));
                    builder.setMsg("process.msg.sync.fail");
                }

                sw.stop();
                log.info("dashboard export adtype charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
            }
        } catch (Exception e) {
            builder.setCode(Int32Value.of(Result.ERROR));
            log.error("dashboard export adtype charts, puid: {}", request.getPuid(), e);
            builder.setMsg("process.msg.sync.fail");
        }


        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 处理参数
     * @param request
     * @return
     */
    private DashboardAdTypeReqVo processParam(DashboardAdTypeRequest request) {
        //校验值是否合法
        if (!DashboardCurrencyEnum.currencySet.contains(request.getCurrency())) {
            return null;
        }

        //同比期
        List<String> yoyDate = YoyDateUtil.getYoyDate(request.getStartDate(), request.getEndDate());

        //将grpc请求对象转换未查询实体类
        DashboardAdTypeReqVo reqVo = new DashboardAdTypeReqVo();
        BeanUtils.copyProperties(request, reqVo);
        reqVo.setPuid(reqVo.getPuid());
        reqVo.setMarketplaceIdList(reqVo.getMarketplaceIdList());
        reqVo.setShopIdList(reqVo.getShopIdList());
        reqVo.setPercent(reqVo.getPercent());
        reqVo.setYoy(reqVo.getYoy());
        reqVo.setMom(reqVo.getMom());
        if (CollectionUtils.isEmpty(yoyDate)) {
            // 开始时间故意写得比结束时间晚,令后续查出来的同比数据为空
            reqVo.setYoyOverLimit(true);
            reqVo.setYoyStartDate(DateUtil.toFormatDate(request.getStartDate()));
            reqVo.setYoyEndDate(DateUtil.dateToStrWithTime(DateUtil.addDay(DateUtil.strToDate(request.getStartDate(), DateUtil.PATTERN_YYYYMMDD), -1), DateUtil.PATTERN));
        } else {
//            reqVo.setYoyStartDate(yoyDate.get(0));
//            reqVo.setYoyEndDate(yoyDate.get(1));
            Optional.ofNullable(yoyDate.get(0)).map(DateUtil::toFormatDate).ifPresent(reqVo::setYoyStartDate);
            Optional.ofNullable(yoyDate.get(1)).map(DateUtil::toFormatDate).ifPresent(reqVo::setYoyEndDate);
        }
        Optional.of(request.getStartDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setStartDate);
        Optional.of(request.getEndDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setEndDate);
        Optional.of(request.getMomStartDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setMomStartDate);
        Optional.of(request.getMomEndDate()).map(DateUtil::toFormatDate).ifPresent(reqVo::setMomEndDate);
        DateTimeUtil.resetTimeRange(reqVo, DashboardModuleEnum.AD_TYPE);
        Optional.of(request.getPortfolioIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setPortfolioIds);
        Optional.of(request.getCampaignIdsList()).filter(CollectionUtils::isNotEmpty).ifPresent(reqVo::setCampaignIds);
        return reqVo;
    }


}
