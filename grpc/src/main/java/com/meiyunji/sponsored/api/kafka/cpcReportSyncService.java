package com.meiyunji.sponsored.api.kafka;

import com.amazon.advertising.mode.campaigns.Campaign;
import com.amazon.advertising.spV3.campaign.entity.CampaignExtendEntityV3;
import com.amazon.advertising.sb.mode.campaigm.CampaignV4;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.rpc.syncReport.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import io.grpc.stub.StreamObserver;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: wade
 * @date: 2021/11/15 11:22
 * @describe: 广告同步报告grpc api
 */
@GRpcService
public class cpcReportSyncService extends RPCAdSyncReportServiceGrpc.RPCAdSyncReportServiceImplBase {
    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;

    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;

    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;


    @Override
    public void detectAdTypeSyncCampaign(DetectAdTypeSyncCampaignRequest request, StreamObserver<DetectAdTypeSyncCampaignResponse> responseObserver) {
        DetectAdTypeSyncCampaignResponse.Builder builder = DetectAdTypeSyncCampaignResponse.newBuilder();
        ShopAuth shop = shopAuthService.getById(request.getShopId());

        Boolean res = null;
        if (request.getType().getNumber() == AdTypeEnum.sp_VALUE) {
            List<CampaignExtendEntityV3> spCampaignList = cpcCampaignApiService.detectAdTypeSyncSpCampaign(shop);
            res = !CollectionUtils.isEmpty(spCampaignList);
        }
        if (request.getType().getNumber() == AdTypeEnum.sb_VALUE) {
            List<CampaignV4> sbCampaignList = cpcSbCampaignApiService.detectAdTypeSyncSbCampaign(shop);
            res = !CollectionUtils.isEmpty(sbCampaignList);
        }
        if (request.getType().getNumber() == AdTypeEnum.sd_VALUE) {
            List<com.amazon.advertising.sd.mode.Campaign> sdCampaignList = cpcSdCampaignApiService.detectAdTypeSyncSdCampaign(shop);
            res = !CollectionUtils.isEmpty(sdCampaignList);
        }
        if (res == null) {
            res = true;
        }
        builder.setCode(Result.SUCCESS);
        builder.setData(res);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 按门店同步广告管理数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void syncAdDataByShop(SyncAdDataByShopRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        ShopAuth shop = shopAuthService.getById(request.getShopId());
        if (request.getType().getNumber()==AdTypeEnum.sp_VALUE) {
            cpcAdSyncService.syncSpByShop(shop,null,null);
        } else if (request.getType().getNumber()==AdTypeEnum.sb_VALUE) {
            cpcAdSyncService.syncSbByShop(shop,null,null);
        } else if (request.getType().getNumber()==AdTypeEnum.sd_VALUE) {
            cpcAdSyncService.syncSdByShop(shop,null, null);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
