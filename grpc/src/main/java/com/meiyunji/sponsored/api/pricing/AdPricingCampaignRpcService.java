package com.meiyunji.sponsored.api.pricing;


import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.campaign.pricing.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdPricingCampaignService;
import com.meiyunji.sponsored.service.cpc.vo.pricing.*;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * @describe: 分时调价活动
 */
@GRpcService
@Slf4j
public class AdPricingCampaignRpcService extends RPCPricingCampaignServiceGrpc.RPCPricingCampaignServiceImplBase {

    @Autowired
    private IAmazonAdPricingCampaignService campaignService;


    @Override
    public void getAdAsinList(GetAdAsinListRequest request, StreamObserver<GetAdAsinResponse> responseObserver) {
        log.info("活动getAdAsinList request {}", request);
        GetAdAsinResponse.Builder builder = GetAdAsinResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasType()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<String> result = campaignService.getAdAsinList(request.getPuid(), request.getShopId(), request.getCampaignId(), request.getGroupId(), request.getType());

            if (result.success()) {
                builder.setCode(Result.SUCCESS);
                builder.setData(result.getData() != null ? String.valueOf(result.getData()) : "");
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPortfolioAsinList(GetPortfolioAsinListRequest request, StreamObserver<GetPortfolioAsinListResponse> responseObserver) {
        log.info("广告组合getPortfolioAsinList request {}", request);
        GetPortfolioAsinListResponse.Builder builder = GetPortfolioAsinListResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasPortfolioId()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<String> result = campaignService.getPortfolioAsinList(request.getPuid(), request.getShopId(), request.getPortfolioId());

            if (result.success()) {
                builder.setCode(Result.SUCCESS);
                builder.setData(result.getData() != null ? String.valueOf(result.getData()) : "");
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPortfolioAsinMetrics(GetPortfolioAsinMetricsRequest request, StreamObserver<GetPortfolioAsinMetricsResponse> responseObserver) {
        log.info("广告组合getPortfolioAsinMetrics request {}", request);
        GetPortfolioAsinMetricsResponse.Builder builder = GetPortfolioAsinMetricsResponse.newBuilder();
        if (!request.hasShopId() || !request.hasPuid() || !request.hasPortfolioId()) {
            builder.setMsg("请求参数错误");
            builder.setCode(Result.ERROR);
        } else {
            Result<List<AdPortfolioAsinOrderNum>> result = new Result<>();
            if ("1".equals(request.getType())) {
                result = campaignService.getPortfolioAsinOrderNumDay(request.getPuid(), request.getShopId(),request.getStartTime(),request.getEndTime(), request.getPortfolioId(),request.getAsinList());
            } else if ("2".equals(request.getType())) {
                result = campaignService.getPortfolioAsinOrderNumWeekSuperposition(request.getPuid(), request.getShopId(),request.getStartTime(),request.getEndTime(), request.getPortfolioId(),request.getAsinList());
            }
            if (result.success()) {
                builder.setCode(Result.SUCCESS);
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    result.getData().forEach(e->{
                        PortfolioAsinMetrics.Builder builder1 = PortfolioAsinMetrics.newBuilder();
                        builder1.setAdOrderNum(e.getAdOrderNum());
                        builder1.setOrderNum(e.getOrderNum());
                        builder1.setLabel(e.getLabel());
                        builder.addData(builder1.build());
                    });
                }
            } else {
                builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
                builder.setCode(Result.ERROR);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
