package com.meiyunji.sponsored.api.sp;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.sp.asinReport.AsinReportRpcPageVo;
import com.meiyunji.sponsored.rpc.sp.asinReport.PageListAsinReportRequest;
import com.meiyunji.sponsored.rpc.sp.asinReport.PageListAsinReportResponse;
import com.meiyunji.sponsored.rpc.sp.asinReport.RPCSpAsinReportServiceGrpc;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAsinReportService;
import com.meiyunji.sponsored.service.cpc.vo.AsinReportPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AsinReportPageVo;
import io.grpc.stub.StreamObserver;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/27 23:30
 * @describe: 购买Asin报告
 */
@GRpcService
public class AsinReportSpRpcService extends RPCSpAsinReportServiceGrpc.RPCSpAsinReportServiceImplBase {
    @Autowired
    private ICpcAsinReportService cpcAsinReportService;

    @Override
    public void pageList(PageListAsinReportRequest request, StreamObserver<PageListAsinReportResponse> responseObserver) {
        PageListAsinReportResponse.Builder builder = PageListAsinReportResponse.newBuilder();

        AsinReportPageParam param = new AsinReportPageParam();
        param.setPageNo(request.hasPageNo() ? request.getPageNo().getValue() : 1);
        param.setPageSize(request.hasPageSize() ? request.getPageSize().getValue() : 20);
        param.setShopId(request.getShopId().getValue());
        param.setPuid(request.getPuid().getValue());
        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setSearchField(request.getSearchField());
        param.setSearchValue(request.getSearchValue());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        //做参数校验
        String err = checkPageParam(param);
        if (!request.hasPuid() || !request.hasShopId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else if (StringUtils.isNotBlank(err)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(err);
        } else {
            Result<Page<AsinReportPageVo>> res = cpcAsinReportService.pageList(param);
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }

            //处理分页
            if (res.success()) {
                Page<AsinReportPageVo> voPage = res.getData();
                PageListAsinReportResponse.PageData.Builder voBuilder = PageListAsinReportResponse.PageData.newBuilder();
                voBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
                voBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
                voBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
                voBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
                //处理list
                List<AsinReportPageVo> rows = voPage.getRows();
                if (CollectionUtils.isNotEmpty(rows)) {
                    List<AsinReportRpcPageVo> rpvcVo = rows.stream().filter(Objects::nonNull).map(item -> {
                        AsinReportRpcPageVo.Builder innerVoBuilder = AsinReportRpcPageVo.newBuilder();
                        if (item.getShopId() != null) {
                            innerVoBuilder.setShopId(Int32Value.of(item.getShopId()));
                        }
                        if (item.getAsin() != null) {
                            innerVoBuilder.setAsin(item.getAsin());
                        }
                        if (item.getAsinImage() != null) {
                            innerVoBuilder.setAsinImage(item.getAsinImage());
                        }
                        if (item.getSku() != null) {
                            innerVoBuilder.setSku(item.getSku());
                        }
                        if (item.getOtherAsin() != null) {
                            innerVoBuilder.setOtherAsin(item.getOtherAsin());
                        }
                        if (item.getOtherAsinImage() != null) {
                            innerVoBuilder.setOtherAsinImage(item.getOtherAsinImage());
                        }
                        if (item.getTargetValue() != null) {
                            innerVoBuilder.setTargetValue(item.getTargetValue());
                        }
                        if (item.getMatchType() != null) {
                            innerVoBuilder.setMatchType(item.getMatchType());
                        }
                        if (item.getOtherSkuSale() != null) {
                            innerVoBuilder.setOtherSkuSale(item.getOtherSkuSale());
                        }
                        if (item.getOtherSkuOrderNum() != null) {
                            innerVoBuilder.setOtherSkuOrderNum(Int32Value.of(item.getOtherSkuOrderNum()));
                        }
                        return innerVoBuilder.build();
                    }).collect(Collectors.toList());
                    voBuilder.addAllRows(rpvcVo);
                }


                builder.setData(voBuilder.build());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    // 校验分页查询的参数
    private String checkPageParam(AsinReportPageParam param) {
        if (param == null || param.getShopId() == null) {
            return "请求参数错误";
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        } else {
            param.setStartDate(param.getStartDate().replace("-", ""));
            param.setEndDate(param.getEndDate().replace("-", ""));
        }
        return null;
    }
}
