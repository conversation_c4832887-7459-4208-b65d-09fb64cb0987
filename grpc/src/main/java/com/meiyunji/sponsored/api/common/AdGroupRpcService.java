package com.meiyunji.sponsored.api.common;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.adGroup.GroupInfoRequest;
import com.meiyunji.sponsored.rpc.adGroup.GroupInfoResponse;
import com.meiyunji.sponsored.rpc.adGroup.RPCAdGroupServiceGrpc;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: ys
 * @date: 2024/1/20 17:26
 * @describe:
 */
@GRpcService
@Slf4j
public class AdGroupRpcService extends RPCAdGroupServiceGrpc.RPCAdGroupServiceImplBase {
    @Autowired
    private ICpcAdGroupService cpcAdGroupService;
    @Override
    public void getGroupInfo(GroupInfoRequest request, StreamObserver<GroupInfoResponse> responseObserver) {
        GroupInfoResponse.Builder response = GroupInfoResponse.newBuilder();
        GroupInfoResponse.GroupPageVo.Builder info = GroupInfoResponse.GroupPageVo.newBuilder();
        //查询广告活动基本数据
        Integer puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(request.getShopId()).map(Int32Value::getValue).orElse(null);
        String campaignId = Optional.of(request.getCampaignId()).orElse("");
        String adGroupId = Optional.of(request.getAdGroupId()).orElse("");
        String type = Optional.of(request.getType()).orElse(Constants.SP);
        if (!checkParams(puid, shopId, campaignId, adGroupId, type)) {
            response.setCode(Int32Value.of(Result.ERROR));
            response.setMsg("请求参数错误");
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }
        StopWatch sw = new StopWatch();
        buildAdGroupInfo(info, cpcAdGroupService.getInfo(puid, shopId, campaignId, adGroupId, type));
        response.setData(info.build());
        response.setCode(Int32Value.of(Result.SUCCESS));
        log.info("获取广告组基本信息接口调用花费时间 :{} ,puid: {},shopId: {}", sw.getTotalTimeSeconds(), request.getPuid(), request.getShopId());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    private boolean checkParams(Integer puid, Integer shopId,
                                String campaignId, String adGroupId,
                                String type) {
        return !Objects.isNull(puid) && !Objects.isNull(shopId)
                && !StringUtils.isBlank(campaignId) && !StringUtils.isBlank(adGroupId)
                && !StringUtils.isBlank(type);
    }

    private void buildAdGroupInfo(GroupInfoResponse.GroupPageVo.Builder response, GroupPageVo vo) {
        BeanUtils.copyProperties(vo, response, ParamCopyUtil.checkPropertiesNullOrEmpty(vo));
        Optional.ofNullable(vo.getAdGroupType()).ifPresent(response::setGroupType);
        Optional.ofNullable(vo.getShopId()).map(Int32Value::of).ifPresent(response::setShopId);
        Optional.ofNullable(vo.getType()).ifPresent(response::setAdType);
        Optional.ofNullable(vo.getName()).ifPresent(response::setAdGroupName);
        Optional.ofNullable(vo.getTargetingType()).ifPresent(response::setTargetType);
        Optional.ofNullable(vo.getCampaignTargetingType()).ifPresent(response::setCampaignTargetingType);
        Optional.ofNullable(vo.getBrandEntityId()).ifPresent(response::setBrandEntityId);
        Optional.ofNullable(vo.getLandingPage()).ifPresent(response::setLandingPage);
        Optional.ofNullable(vo.getAdFormat()).ifPresent(response::setAdFormat);
        Optional.ofNullable(vo.getCreativeType()).ifPresent(response::setCreativeType);
        Optional.ofNullable(vo.getLandingPageType()).ifPresent(response::setLandingPageType);
    }
}
