package com.meiyunji.sponsored.api.reportImport2;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.reportImport.*;
import com.meiyunji.sponsored.grpc.reportImport2.AddAmazonAdReportImportTaskRequestPb;
import com.meiyunji.sponsored.grpc.reportImport2.AddAmazonAdReportImportTaskResponsePb;
import com.meiyunji.sponsored.grpc.reportImport2.AmazonAdReportImportApiServiceGrpc;

import com.meiyunji.sponsored.service.reportImport2.service.impl.AmazonAdReportsImportServiceImpl;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportCosFileParam;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportAddParam;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportFileParam;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@GRpcService
@Slf4j
public class AmazonAdReportImportRpcService extends AmazonAdReportImportApiServiceGrpc.AmazonAdReportImportApiServiceImplBase {

    private final AmazonAdReportsImportServiceImpl amazonAdReportImportRpcService;

    public AmazonAdReportImportRpcService(AmazonAdReportsImportServiceImpl amazonAdReportImportRpcService) {
        this.amazonAdReportImportRpcService = amazonAdReportImportRpcService;
    }

    @Override
    public void addReportImportTask(AddAmazonAdReportImportTaskRequestPb.AddAmazonAdReportImportTaskRequest request, StreamObserver<AddAmazonAdReportImportTaskResponsePb.AddAmazonAdReportImportTaskResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            AmazonAdReportImportAddParam reportImportAddParam = new AmazonAdReportImportAddParam();
            ArrayList<AmazonAdReportImportFileParam> reportImportFileParams = new ArrayList<>();
            for (AddAmazonAdReportImportTaskRequestPb.ReportImportFile reportImportFile : request.getImportFileList()) {
                AmazonAdReportImportFileParam reportImportFileParam = new AmazonAdReportImportFileParam();
                reportImportFileParam.setReportType(request.getReportType());
                reportImportFileParam.setAdType(request.getAdType());
                reportImportFileParam.setCountDate(reportImportFile.getCountDate());
                reportImportAddParam.setPuid(request.getPuid());
                List<AmazonAdReportCosFileParam> cosFileParams = new ArrayList<>();
                reportImportFile.getFileList().forEach(item -> {
                    AmazonAdReportCosFileParam reportCosFileParam = new AmazonAdReportCosFileParam();
                    reportCosFileParam.setTitle(item.getTitle());
                    reportCosFileParam.setFilePath(item.getFilePath());
                    reportCosFileParam.setFileId(item.getFileId());
                    reportCosFileParam.setOrder(item.getOrder());
                    cosFileParams.add(reportCosFileParam);
                });

                reportImportFileParam.setReportCosFiles(cosFileParams);
                reportImportFileParams.add(reportImportFileParam);
            }
            reportImportAddParam.setDataSource(request.getDataSource());
            reportImportAddParam.setStart(request.getStart());
            reportImportAddParam.setEnd(request.getEnd());
            reportImportAddParam.setShopId(request.getShopId());
            reportImportAddParam.setCreateName(request.getCreateName());
            reportImportAddParam.setTaskUuid(request.getUuid());
            reportImportAddParam.setUid(request.getCreateUid());
            reportImportAddParam.setAdType(request.getAdType());
            reportImportAddParam.setReportType(request.getReportType());
            reportImportAddParam.setReportImportFileParam(reportImportFileParams);
            amazonAdReportImportRpcService.addReportImportTask(request.getPuid(), reportImportAddParam);

            AddAmazonAdReportImportTaskResponsePb.AddAmazonAdReportImportTaskResponse response = AddAmazonAdReportImportTaskResponsePb.AddAmazonAdReportImportTaskResponse.newBuilder().build();
            log.info("response: {}", response);
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

}
