package com.meiyunji.sponsored.api.sd;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sd.sdAsset.AssetImageRequest;
import com.meiyunji.sponsored.rpc.sd.sdAsset.AssetImageResponse;
import com.meiyunji.sponsored.rpc.sd.sdAsset.AssetImageResult;
import com.meiyunji.sponsored.rpc.sd.sdAsset.RPCCpcSdAssetServiceGrpc;
import com.meiyunji.sponsored.service.cpc.service2.sd.CpcSdAssetService;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: ys
 * @date: 2024/9/2 15:37
 * @describe:
 */
@GRpcService
@Slf4j
public class SdAssetRpcService extends RPCCpcSdAssetServiceGrpc.RPCCpcSdAssetServiceImplBase {

    @Autowired
    private CpcSdAssetService cpcSdAssetService;
    @Override
    public void getAssetImage(AssetImageRequest request, StreamObserver<AssetImageResponse> responseObserver) {
        AssetImageResponse.Builder builder = AssetImageResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getSubType())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {

            Result<List<ImageAssetVo>> result = cpcSdAssetService.getAssetImage(request.getPuid().getValue(),
                    request.getShopId().getValue(), request.getSubType(), request.getFilterType());

            List<ImageAssetVo> data = result.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<AssetImageResult> rpcList = new ArrayList<>(data.size());
                for (ImageAssetVo vo : data) {
                    AssetImageResult.Builder builder2 = AssetImageResult.newBuilder();
                    if (vo.getAssetId() != null) {
                        builder2.setAssetId(vo.getAssetId());
                    }
                    if (StringUtils.isNotEmpty(vo.getVersionId())) {
                        builder2.setAssetId(builder2.getAssetId().concat(":").concat(vo.getVersionId()));
                    }
                    if (vo.getUrl() != null) {
                        builder2.setUrl(vo.getUrl());
                    }
                    if (vo.getMediaType() != null) {
                        builder2.setMediaType(vo.getMediaType());
                    }
                    if (vo.getName() != null) {
                        builder2.setName(vo.getName());
                    }
                    if (vo.getHeight() != null) {
                        builder2.setHeight(Int32Value.of(vo.getHeight()));
                    }
                    if (vo.getWidth() != null) {
                        builder2.setWidth(Int32Value.of(vo.getWidth()));
                    }
                    rpcList.add(builder2.build());
                }
                builder.addAllData(rpcList);
            }

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }
}
