package com.meiyunji.sponsored.service.export.vo.perspective;

import com.alibaba.excel.annotation.ExcelProperty;
import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.StreamDataViewVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.RoundingMode;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TargetViewExcelVO {

    // sp sb sd
    // asin 类目
    // 6种

    @ExcelProperty(value = "运行状态")
    private String state; // 运行 暂停 归档
    @ExcelProperty(value = "投放类型")
    private String targetType; // 类目定位 asin定位
    @ExcelProperty(value = "筛选条件")
    private String selectType; // 精准/扩展 sb-asin sd-asin sb-类目 sd-类目 sd-与推广商品相似 无
    @ExcelProperty(value = "标签")
    private String adTags;

    @ExcelProperty(value = "类目详情")
    private String categoryDetail; // 类目定位下才有

    @ExcelProperty(value = "服务状态")
    private String servingStatusName;
    @ExcelProperty(value = "广告组")
    private String adGroupName;
    @ExcelProperty(value = "广告活动")
    private String campaignName;
    @ExcelProperty(value = "广告组合")
    private String portfolioName;
    @ExcelProperty(value = "建议竞价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String suggestBid;
    @ExcelProperty(value = "建议竞价范围")
    private String suggestBidScope;
    @ExcelProperty(value = "竞价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String bid;

    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCost;
    @ExcelProperty(value = "广告花费占比")
    private String adCostPercentage;

    @ExcelProperty(value = "广告曝光量")
    private Long impressions;

    @ExcelProperty("搜索结果首页首位IS")
    private String topImpressionShare;

    @ExcelProperty(value = "广告点击量")
    private Long clicks;

    @ExcelProperty(value = "可见展示次数")
    private Long viewImpressions;

    @ExcelProperty(value = "CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;

    @ExcelProperty(value = "CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCostPerClick;

    @ExcelProperty(value = "VCPM")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String vcpm;

    @ExcelProperty(value = "广告点击率")
    private String ctr;

    @ExcelProperty(value = "广告转化率")
    private String cvr;

    @ExcelProperty(value = "ACoS")
    private String acos;

    @ExcelProperty(value = "ROAS")
    private String roas;

    @ExcelProperty(value = "ACoTS")
    private String acots;

    @ExcelProperty(value = "ASoTS")
    private String asots;

    @ExcelProperty("广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String advertisingUnitPrice;

    @ExcelProperty(value = "广告订单量")
    private Integer adOrderNum;
    @ExcelProperty(value = "广告订单量占比")
    private String adOrderNumPercentage;
    @ExcelProperty(value = "本广告产品订单量")
    private Integer adSaleNum;
    @ExcelProperty(value = "其他产品广告订单量")
    private Integer adOtherOrderNum;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;
    @ExcelProperty(value = "广告销售额占比")
    private String adSalePercentage;
    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSales;
    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSales;

    @ExcelProperty(value = "广告销量")
    private Integer orderNum;
    @ExcelProperty(value = "广告销量占比")
    private String orderNumPercentage;
    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;
    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;

    @ExcelProperty(value = "“品牌新买家”订单量")
    private Integer ordersNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”订单百分比")
    private String orderRateNewToBrandFTD;

    @ExcelProperty(value = "“品牌新买家”销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String salesNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”销售额百分比")
    private String salesRateNewToBrandFTD;

    @ExcelProperty(value = "品牌新买家销量")
    private Integer unitsOrderedNewToBrand;
    @ExcelProperty(value = "品牌新买家销量占比")
    private String unitsOrderedNewToBrandPercentage;

    public TargetViewExcelVO(String currency, CategoryTargetViewVo vo) {
        this.state = AllAdStateEnum.getStateValueIgnoreCase(vo.getState());
        if (StringUtils.equalsIgnoreCase(vo.getTargetType(), "asin")) {
            this.targetType = "ASIN定位";
        }
        if (StringUtils.equalsIgnoreCase(vo.getTargetType(), "category")) {
            this.targetType = "类目定位";
        }
        if (StringUtils.equalsIgnoreCase(vo.getTargetType(), "similarProduct")) {
            this.targetType = "与您推广商品类似的商品";
        }

        if (StringUtils.isNotBlank(vo.getSelectType())) {
            if (SpV3ExpressionEnum.asinSameAs.getValue().equals(vo.getSelectType()) || SpV3ExpressionEnum.asinSameAs.getValueV3().equals(vo.getSelectType())) {
                this.selectType = "精准";
            } else if (SpV3ExpressionEnum.asinExpandedFrom.getValue().equals(vo.getSelectType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equals(vo.getSelectType())) {
                this.selectType = "扩展";
            } else {
                this.selectType = vo.getSelectType();
            }
        }
        if (CollectionUtils.isNotEmpty(vo.getAdTags())) {
            this.adTags = vo.getAdTags().stream().map(AdTag::getName).distinct().collect(Collectors.joining("、"));
        }

        CurrencyUnitEnum unitEnum = CurrencyUnitEnum.getByCurrency(currency);
        String unit = Objects.nonNull(unitEnum) ? unitEnum.getUnit() : "";
        if (TargetTypeEnum.category.name().equalsIgnoreCase(vo.getTargetType())) {
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotBlank(vo.getBrandName())) {
                sb.append(" 品牌：").append(vo.getBrandName());
            }
            if (StringUtils.isNotBlank(vo.getCommodityPriceRange())) {
                String range = vo.getCommodityPriceRange();
                if (BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE.equals(range)) {
                    sb.append(" 价格：").append(range);
                } else {
                    String[] ranges = vo.getCommodityPriceRange().split(",");
                    range = ranges.length == 2 ? ExportStringUtil.getSuggestBidScope(ranges[0], ranges[1], unit) : ExportStringUtil.getSuggest(range, unit);
                    sb.append(" 价格：").append(range);
                }
            }
            if (StringUtils.isNotBlank(vo.getRating())) {
                sb.append(" 星级：").append(vo.getRating());
            }
            if (StringUtils.isNotBlank(vo.getDistribution())) {
                sb.append(" 配送：").append(vo.getDistribution());
            }
            this.categoryDetail = sb.toString();
        }
        this.servingStatusName = vo.getServingStatusName();
        this.adGroupName = vo.getAdGroupName();
        this.campaignName = vo.getCampaignName();
        this.portfolioName = vo.getPortfolioName();
        this.suggestBid = StringUtils.isNotBlank(vo.getSuggestBid()) ? currency + vo.getSuggestBid() : "";
        this.suggestBidScope = ExportStringUtil.getSuggestBidScope(vo.getRangeStart(), vo.getRangeEnd(), unit);
        this.bid = StringUtils.isNotBlank(vo.getBid()) ? currency + vo.getBid() : "";
        this.topImpressionShare = ExportStringUtil.parseTopImpressionShare(vo.getTopImpressionShare());
        buildData(currency, this, vo);
        if (!SBCampaignCostTypeEnum.VCPM.getCode().equalsIgnoreCase(vo.getCostType())) {
            this.setVcpm("-");
        }
    }

    public static void buildData(String currency, TargetViewExcelVO excel, StreamDataViewVo data) {
        excel.setAdCost(currency + MathUtil.toBigDecimalStrWithScale(data.getAdCost(), 2));
        excel.setAdCostPercentage(MathUtil.toBigDecimalStrWithScale(data.getAdCostPercentage(), 2) + "%");
        excel.setImpressions(data.getImpressions());
        excel.setClicks(data.getClicks());
        excel.setViewImpressions(data.getViewImpressions());
        excel.setCpa(currency + MathUtil.toBigDecimalStrWithScale(data.getCpa(), 2));
        excel.setAdCostPerClick(currency + MathUtil.toBigDecimalStrWithScale(data.getAdCostPerClick(), 2));
        excel.setVcpm(currency + MathUtil.toBigDecimalStrWithScale(data.getVcpm(), 2));
        excel.setCtr(MathUtil.toBigDecimalStrWithScale(data.getCtr(), 2) + "%");
        excel.setCvr(MathUtil.toBigDecimalStrWithScale(data.getCvr(), 2) + "%");
        excel.setAcos(MathUtil.toBigDecimalStrWithScale(data.getAcos(), 2) + "%");
        excel.setRoas(MathUtil.toBigDecimalStrWithScale(data.getRoas(), 2));
        excel.setAcots(MathUtil.toBigDecimalStrWithScale(data.getAcots(), 2) + "%");
        excel.setAsots(MathUtil.toBigDecimalStrWithScale(data.getAsots(), 2) + "%");
        excel.setAdvertisingUnitPrice(currency + (Objects.isNull(data.getAdvertisingUnitPrice()) ? "0" : data.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP)));
        excel.setAdOrderNum(data.getAdOrderNum());
        excel.setAdOrderNumPercentage(MathUtil.toBigDecimalStrWithScale(data.getAdOrderNumPercentage(), 2) + "%");
        excel.setAdSaleNum(data.getAdSaleNum());
        excel.setAdOtherOrderNum(data.getAdOtherOrderNum());
        excel.setAdSale(currency + MathUtil.toBigDecimalStrWithScale(data.getAdSale(), 2));
        excel.setAdSalePercentage(MathUtil.toBigDecimalStrWithScale(data.getAdSalePercentage(), 2) + "%");
        excel.setAdSales(currency + MathUtil.toBigDecimalStrWithScale(data.getAdSales(), 2));
        excel.setAdOtherSales(currency + MathUtil.toBigDecimalStrWithScale(data.getAdOtherSales(), 2));
        excel.setOrderNum(data.getOrderNum());
        excel.setOrderNumPercentage(MathUtil.toBigDecimalStrWithScale(data.getOrderNumPercentage(), 2) + "%");
        excel.setAdSelfSaleNum(data.getAdSelfSaleNum());
        excel.setAdOtherSaleNum(data.getAdOtherSaleNum());
        excel.setOrdersNewToBrandFTD(data.getOrdersNewToBrandFTD());
        excel.setOrderRateNewToBrandFTD(MathUtil.toBigDecimalStrWithScale(data.getOrderRateNewToBrandFTD(), 2) + "%");
        excel.setSalesNewToBrandFTD(currency + MathUtil.toBigDecimalStrWithScale(data.getSalesNewToBrandFTD(), 2));
        excel.setSalesRateNewToBrandFTD(MathUtil.toBigDecimalStrWithScale(data.getSalesRateNewToBrandFTD(), 2) + "%");
        excel.setUnitsOrderedNewToBrand(data.getUnitsOrderedNewToBrandFTD());
        excel.setUnitsOrderedNewToBrandPercentage(MathUtil.toBigDecimalStrWithScale(data.getUnitsOrderedRateNewToBrandFTD(), 2) + "%");
    }


}
