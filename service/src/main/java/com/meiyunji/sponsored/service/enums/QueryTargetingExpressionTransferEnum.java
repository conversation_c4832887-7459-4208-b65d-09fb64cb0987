package com.meiyunji.sponsored.service.enums;

/**
 * sponsored product target expression type
 */
public enum QueryTargetingExpressionTransferEnum {
    ASIN_SAME_AS("ASIN_SAME_AS", "asin=", true),
    ASIN_EXPANDED_FROM("ASIN_EXPANDED_FROM", "asin-expanded=", true),
    ASIN_CATEGORY_SAME_AS("ASIN_CATEGORY_SAME_AS", "category=", true),
    ASIN_BRAND_SAME_AS("ASIN_BRAND_SAME_AS","brand=", true),
    ASIN_PRICE_BETWEEN("ASIN_PRICE_BETWEEN", "price=", false),
    ASIN_PRICE_LESS_THAN("ASIN_PRICE_LESS_THAN", "price<", false),
    ASIN_PRICE_GREATER_THAN("ASIN_PRICE_GREATER_THAN", "price>", false),
    ASIN_REVIEW_RATING_BETWEEN("ASIN_REVIEW_RATING_BETWEEN", "rating=", false),
    ASIN_REVIEW_RATING_LESS_THAN("ASIN_REVIEW_RATING_LESS_THAN", "rating<", false),
    ASIN_REVIEW_RATING_GREATER_THAN("ASIN_REVIEW_RATING_GREATER_THAN", "rating>", false),
    ASIN_AGE_RANGE_SAME_AS("ASIN_AGE_RANGE_SAME_AS", "age-range=", true),
    ASIN_IS_PRIME_SHIPPING_ELIGIBLE("ASIN_IS_PRIME_SHIPPING_ELIGIBLE", "prime-shipping-eligible=", true),
    ;

    QueryTargetingExpressionTransferEnum(String type, String value, Boolean hasQuotationMarks) {
        this.type = type;
        this.value = value;
        this.hasQuotationMarks = hasQuotationMarks;
    }

    private String type;
    private String value;
    private Boolean hasQuotationMarks;

    public String getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public boolean getHasQuotationMarks() {
        return hasQuotationMarks;
    }


    public static QueryTargetingExpressionTransferEnum fromType(String type){
        for (QueryTargetingExpressionTransferEnum typeV3 : values()) {
            if (typeV3.getType().equals(type)) {
                return typeV3;
            }
        }
        return null;
    }

    public static QueryTargetingExpressionTransferEnum fromValue(String value){
        for (QueryTargetingExpressionTransferEnum typeV3 : values()) {
            if (typeV3.getValue().equals(value)) {
                return typeV3;
            }
        }
        return null;
    }
}
