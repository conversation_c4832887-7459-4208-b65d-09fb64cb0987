package com.meiyunji.sponsored.service.syncAd.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.service.syncAd.dao.IAmazonAdShopDataInitTaskDao;
import com.meiyunji.sponsored.service.syncAd.dto.InitTaskCountDto;
import com.meiyunji.sponsored.service.syncAd.dto.OneTimeSuccessDto;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataInitRecordStateEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataInitTaskStateEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitRecord;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-29  19:04
 */

@Repository
public class AmazonAdShopDataInitTaskDaoImpl extends AdBaseDaoImpl<AmazonAdShopDataInitTask> implements IAmazonAdShopDataInitTaskDao {


    @Override
    public void insertOrUpdate(List<AmazonAdShopDataInitTask> taskList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (puid, shop_id, ad_type, task_type, ad_group_id, state, extend_info, next_sync_time, execute_count, create_time, update_time) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdShopDataInitTask task : taskList) {
            sql.append(" (?,?,?,?,?,?,?,now(),?,now(),now()),");
            argsList.add(task.getPuid());
            argsList.add(task.getShopId());
            argsList.add(task.getAdType());
            argsList.add(task.getTaskType());
            argsList.add(task.getAdGroupId());
            argsList.add(task.getState());
            argsList.add(task.getExtendInfo());
            argsList.add(task.getExecuteCount());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `extend_info`=values(extend_info),`update_time`=now()");
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public AmazonAdShopDataInitTask selectTask(Integer puid, Integer shopId, String adType, Byte taskType, String groupId) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>(5);
        sql.append(" where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        if (StringUtils.isNotBlank(groupId)) {
            sql.append(" and ad_group_id = ?");
            args.add(groupId);
        }
        sql.append(" and ad_type = ? and task_type = ? ");
        sql.append(" and state = ").append(ShopDataInitTaskStateEnum.SYNCING.getState());
        sql.append(" and next_sync_time <= now() ");
        args.add(adType);
        args.add(taskType);

        List<AmazonAdShopDataInitTask> list = getJdbcTemplate().query(sql.toString(), getRowMapper(), args.toArray());

        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public void updateTaskSuccessById(AmazonAdShopDataInitTask task) {
        StringBuilder sql = new StringBuilder("update ");
        sql.append(getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>(2);
        sql.append(" set state = ?, execute_count = execute_count + 1, update_time = now() where id = ? ");
        args.add(ShopDataInitTaskStateEnum.SUCCESS.getState());
        args.add(task.getId());
        getJdbcTemplate().update(sql.toString(), args.toArray());
    }

    @Override
    public void updateTaskFailById(AmazonAdShopDataInitTask task) {
        StringBuilder sql = new StringBuilder("update ");
        sql.append(getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>(5);
        sql.append(" set state = ?, extend_info=?, execute_count = execute_count + 1, next_sync_time = ?, update_time=now() where id = ? and state = ?");
        args.add(task.getState());
        args.add(task.getExtendInfo());
        args.add(task.getNextSyncTime());
        args.add(task.getId());
        args.add(ShopDataInitTaskStateEnum.SYNCING.getState());
        getJdbcTemplate().update(sql.toString(), args.toArray());
    }

    @Override
    public List<AmazonAdShopDataInitTask> queryRetryTaskByTaskTypeWithLimit(Integer puid, Integer shopId, List<String> adTypeList, List<Byte> taskTypeList, Long maxId, int limit) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>(5);
        sql.append(" where puid = ? and shop_id = ?");
        sql.append(" and ad_type  in ('").append(StringUtils.join(adTypeList,"','")).append("') ");
        sql.append(" and task_type in (").append(StringUtils.join(taskTypeList,",")).append(") ");
        sql.append(" and state = ").append(ShopDataInitTaskStateEnum.SYNCING.getState());
        sql.append(" and next_sync_time <= now() ");
        sql.append(" and id > ? order by id limit ? ");
        args.add(puid);
        args.add(shopId);
        args.add(maxId);
        args.add(limit);
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AmazonAdShopDataInitTask> queryRetryGroupLevelTaskLimit(List<Integer> shopIdList, List<String> groupIdList, List<String> adTypeList, List<Byte> taskTypeList, Long maxId, int limit) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        List<Object> args = new ArrayList<>(2);
        sql.append(" where ");
        sql.append(" ad_type  in ('").append(StringUtils.join(adTypeList,"','")).append("') ");
        sql.append(" and task_type in (").append(StringUtils.join(taskTypeList,",")).append(") ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(" and shop_id  in (").append(StringUtils.join(shopIdList,",")).append(") ");
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sql.append(" and ad_group_id  in ('").append(StringUtils.join(groupIdList,"','")).append("') ");
        }
        sql.append(" and state = ").append(ShopDataInitTaskStateEnum.SYNCING.getState());
        sql.append(" and next_sync_time <= now() ");
        sql.append(" and id > ? order by id limit ? ");
        args.add(maxId);
        args.add(limit);
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<InitTaskCountDto> getTaskCountsAndLastUpdateTimeByShopId(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select state, count(*) count, max(update_time) update_time from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? group by state");
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(InitTaskCountDto.class), puid, shopId);
    }


    @Override
    public List<AmazonAdShopDataInitTask> queryByTaskId(Long id) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where id = ? ");
        sql.append(" and state = ").append(ShopDataInitTaskStateEnum.SYNCING.getState());
        sql.append(" and next_sync_time <= now() ");
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), id);
    }

    @Override
    public int deleteByShopId(int puid, int shopId) {
        StringBuilder sql = new StringBuilder("delete from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        return getJdbcTemplate().update(sql.toString(), puid, shopId);
    }

    @Override
    public int countGroupLevelTask(Integer puid, Integer shopId, List<String> adTypeList, List<Byte> taskTypeList) {
        StringBuilder sql = new StringBuilder("select count(*) from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        sql.append(" and ad_type in ('").append(StringUtils.join(adTypeList,"','")).append("') ");
        sql.append(" and task_type in (").append(StringUtils.join(taskTypeList,",")).append(") ");
        List<Integer> list = getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> rs.getInt(1), puid, shopId);
        return CollectionUtils.isEmpty(list) ? 0 : list.get(0);
    }

    @Override
    public OneTimeSuccessDto getOneTimeSuccessByShopId(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select count(*) successCount, max(update_time) lastUpdateTime from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? and state = ? and execute_count = 1 ");
        List<OneTimeSuccessDto> dtoList = getJdbcTemplate().query(sql.toString(),
                new BeanPropertyRowMapper<>(OneTimeSuccessDto.class),
                puid,
                shopId,
                ShopDataInitTaskStateEnum.SUCCESS.getState());
        return CollectionUtils.isEmpty(dtoList) ? new OneTimeSuccessDto(0, null) : dtoList.get(0);
    }

    @Override
    public int deleteHistoryTasks(List<Integer> shopIdList, int deleteTimeLimitSecond, int onceDelMaxCount) {
        StringBuilder sql = new StringBuilder("delete from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where state in (1,2) and update_time < DATE_SUB(now(), INTERVAL ? SECOND) ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(" and shop_id in (").append(StringUtils.join(shopIdList, ",")).append(") ");
        }
        sql.append(" limit ?");
        return getJdbcTemplate().update(sql.toString(), deleteTimeLimitSecond, onceDelMaxCount);
    }
}
