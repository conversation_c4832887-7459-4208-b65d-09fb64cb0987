package com.meiyunji.sponsored.service.system.service.impl;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.enums.CurrencyType;
import com.meiyunji.sponsored.service.system.dao.ICurrencyExchangeRateDao;
import com.meiyunji.sponsored.service.system.dao.IUserRateDao;
import com.meiyunji.sponsored.service.system.po.CurrencyExchangeRate;
import com.meiyunji.sponsored.service.system.po.UserRate;
import com.meiyunji.sponsored.service.system.service.ICurrencyExchangeRateService;
import com.meiyunji.sponsored.service.util.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName CurrencyExchangeRateServiceImpl
 * @Description 汇率相关服务
 * <AUTHOR>
 * @Date 2020/6/8 13:40
 **/
@Service
public class CurrencyExchangeRateServiceImpl implements ICurrencyExchangeRateService {
    private static final int RATE_UPDATE_FREQUENCY = 10000;  //访问频率(毫秒)
    private static final String charset = "UTF-8";
    private static final String[] STANDARD_CURRENCIES = new String[]{"CNY", "USD"};

    @Autowired
    private ICurrencyExchangeRateDao currencyExchangeRateDao;
    @Autowired
    private IUserRateDao userRateDao;
    @Autowired
    private IUserDao userDao;



    /**
     * 返回官方汇率
     *
     * @param date
     * @param originUnit
     * @param targetUnit
     * @return 该汇率用乘法
     */
    @Override
    public BigDecimal getRate(Date date, String originUnit, String targetUnit) {
        BigDecimal rate;
        //源币种或目标币种为人民币或美元的直接转
        if (CurrencyType.USD.code().equals(originUnit.toUpperCase()) || CurrencyType.CNY.code().equals(originUnit.toUpperCase())) {
            String currencyId = originUnit.toUpperCase() + targetUnit.toUpperCase();
            rate = getRate(currencyId, date);
        } else if (CurrencyType.USD.code().equals(targetUnit.toUpperCase()) || CurrencyType.CNY.code().equals(targetUnit.toUpperCase())) {
            String currencyId = targetUnit.toUpperCase() + originUnit.toUpperCase();
            rate = MathUtil.divideS10(BigDecimal.ONE, getRate(currencyId, date));
        } else {
            //源币种和目标币种都不是人民币和美元用美元币种作为中间币种转换
            String orignUsdCurrency = CurrencyType.USD.code() + originUnit.toUpperCase();
            String targetUsdCurrency = CurrencyType.USD.code() + targetUnit.toUpperCase();
            rate = getRate(orignUsdCurrency, date);
            rate = MathUtil.divideS10(getRate(targetUsdCurrency, date), rate);
        }
        return rate;
    }

    /**
     * 返回官方最新汇率
     *
     * @param originUnit
     * @param targetUnit
     * @return 该汇率用乘法
     */
    @Override
    public BigDecimal getRate(String originUnit, String targetUnit) {
        return getRate(new Date(), originUnit, targetUnit);
    }

    private BigDecimal getRate(String name, Date date) {
        if (date == null) {
            date = new Date();
        }
        String dateStr = DateUtil.dateToStrWithFormat(date, "yyyyMMdd");
        BigDecimal rate = ConvertUtil.getRate(name, dateStr);
        if (rate == null) {
            List<CurrencyExchangeRate> list = currencyExchangeRateDao.listAll();
            ConvertUtil.setRate(list);
        }
        //如果当天的汇率没取到就取最近的一个汇率
        if (rate == null) {
            rate = currencyExchangeRateDao.getRate(name);
            ConvertUtil.setRate(name, dateStr, rate);
        }
        if (rate == null) {
            throw new ServiceException(String.format("getRate no name:%s", name));
        }
        return rate;
    }

    /**
     * 返回用户汇率
     *
     * @param puid
     * @param month
     * @param originUnit
     * @param targetUnit
     * @return <b>该汇率用除法</b>
     */
    @Override
    public BigDecimal getUserRate(Integer puid, String month, String originUnit, String targetUnit) throws ServiceException {
//        long t1 = System.currentTimeMillis();
        String key = originUnit + targetUnit;
        BigDecimal rate = ConvertUtil.getRate(puid, key, month);
        if (rate == null) {
            rate = getUserRateImpl(puid, month, originUnit, targetUnit);
        }
//        long t2 = System.currentTimeMillis();
//        logger.info("get rate time: {}", t2 - t1);
        return rate;
    }

    @Override
    public BigDecimal getUSDPrice(int puid, String originUnit, BigDecimal price, BigDecimal rate) {
        if (rate != null) {
            return MathUtil.divideS10(price, rate);
        }
        return price;
    }

    @Override
    public BigDecimal getCurrencyByUserRate(String originUnit, String targetUnit, BigDecimal price, BigDecimal rate){

        if (rate != null) {
            return MathUtil.divideS10(price, rate);
        }
        return price;
    }

    @Override
    public BigDecimal getCurrencyChangeValue(String originUnit, String targetUnit, BigDecimal originValue) {
        //先检查是否有对应币种汇率
        if (CurrencyType.getCurrencyFromCode(originUnit) == null || CurrencyType.getCurrencyFromCode(targetUnit) == null) {
            throw new ServiceException("getCurrencyChangeValue no originUnit or targetUnit info  originUnit = " + originUnit + "  targetUnit = " + targetUnit);
        }
        // 转换币种相同，则返回原值
        if (originUnit.equalsIgnoreCase(targetUnit)) {
            return originValue;
        }
        BigDecimal rate = getRate(originUnit, targetUnit);
        return MathUtil.multiply(originValue, rate);
    }


    private BigDecimal getUserRateImpl(Integer puid, String month, String originUnit, String targetUnit) throws ServiceException {
        if (StringUtils.isBlank(originUnit) || StringUtils.isBlank(targetUnit)) {
            return BigDecimal.ONE;
        }
        if (puid == null) {
            return MathUtil.divideS10(BigDecimal.ONE, getRate(originUnit, targetUnit));
        }
        //获取所传月的汇率
        if (StringUtils.isBlank(month)) { //如果month为空，就去当前时间
            month = DateUtil.dateToStrWithFormat(new Date(), "yyyyMM");
        }
        if (StringUtils.equals(originUnit, targetUnit)) {
            return BigDecimal.ONE;
        }
        BigDecimal result = null;
        if (CurrencyType.CNY.code().equals(originUnit)) {
            UserRate rate = userRateDao.get(puid, originUnit + targetUnit, month);
            if (rate != null) {
                result = rate.getUserRate() == null ? rate.getRate() : rate.getUserRate();
            }
        } else if (CurrencyType.CNY.code().equals(targetUnit)) {
            UserRate rate = userRateDao.get(puid, targetUnit + originUnit, month);
            //除法不能保留4位小数位
            if (rate != null) {
                result = rate.getUserRate() != null && rate.getUserRate().compareTo(BigDecimal.ZERO) != 0 ?
                        MathUtil.divideS10(BigDecimal.ONE, rate.getUserRate()) :
                        MathUtil.divideS10(BigDecimal.ONE, rate.getRate());
            }
        } else {
            BigDecimal rateOrigin = getUserRate(puid, month, CurrencyType.CNY.code(), originUnit);
            BigDecimal rateTarget = getUserRate(puid, month, CurrencyType.CNY.code(), targetUnit);

            result = MathUtil.divideS10(rateTarget, rateOrigin);
        }

        if (result != null && result.compareTo(BigDecimal.ZERO) != 0) {
            return result;
        }

        //如果没有汇率找上个月汇率
        Date yyyyMM = DateUtil.strToDate(month, "yyyyMM");
        if (Integer.parseInt(month) < 202001) {
            //如果小于2020年取官方汇率
            return MathUtil.divideS10(BigDecimal.ONE, getRate(originUnit, targetUnit));
        }
        return getUserRate(puid, DateUtil.dateToStrWithFormat(DateUtil.addMonth(yyyyMM, -1), "yyyyMM"), originUnit, targetUnit);
    }





}
