package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.TargetFirstPlaceIsDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
public class AmazonAdSbTargetingReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSbTargetingReport> implements IAmazonAdSbTargetingReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSbTargetingReport> list) {
        //插入原表
        insertOrUpdateListOriginAndHotTable(puid, list, getJdbcHelper().getTable());

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //筛选出95天内的数据
            List<AmazonAdSbTargetingReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertOrUpdateListOriginAndHotTable(puid, hotList, getHotTableName());
            }
        }

    }

    private void insertOrUpdateListOriginAndHotTable(Integer puid, List<AmazonAdSbTargetingReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`campaign_name`,")
                .append("`campaign_id`,`campaign_status`,`campaign_budget`,`campaign_budget_type`,`ad_group_name`,`ad_group_id`,`target_id`,")
                .append("`targeting_expression`,`targeting_text`,`targeting_type`,`match_type`,")
                .append("`impressions`,`clicks`,`currency`,`cost`,`sales14d`,`sales14d_same_sku`,`conversions14d`,`conversions14d_same_sku`,`detail_page_views_clicks14d`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,")
                .append("`units_ordered_new_to_brand14d`,`units_ordered_new_to_brand_percentage14d`,`units_sold14d`,`dpv14d`,`create_time`,`update_time`,`vctr`,`video5second_view_rate`,`video5second_views`,`video_complete_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views`,`video_unmutes`,`viewable_impressions`,`vtr`,`ad_format`, `top_of_search_is`, `branded_searches14d`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSbTargetingReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now(),?,?,?,?,?,?,?,?,?,?,?,?,?),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getTargetId());
            argsList.add(report.getTargetingExpression());
            argsList.add(report.getTargetingText());
            argsList.add(report.getTargetingType());
            argsList.add(report.getMatchType());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getDetailPageViewsClicks14d());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage14d());
            argsList.add(report.getOrderRateNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getSalesNewToBrandPercentage14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getUnitsSold14d());
            argsList.add(report.getDpv14d());
            //商品投放新增sbv类型报告
            argsList.add(report.getVctr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVtr());
            argsList.add(report.getAdFormat());
            argsList.add(report.getTopOfSearchIs());
            argsList.add(report.getBrandedSearches());
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),`ad_group_name`=values(ad_group_name),");
        sql.append("`ad_group_id`=values(ad_group_id),`target_id`=values(target_id),`targeting_expression`=values(targeting_expression),`targeting_text`=values(targeting_text),`targeting_type`=values(targeting_type),`match_type`=values(match_type),");
        sql.append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`sales14d`=values(sales14d),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`conversions14d`=values(conversions14d),`conversions14d_same_sku`=values(conversions14d_same_sku),`detail_page_views_clicks14d`=values(detail_page_views_clicks14d),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`orders_new_to_brand_percentage14d`=values(orders_new_to_brand_percentage14d),`order_rate_new_to_brand14d`=values(order_rate_new_to_brand14d),");
        sql.append("`sales_new_to_brand14d`=values(sales_new_to_brand14d),`sales_new_to_brand_percentage14d`=values(sales_new_to_brand_percentage14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`units_ordered_new_to_brand_percentage14d`=values(units_ordered_new_to_brand_percentage14d),`units_sold14d`=values(units_sold14d),`dpv14d`=values(dpv14d)");
        //商品投放新增sbv类型报告
        sql.append(",`vctr`=values(vctr),`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_complete_views`=values(video_complete_views),`video_first_quartile_views`=values(video_first_quartile_views),`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),`video_unmutes`=values(video_unmutes),`viewable_impressions`=values(viewable_impressions),`vtr`=values(vtr),`ad_format`=values(ad_format) ,`top_of_search_is` = values(top_of_search_is),`branded_searches14d` = values(branded_searches14d)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        String tableName = getTableNameByStartDate(search.getStart());
        StringBuilder selectSql = new StringBuilder("select count_date, puid, shop_id, match_type, ad_group_name, marketplace_id,campaign_name,target_id,targeting_expression,targeting_text,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d,")
                .append("sum(`conversions14d_same_sku`) conversions14d_same_sku, sum(`units_sold14d`) units_sold14d, sum(`sales14d_same_sku`) sales14d_same_sku,sum(`vctr`) vctr,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`orders_new_to_brand_percentage14d`) orders_new_to_brand_percentage14d,sum(`order_rate_new_to_brand14d`) order_rate_new_to_brand14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`sales_new_to_brand_percentage14d`) sales_new_to_brand_percentage14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`units_ordered_new_to_brand_percentage14d`) units_ordered_new_to_brand_percentage14d ,")
                .append("sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,")
                .append("sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,sum(`video_complete_views`) video_complete_views,sum(`vtr`) vtr")
                .append(" FROM ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select target_id FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getAdFormat() != null) {
            whereSql.append("and ad_format=?");
            argsList.add(search.getAdFormat());
        }
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if (StringUtils.isNotBlank(search.getCampaignId())) {
            whereSql.append(" and campaign_id = ?");
            argsList.add(search.getCampaignId());
        }
        if(StringUtils.isNotBlank(search.getSearchValue())){  //搜索查询
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            if("asin".equals(search.getSearchType())){
                whereSql.append(" and targeting_text like ? ");
                argsList.add("asin=%");
                whereSql.append(" and targeting_text = ? ");
                String value = "asin=\"" + search.getSearchValue() + "\"";
                argsList.add(value);
            }else if("category".equals(search.getSearchType())){
                whereSql.append(" and targeting_text like ? ");
                argsList.add("category=%");
                whereSql.append(" and targeting_text like ? ");
                argsList.add("%" + search.getSearchValue()+"%");
            }
        }
        whereSql.append("group by shop_id,target_id ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){  //排序
            String field = ReportService.getSbReportField(search.getOrderField(), true);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,target_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdSbTargetingReport.class);
    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder(" SELECT * from ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*)").append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and target_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getTargetId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String field = ReportService.getSbReportField(param.getOrderField(), false);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        } else {
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdSbTargetingReport.class);
    }

    @Override
    public List<AmazonAdSbTargetingReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where puid=? and shop_id=? and marketplace_id=? and target_id=? and count_date>=? and count_date<=?  order by count_date";
        return getJdbcTemplate(puid).query(sql,new Object[]{puid,shopId,marketplaceId,targetId,startStr,endStr},getMapper());
    }

    @Override
    public AmazonAdSbTargetingReport getSumReportByTargetId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=? ")
                .append("  and`marketplace_id`=? and target_id=? and `count_date`>=? and count_date<=?  ");
        List<AmazonAdSbTargetingReport> list = getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, marketplaceId, targetId, startStr, endStr}, getMapper());
        return list!=null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId) {
        String sql = "select campaign_id id,campaign_name `name` from t_amazon_ad_sb_targeting_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        return getJdbcTemplate(puid).queryForList(sql,arg.toArray());
    }

    @Override
    public List<AmazonAdSbTargetingReport> listSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> targetIds) {
        String sql = "SELECT puid, shop_id, campaign_id, ad_group_id, target_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .in("target_id", targetIds.toArray())
                .groupBy("target_id")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdSbTargetingReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String targetId) {
        String sql = "SELECT puid, shop_id, count_date, campaign_id, ad_group_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate);

        if (StringUtils.isNotBlank(targetId)) {
            builder.equalToWithoutCheck("target_id", targetId);
        }

        builder.groupBy("count_date");
        ConditionBuilder conditionBuilder = builder.build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AdHomePerformancedto> listSumReportByTargetIds(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sb' as type, `target_id`,sum(impressions) `impressions`, ");
        sql.append(" sum(clicks) `clicks`, sum(cost) `cost`, sum(sales14d) as total_sales, sum(sales14d_same_sku) as ad_sales, ");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(units_sold14d) `units_sold14d`,max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is,");
        sql.append(" sum(`video5second_views`) AS `video5second_views`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`viewable_impressions`) AS `viewable_impressions`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by target_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .type(re.getString("type"))
                        .targetId(re.getString("target_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .maxTopIs(Optional.ofNullable(re.getBigDecimal("max_top_is")).orElse(null))
                        .minTopIs(Optional.ofNullable(re.getBigDecimal("min_top_is")).orElse(null))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getSbReportByDate(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' as type, c.target_id target_id,`count_date`,sum(impressions) `impressions`, ");
        sql.append(" sum(clicks) `clicks`, sum(cost) `cost`, sum(sales14d) as total_sales, sum(sales14d_same_sku) as ad_sales,");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(units_sold14d) `units_sold14d`,");
        sql.append(" sum(`video5second_views`) AS `video5second_views`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`viewable_impressions`) AS `viewable_impressions`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`");
        sql.append(" FROM t_amazon_ad_targeting_sb c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.target_id=c.target_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.target_text = ?  ");
            argsList.add(param.getSearchValue());
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.target_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .targetId(re.getString("target_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .type(re.getString("type"))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getSbReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupBy) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' as type,`count_date`,sum(impressions) `impressions`,sum(clicks) `clicks`, ");
        sql.append(" sum(cost) `cost`, sum(sales14d) as total_sales, sum(sales14d_same_sku) as ad_sales,");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(units_sold14d) `units_sold14d`,");
        sql.append(" sum(`video5second_views`) AS `video5second_views`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`viewable_impressions`) AS `viewable_impressions`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? ");
        if (isGroupBy) {
            sql.append(" group by count_date  ");
        }
        argsList.add(startStr);
        argsList.add(endStr);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .type(re.getString("type"))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sb' as type, sum(impressions) `impressions`, sum(cost) `cost`, sum(sales14d) `total_sales`,sum(conversions14d)  `sale_num`,sum(units_sold14d)  `units_sold14d`, sum(orders_new_to_brand14d) sales_num, ");
        sql.append(" sum(clicks) `clicks`, sum(sales14d_same_sku) as ad_sales,");
        sql.append("sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d` FROM ");
        sql.append(" t_amazon_ad_targeting_sb c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.target_id=c.target_id where c.puid= ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id

            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));

        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.target_id", param.getTargetIds(), argsList));
        }


        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.target_text = ?  ");
            argsList.add(param.getSearchValue());
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        List<AdMetricDto> list = getJdbcTemplate(puid).query(sql.toString(), (re, i) -> {
            AdMetricDto dto = AdMetricDto.builder()
                    .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                    .sumOrderNum(Optional.ofNullable(re.getBigDecimal("units_sold14d")).orElse(BigDecimal.ZERO))
                    .build();
            return dto;
        }, argsList.toArray());
        return list!= null && list.size() > 0 ? list.get(0) : null;
    }


    @Override
    public List<String> getTargetListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select target_id from (select sum(`impressions`) impressions, target_id from t_amazon_ad_sb_targeting_report where " +
                " puid = ? and shop_id=?  and update_time > ? group by target_id having impressions > 0) a ";

        return getJdbcTemplate(puid).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
    }

    @Override
    public List<AmazonAdSbTargetingReport> getReportByTargetId(Integer puid, Integer shopId, String start, String end, String marketplaceId, String targetId) {
        String sql = "SELECT * FROM " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid).equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("target_id", targetId)
                .greaterThanOrEqualTo("count_date", start)
                .lessThanOrEqualTo("count_date", end).build();
        sql += " " + builder.getSql();

        return getJdbcTemplate(puid).query(sql, builder.getValues(), getMapper());
    }

    private StringBuilder subWhereSql(TargetingPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }


            /**********************************广告管理高级筛选新增查询指标***********************************/

            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/sale_num, 0), 2) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/sale_num, 0), 2) <= ?");
                argsList.add(param.getCpaMax());
            }

            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }

            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }

            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - conversions14d_same_sku, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - conversions14d_same_sku, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本产品广告销售额
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他广告产品销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }

            //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
            if (param.getOrdersNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ?");
                argsList.add(param.getOrdersNewToBrandFTDMin());
            }
            if (param.getOrdersNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ?");
                argsList.add(param.getOrdersNewToBrandFTDMax());
            }

            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            if (param.getOrderRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/sale_num) * 100, 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getOrderRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull((orders_new_to_brand14d/sale_num) * 100, 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            if (param.getSalesNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ?");
                argsList.add(param.getSalesNewToBrandFTDMin());
            }

            if (param.getSalesNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ?");
                argsList.add(param.getSalesNewToBrandFTDMax());
            }

            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            if (param.getSalesRateNewToBrandFTDMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (param.getSalesRateNewToBrandFTDMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((sales_new_to_brand14d/total_sales) * 100, 0), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //品牌新买家订单转化率
            if (param.getBrandNewBuyerOrderConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((orders_new_to_brand14d/clicks) * 100, 0), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(param.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100)));
            }

            if (param.getBrandNewBuyerOrderConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((orders_new_to_brand14d/clicks) * 100, 0), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(param.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100)));
            }

            // 5秒观看次数 筛选
            if (param.getVideo5SecondViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video5second_views , 0) >= ? ");
                argsList.add(param.getVideo5SecondViewsMin());
            }
            if (param.getVideo5SecondViewsMax() != null) {
                subWhereSql.append(" and ifnull(video5second_views , 0) <= ? ");
                argsList.add(param.getVideo5SecondViewsMax());
            }

            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }

            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and viewable_impressions >= ?");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and viewable_impressions <= ?");
                argsList.add(param.getViewImpressionsMax());
            }

            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }

            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) >= ? ");
                argsList.add(param.getBrandedSearchesMin()); }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) <= ? ");
                argsList.add(param.getBrandedSearchesMax()); }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
            // 搜索结果首页首位IS
            if (param.getTopImpressionShareMin() != null){
                subWhereSql.append(" and max(top_of_search_is) >= ?");
                argsList.add(param.getTopImpressionShareMin());
            }
            if (param.getTopImpressionShareMax() != null){
                subWhereSql.append(" and min(top_of_search_is) <= ?");
                argsList.add(param.getTopImpressionShareMax());
            }
        }
        return subWhereSql;
    }


    @Override
    public List<AmazonAdSbTargetingReport> getReportByTargetIdsGroupByCountDate(int puid, Integer shopId, String startDate, String endDate, String marketplaceId,  List<String> targetIds) {
        StringBuilder selectSql = new StringBuilder("select count_date, puid, shop_id, match_type, ad_group_name, marketplace_id,campaign_name,target_id,targeting_expression,targeting_text,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d,")
                .append("sum(`conversions14d_same_sku`) conversions14d_same_sku, sum(`units_sold14d`) units_sold14d, sum(`sales14d_same_sku`) sales14d_same_sku,sum(`vctr`) vctr,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`orders_new_to_brand_percentage14d`) orders_new_to_brand_percentage14d,sum(`order_rate_new_to_brand14d`) order_rate_new_to_brand14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`sales_new_to_brand_percentage14d`) sales_new_to_brand_percentage14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`units_ordered_new_to_brand_percentage14d`) units_ordered_new_to_brand_percentage14d ,")
                .append("sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,")
                .append("sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,sum(`video_complete_views`) video_complete_views,sum(`vtr`) vtr  FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=? and marketplace_id = ?  and `count_date`>=? and count_date<=? ");

        List<Object> args = Lists.newArrayList(puid, shopId, marketplaceId, startDate, endDate);
        selectSql.append(SqlStringUtil.dealInList("target_id", targetIds, args));
        selectSql.append(" group by count_date ");
        return getJdbcTemplate(puid).query(selectSql.toString(), args.toArray(), getMapper());
    }

    @Override
    public List<AmazonAdSbTargetingReport> getFirstPlaceIsByTargetId(int puid, int shopId, String marketId, String startDate, String endDate, String targetId) {
        StringBuilder sql = new StringBuilder("SELECT count_date, sum(`top_of_search_is`) top_of_search_is")
                .append(" FROM t_amazon_ad_sb_targeting_report ");
        sql.append(" where `puid`=? and`shop_id`=? and `target_id`=? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, shopId, targetId, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN),"yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN),"yyyyMMdd"));
        sql.append(" group by count_date ");
        return getJdbcTemplate(puid).query(sql.toString(),args.toArray(),getMapper());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<SbTargetPageVo> getReportDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select target_id targetId")
                .append(",sum(cost) `cost`, sum(impressions) `impressions`, sum(clicks) `clicks`, sum(conversions14d) sale_num, sum(conversions14d_same_sku) `ad_sale_num`, sum(sales14d) total_sales, sum(sales14d_same_sku) `ad_sales`, sum(units_sold14d) `order_num`")
                .append(",sum(orders_new_to_brand14d) `ordersNewToBrand14d`,sum(sales_new_to_brand14d) `salesNewToBrand14d`, max(top_of_search_is) max_top_is, min(top_of_search_is) min_top_is, ")
                .append(" sum(`video5second_views`) AS `video5SecondViews`,")
                .append(" sum(`video_first_quartile_views`) AS `videoFirstQuartileViews`,")
                .append(" sum(`video_Midpoint_Views`) AS `videoMidpointViews`,")
                .append(" sum(`video_third_quartile_views`) AS `videoThirdQuartileViews`,")
                .append(" sum(`video_complete_views`) AS `videoCompleteViews`,")
                .append(" sum(`video_unmutes`) AS `videoUnmutes`,")
                .append(" sum(`viewable_impressions`) AS `viewableImpressions`,")
                .append(" sum(`branded_searches14d`) AS `brandedSearches`")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sqlSb.append(" where puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getMarketplaceId());
        sqlSb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sqlSb.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        }
        sqlSb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sqlSb.append(this.getTargetPageHavingSql(param, argsList));
        }

        return getJdbcTemplate(puid).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(SbTargetPageVo.class));
    }

    @Override
    public List<AdOrderBo> getTargetIdAndIndexList(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id id, ")
                .append(SqlStringReportUtil.getSbOrderField(param.getOrderField())).append(" orderField ")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getMarketplaceId());
        sb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }
        sb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.TARGET_PAGE_QUERY_REPORT_ID_LIMIT);

        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdOrderBo.class));
    }

    @Override
    public List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select target_id targetId from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));

        sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date >= ? and count_date <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getMarketplaceId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealInList("target_id", param.getTargetIds(), argsList));
        }
        sb.append(" group by target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.TARGET_PAGE_QUERY_REPORT_ID_LIMIT);

        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public AdMetricDto getTargetPageSumMetricDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new AdMetricDto();
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sqlSb = new StringBuilder("select SUM(cost) sumCost,SUM(conversions14d) sumAdOrderNum, SUM(sales14d) sumAdSale, sum(units_sold14d) sumOrderNum from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sqlSb.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        sqlSb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        List<AdMetricDto> list = getJdbcTemplate(puid).query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdMetricDto.class));
        return list.size() == 1 ? list.get(0) : null;
    }

    @Override
    public List<String> getTargetIdsByTargetIds(int puid, int shopId, String start, List<String> targetIds) {
        StringBuilder sql = new StringBuilder(" select distinct target_id from " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, start);
        whereSql.append(SqlStringUtil.dealInList("target_id", targetIds, argsList));
        return getJdbcTemplate(puid).queryForList(sql.append(whereSql).toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<TargetFirstPlaceIsDto> getFirstPlaceIsDtoByKeywordId(int puid, List<Integer> shopIdList, String startDate, String endDate, List<String> targetIdList) {
        StringBuilder sql = new StringBuilder("SELECT target_id targetId, max(top_of_search_is) maxTopIs, min(top_of_search_is) minTopIs")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
        sql.append(" where `puid`=? and `count_date`>= ? and `count_date`<= ? ");
        List<Object> args = Lists.newArrayList(puid, DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, DateUtil.PATTERN), "yyyyMMdd"), DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, DateUtil.PATTERN), "yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sql.append(SqlStringUtil.dealInList("target_id", targetIdList, args));
        }
        sql.append(" group by target_id ");
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(TargetFirstPlaceIsDto.class));
    }

    /**
     * 投放列表页having条件sql拼接
     */
    private String getTargetPageHavingSql(TargetingPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getSbTargetPageHavingSql(qo, argsList);
    }

    @Override
    public List<AdReportData> getAllReportByTargetIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder selectSql = new StringBuilder("select target_id,count_date,ad_group_id,")
                .append("IFNULL(SUM(cost), 0) cost,")
                .append("IFNULL(SUM(sales14d), 0) adSale,")
                .append("IFNULL(SUM(sales14d_same_sku), 0) adSelfSale,")
                .append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(conversions14d),0) adOrderNum,")
                .append("sum(0) adSelfSaleNum,")
                .append("IFNULL(SUM(units_sold14d),0) adSaleNum,")
                .append("IFNULL(SUM(conversions14d_same_sku),0) adSelfOrderNum FROM ");

        startStr = startStr.contains("-") ? startStr.replaceAll("-", "") : startStr;
        endStr = endStr.contains("-") ? endStr.replaceAll("-", "") : endStr;
        selectSql.append(this.getJdbcHelper().getTable());
        selectSql.append(" WHERE puid = ? ");
        argsList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            argsList.add(shopId);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(targetIdList)) {
            selectSql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }

        selectSql.append(" and count_date >= ?");
        argsList.add(startStr);
        selectSql.append(" and count_date <= ?");
        argsList.add(endStr);
        selectSql.append(" group by target_id,count_date ");
        HintManager hintManager = HintManager.getInstance();
        try {
            // 也可以直接在对象 写注解
            return getJdbcTemplate(puid).query(selectSql.toString(), (re, i) -> {
                AdReportData dto = new AdReportData();
                dto.setOtherId(re.getString("ad_group_id"));
                dto.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                dto.setItemId(re.getString("target_id"));
                dto.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
                dto.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
                dto.setAdOrderNum(Optional.of(re.getInt("adOrderNum")).orElse(0));
                dto.setAdSelfOrderNum(Optional.of(re.getInt("adSelfOrderNum")).orElse(0));
                dto.setAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO));
                dto.setAdSelfSale(Optional.ofNullable(re.getBigDecimal("adSelfSale")).orElse(BigDecimal.ZERO));
                dto.setAdSaleNum(Optional.of(re.getInt("adSaleNum")).orElse(0));
                dto.setAdSelfSaleNum(Optional.of(re.getInt("adSelfSaleNum")).orElse(0));
                dto.setType(Constants.SB);
                dto.setCountDate(re.getString("count_date"));
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }
}
