package com.meiyunji.sponsored.service.strategyTask.api.strategy;

import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sellfox.aadas.types.exception.AmazonDuplicateAdItemIdException;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.log.enums.*;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.enums.BiddingValueEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.helper.AdvertiseStrategyGroupTargetBidHelper;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.api.StrategyApi;
import com.meiyunji.sponsored.service.strategyTask.api.StrategyCommonApi;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordSequenceDao;
import com.meiyunji.sponsored.service.strategyTask.enums.ItemType;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskAdType;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyRealTimeBid;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import com.meiyunji.sponsored.service.strategyTask.vo.DeleteStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.TransferStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.UpdateStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.UpdateStrategyStateResponseVo;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import jdk.nashorn.internal.ir.debug.ObjectSizeCalculator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.AD_GROUP_STRATEGY_MAX_COUNT_MSG;
import static com.meiyunji.sponsored.service.cpc.util.Constants.AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-02-05  15:04
 */
@Component
@Slf4j
public class AdGroupTargetBidStrategy extends StrategyCommonApi implements StrategyApi {


    @Resource
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;


    protected AdGroupTargetBidStrategy(AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao, AdvertiseStrategyStatusDao advertiseStrategyStatusDao, AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao, AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao, IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonSbAdKeywordDao amazonSbAdKeywordDao, IAmazonSbAdTargetingDao amazonSbAdTargetingDao, IAmazonSdAdTargetingDao amazonSdAdTargetingDao, AadasApiFactory aadasApiFactory, AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao, AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAdManageOperationLogService adManageOperationLogService, IAmazonAdPortfolioDao portfolioDao, AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao, RedisService redisService, IndexStrategyConfig indexStrategyConfig, AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao, AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao, AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao, RedissonClient redissonClient, AdvertiseStrategyGroupTargetBidHelper advertiseStrategyGroupTargetBidHelper) {
        super(advertiseStrategyTemplateDao, advertiseStrategyStatusDao, advertiseStrategyStatusDeleteDao, advertiseStrategyScheduleDao, amazonAdCampaignAllDao, amazonSbAdKeywordDao, amazonSbAdTargetingDao, amazonSdAdTargetingDao, aadasApiFactory, advertiseStrategyStatusSequenceDao, advertiseStrategyScheduleSequenceDao, amazonAdGroupDao, amazonSbAdGroupDao, amazonSdAdGroupDao, adManageOperationLogService, portfolioDao, advertiseStrategyTemplateSequenceDao, redisService, indexStrategyConfig, advertiseStrategyAdGroupDao, advertiseStrategyRealTimeBidDao, advertiseStrategyTaskRecordSequenceDao, redissonClient, advertiseStrategyGroupTargetBidHelper);
    }

    @Override
    public boolean checkValid(String itemType) {
        return ItemType.AD_GROUP_TARGET.name().equals(itemType);
    }

    @Override
    public AddStrategyVo submitStrategy(Integer puid, List<SubmitStrategyVo> submitStrategyVos, Long templateId, Integer updateId, String loginIp, String traceId) {
        return null;
    }

    @Override
    public List<UpdateStrategyResponseVo> updateStrategy(Integer puid, Long templateId, List<UpdateStrategyVo> updateStrategyVoList, AdvertiseStrategyTemplate vo, Integer updateId, String traceId,Integer taskAction, Map<Long, AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBidMap) {
        List<UpdateStrategyResponseVo> responseVoList = Lists.newArrayList();
        Map<String, UpdateStrategyVo> updateStrategyVoMap = updateStrategyVoList.stream().collect(Collectors.toMap(UpdateStrategyVo::getItemId,Function.identity(),(e1,e2)->e1));
        List<Long> statusIds = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        Vector<UpdateStrategyVo> successList = new Vector<>();
        List<AdvertiseStrategyAdGroup> strategyAdGroups = advertiseStrategyAdGroupDao.getByStatusIds(puid, statusIds);
        String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, strategyAdGroups.get(0).getAdGroupId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            strategyAdGroups.forEach(e->{
                UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(e.getShopId());
                responseVo.setItemId(e.getAdGroupId());
                responseVo.setItemType(e.getItemType());
                responseVo.setAdType(e.getAdType());
                responseVo.setStatusId(e.getId());
                responseVo.setIsRetry(1);
                responseVo.setMsg("当前广告组下还有投放在执行中，请稍后重试");
                responseVoList.add(responseVo);
            });
            return responseVoList;
        }
        try {
            //指针分页获取数据处理
            List<Long> strategyStatusId = advertiseStrategyStatusDao.getIdListByStrategyAdGroupIds(puid, strategyAdGroups.get(0).getShopId(),strategyAdGroups.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
            List<AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBidList = new CopyOnWriteArrayList();
            if (CollectionUtils.isNotEmpty(strategyStatusId)) {
                List<List<Long>> partition = Lists.partition(strategyStatusId, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                List<CompletableFuture> futureList = Lists.newArrayList();
                partition.forEach(p -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, p);
                        if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                            return;
                        }
                        List<Long> recordIds = advertiseStrategyTaskRecordSequenceDao.batchGenId(advertiseStrategyStatusList.size());
                        for (int s = 0; s < advertiseStrategyStatusList.size(); s++) {
                            AdvertiseStrategyRealTimeBid advertiseStrategyRealTimeBid = new AdvertiseStrategyRealTimeBid();
                            Boolean falg = true;
                            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
                            AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusList.get(s);
                            advertiseStrategyRealTimeBid.setId(recordIds.get(s));
                            advertiseStrategyRealTimeBid.setPuid(puid);
                            advertiseStrategyRealTimeBid.setShopId(advertiseStrategyStatus.getShopId());
                            advertiseStrategyRealTimeBid.setStatuId(advertiseStrategyStatus.getId());
                            advertiseStrategyRealTimeBid.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                            if (MapUtils.isNotEmpty(updateStrategyVoMap) && updateStrategyVoMap.containsKey(advertiseStrategyStatus.getAdGroupId())) {
                                advertiseStrategyRealTimeBid.setRecordId(updateStrategyVoMap.get(advertiseStrategyStatus.getAdGroupId()).getRecordId());
                            }
                            advertiseStrategyRealTimeBid.setCampaignId(advertiseStrategyStatus.getCampaignId());
                            advertiseStrategyRealTimeBid.setAdGroupId(advertiseStrategyStatus.getAdGroupId());
                            advertiseStrategyRealTimeBid.setTargetId(advertiseStrategyStatus.getItemId());
                            advertiseStrategyRealTimeBid.setTargetName(advertiseStrategyStatus.getTargetName());
                            advertiseStrategyRealTimeBid.setTargetType(advertiseStrategyStatus.getTargetType());
                            Integer code = TaskAdType.getCode(advertiseStrategyStatus.getAdType());
                            if (code == null) {
                                advertiseStrategyRealTimeBid.setAdType(3);
                            } else {
                                advertiseStrategyRealTimeBid.setAdType(TaskAdType.getCode(advertiseStrategyStatus.getAdType()));
                            }
                            String originValueJson = null;
                            TaskTimeType taskTimeType = null;
                            OriginValueVo originValueVo = new OriginValueVo();
                            if (taskAction != 6) {
                                originValueVo = JSONUtil.jsonToObject(advertiseStrategyStatus.getOriginValue(), OriginValueVo.class);
                            } else {
                                //只修改值时重置版本号并获取当前受控对象的策略规则
                                if (MapUtils.isNotEmpty(advertiseStrategyRealTimeBidMap) && advertiseStrategyRealTimeBidMap.containsKey(advertiseStrategyStatus.getId())) {
                                    if (advertiseStrategyRealTimeBidMap.get(advertiseStrategyStatus.getId()).getBiddingValue() != null && !BigDecimal.ZERO.equals(advertiseStrategyRealTimeBidMap.get(advertiseStrategyStatus.getId()).getBiddingValue())) {
                                        originValueVo.setBiddingValue(advertiseStrategyRealTimeBidMap.get(advertiseStrategyStatus.getId()).getBiddingValue());
                                        if (!advertiseStrategyStatus.getVersion().equals(vo.getVersion())) {
                                            vo.setVersion(-1);
                                        }
                                        vo.setRule(advertiseStrategyStatus.getRule());
                                        vo.setType(advertiseStrategyStatus.getType());
                                        advertiseStrategyRealTimeBid.setState(1);
                                        advertiseStrategyRealTimeBidList.add(advertiseStrategyRealTimeBid);
                                    } else {
                                        advertiseStrategyRealTimeBid.setState(-1);
                                        advertiseStrategyRealTimeBid.setStateError("查询实时竞价失败，不可修改");
                                        advertiseStrategyRealTimeBidList.add(advertiseStrategyRealTimeBid);
                                        continue;
                                    }
                                } else {
                                    advertiseStrategyRealTimeBid.setState(-1);
                                    advertiseStrategyRealTimeBid.setStateError("查询实时竞价失败，不可修改");
                                    advertiseStrategyRealTimeBidList.add(advertiseStrategyRealTimeBid);
                                    continue;
                                }
                            }
                            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(vo.getRule(), TargetRuleVo.class);
                            if (advertiseStrategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                                taskTimeType = TaskTimeType.adGroupKeyword;
                            } else {
                                taskTimeType = TaskTimeType.adGroupTarget;
                            }
                            //竞价值计算
                            for (int i = 0; i < ruleVoList.size(); i++) {
                                BigDecimal biddingValue = originValueVo.getBiddingValue();
                                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                                advertiseStrategySchedule.setTaskId(advertiseStrategyStatus.getTaskId());
                                advertiseStrategySchedule.setPuid(puid);
                                advertiseStrategySchedule.setShopId(advertiseStrategyStatus.getShopId());
                                advertiseStrategySchedule.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                                advertiseStrategySchedule.setAdType(advertiseStrategyStatus.getAdType());
                                advertiseStrategySchedule.setItemType(advertiseStrategyStatus.getItemType());
                                advertiseStrategySchedule.setItemId(advertiseStrategyStatus.getItemId());
                                advertiseStrategySchedule.setCampaignId(advertiseStrategyStatus.getCampaignId());
                                advertiseStrategySchedule.setAdGroupId(advertiseStrategyStatus.getAdGroupId());
                                advertiseStrategySchedule.setType(vo.getType());
                                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                                advertiseStrategySchedule.setTargetType(advertiseStrategyStatus.getTargetType());
                                originValueJson = JSONUtil.objectToJson(originValueVo);
                                advertiseStrategySchedule.setOriginValue(originValueJson);
                                if (targetRuleVo.getBiddingType() == 0) {
                                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).setScale(2, BigDecimal.ROUND_HALF_UP);
                                } else if (targetRuleVo.getBiddingType() == 1) {
                                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                } else if (targetRuleVo.getBiddingType() == 2) {
                                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                                            setScale(2, BigDecimal.ROUND_HALF_UP);
                                } else if (targetRuleVo.getBiddingType() == 3) {
                                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                } else if (targetRuleVo.getBiddingType() == 4) {
                                    biddingValue = targetRuleVo.getBiddingValue();
                                }
                                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                                    if (targetRuleVo.getBiddingMaxValue() != null) {
                                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                                            biddingValue = targetRuleVo.getBiddingMaxValue();
                                        }
                                    }
                                }
                                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                                    if (targetRuleVo.getBiddingMinValue() != null) {
                                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                                            biddingValue = targetRuleVo.getBiddingMinValue();
                                        }
                                    }
                                }
                                OriginValueVo newValueVo = new OriginValueVo();
                                newValueVo.setBiddingValue(biddingValue);
                                String newValueJson = JSONUtil.objectToJson(newValueVo);
                                advertiseStrategySchedule.setNewValue(newValueJson);
                                list.add(advertiseStrategySchedule);
                            }
                            try {
                                // 更新状态时调用
                                if ("ENABLED".equals(advertiseStrategyStatus.getStatus())) {
                                    aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(advertiseStrategyStatus.getTaskId(),
                                            templateId, list, false);
                                }
                                advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
                                        advertiseStrategyStatus.getId(), vo.getRule(), advertiseStrategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
                                List<Long> ids = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(),
                                        advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                                if (CollectionUtils.isNotEmpty(ids)) {
                                    advertiseStrategyScheduleDao.deleteStrategyByIds(puid, ids);
                                }
                                List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
                                for (int i = 0; i < longIdList.size(); i++) {
                                    list.get(i).setId(longIdList.get(i));
                                }
                                advertiseStrategyScheduleDao.batchInsert(puid, list);
                                //记录修改成功的受控对象
                                if (originValueVo != null && originValueVo.getBiddingValue() != null) {
                                    UpdateStrategyVo logVo = new UpdateStrategyVo();
                                    logVo.setStatusId(advertiseStrategyStatus.getId());
                                    logVo.setOriginBiddingValue(JSONUtil.jsonToObject(advertiseStrategyStatus.getOriginValue(),
                                            OriginValueVo.class).getBiddingValue());
                                    logVo.setBiddingValue(originValueVo.getBiddingValue());
                                    successList.add(logVo);
                                }
//                        successList.add(updateStrategyVo);
                            } catch (Exception exception) {
                                if (exception instanceof AmazonDuplicateAdItemIdException) {
//                            responseVo.setMsg(e.getMessage());
//                            responseVo.setIsGrayPlacement(false);
                                } else {
//                            responseVo.setMsg("API接口超时，请重试");
//                            responseVo.setIsGrayPlacement(true);
                                }
                                log.error("traceId:{} puid={} 分时调价任务修改异常:", traceId, puid, exception);
                            }
                        }
                    }, ThreadPoolUtil.getTargetProcessPool());
                    futureList.add(future);
                });
                //阻塞等待
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                try {
                    allFutures.get();
                } catch (Exception e) {
                    log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                }
                if (CollectionUtils.isNotEmpty(advertiseStrategyRealTimeBidList)) {
                    Map<String, List<AdvertiseStrategyRealTimeBid>> map = advertiseStrategyRealTimeBidList.stream().collect(Collectors.groupingBy(AdvertiseStrategyRealTimeBid::getAdGroupId));
                    strategyAdGroups.stream().forEach(e -> {
                        if (MapUtils.isNotEmpty(map) && map.containsKey(e.getAdGroupId())) {
                            List<AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBids = map.get(e.getAdGroupId());
                            List<Integer> stateList = advertiseStrategyRealTimeBids.stream().map(AdvertiseStrategyRealTimeBid::getState).collect(Collectors.toList());
                            if (stateList.contains(-1)) {
                                UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
                                responseVo.setPuid(e.getPuid());
                                responseVo.setShopId(e.getShopId());
                                responseVo.setIsRetry(1);
                                responseVo.setItemId(e.getAdGroupId());
                                responseVo.setItemType(e.getItemType());
                                responseVo.setAdType(e.getAdType());
                                responseVo.setStatusId(e.getId());
                                responseVo.setMsg("广告组更新竞价失败");
                                responseVoList.add(responseVo);
                            }
                        } else {
                            UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
                            responseVo.setPuid(e.getPuid());
                            responseVo.setShopId(e.getShopId());
                            responseVo.setIsRetry(1);
                            responseVo.setItemId(e.getAdGroupId());
                            responseVo.setItemType(e.getItemType());
                            responseVo.setAdType(e.getAdType());
                            responseVo.setStatusId(e.getId());
                            responseVo.setMsg("当前广告组下没有投放");
                            responseVoList.add(responseVo);
                        }
                    });
                    advertiseStrategyRealTimeBidDao.batchInsert(puid, advertiseStrategyRealTimeBidList, false);
                }
            }
            if (taskAction == 6 && CollectionUtils.isEmpty(advertiseStrategyRealTimeBidList)) {
                strategyAdGroups.stream().forEach(e -> {
                    UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
                    responseVo.setPuid(e.getPuid());
                    responseVo.setShopId(e.getShopId());
                    responseVo.setIsRetry(1);
                    responseVo.setItemId(e.getAdGroupId());
                    responseVo.setItemType(e.getItemType());
                    responseVo.setAdType(e.getAdType());
                    responseVo.setStatusId(e.getId());
                    responseVo.setMsg("当前广告组下没有投放");
                    responseVoList.add(responseVo);
                });
            }
            if (6 != taskAction) {
                advertiseStrategyAdGroupDao.batchUpdateStrategy(puid, strategyAdGroups.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()), vo.getRule(), vo.getVersion());
            }
            if (CollectionUtils.isNotEmpty(responseVoList)) {
                //填充错误返回的数据
                filterUpdateResponse(responseVoList);
            }

            if (6 == taskAction) {
                filterMatchData(puid, successList, new ArrayList<>(), responseVoList,updateId);
            } else {
                syncTemplateOperationLog(puid, vo, updateStrategyVoList, updateId);
            }
        } catch (Exception e) {
            log.error("traceId:{} puid:{} 分时调价任务修改异常:", traceId, puid, e);
        } finally {
            lock.unlock();
        }
        return responseVoList;
    }

    @Override
    public List<UpdateStrategyStateResponseVo> updateStrategyStatus(Integer puid, Long templateId, List<Long> statusIdList, String status, String loginIp, Integer uid, String traceId) {
        List<UpdateStrategyStateResponseVo> responseVoList = Lists.newArrayList();
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getByStatusIds(puid, statusIdList);
        if (CollectionUtils.isEmpty(advertiseStrategyAdGroupList)) {
            UpdateStrategyStateResponseVo responseVo = new UpdateStrategyStateResponseVo();
            responseVo.setPuid(puid);
            responseVo.setStatusId(statusIdList.get(0));
            responseVo.setIsRetry(1);
            responseVo.setMsg("当前受控对象不存在");
            responseVoList.add(responseVo);
        }
        String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, advertiseStrategyAdGroupList.get(0).getAdGroupId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            advertiseStrategyAdGroupList.forEach(e->{
                UpdateStrategyStateResponseVo responseVo = new UpdateStrategyStateResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(e.getShopId());
                responseVo.setItemId(e.getAdGroupId());
                responseVo.setItemType(e.getItemType());
                responseVo.setAdType(e.getAdType());
                responseVo.setStatusId(e.getId());
                responseVo.setIsRetry(1);
                responseVo.setMsg("当前广告组下还有投放在执行中，请稍后重试");
                responseVoList.add(responseVo);
            });
            return responseVoList;
        }
        // 新增策略开启/暂停的
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        AdvertiseStrategyAdGroup  advertiseStrategyAdGroup = advertiseStrategyAdGroupList.get(0);
        try {
            //记录日志
            addControlledObjectsLog(puid, uid, loginIp, advertiseStrategyAdGroup.getItemType(), advertiseStrategyAdGroup.getTemplateId(), adManageOperationLog, false);
            filterBaseMessage(advertiseStrategyAdGroup.getAdType(), advertiseStrategyAdGroup.getMarketplaceId(),
                    advertiseStrategyAdGroup.getCampaignId(), advertiseStrategyAdGroup.getAdGroupId(), null,adManageOperationLog, null);
            adManageOperationLog.setShopId(advertiseStrategyAdGroup.getShopId());
            String statusName = "";
            if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId());
                statusName = Optional.ofNullable(amazonAdGroup).map(AmazonAdGroup::getName).orElse("");
            } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByAdGroupId(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId());
                statusName = Optional.ofNullable(amazonSbAdGroup).map(AmazonSbAdGroup::getName).orElse("");
            } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId());
                statusName = Optional.ofNullable(amazonSdAdGroup).map(AmazonSdAdGroup::getName).orElse("");
            }
            operationContent.setNewValue(statusName);
            adManageOperationLog.setTargetName(statusName);
            // 回写受控对象表数据
            if ("ENABLED".equals(status)) {
                String flag = "";
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 1, uid);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 1, uid);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 1, uid);
                }
                operationContent.setTitle("开启分时调竞价任务");
            } else {
                String flag = "";
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 0, uid);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 0, uid);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroupList.get(0).getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), 1, 0, uid);
                }
                operationContent.setTitle("暂停分时调竞价任务");
            }
            advertiseStrategyAdGroupDao.updateStrategyStatusById(puid, advertiseStrategyAdGroupList.get(0).getId(), status);
            Long id = 0L;
            while (true) {
                //指针分页获取数据处理
                List<AdvertiseStrategyStatus> strategyStatuses = advertiseStrategyStatusDao.getListByStrategyAdGroupId(puid, advertiseStrategyAdGroupList.get(0).getShopId(), id, advertiseStrategyAdGroupList.get(0).getId());
                if (CollectionUtils.isEmpty(strategyStatuses)) {
                    break;
                }
                id = strategyStatuses.get(strategyStatuses.size() - 1).getId();
                if (strategyStatuses.size() > com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE) {
                    List<List<AdvertiseStrategyStatus>> list = Lists.partition(strategyStatuses, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                    List<CompletableFuture> futureList = Lists.newArrayList();
                    list.forEach(e -> {
                        futureList.add(CompletableFuture.runAsync(() -> {
                            try {
                                advertiseStrategyGroupTargetBidHelper.processTargetStatus(puid, status, e);
                            } catch (Exception exception) {
                                log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), exception);
                            }
                        }, ThreadPoolUtil.getTargetProcessPool()));
                    });
                    //阻塞等待
                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                    try {
                        allFutures.get();
                    } catch (Exception e) {
                        log.error("处理广告组下的投放添加策略, 多线程执行异常", e);
                    }
                } else {
                    try {
                        advertiseStrategyGroupTargetBidHelper.processTargetStatus(puid, status, strategyStatuses);
                    } catch (Exception exception) {
                        log.error("puid:{} shopId:{} adGroupId:{} 处理广告组下的投放添加策略异常:", puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.get(0).getAdGroupId(), exception);
                    }
                }
            }
            adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} statusId:{} 分时调价任务修改状态异常", traceId,puid, statusIdList.get(0),e);
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo("分时调价任务修改状态异常:"+e.getMessage());
        } finally {
            lock.unlock();
        }

        adManageOperationLogList.add(adManageOperationLog);
        adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        return responseVoList;
    }

    @Override
    public List<TransferStrategyResponseVo> transferStrategy(Integer puid, Long templateId, List<Long> statusIdList, Integer operation, String loginIp, Integer uid, String traceId) {
        List<TransferStrategyResponseVo> transferStrategyResponseVos = Lists.newArrayList();
        List<AdvertiseStrategyAdGroup> strategyAdGroups = advertiseStrategyAdGroupDao.getByStatusIds(puid, statusIdList);
        List<Long> templateList = advertiseStrategyAdGroupDao.getTemplateCount(puid);
        if (!templateList.contains(templateId)) {
            if (templateList.size() + 1 > indexStrategyConfig.getAdGroupTemplateStrategyMaxSize()) {
                strategyAdGroups.forEach(e->{
                    TransferStrategyResponseVo responseVo = new TransferStrategyResponseVo();
                    responseVo.setPuid(puid);
                    responseVo.setShopId(e.getShopId());
                    responseVo.setItemId(e.getAdGroupId());
                    responseVo.setItemType(e.getItemType());
                    responseVo.setAdType(e.getAdType());
                    responseVo.setStatusId(e.getId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg(AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG);
                    transferStrategyResponseVos.add(responseVo);
                });
                return transferStrategyResponseVos;
            }
        }
        Integer count = advertiseStrategyAdGroupDao.getCount(puid, templateId);
        if (statusIdList.size() + count > indexStrategyConfig.getAdGroupStrategyMaxSize()) {
            strategyAdGroups.forEach(e->{
                TransferStrategyResponseVo responseVo = new TransferStrategyResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(e.getShopId());
                responseVo.setItemId(e.getAdGroupId());
                responseVo.setItemType(e.getItemType());
                responseVo.setAdType(e.getAdType());
                responseVo.setStatusId(e.getId());
                responseVo.setIsRetry(1);
                responseVo.setMsg(String.format(AD_GROUP_STRATEGY_MAX_COUNT_MSG, indexStrategyConfig.getAdGroupStrategyMaxSize()));
                transferStrategyResponseVos.add(responseVo);
            });
            return transferStrategyResponseVos;
        }
        String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, strategyAdGroups.get(0).getAdGroupId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        // 其他操作日志记录(编辑:转移受控对象 包含移除和添加)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        AdManageOperationLog addAdManageOperationLog = new AdManageOperationLog();
        OperationContent addOperationContent = new OperationContent();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        if (!b) {
            strategyAdGroups.forEach(e->{
                TransferStrategyResponseVo responseVo = new TransferStrategyResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(e.getShopId());
                responseVo.setItemId(e.getAdGroupId());
                responseVo.setItemType(e.getItemType());
                responseVo.setAdType(e.getAdType());
                responseVo.setStatusId(e.getId());
                responseVo.setIsRetry(1);
                responseVo.setMsg("当前广告组下还有投放在执行中，请稍后重试");
                transferStrategyResponseVos.add(responseVo);
            });
            return transferStrategyResponseVos;
        }

        try {
            AdvertiseStrategyTemplate vo = advertiseStrategyTemplateDao.selectByPrimaryKey(puid, templateId);
            if (operation == 1) {
                //指针分页获取数据处理
                List<Long> strategyStatusId = advertiseStrategyStatusDao.getIdListByStrategyAdGroupIds(puid, strategyAdGroups.get(0).getShopId(),strategyAdGroups.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(strategyStatusId)) {
                    List<List<Long>> partition = Lists.partition(strategyStatusId, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                    List<CompletableFuture> futureList = Lists.newArrayList();
                    partition.forEach(e -> {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, e);
                            if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                                return;
                            }
                            for (AdvertiseStrategyStatus strategyStatus : advertiseStrategyStatusList) {
                                advertiseStrategyStatusDao.updateStatusTemplateById(puid, strategyStatus.getId(), templateId);
                            }
                        }, ThreadPoolUtil.getTargetProcessPool());
                        futureList.add(future);
                    });
                    //阻塞等待
                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                    try {
                        allFutures.get();
                    } catch (Exception e) {
                        log.error("处理广告组下的投放转移策略, 多线程执行异常", e);
                    }
                }
            } else {
                String rule = vo.getRule();
                List<Long> strategyStatusId = advertiseStrategyStatusDao.getIdListByStrategyAdGroupIds(puid, strategyAdGroups.get(0).getShopId(), strategyAdGroups.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(strategyStatusId)) {
                    List<List<Long>> partition = Lists.partition(strategyStatusId, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                    List<CompletableFuture> futureList = Lists.newArrayList();
                    partition.forEach(e -> {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, e);
                            if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                                return;
                            }
                            for (AdvertiseStrategyStatus strategyStatus : advertiseStrategyStatusList) {
                                List<AdvertiseStrategySchedule> list = com.google.api.client.util.Lists.newArrayList();
                                OriginValueVo originValueVo = JSONUtil.jsonToObject(strategyStatus.getOriginValue(), OriginValueVo.class);
                                String originValueJson = JSONUtil.objectToJson(originValueVo);
                                strategyStatus.setOriginValue(originValueJson);
                                strategyStatus.setType(vo.getType());
                                TaskTimeType taskTimeType = null;
                                List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, TargetRuleVo.class);
                                if (strategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                                    taskTimeType = TaskTimeType.adGroupKeyword;
                                } else {
                                    taskTimeType = TaskTimeType.adGroupTarget;
                                }
                                for (int i = 0; i < ruleVoList.size(); i++) {
                                    Long scheduleId = advertiseStrategyScheduleSequenceDao.genId();
                                    AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                                    TargetRuleVo targetRuleVo = ruleVoList.get(i);
                                    advertiseStrategySchedule.setId(scheduleId);
                                    advertiseStrategySchedule.setPuid(puid);
                                    advertiseStrategySchedule.setShopId(strategyStatus.getShopId());
                                    advertiseStrategySchedule.setAdType(strategyStatus.getAdType());
                                    advertiseStrategySchedule.setMarketplaceId(strategyStatus.getMarketplaceId());
                                    advertiseStrategySchedule.setTaskId(strategyStatus.getTaskId());
                                    advertiseStrategySchedule.setItemType(vo.getItemType());
                                    advertiseStrategySchedule.setItemId(strategyStatus.getItemId());
                                    advertiseStrategySchedule.setCampaignId(strategyStatus.getCampaignId());
                                    advertiseStrategySchedule.setAdGroupId(strategyStatus.getAdGroupId());
                                    advertiseStrategySchedule.setType(vo.getType());
                                    advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                                    advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                                    advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                                    BigDecimal biddingValue = originValueVo.getBiddingValue();
                                    if (targetRuleVo.getBiddingType() == 0) {
                                        biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    } else if (targetRuleVo.getBiddingType() == 1) {
                                        biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                                                multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    } else if (targetRuleVo.getBiddingType() == 2) {
                                        biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                                                setScale(2, BigDecimal.ROUND_HALF_UP);
                                    } else if (targetRuleVo.getBiddingType() == 3) {
                                        biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                                                .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    } else if (targetRuleVo.getBiddingType() == 4) {
                                        biddingValue = targetRuleVo.getBiddingValue();
                                    }

                                    if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                                        if (targetRuleVo.getBiddingMaxValue() != null) {
                                            if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                                                biddingValue = targetRuleVo.getBiddingMaxValue();
                                            }
                                        }
                                    }
                                    if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                                        if (targetRuleVo.getBiddingMinValue() != null) {
                                            if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                                                biddingValue = targetRuleVo.getBiddingMinValue();
                                            }
                                        }
                                    }
                                    OriginValueVo newValueVo = new OriginValueVo();
                                    newValueVo.setBiddingValue(biddingValue);
                                    String newValueJson = JSONUtil.objectToJson(newValueVo);
                                    advertiseStrategySchedule.setNewValue(newValueJson);
                                    advertiseStrategySchedule.setOriginValue(originValueJson);
                                    list.add(advertiseStrategySchedule);
                                }
                                // 更新状态时调用
                                if ("ENABLED".equals(strategyStatus.getStatus())) {
                                    advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
                                            strategyStatus.getId(), vo.getRule(), strategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
                                    advertiseStrategyScheduleDao.deleteStrategySchedule(puid, strategyStatus.getShopId(), strategyStatus.getTaskId(), strategyStatus.getItemType());
                                    advertiseStrategyScheduleDao.batchInsert(puid, list);
                                    //推送数据到aadas调度服务
                                    try {
                                        aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(strategyStatus.getTaskId(),
                                                templateId, list, false);
                                    } catch (Exception exception) {
                                        log.error("推送aadas服务异常:", exception);
                                    }
                                }
                                // 更新状态时调用
                                else if ("DISABLED".equals(strategyStatus.getStatus())) {
                                    advertiseStrategyStatusDao.updateStatus(puid, templateId, vo.getType(),
                                            strategyStatus.getId(), vo.getRule(), strategyStatus.getStatus(), vo.getVersion(), originValueJson, vo.getReturnValue());
                                    advertiseStrategyScheduleDao.deleteStrategySchedule(puid, strategyStatus.getShopId(), strategyStatus.getTaskId(), strategyStatus.getItemType());
                                    advertiseStrategyScheduleDao.batchInsert(puid, list);
                                }
                            }
                        }, ThreadPoolUtil.getTargetProcessPool());
                        futureList.add(future);
                    });
                    //阻塞等待
                    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                    try {
                        allFutures.get();
                    } catch (Exception e) {
                        log.error("处理广告组下的投放转移策略, 多线程执行异常", e);
                    } finally {
                    }
                }
            }
            if (1 == operation) {
                advertiseStrategyAdGroupDao.batchUpdateStatusTemplateByIds(puid, statusIdList, templateId);
            } else {
                advertiseStrategyAdGroupDao.batchUpdateStatusTemplateById(puid, statusIdList, templateId, vo.getRule(), vo.getVersion());
            }
            // 记录日志
            addControlledObjectsLog(puid, uid, loginIp, vo.getItemType(), templateId, addAdManageOperationLog, true);
            addControlledObjectsLog(puid, uid, loginIp, vo.getItemType(), strategyAdGroups.get(0).getTemplateId(), adManageOperationLog, true);
            addTransferLog(puid, adManageOperationLog, operationContent, addAdManageOperationLog, addOperationContent, vo, strategyAdGroups);
            adManageOperationLogList.add(adManageOperationLog);
            adManageOperationLogList.add(addAdManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} 分时调价转移异常", traceId, puid, e);
            adManageOperationLog.setResult(0);
            adManageOperationLog.setResultInfo("分时调价转移异常:" + e.getMessage());
            addAdManageOperationLog.setResult(0);
            addAdManageOperationLog.setResultInfo("分时调价转移异常:" + e.getMessage());
            adManageOperationLogList.add(adManageOperationLog);
            adManageOperationLogList.add(addAdManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        } finally {
            lock.unlock();
        }
        return transferStrategyResponseVos;
    }

    @Override
    public List<DeleteStrategyResponseVo> removeStrategy(Integer puid, Long templateId, List<RemoveStrategyVo> removeStrategyVoList, Integer updateId, String loginIp, String traceId) {
        List<DeleteStrategyResponseVo> responseVoList = Lists.newArrayList();

        // 其他操作日志记录(编辑:移除受控对象)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> list = new ArrayList<>();

        // 广告活动name
        List<String> campaignNames = new CopyOnWriteArrayList<>();
        List<String> targetNames = new CopyOnWriteArrayList<>();
        List<String> targetTypes = new CopyOnWriteArrayList<>();
        List<String> spKeywordIds = new CopyOnWriteArrayList<>();
        List<String> sbKeywordIds = new CopyOnWriteArrayList<>();

        addControlledObjectsLog(puid, updateId, loginIp, removeStrategyVoList.get(0).getItemType(), null, adManageOperationLog, true);
        List<Long> ids = removeStrategyVoList.stream().map(RemoveStrategyVo::getStatusId).collect(Collectors.toList());
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList = advertiseStrategyAdGroupDao.getByStatusIds(puid, ids);
        String lockKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY, advertiseStrategyAdGroupList.get(0).getAdGroupId());
        RLock lock = redissonClient.getLock(lockKey);
        boolean b = lock.tryLock();
        if (!b) {
            advertiseStrategyAdGroupList.forEach(e->{
                DeleteStrategyResponseVo responseVo = new DeleteStrategyResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(e.getShopId());
                responseVo.setItemId(e.getAdGroupId());
                responseVo.setItemType(e.getItemType());
                responseVo.setAdType(e.getAdType());
                responseVo.setStatusId(e.getId());
                responseVo.setIsRetry(1);
                responseVo.setMsg("当前广告组下还有投放在执行中，请稍后重试");
                responseVoList.add(responseVo);
            });
            return responseVoList;
        }
        try {
            List<Long> strategyStatusId = advertiseStrategyStatusDao.getIdListByStrategyAdGroupIds(puid, advertiseStrategyAdGroupList.get(0).getShopId(), advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(strategyStatusId)) {
//                Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
//                if (CollectionUtils.isNotEmpty(strategyStatusList)) {
//                    advertiseStrategyStatusMap = strategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity()));
//                }
                List<List<Long>> partition = Lists.partition(strategyStatusId, com.meiyunji.sponsored.service.cpc.util.Constants.STRATEGY_PARTITION_SIZE);
                List<CompletableFuture> futureList = Lists.newArrayList();
                partition.forEach(e -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, e);
                        if (CollectionUtils.isEmpty(advertiseStrategyStatusList)) {
                            return;
                        }
                        for (int s = 0; s < advertiseStrategyStatusList.size(); s++) {
                            TaskTimeType taskTimeType = null;
                            AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusList.get(s);
                            if (advertiseStrategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                                taskTimeType = TaskTimeType.adGroupKeyword;
                            } else {
                                taskTimeType = TaskTimeType.adGroupTarget;
                                targetNames.add(advertiseStrategyStatus.getTargetName());
                                targetTypes.add(advertiseStrategyStatus.getTargetType());
                            }

                            //任务调度服务删除策略
                            try {
                                aadasApiFactory.getStrategyApi(taskTimeType).removeSchedule(puid, advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), true);
                            } catch (Exception exception) {
                                log.error("aadas服务异常:", exception);
                            }
                            //广告服务删除策略
                            Long statusId = advertiseStrategyStatus.getId();
                            Long taskId = advertiseStrategyStatus.getTaskId();
                            advertiseStrategyStatusDao.deleteStrategyStatus(puid, statusId, advertiseStrategyStatus.getShopId());
                            List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(), taskId, advertiseStrategyStatus.getItemType());
                            if (CollectionUtils.isNotEmpty(scheduleIds)) {
                                advertiseStrategyScheduleDao.deleteStrategyByIds(puid, scheduleIds);
                            }
                            //回写受控对象表数据
                            advertiseStrategyStatusDeleteDao.insetStrategyStatus(puid, advertiseStrategyStatus);
                        }
                    }, ThreadPoolUtil.getTargetProcessPool());
                    futureList.add(future);
                });
                //阻塞等待
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
                try {
                    allFutures.get();
                } catch (Exception e) {
                    log.error("处理广告组下的投放转移策略, 多线程执行异常", e);
                } finally {

                }
            }
            advertiseStrategyAdGroupDao.batchDeleteStrategyStatus(puid, removeStrategyVoList.stream().map(RemoveStrategyVo::getStatusId).collect(Collectors.toList()));
            for (AdvertiseStrategyAdGroup advertiseStrategyAdGroup : advertiseStrategyAdGroupList) {
                if (AdType.SP.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                } else if (AdType.SB.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSbAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                } else if (AdType.SD.name().equalsIgnoreCase(advertiseStrategyAdGroup.getAdType())) {
                    amazonSdAdGroupDao.updatePricing(puid, advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId(), 0, 0, updateId);
                }
            }
            //记录日志
            Set<String> campaignIdSet = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getCampaignId).collect(Collectors.toSet());
            Map<String, String> groupIdMap = this.getGroupIdNameMap(puid, removeStrategyVoList.get(0).getShopId(),
                    advertiseStrategyAdGroupList.stream().collect(Collectors.toMap(AdvertiseStrategyAdGroup::getAdGroupId, AdvertiseStrategyAdGroup::getAdType)));
            operationContent.setNewValue(StringUtils.join(groupIdMap.values(), "、"));
            operationContent.setTitle("移除受控对象");
            adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            adManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
            adManageOperationLog.setAdGroupId(StringUtils.join(groupIdMap.keySet(), "、"));
            adManageOperationLog.setResult(0);
            adManageOperationLog.setShopId(removeStrategyVoList.get(0).getShopId());
            adManageOperationLog.setMarketplaceId(advertiseStrategyAdGroupList.get(0).getMarketplaceId());
            adManageOperationLog.setTemplateId(advertiseStrategyAdGroupList.get(0).getTemplateId());
            adManageOperationLog.setTargetId(String.valueOf(advertiseStrategyAdGroupList.get(0).getTemplateId()));
            list.add(adManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(list);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} 转移受控对象异常", traceId, puid, e);
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo("转移受控对象异常:" + e.getMessage());
            list.add(adManageOperationLog);
            adManageOperationLogService.printAdOtherOperationLog(list);
        } finally {
            lock.unlock();
        }
        return responseVoList;
    }

    // 填充关键词投放信息
    private void filterKeywordMatchData(Integer puid, Integer shopId, List<String> spKeywordIds, List<String> sbKeywordIds ,List<String> targetNames, List<String> targetTypes) {
        if (CollectionUtils.isNotEmpty(spKeywordIds)) {
            List<AmazonAdKeyword> amazonAdKeywordList = amazonAdKeywordDaoRoutingService.getByKeywordIds(puid, shopId, spKeywordIds, com.meiyunji.sponsored.service.cpc.util.Constants.BIDDABLE);
            for (AmazonAdKeyword adKeyword : amazonAdKeywordList) {
                targetNames.add(adKeyword.getKeywordText()+"【"+ MatchTypeEnum.getMatchValue(adKeyword.getMatchType())+"】");
                targetTypes.add(OperationLogAdTargetTypeEnum.KEYWORD_TARGET.getAdTargetType());
            }
        }
        if (CollectionUtils.isNotEmpty(sbKeywordIds)) {
            List<AmazonSbAdKeyword> amazonSbAdKeywordList = amazonSbAdKeywordDao.listByKeywordId(puid, shopId, sbKeywordIds);
            for (AmazonSbAdKeyword sbAdKeyword : amazonSbAdKeywordList) {
                targetNames.add(sbAdKeyword.getKeywordText()+"【"+MatchTypeEnum.getMatchValue(sbAdKeyword.getMatchType())+"】");
                targetTypes.add(OperationLogAdTargetTypeEnum.KEYWORD_TARGET.getAdTargetType());
            }
        }
    }

    /**
     * 获取广告组id和名称map
     * @param puid
     * @param shopId
     * @param groupIdTypeMap 键：广告组id  值：广告类型sp、sb、sd
     * @return
     */
    private Map<String, String> getGroupIdNameMap(Integer puid, Integer shopId, Map<String, String> groupIdTypeMap) {
        if (MapUtils.isEmpty(groupIdTypeMap)) {
            return new HashMap<>();
        }
        //获取广告组名称
        List<String> groupIdList = new ArrayList<>();
        List<String> sbGroupIdList = new ArrayList<>();
        List<String> sdGroupIdList = new ArrayList<>();
        Map<String, String> groupIdMap = new HashMap<>();
        groupIdTypeMap.forEach((k, v) -> {
            if (AdType.SP.name().equalsIgnoreCase(v)) {
                groupIdList.add(k);
            } else if (AdType.SB.name().equalsIgnoreCase(v)) {
                sbGroupIdList.add(k);
            } else if (AdType.SD.name().equalsIgnoreCase(v)) {
                sdGroupIdList.add(k);
            }
        });
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            groupIdMap.putAll(amazonAdGroupDao.getAdGroupByIds(puid, shopId, groupIdList).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sbGroupIdList)) {
            groupIdMap.putAll(amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, sbGroupIdList).stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sdGroupIdList)) {
            groupIdMap.putAll(amazonSdAdGroupDao.getByGroupIds(puid, shopId, sdGroupIdList).stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getName)));
        }
        return groupIdMap;
    }

    /**
     * 同步更新模板日志记录
     * @param puid
     * @param template
     * @param updateStrategyVoList
     */
    private void syncTemplateOperationLog(Integer puid, AdvertiseStrategyTemplate template, List<UpdateStrategyVo> updateStrategyVoList, Integer updateId) {
        List<AdManageOperationLog> list = new ArrayList<>();
        List<OperationContent> contentList = new ArrayList<>();
        if (CollectionUtils.isEmpty(updateStrategyVoList)) {
            return;
        }
        List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
        List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroups = advertiseStrategyAdGroupDao.getListByLongIdList(puid, statusIdList);
        OperationContent operationContent = new OperationContent();
        operationContent.setTitle("同步更新为最新模板");
        contentList.add(operationContent);
        String content = JSONUtil.objectToJson(operationContent);

        for (AdvertiseStrategyAdGroup strategyStatus : advertiseStrategyAdGroups) {
            AdManageOperationLog adManageOperationLog = new AdManageOperationLog();

            adManageOperationLog.setPuid(puid);
            adManageOperationLog.setUid(updateId);
            adManageOperationLog.setShopId(template.getShopId());
            adManageOperationLog.setMarketplaceId(template.getMarketplaceId());
            adManageOperationLog.setTemplateId(template.getId());

            adManageOperationLog.setCampaignId(strategyStatus.getCampaignId());
            adManageOperationLog.setAdType(strategyStatus.getAdType().toLowerCase());
            adManageOperationLog.setAdGroupId(strategyStatus.getAdGroupId());

            adManageOperationLog.setAdjustmentRange(OperationLogAdjustmentRangeEnum.SYNC_LASTED_TEMPLATED.getAdjustmentType());
            adManageOperationLog.setOperationContent(content);
            adManageOperationLog.setResult(0);
            adManageOperationLog.setUnixTimestamp(System.currentTimeMillis());
            adManageOperationLog.setId(MD5Util.getMD5(adManageOperationLog.deDeduplicationId()));
            adManageOperationLog.setMessage(adManageOperationLog.handleMessage(contentList));
            list.add(adManageOperationLog);
        }
        adManageOperationLogService.printAdOperationLog(list);
    }

    //转移受控对象日志添加
    private void addTransferLog(Integer puid, AdManageOperationLog adManageOperationLog, OperationContent operationContent, AdManageOperationLog addAdManageOperationLog, OperationContent addOperationContent, AdvertiseStrategyTemplate vo, List<AdvertiseStrategyAdGroup> advertiseStrategyAdGroupList) {
        Set<String> campaignIdSet = advertiseStrategyAdGroupList.stream().map(AdvertiseStrategyAdGroup::getCampaignId).collect(Collectors.toSet());
        Map<String, String> groupIdTypeMap = advertiseStrategyAdGroupList.stream().collect(Collectors.toMap(AdvertiseStrategyAdGroup::getAdGroupId, AdvertiseStrategyAdGroup::getAdType));
        Map<String, String> groupIdNameMap = this.getGroupIdNameMap(puid, advertiseStrategyAdGroupList.get(0).getShopId(), groupIdTypeMap);
        Set<String> groupIdList = groupIdNameMap.keySet();
        adManageOperationLog.setAdGroupId(StringUtils.join(groupIdList, "、"));
        addAdManageOperationLog.setAdGroupId(StringUtils.join(groupIdList, "、"));
        adManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
        addAdManageOperationLog.setCampaignId(StringUtils.join(campaignIdSet, "、"));
        operationContent.setTitle("移除受控对象");
        operationContent.setNewValue(StringUtils.join(groupIdNameMap.values(), "、"));
        adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
        addOperationContent.setTitle("添加受控对象");
        addOperationContent.setNewValue(StringUtils.join(groupIdNameMap.values(), "、"));
        addAdManageOperationLog.setOperationContent(JSONUtil.objectToJson(addOperationContent));
        adManageOperationLog.setResult(1);
        addAdManageOperationLog.setResult(1);
        adManageOperationLog.setShopId(vo.getShopId());
        adManageOperationLog.setMarketplaceId(vo.getMarketplaceId());
        addAdManageOperationLog.setShopId(vo.getShopId());
        addAdManageOperationLog.setMarketplaceId(vo.getMarketplaceId());
    }
}
