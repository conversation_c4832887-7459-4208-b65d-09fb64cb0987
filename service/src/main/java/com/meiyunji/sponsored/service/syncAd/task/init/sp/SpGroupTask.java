package com.meiyunji.sponsored.service.syncAd.task.init.sp;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.service.IAmazonAdShopDataInitTaskService;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sp + ShopDataSyncConstant.group)
public class SpGroupTask extends AdShopDataSyncTask {

    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;

    @Autowired
    private IAmazonAdShopDataInitTaskService amazonAdShopDataInitTaskService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sp);
        setTaskType(ShopDataSyncTaskTypeEnum.GROUP);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步广告组，查询所有状态
        //并为广告组添加回调：保存该广告组的产品、投放、否投任务
        cpcAdGroupApiService.syncAdGroups(shop, null, null, null, true, (groupList) -> {
            if (CollectionUtils.isNotEmpty(groupList)) {
                List<String> groupIdList = groupList.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList());
                ShopDataSyncTaskTypeEnum.groupLevelTask.forEach(x -> {
                    amazonAdShopDataInitTaskService.saveTaskByTypeBatch(shop, adType, x, groupIdList);
                });
            }
        });
    }
}
