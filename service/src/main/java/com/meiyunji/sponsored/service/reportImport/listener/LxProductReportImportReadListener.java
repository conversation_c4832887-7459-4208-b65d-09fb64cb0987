package com.meiyunji.sponsored.service.reportImport.listener;

import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportPlatformDao;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport.model.LxAdProductReport;
import com.meiyunji.sponsored.service.reportImport.processor.AbstractLxReportImportProcessor;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 注意ReadListener必须为多例模式
 */
@Component
@Scope("prototype")
public class LxProductReportImportReadListener extends AbstractLxReportReadListener<LxAdProductReport> {

    public LxProductReportImportReadListener(AbstractLxReportImportProcessor<LxAdProductReport> abstractLxReportImportProcessor, ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao, ICpcReportsImportPlatformDao cpcReportsImportPlatformDao) {
        super(abstractLxReportImportProcessor, cpcReportsImportTaskScheduleDao, cpcReportsImportPlatformDao);
    }
}
