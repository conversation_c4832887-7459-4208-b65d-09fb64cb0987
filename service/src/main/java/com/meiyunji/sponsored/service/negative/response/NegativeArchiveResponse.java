package com.meiyunji.sponsored.service.negative.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
public class NegativeArchiveResponse {
    private Integer successNum;
    private Integer failNum;
    private Integer countNum;
    private List<FailInfo> errorList;

    public static NegativeArchiveResponse empty() {
        NegativeArchiveResponse response = new NegativeArchiveResponse();
        response.setSuccessNum(0);
        response.setFailNum(0);
        response.setCountNum(0);
        return response;
    }

    public static NegativeArchiveResponse getInstance(Integer successNum, Integer failNum, Integer countNum) {
        NegativeArchiveResponse response = new NegativeArchiveResponse();
        response.setSuccessNum(Objects.nonNull(successNum) ? successNum : 0);
        response.setFailNum(Objects.nonNull(failNum) ? failNum : 0);
        response.setCountNum(Objects.nonNull(countNum) ? countNum : 0);
        return response;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FailInfo {
        private String name;
        private String failReason;
    }

}
