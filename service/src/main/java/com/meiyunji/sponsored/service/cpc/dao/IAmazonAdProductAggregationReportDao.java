package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductAggregationReport;
import com.meiyunji.sponsored.service.cpc.vo.AdProductSearchVo;
import com.meiyunji.sponsored.service.cpc.vo.ProductReportDetailsVo;

import java.util.Date;
import java.util.List;

public interface IAmazonAdProductAggregationReportDao extends IBaseShardingSphereDao<AmazonAdProductAggregationReport> {


    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdProductAggregationReport> list);

    Page<AmazonAdProductAggregationReport> getPageList(Integer puid, AdProductSearchVo searchVo);


    List<AmazonAdProductAggregationReport> getListBySearch(Integer puid, AdProductSearchVo searchVo);

    /**
     * 详情 汇总
     * @param puid
     * @return
     */
    AmazonAdProductAggregationReport getDetailsSumVo(Integer puid, Integer shopId, String type, String asin, String startStr, String endStr);

    /**
     * 按天获取报告
     * @param puid
     * @param detailsVo
     * @return
     */
    List<AmazonAdProductAggregationReport> getReportListByDay(Integer puid, ProductReportDetailsVo detailsVo);

    /**
     * 根据asin找出所有广告组
     * @return
     */
    List<AmazonAdProductAggregationReport> getListByAsin(Integer puid, Integer shopId, Date start, Date end, String type, String asin);

    /**
     * 根据父asin找出聚合报告数据
     */
    List<AmazonAdProductAggregationReport> getListByParentAsin(Integer puid, Integer shopId, Date start, Date end, String type, String parentAsin);

    /**
     * 根据时间范围获取报告
     * @param puid
     * @paran shopId
     * @param limit
     * @return
     */
    List<AmazonAdProductAggregationReport> getNeedsSyncParentAsin(Integer puid, Integer shopId, Integer limit);


    /**
     * 更新父asin字段
     * @param puid
     * @param reports
     * @return
     */
    int[] batchUpdateParentAsin(Integer puid, List<AmazonAdProductAggregationReport> reports);

    /**
     * 更新无父asin (下次同步时间字段 )
     * @param puid
     * @param reports
     * @return
     */
    int[] batchUpdateSyncNextDate(Integer puid, List<AmazonAdProductAggregationReport> reports);

    /**
     * 筛选没有父asin数据：parentAsin:"None"
     * @param puid
     * @param shopId
     * @param limit
     * @param syncDay
     */
    List<AmazonAdProductAggregationReport> getNeedsSyncNoneParentAsin(Integer puid, Integer shopId, Integer limit, Integer syncDay);

    /**
     * 父asin维度聚合产品报告
     * @param puid
     * @param searchVo
     * @return
     */
    Page<AmazonAdProductAggregationReport> getPageListGroupByParentAsin(Integer puid, AdProductSearchVo searchVo);

    /**
     * 父asin维度查询聚合报告
     * @param puid
     * @param searchVo
     * @return
     */
    List<AmazonAdProductAggregationReport> getListGroupByParentAsin(Integer puid, AdProductSearchVo searchVo);
}
