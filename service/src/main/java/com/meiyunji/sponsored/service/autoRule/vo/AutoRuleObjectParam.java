package com.meiyunji.sponsored.service.autoRule.vo;

import lombok.Data;

import java.util.List;

@Data
public class AutoRuleObjectParam {
    private Integer puid;
    private Long templateId;
    private Integer shopId;
    private List<String> portfolioIds;
    private String searchValue;
    private String itemType;
    private Integer pageNo;
    private Integer pageSize;
    private List<String> adTypeList;
    private List<String> targetTypeList;
    private String campaignName;
    private String groupName;
    private List<String> campaignIds;
    private List<String> groupIds;
    private String state;
    private List<String> targetIdList;
    private String matchType;
    private String ruleType;
    private String targetType;
    private String traceId;
    private List<String> servingStatusList;
    private String queryType;
    private List<String> itemIdList;
    //竞价策略
    private String strategyType;
}
