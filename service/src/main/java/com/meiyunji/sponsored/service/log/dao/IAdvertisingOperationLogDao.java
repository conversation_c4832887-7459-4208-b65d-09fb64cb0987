package com.meiyunji.sponsored.service.log.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.log.po.AdvertisingOperationLog;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.util.List;

/**
 * IAdvertisingOperationLogDao
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 */
public interface IAdvertisingOperationLogDao extends IBaseShardingDao<AdvertisingOperationLog> {

    int batchInsert(int puid, List<AdvertisingOperationLog> list);

    int deleteByIds(JdbcTemplate jdbcTemplate, List<Long> ids);

    int deleteByCreateTime(JdbcTemplate jdbcTemplate, LocalDateTime endDate, int limit);

    List<AdvertisingOperationLog> listPage(JdbcTemplate jdbcTemplate, int pageNo, int pageSize);

    long countAll(JdbcTemplate jdbcTemplate, LocalDateTime endDate);
}
