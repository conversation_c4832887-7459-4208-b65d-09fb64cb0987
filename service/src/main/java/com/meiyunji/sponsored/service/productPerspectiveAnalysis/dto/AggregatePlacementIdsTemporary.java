package com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-03-11  10:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggregatePlacementIdsTemporary {
    private List<String> adIdList;
    private List<CampaignIdPlacement> campaignIdPlacements;

    @Data
    public static class CampaignIdPlacement {
        private String campaignId;
        private String placement;
    }
}
