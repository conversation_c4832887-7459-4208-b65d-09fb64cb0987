package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * amazon SD广告投放定位表(OdsAmazonAdTargetingSd)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Data
@DbTable("ods_t_amazon_ad_targeting_sd")
public class OdsAmazonAdTargetingSd  implements Serializable {
	/**
     * 商户uid
     */    
	@DbColumn(value = "puid")
    private Integer puid;

	/**
     * 店铺ID
     */    
	@DbColumn(value = "shop_id")
    private Integer shopId;

	/**
     * 定位id
     */    
	@DbColumn(value = "target_id")
    private String targetId;

	/**
     * 站点
     */    
	@DbColumn(value = "marketplace_id")
    private String marketplaceId;

	/**
     * 配置ID
     */    
	@DbColumn(value = "profile_id")
    private String profileId;

	/**
     * 广告组id
     */    
	@DbColumn(value = "ad_group_id")
    private String adGroupId;

	/**
     * 状态（enabled，paused，archived）
     */    
	@DbColumn(value = "state")
    private String state;

	/**
     * category,asin,exactProduct,similarProduct
     */    
	@DbColumn(value = "type")
    private String type;

	/**
     * 冗余字段：投放内容，值为asin或分类
     */    
	@DbColumn(value = "target_text")
    private String targetText;

	/**
     * 竞价(否定关键词没有)
     */    
	@DbColumn(value = "bid")
    private BigDecimal bid;

	/**
     * manual,auto
     */    
	@DbColumn(value = "expression_type")
    private String expressionType;

	/**
     * 投放表达式：[{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
     */    
	@DbColumn(value = "expression")
    private String expression;

	/**
     * 接口返回的经处理后的投放表达式，包含可读性的描述信息：[{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
     */    
	@DbColumn(value = "resolved_expression")
    private String resolvedExpression;

	/**
     * asin标题（设为非空目的是会涉及查标题为空的场景）
     */    
	@DbColumn(value = "title")
    private String title;

	/**
     * asin图片（设为非空目的是会涉及查图片为空的场景）
     */    
	@DbColumn(value = "img_url")
    private String imgUrl;

	/**
     * 建议竞价
     */    
	@DbColumn(value = "suggested")
    private BigDecimal suggested;

	/**
     * 建议竞价最小值
     */    
	@DbColumn(value = "range_start")
    private BigDecimal rangeStart;

	/**
     * 建议竞价最大值
     */    
	@DbColumn(value = "range_end")
    private BigDecimal rangeEnd;

	/**
     * 投放的具体状态
     */    
	@DbColumn(value = "serving_status")
    private String servingStatus;

	/**
     * 平台创建时间
     */    
	@DbColumn(value = "creation_date")
    private Date creationDate;

	/**
     * 平台上次更新时间
     */    
	@DbColumn(value = "last_updated_date")
    private Date lastUpdatedDate;

	/**
     * 是否在赛狐创建的：1在amzup创建，0从amazon同步
     */    
	@DbColumn(value = "create_in_amzup")
    private Integer createInAmzup;

	/**
     * expression字段的第一个type值作为投放类型
     */    
	@DbColumn(value = "target_type")
    private String targetType;

	/**
     * 创建人id
     */    
	@DbColumn(value = "create_id")
    private Integer createId;

	/**
     * 修改人id
     */    
	@DbColumn(value = "update_id")
    private Integer updateId;

	/**
     * 报告数据最新更新时间 yyyy-MM-dd
     */    
	@DbColumn(value = "data_update_time")
    private LocalDate dataUpdateTime;

	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

	/**
     * 是否应用分时调价 0,1
     */    
	@DbColumn(value = "is_pricing")
    private Integer isPricing;

	/**
     * 分时调价状态 0,1 关闭，开启
     */    
	@DbColumn(value = "pricing_state")
    private Integer pricingState;

	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;

	/**
	 * 投放类型（audienceTarget，productTarget）
	 */
	@DbColumn(value = "tactic_type")
	private String tacticType;

}

