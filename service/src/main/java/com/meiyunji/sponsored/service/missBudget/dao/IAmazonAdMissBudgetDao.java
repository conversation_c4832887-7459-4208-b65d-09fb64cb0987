package com.meiyunji.sponsored.service.missBudget.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;

import java.time.LocalDate;
import java.util.List;

public interface IAmazonAdMissBudgetDao extends IBaseShardingDao<AmazonAdMissBudget> {

    void batchAdd(int puid, List<AmazonAdMissBudget> list);

    void batchUpdate(int puid, List<AmazonAdMissBudget> list);

    List<AmazonAdMissBudget> listByCampaignIds(int puid, int shopId, List<String> campaignIds);

    List<AmazonAdMissBudget> listByCampaignIdsAndShopIds(int puid, List<Integer> shopIds, List<String> campaignIds);
}
