package com.meiyunji.sponsored.service.adTagSystem.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-22  22:44
 * 广告标签组权限表
 */
@Data
@DbTable(value = "t_ad_manage_tag_group_user")
public class AdManageTagGroupUser extends BasePo {
    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    //com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum
    @DbColumn(value = "type")
    private Integer type;

    @DbColumn(value = "group_id")
    private Long groupId;

    @DbColumn(value = "user_id")
    private Integer userId;
}
