package com.meiyunji.sponsored.service.autoRule.vo;

import com.meiyunji.sponsored.service.autoRule.vo.rule.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 编辑受控对象请求参数
 * @author: zzh
 * @date: 2024/12/16 11:35
 **/
@Data
public class EditAutoRuleStatusParam {

    @ApiModelProperty("商户id")
    private Integer puid;

    @ApiModelProperty("用户id")
    private Integer uid;

    @ApiModelProperty("店铺id")
    private Integer shopId;

    @ApiModelProperty("受控对象主键id")
    private Long statusId;

    @ApiModelProperty("模板id")
    private Long templateId;

    @ApiModelProperty("规则类型")
    private String ruleType;

    @ApiModelProperty("受控对象类型")
    private String itemType;

    @ApiModelProperty("执行方式")
    private String executeType;

    @ApiModelProperty("时间类型")
    private String timeType;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("检查频率")
    private String checkFrequency;

    @ApiModelProperty("邮编设置")
    private String postalCodeSettings;

    @ApiModelProperty("选择时间类型")
    private String chooseTimeType;

    @ApiModelProperty("集合条件")
    private String setRelation;

    @ApiModelProperty("执行操作时间隔值")
    private String executeTimeSpaceValue;

    @ApiModelProperty("执行操作时间隔单位")
    private String executeTimeSpaceUnit;

    @ApiModelProperty("消息提醒类型")
    private String messageReminderType;

    @ApiModelProperty("回调状态")
    private Boolean callbackState;

    @ApiModelProperty("自动化规则列表")
    private List<AutoRuleVo> ruleList;

    @ApiModelProperty("自动化执行操作列表")
    private List<PerformOperationVo> performOperationList;

    @ApiModelProperty("时间规则列表")
    private List<TimeRuleVo> timeRules;

    @ApiModelProperty("自动化回调操作规则")
    private CallbackOperateVo callbackOperate;

    @ApiModelProperty("抢排名期望位置")
    private DesiredPositionVo desiredPosition;

    @ApiModelProperty("抢排名广告数据规则")
    private List<AdDataRuleVo> adDataRules;

    @ApiModelProperty("抢排名广告执行操作")
    private AdDataOperateVo adDataOperate;

    @ApiModelProperty("抢排名自动降价规则")
    private AutoPriceRuleVo autoPriceRule;

    @ApiModelProperty("抢排名自动降价执行操作")
    private AutoPriceOperateVo autoPriceOperate;

    @ApiModelProperty("抢排名竞价回调")
    private BiddingCallbackOperateVo biddingCallbackOperate;
}
