package com.meiyunji.sponsored.service.multiPlatform.walmart.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingMultiplierDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeyword;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingMultiplier;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告竞价乘数Dao
 */

@Repository
public class WalmartAdvertisingMultiplierDaoImpl extends BaseShardingDaoImpl<WalmartAdvertisingMultiplier> implements IWalmartAdvertisingMultiplierDao {


    @Override
    public int update(WalmartAdvertisingMultiplier multipliers) {
        if (multipliers == null) {
            return 0;
        }

        List<Object> arg = new ArrayList<>();
        arg.add(multipliers.getMultiplier());
        arg.add(multipliers.getPuid());
        arg.add(multipliers.getShopId());
        arg.add(multipliers.getCampaignId());
        arg.add(multipliers.getIsPlatform());
        arg.add(multipliers.getType());

        String sql = "update t_walmart_advertising_multiplier set multiplier = ?  where puid = ? and shop_id = ? and campaign_id = ? and is_platform = ? and type = ? ";
        return getJdbcTemplate(multipliers.getPuid()).update(sql, arg.toArray());
    }

    @Override
    public int delete(Integer puid, Long id) {
        String sql = "delete from " + getJdbcHelper().getTable() + " where puid = ? and id =?";
        return getJdbcTemplate(puid).update(sql, puid, id);
    }

    @Override
    public WalmartAdvertisingMultiplier getById(Integer puid, Long id) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingMultiplier> getByCampaignId(Integer puid, Long shopId, Long campaignId, Integer isPlatform) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingMultiplier> getByCampaignIds(Integer puid, List<Integer> shopIds, List<String> campaignIds, Integer isPlatform) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (Objects.nonNull(isPlatform)) {
            sql.append(" and is_platform = ? ");
            argsList.add(isPlatform);
        }
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(WalmartAdvertisingMultiplier.class), argsList.toArray());
    }

    @Override
    public int deleteByCampaignId(Integer puid, Long shopId, Long campaignId) {
        return 0;
    }

    @Override
    public void batchUpdateMultiplier(Integer puid, Integer shopId, String campaignId, int isPlatform, List<WalmartAdvertisingMultiplier> updateMultipliers) {
        if (CollectionUtils.isEmpty(updateMultipliers)) {
            return;
        }
        StringBuilder sql = new StringBuilder("INSERT INTO `t_walmart_advertising_multiplier` (puid, shop_id, campaign_id, `type`, multiplier, is_platform, create_time, update_time ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (WalmartAdvertisingMultiplier multipler : updateMultipliers) {
            sql.append(" (?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(campaignId);
            argsList.add(multipler.getType());
            argsList.add(multipler.getMultiplier());
            argsList.add(isPlatform);
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `multiplier`=values(multiplier),`update_time`=values(now())");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void add(WalmartAdvertisingMultiplier multipliers) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(multipliers.getPuid());

        StringBuilder sb = new StringBuilder();
        sb.append("insert into t_walmart_advertising_multiplier (puid,shop_id,campaign_id,`type`,multiplier,is_platform,create_time,update_time ) values ");
        sb.append(" (?,?,?,?,?,?,now(),now())");
        List<Object> argsList = new ArrayList<>();
        argsList.add(multipliers.getPuid());
        argsList.add(multipliers.getShopId());
        argsList.add(multipliers.getCampaignId());
        argsList.add(multipliers.getType());
        argsList.add(multipliers.getMultiplier());
        argsList.add(multipliers.getIsPlatform());
        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    @Override
    public void addOrUpdate(Integer puid, List<WalmartAdvertisingMultiplier> multipliers) {
        if (CollectionUtils.isEmpty(multipliers)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("insert into t_walmart_advertising_multiplier (puid,shop_id,campaign_id,`type`,multiplier,is_platform,create_time,update_time ) values ");
        sb.append(" (?, ?, ?, ?, ?, ?, now(), now()),");
        List<Object[]> argsList = Lists.newArrayList();
        Object[] args;
        for (WalmartAdvertisingMultiplier mDTO : multipliers) {
            args = new Object[]{
                    mDTO.getPuid(),
                    mDTO.getShopId(),
                    mDTO.getCampaignId(),
                    mDTO.getType(),
                    mDTO.getMultiplier(),
                    mDTO.getIsPlatform()
            };
            argsList.add(args);
        }
        sb.deleteCharAt(sb.length() - 1);

        sb.append(" on duplicate key update `multiplier`=values(multiplier), `update_time` = now(3) ");
        getJdbcTemplate(puid).batchUpdate(sb.toString(), argsList);
    }

    @Override
    public int deleteByCampaignId(Integer puid, Integer shopId, String campaignId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        return jdbcTemplate.update("DELETE from t_walmart_advertising_multiplier where puid = ? and shop_id = ? and campaign_id = ?", puid, shopId, campaignId);
    }

    @Override
    public int deleteByCampaignIdBatch(Integer puid, List<Integer> shopIdList, List<String> delCampaignIdList) {
        if (CollectionUtils.isEmpty(delCampaignIdList) || CollectionUtils.isEmpty(delCampaignIdList = delCampaignIdList.parallelStream().filter(org.apache.commons.lang3.StringUtils::isNotEmpty).collect(Collectors.toList()))) {
            return 0;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("DELETE from t_walmart_advertising_multiplier where puid = ?");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        sql.append(SqlStringUtil.dealInList("campaign_id", delCampaignIdList, argsList));
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<WalmartAdvertisingMultiplier> getByCampaignId(Integer puid, Integer shopId, String campaignId, Integer isPlatform) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        String sql = "select * from `t_walmart_advertising_multiplier` where `puid`=? and shop_id = ? and campaign_id = ?";
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(campaignId);
        if (isPlatform != null) {
            sql += "  and is_platform = ?";
            args.add(isPlatform);
        }
        return jdbcTemplate.query(sql, args.toArray(), getMapper());
    }

    @Override
    public List<WalmartAdvertisingMultiplier> getByAllPlatformCampaignId(Integer puid, Integer shopId, String campaignId) {
        String sql = "select * from `t_walmart_advertising_multiplier` where `puid`=? and shop_id = ? and campaign_id = ?";
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(campaignId);
        sql += "  and is_platform in (0,1)";
        return getJdbcTemplate(puid).query(sql, args.toArray(), getMapper());
    }
}
