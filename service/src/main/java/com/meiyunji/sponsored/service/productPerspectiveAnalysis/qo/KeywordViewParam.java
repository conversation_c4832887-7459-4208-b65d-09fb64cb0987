package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-06  16:52
 */
@Data
@ApiModel
public class KeywordViewParam extends ViewBaseParam {
    @ApiModelProperty(value = "关键词")
    private String keywordText;

    @ApiModelProperty("活动ID,多个用逗号分隔")
    private String campaignId;

    @ApiModelProperty("广告组ID,多个用逗号分隔")
    private String groupId;

    @ApiModelProperty("匹配类型 EXACT->精确匹配  BROAD->广泛匹配  PHRASE->词组匹配")
    private String matchType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    @ApiModelProperty("搜索值：关键词")
    private String queryValue;

    @ApiModelProperty(value = "搜索类型，模糊或精确")
    private String queryType;

    private List<String> adGroupIdList;
    //用于小时分析
    private List<String> keywordIdList;

    private String uuid;

    public enum OrderFieldEnum implements BaseEnum {
        BID("bid", "竞价"),
        AD_OTHER_ORDER_NUM("adOtherOrderNum", "其他产品广告订单量"),
        VCPM("vcpm", "千次可见展示成本");
        private String field;
        private String desc;

        OrderFieldEnum(String field, String desc) {
            this.field = field;
            this.desc = desc;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }
}
