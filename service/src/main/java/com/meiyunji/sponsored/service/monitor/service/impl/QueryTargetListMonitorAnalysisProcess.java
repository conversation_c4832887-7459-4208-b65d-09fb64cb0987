package com.meiyunji.sponsored.service.monitor.service.impl;

import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdListMonitor;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.monitor.dao.IAmazonAdListMonitorDao;
import com.meiyunji.sponsored.service.monitor.enums.MonitorCompareStateEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.monitor.service.AbstractMonitorAnalysis;
import com.meiyunji.sponsored.service.monitor.vo.MonitorCompare;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/7/24 20:45
 */
@Service
public class QueryTargetListMonitorAnalysisProcess extends AbstractMonitorAnalysis {

    protected QueryTargetListMonitorAnalysisProcess(IAmazonAdListMonitorDao amazonAdListMonitorDao) {
        super(amazonAdListMonitorDao);
    }

    @Override
    protected AmazonAdListMonitor analysisData(Object[] args, Object result, MonitorPageFunctionEnum monitorPageFunctionEnum, MonitorTypeEnum monitorTypeEnum) {
        AmazonAdListMonitor amazonAdListMonitor;
        if (MonitorTypeEnum.LIST == monitorTypeEnum) {
            CpcQueryWordDto param = (CpcQueryWordDto) args[1];
            if (Boolean.TRUE.equals(param.getOnlyCount())) {
                amazonAdListMonitor = new AmazonAdListMonitor();
                amazonAdListMonitor.setPageSign(null);
                return amazonAdListMonitor;
            }
            amazonAdListMonitor = handleList(param, monitorPageFunctionEnum, (AllQueryTargetDataResponse.AdQueryTargetingHomeVo) result);
        } else {
            CpcQueryWordDto param = (CpcQueryWordDto) args[1];
            amazonAdListMonitor = handleSum(param, monitorPageFunctionEnum, (AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo) result);
        }
        return amazonAdListMonitor;
    }

    private AmazonAdListMonitor handleList(CpcQueryWordDto param, MonitorPageFunctionEnum monitorPageFunctionEnum, AllQueryTargetDataResponse.AdQueryTargetingHomeVo vo) {
        List<ReportRpcVo> rowsList = vo.getPage().getRowsList();
        MonitorCompare monitorCompare = initCompare();
        if (CollectionUtils.isNotEmpty(rowsList)) {
            rowsList.forEach(e -> monitorCompare.sum(handleListCompareData(e)));
        }
        AmazonAdListMonitor amazonAdListMonitor = new AmazonAdListMonitor();
        amazonAdListMonitor.setPuid(param.getPuid());
        amazonAdListMonitor.setShopId(param.getShopId());
        amazonAdListMonitor.setMonitorDataTypeEnum(1);
        amazonAdListMonitor.setCompareState(MonitorCompareStateEnum.WAITING.getType());
        amazonAdListMonitor.setListRequestParam(JSONUtil.objectToJson(param));
        amazonAdListMonitor.setListCompareData(JSONUtil.objectToJson(monitorCompare));
        amazonAdListMonitor.setPageSign(param.getPageSign());
        amazonAdListMonitor.setPageFunction(monitorPageFunctionEnum.getType());
        amazonAdListMonitor.setTotalPage(vo.getPage().getTotalPage().getValue());
        return amazonAdListMonitor;

    }

    private MonitorCompare handleListCompareData(ReportRpcVo vo) {
        MonitorCompare build = MonitorCompare.builder()
                .cost(getDoubleValueBigDecimal(vo.getCost()))
                .adSales(getDoubleValueBigDecimal(vo.getAdSales()))
                .clicks(vo.hasClicks() ? vo.getClicks().getValue() : 0L)
                .impressions(vo.hasImpressions() ? vo.getImpressions().getValue() : 0L)
                .saleNum(vo.hasAdSaleNum() ? vo.getSaleNum().getValue() : 0)
                .adSale(getDoubleValueBigDecimal(vo.getSales()))
                .adOrderNum(vo.hasOrderNum() ? vo.getOrderNum().getValue() : 0)
                .orderNum(vo.hasSaleNum() ? vo.getSaleNum().getValue() : 0)
                .saleNum(vo.hasAdSaleNum() ? vo.getAdSaleNum().getValue() : 0)
                .build();
        return build;
    }


    private AmazonAdListMonitor handleSum(CpcQueryWordDto param, MonitorPageFunctionEnum monitorPageFunctionEnum, AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo vo) {
        AdHomeAggregateDataRpcVo aggregateDataVo = vo.getAggregateDataVo();
        MonitorCompare monitorCompare = MonitorCompare.builder()
                .orderNum(aggregateDataVo.hasOrderNum() ? aggregateDataVo.getOrderNum().getValue() : 0)
                .adOrderNum(aggregateDataVo.hasAdOrderNum() ? aggregateDataVo.getAdOrderNum().getValue() : 0)
                .adSales(aggregateDataVo.hasAdSales() ? getStringBigDecimal(aggregateDataVo.getAdSales()) : BigDecimal.ZERO)
                .adSale(aggregateDataVo.hasAdSale() ? getStringBigDecimal(aggregateDataVo.getAdSale()) : BigDecimal.ZERO)
                .impressions(aggregateDataVo.hasImpressions() ? aggregateDataVo.getImpressions().getValue() : 0L)
                .clicks(aggregateDataVo.hasClicks() ? aggregateDataVo.getClicks().getValue() : 0L)
                .cost(aggregateDataVo.hasAdCost() ? getStringBigDecimal(aggregateDataVo.getAdCost()) : BigDecimal.ZERO)
                .saleNum(aggregateDataVo.hasAdSaleNum() ? aggregateDataVo.getAdSaleNum().getValue() : 0).build();

        AmazonAdListMonitor amazonAdListMonitor = new AmazonAdListMonitor();
        amazonAdListMonitor.setPuid(param.getPuid());
        amazonAdListMonitor.setShopId(param.getShopId());
        amazonAdListMonitor.setMonitorDataTypeEnum(1);
        amazonAdListMonitor.setCompareState(MonitorCompareStateEnum.WAITING.getType());
        amazonAdListMonitor.setSumRequestParam(JSONUtil.objectToJson(param));
        amazonAdListMonitor.setSumCompareData(JSONUtil.objectToJson(monitorCompare));
        amazonAdListMonitor.setPageSign(param.getPageSign());
        amazonAdListMonitor.setPageFunction(monitorPageFunctionEnum.getType());
        return amazonAdListMonitor;

    }


}
