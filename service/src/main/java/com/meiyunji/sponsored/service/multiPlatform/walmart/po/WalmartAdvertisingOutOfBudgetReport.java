package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告超出预算报告
 */
@Data
public class WalmartAdvertisingOutOfBudgetReport extends BasePo {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @DbColumn(value = "id",autoIncrement=true,key = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "marketplace_code")
    private String marketplaceCode;

    /**
     * 统计时间
     */
    @DbColumn(value = "count_date")
    private Date countDate;

    /**
     * 开始时间
     */
    @DbColumn(value = "start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @DbColumn(value = "end_date")
    private Date endDate;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 建议每日预算
     */
    @DbColumn(value = "suggested_latest_daily_budget")
   private Double suggestedLatestDailyBudget;

    /**
     * 建议总预算
     */
    @DbColumn(value = "suggested_latest_total_budget")
   private Double suggestedLatestTotalBudget;



}
