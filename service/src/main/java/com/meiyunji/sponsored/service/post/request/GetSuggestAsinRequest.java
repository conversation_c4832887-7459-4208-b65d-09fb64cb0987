package com.meiyunji.sponsored.service.post.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class GetSuggestAsinRequest implements Serializable {
    private static final long serialVersionUID = 123456L;

    private Integer puid;

    @NotNull(message = "shopId不能为空")
    private Integer shopId;

    private List<Integer> shopIdList;

    private List<String> marketplaceIdList;

    private String orderFiled;

    private String orderType;

    private String onlineStatus;

    private String searchField;

    private String searchValue;

    @NotNull(message = "广告类型不能为空")
    private String type;

    private Integer pageNo;

    private Integer pageSize;

    // true为输入框 / false为搜索框
    private Boolean search;

    private String postId;
}
