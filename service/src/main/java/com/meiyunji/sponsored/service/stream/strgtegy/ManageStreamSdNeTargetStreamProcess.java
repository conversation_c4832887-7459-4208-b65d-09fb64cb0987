package com.meiyunji.sponsored.service.stream.strgtegy;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdNeTargetingApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageStreamSdNeTargetStreamProcess extends AbstractManageStreamRetryProcessStrategy {


    @Resource
    private CpcSdNeTargetingApiService cpcSdNeTargetingApiService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageStreamSdNeTargetStreamProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration);
    }


    @Override
    public int getMaxCount() {
        return StreamConstants.SD_MAX_NE_TARGET_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcSdNeTargetingApiService.syncNeTargetings(shopAuth, null, null, ids,  null,true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
    }
}
