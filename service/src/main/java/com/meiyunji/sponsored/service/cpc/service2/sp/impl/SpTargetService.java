package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sp.campaign.*;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dto.sp.CreateSpTargetDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * SpTargetService
 *
 * @Author: hejh
 * @Date: 2024/7/12 13:34
 */
@Service
@Slf4j
public class SpTargetService {
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;

    @Autowired
    private ICpcKeywordsService cpcKeywordsService;

    /**
     * 创建sp广告投放
     *
     * @param dto dto
     * @return targetId
     */
    public TargetResp createSpTarget(AmazonAdProfile amazonAdProfile, CreateSpTargetDto dto, ShopAuth shop, AmazonAdGroup group) {
        String loginIp = dto.getLoginIp();
        TargetResp.Builder builder = TargetResp.newBuilder();
        //1,参数校验
        String err = checkCreateTargetParams(dto);
        if (StringUtils.isNotBlank(err)) {
            builder.setCode(Result.ERROR);
            builder.setTargetErrMsg(err);
            return builder.build();
        }
        //2、创建自动投放
        if (dto.getAutoTarget()) {
            builder.setTargetType(Constants.AUTO_TARGET);
            CreateAutoTargetingVo createAutoTargetingVo = new CreateAutoTargetingVo();
            BeanUtils.copyProperties(dto, createAutoTargetingVo);
            try {
                Boolean sync = false;
                // 至少启用一种匹配类型,表示编辑：按定向组设置竞价（编辑默认生成的4条投放）没有编辑则不需要调用亚马逊接口同步
                if ((createAutoTargetingVo.getQueryHighRelMatchesState() != null && createAutoTargetingVo.getQueryHighRelMatchesState())
                        || (createAutoTargetingVo.getQueryBroadRelMatchesState() != null && createAutoTargetingVo.getQueryBroadRelMatchesState())
                        || (createAutoTargetingVo.getAsinSubstituteRelatedState() != null && createAutoTargetingVo.getAsinSubstituteRelatedState())
                        || (createAutoTargetingVo.getAsinAccessoryRelatedState() != null && createAutoTargetingVo.getAsinAccessoryRelatedState())) {
                    sync = true;
                }
                Result<List<TargetInfoResp>> autoTargetingResult = cpcTargetingService.createAutoTargetingWithAuthed(amazonAdProfile, group, createAutoTargetingVo, shop, loginIp, sync);
                if (!autoTargetingResult.success()) {
                    builder.setCode(Result.ERROR);
                    builder.setTargetErrMsg(autoTargetingResult.getMsg());
                    return builder.build();
                }
                builder.setCode(Result.SUCCESS);
                builder.addAllTargetList(autoTargetingResult.getData());
            } catch (Exception e) {
                builder.setCode(Result.ERROR);
                builder.setTargetErrMsg("按定向组设置竞价异常");
                log.error("按定向组设置竞价异常", e);
            }
            return builder.build();
        }
        //2、创建手动投放
        CreateManualTargetingVo createManualTargetingVo = new CreateManualTargetingVo();
        BeanUtils.copyProperties(dto, createManualTargetingVo);
        try {
            Result<List<TargetInfoResp>> targetingResult;
            if (dto.getKeywordTarget()) {
                //关键词投放
                builder.setTargetType(Constants.KEYWORD_TARGET);
                List<AmazonAdKeyword> amazonAdKeywords = cpcKeywordsService.convertAddKeywordsVoToPo(createManualTargetingVo.getUid(), group, createManualTargetingVo.getKeywords());
                targetingResult = cpcTargetingService.createKeywordsTargetingWithAuthed(amazonAdKeywords, group, loginIp, shop, 0);
            } else {
                //产品投放
                builder.setTargetType(Constants.PRODUCT_TARGET);
                List<AmazonAdTargeting> amazonAdTargetings = cpcTargetingService.convertAddTargetingVoToPO(createManualTargetingVo.getUid(), group, createManualTargetingVo.getTargetings());
                targetingResult = cpcTargetingService.createProductTargetingWithAuthed(amazonAdTargetings, group, loginIp, shop, 0);
            }
            if (!targetingResult.success()) {
                builder.setCode(Result.ERROR);
                builder.setTargetErrMsg(targetingResult.getMsg());
                return builder.build();
            }
            builder.setCode(Result.SUCCESS);
            builder.addAllTargetList(targetingResult.getData());
        } catch (Exception e) {
            builder.setCode(Result.ERROR);
            builder.setTargetErrMsg("创建投放失败");
            log.error("创建投放失败", e);
        }

        return builder.build();
    }

    /**
     * 创建sp广告否定投放
     *
     * @param dto dto
     * @return targetId
     */
    public NeKeywordResp createSpNeKeyword(CreateSpTargetDto dto, ShopAuth shop, AmazonAdGroup group) {
        NeKeywordResp.Builder builder = NeKeywordResp.newBuilder();
        if (CollectionUtils.isEmpty(dto.getNeKeywords())) {
            builder.setCode(Result.SUCCESS);
            return builder.build();
        }
        AddNeKeywordsVo addNeKeywordsVo = new AddNeKeywordsVo();
        addNeKeywordsVo.setGroupId(group.getAdGroupId());
        addNeKeywordsVo.setShopId(shop.getId());
        addNeKeywordsVo.setPuid(shop.getPuid());
        addNeKeywordsVo.setUid(dto.getUid());
        addNeKeywordsVo.setNeKeywords(dto.getNeKeywords());
        try {
            List<AmazonAdKeyword> amazonAdKeywords = cpcNeKeywordsService.convertAddNeKeywordsVoToPo(addNeKeywordsVo.getUid(), group, addNeKeywordsVo.getNeKeywords());
            Result<List<NeKeywordInfoResp>> neKeywordsResult = cpcNeKeywordsService.addNeKeywordsWithAuthed(group, amazonAdKeywords, dto.getLoginIp(), shop, 0);
            if (!neKeywordsResult.success()) {
                builder.setCode(Result.ERROR);
                builder.setNeKeywordErrMsg(neKeywordsResult.getMsg());
                return builder.build();
            }
            builder.setCode(Result.SUCCESS);
            builder.addAllNeKeywords(neKeywordsResult.getData());
            return builder.build();
        } catch (Exception e) {
            builder.setCode(Result.ERROR);
            builder.setNeKeywordErrMsg("创建否定关键词投放失败");
            log.error("创建否定关键词投放失败", e);
        }

        return builder.build();
    }

    /**
     * 创建sp广告否定投放
     *
     * @param dto dto
     * @return targetId
     */
    public NeTargetResp createSpNeProductTarget(CreateSpTargetDto dto, ShopAuth shop, AmazonAdGroup group) {
        NeTargetResp.Builder builder = NeTargetResp.newBuilder();
        if (CollectionUtils.isEmpty(dto.getNeTargetings())) {
            builder.setCode(Result.SUCCESS);
            return builder.build();
        }
        AddNeTargetingVo addNeTargetingVo = new AddNeTargetingVo();
        addNeTargetingVo.setGroupId(group.getAdGroupId());
        addNeTargetingVo.setShopId(shop.getId());
        addNeTargetingVo.setPuid(shop.getPuid());
        addNeTargetingVo.setUid(dto.getUid());
        addNeTargetingVo.setNeTargetings(dto.getNeTargetings());
        try {
            //参数校验
            String err = checkCreateNeProductTargetingWithAuthed(addNeTargetingVo.getNeTargetings());
            if (StringUtils.isNotBlank(err)) {
                builder.setCode(Result.ERROR);
                builder.setNeTargetErrMsg(err);
                return builder.build();
            }
            //调用亚马逊接口创建
            List<SpNeTargetingVo> spNeTargetingVos = cpcTargetingService.convertNeProductTargetingVoToPo(group, addNeTargetingVo.getUid(), addNeTargetingVo.getNeTargetings());
            Result<List<NeTargetInfoResp>> neTargetsResult = cpcTargetingService.createNeProductTargetingWithAuthed(spNeTargetingVos, group, dto.getLoginIp(), shop, 0);
            if (!neTargetsResult.success()) {
                builder.setCode(Result.ERROR);
                builder.setNeTargetErrMsg(neTargetsResult.getMsg());
                return builder.build();
            }
            builder.setCode(Result.SUCCESS);
            builder.addAllNeTargets(neTargetsResult.getData());
            return builder.build();
        } catch (Exception e) {
            builder.setCode(Result.ERROR);
            builder.setNeTargetErrMsg("创建否定产品投放失败");
            log.error("创建否定产品投放失败", e);
        }

        return builder.build();
    }

    private String checkCreateNeProductTargetingWithAuthed(List<NeTargetingVo> neTargetings) {
        for (NeTargetingVo vo : neTargetings) {
            if (!TargetTypeEnum.asin.name().equals(vo.getType()) && !TargetTypeEnum.brand.name().equals(vo.getType())) {
                return "请求参数错误，投放类型不正确";
            }
            if (TargetTypeEnum.asin.name().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin())) {
                return "请求参数错误，asin为空";
            }
            if (TargetTypeEnum.brand.name().equals(vo.getType()) && StringUtils.isBlank(vo.getBrandId())) {
                return "请求参数错误，品牌为空";
            }
        }
        return null;
    }

    private String checkCreateTargetParams(CreateSpTargetDto dto) {
        if (dto == null) {
            return "请求参数为空";
        }
        if (StringUtils.isBlank(dto.getGroupId())) {
            return "广告组为空";
        }
        if (Objects.isNull(dto.getAutoTarget())) {
            return "无法区分自动手动投放";
        }
        if (!dto.getAutoTarget() && Objects.isNull(dto.getKeywordTarget())) {
            return "无法区分关键词商品投放";
        }
        return null;
    }

}
