package com.meiyunji.sponsored.service.newDashboard.dto.base;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-03-26  14:30
 */

@Data
public class DashboardAdPercentDataDto extends DashboardAdCalDataDto {

    //占比指标
    @ExcelProperty("广告花费占比")
    private String costPercent;

    @ExcelProperty("广告销售额占比")
    private String totalSalesPercent;

    @ExcelProperty("广告曝光量占比")
    private String impressionsPercent;

    @ExcelProperty("广告点击量占比")
    private String clicksPercent;

    @ExcelProperty("广告订单量占比")
    private String orderNumPercent;

    @ExcelProperty("广告销量占比")
    private String saleNumPercent;

    @ExcelProperty("店铺销售额占比")
    private String shopSalesPercent;

    @ExcelProperty("店铺销量占比")
    private String shopSaleNumPercent;

}
