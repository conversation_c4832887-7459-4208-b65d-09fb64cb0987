package com.meiyunji.sponsored.service.cpc.service2.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.annotation.RateLimit;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.adCampaign.enums.AdCampaignDefaultOrderEnum;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.adCampaign.service.ISyncAmazonAdService;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagDao;
import com.meiyunji.sponsored.service.adTagSystem.dao.impl.AdManageTagDaoGroupUserImpl;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.service.impl.AdManageTagRelationService;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.budgetUsage.AmazonAdBudgetUsageService;
import com.meiyunji.sponsored.service.budgetUsage.entity.AmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AllCampaignOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdCampaignStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.dto.SyncBasicDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.IAdCampaignPlacementReportRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdCampaignAllService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageTagRelationDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignPlacementReportDao;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;
import com.meiyunji.sponsored.service.missBudget.service.AmazonAdMissBudgetService;
import com.meiyunji.sponsored.service.reportHour.vo.AdAnalysisAndCompareVo;
import com.meiyunji.sponsored.service.util.AdPageUtil;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> on 2021/7/8
 */
@Service
@Slf4j
public class CpcCampaignServiceImpl implements ICpcCampaignService {

    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcShopDataService CpCShopDataService;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IOdsAdManageTagRelationDao odsAdManageTagRelationDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdCampaignAllService amazonAdCampaignAllService;
    @Autowired
    private AmazonAdBudgetUsageService amazonAdBudgetUsageService;
    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;
    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;

    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;

    @Autowired
    private AmazonAdMissBudgetService amazonAdMissBudgetService;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;
    @Autowired
    private ISyncAmazonAdService iSyncAmazonAdService;
    @Autowired
    private IAmazonAdOperationLogService amazonAdOperationLogService;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Autowired
    private StringRedisService stringRedisService;
    private static final String GET_CAMPAIGN_PAGE_LOG = "get campaign page:";

    @Autowired
    private RedisService redisService;

    @Autowired
    private IUserDao userDao;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;

    @Autowired
    private IOdsAmazonAdCampaignPlacementReportDao odsAmazonAdCampaignPlacementReportDao;

    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;

    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;

    @Autowired
    private CpcSbTargetApiService cpcSbTargetApiService;

    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;

    @Autowired
    private AdManageTagRelationService adManageTagRelationService;

    @Autowired
    private IAdManageTagDao adManageTagDao;

    @Autowired
    private AdManageTagDaoGroupUserImpl adManageTagDaoGroupUser;

    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;

    @Autowired
    private IAdCampaignPlacementReportRoutingService adCampaignPlacementReportRoutingService;

    @Override
    public AllCampaignDataResponse.CampaignHomeVo getOldCampaignData(Integer puid, CampaignPageParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理 {} --广告活动接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();
        Page<CampaignPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        // 取店铺销售额
        log.info("广告管理{}--广告活动接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        log.info("广告管理{}--广告活动接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        param.setShopSales(shopSalesByDate);
        getOldCampaignVoList(puid, param, voPage, false);
        log.info("广告管理{}--广告活动接口调用-排序,分页,汇总,日月周数据统计- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isNotEmpty(voPage.getRows())) {
            fillAdTagData(puid, shopAuth.getId(), param, voPage.getRows());
        }
        return oldResponseHandle(puid, voPage, param, shopAuth, null).build();
    }

    @Override
    public AllCampaignDataResponse.CampaignHomeVo getDorisCampaignData(Integer puid, CampaignPageParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理 {} --广告活动接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();
        Page<CampaignPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        // 取店铺销售额
        log.info("广告管理{}--广告活动接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        log.info("广告管理{}--广告活动接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        param.setShopSales(shopSalesByDate);
        List<String> campaignIds = getQueryIdsByPageFilter(puid, param);
        if (Objects.nonNull(campaignIds) && campaignIds.isEmpty()) {
            return oldResponseHandle(puid, voPage, param, shopAuth, (x, y) -> amazonAdCampaignAllService.getDorisList(x, y)).build();
        }
        voPage = amazonAdCampaignAllService.getDorisList(puid, param);
        if (CollectionUtils.isNotEmpty(voPage.getRows())) {
            fillAdTagData(puid, shopAuth.getId(), param, voPage.getRows());
        }
        return oldResponseHandle(puid, voPage, param, shopAuth, (x, y) -> amazonAdCampaignAllService.getDorisList(x, y)).build();
    }

    @Override
    public List<CampaignPageVo> getDorisCampaignDataExport(Integer puid, CampaignPageParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理 {} --广告活动接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();
        Page<CampaignPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        // 取店铺销售额
        log.info("广告管理{}--广告活动接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        log.info("广告管理{}--广告活动接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        param.setShopSales(shopSalesByDate);
        List<String> campaignIds = getQueryIdsByPageFilter(puid, param);
        if (Objects.nonNull(campaignIds) && campaignIds.isEmpty()) {
            return new ArrayList<>();
        }
        voPage = amazonAdCampaignAllService.getDorisList(puid, param);
//        if (CollectionUtils.isNotEmpty(voPage.getRows())) {
//            fillAdTagData(puid, shopAuth.getId(), param, voPage.getRows());
//        }
        return voPage.getRows();
    }

    @Override
    public AllCampaignDataResponse.CampaignHomeVo getCampaignListPage(Integer puid, CampaignPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        Page<CampaignPageVo> voPage = checkParamAndShop(puid, param, shopAuth);
        getAllCampaignVoList(puid, param, voPage, false, false);
        if (CollectionUtils.isNotEmpty(voPage.getRows())) {
            fillAdTagData(puid, shopAuth.getId(), param, voPage.getRows());
        }
        return responseHandle(puid, voPage, param, shopAuth).build();
    }

    private AllCampaignDataResponse.CampaignHomeVo.Builder oldResponseHandle(int puid, Page<CampaignPageVo> voPage, CampaignPageParam param, ShopAuth shopAuth, BiFunction<Integer, CampaignPageParam, Page<CampaignPageVo>> function) {
        long nowTime = System.currentTimeMillis();
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        AllCampaignDataResponse.CampaignHomeVo.Builder builder = AllCampaignDataResponse.CampaignHomeVo.newBuilder();
        AllCampaignDataResponse.CampaignHomeVo.Page.Builder pageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<CampaignPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //批量查询预算使用量
            List<String> campaignIds = rows.stream().filter(item -> Arrays.asList("enabled", "paused").contains(item.getState()))
                    .map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
            LocalDate siteDate = LocalDate.now(Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone().toZoneId());
            List<AmazonAdBudgetUsage> budgetUsages = amazonAdBudgetUsageService
                    .listByCampaignIdsAndData(puid, shopAuth.getId(), "CAMPAIGN", siteDate.minusDays(3), siteDate, campaignIds);
            Map<String, Map<String, List<AmazonAdBudgetUsage>>> budgetUsageMap = budgetUsages.stream()
                    .collect(Collectors.groupingBy(AmazonAdBudgetUsage::getAdvertisingProductType,
                            Collectors.groupingBy(AmazonAdBudgetUsage::getBudgetScopeId)));
            // 批量获取建议预算数据
            long t1 = Instant.now().toEpochMilli();
            List<AmazonAdMissBudget> missBudgets = amazonAdMissBudgetService.listByCampaignIds(puid, shopAuth.getId(), campaignIds);
            log.info("批量获取建议预算数据 result: {}, 共耗时: {}", missBudgets, Instant.now().toEpochMilli() - t1);
            List<AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation> missBudgetList = Lists.newArrayList();
            if (!missBudgets.isEmpty()) {
                missBudgets.stream().forEach(succ -> {
                    AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.Builder succBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.newBuilder();
                    succBuilder.setCampaignId(succ.getCampaignId());
                    if (succ.getSuggestedBudget() != null) {
                        succBuilder.setSuggestedBudget(succ.getSuggestedBudget().doubleValue());
                    }

                    if (succ.getEstimatedMissedSalesLower() != null) {
                        succBuilder.setEstimatedMissedSalesLower(succ.getEstimatedMissedSalesLower().doubleValue());
                    }
                    if (succ.getEstimatedMissedSalesUpper() != null) {
                        succBuilder.setEstimatedMissedSalesUpper(succ.getEstimatedMissedSalesUpper().doubleValue());
                    }
                    if (succ.getEstimatedMissedImpressionsLower() != null) {
                        succBuilder.setEstimatedMissedImpressionsLower(succ.getEstimatedMissedImpressionsLower());
                    }
                    if (succ.getEstimatedMissedImpressionsUpper() != null) {
                        succBuilder.setEstimatedMissedImpressionsUpper(succ.getEstimatedMissedImpressionsUpper());
                    }
                    if (succ.getEstimatedMissedClicksLower() != null) {
                        succBuilder.setEstimatedMissedClicksLower(succ.getEstimatedMissedClicksLower());
                    }
                    if (succ.getEstimatedMissedClicksUpper() != null) {
                        succBuilder.setEstimatedMissedClicksUpper(succ.getEstimatedMissedClicksUpper());
                    }
                    if (succ.getPercentTimeInBudget() != null) {
                        succBuilder.setPercentTimeInBudget(succ.getPercentTimeInBudget().doubleValue());
                    }
                    if (StringUtils.isNotBlank(succ.getStartDate())) {
                        succBuilder.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                    }
                    if (StringUtils.isNotBlank(succ.getEndDate())) {
                        succBuilder.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                    }
                    if (StringUtils.isNotBlank(succ.getRuleId())) {
                        succBuilder.setRuleId(succ.getRuleId());
                    }
                    if (StringUtils.isNotBlank(succ.getRuleName())) {
                        succBuilder.setRuleName(succ.getRuleName());
                    }
                    if (succ.getSuggestedBudgetIncreasePercent() != null) {
                        succBuilder.setSuggestedBudgetIncreasePercent(succ.getSuggestedBudgetIncreasePercent().doubleValue());
                    }
                    missBudgetList.add(succBuilder.build());
                });
            }
            Map<String, AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation> missBudgetMap = missBudgetList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCampaignId()))
                    .collect(Collectors.toMap(AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation::getCampaignId, Function.identity()));
            //批量获取日志
            List<String> allCampaignIds = rows.stream().map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
            //24小时前的日志
            Date lastDate = DateUtil.addDay(new Date(), -1);
            Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyIdNew(puid, shopAuth.getId(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), Lists.newArrayList(AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode()), allCampaignIds, lastDate);
            if (CollectionUtils.isNotEmpty(rows)) {
                //查询环比指标
                Map<String, CampaignPageVo> compareCampaignMap = null;

                if (param.getIsCompare()) {
                    //对比时无须高级搜索条件
                    param.setUseAdvanced(false);

                    param.setCampaignIdList(rows.stream().map(CampaignPageVo::getCampaignId).collect(Collectors.toList()));
                    StopWatch sw = new StopWatch();
                    sw.start();
                    BigDecimal shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                    if (shopSalesByDateCompare == null) {
                        shopSalesByDateCompare = BigDecimal.ZERO;
                    }
                    sw.stop();
                    log.info("广告管理--广告活动接口调用-获取店铺销售额- 花费时间 {}", sw.getTotalTimeMillis());
                    param.setShopSales(shopSalesByDateCompare);

                    param.setStartDate(param.getCompareStartDate());
                    param.setEndDate(param.getCompareEndDate());
                    param.setCampaignIdList(rows.stream().map(CampaignPageVo::getCampaignId).collect(Collectors.toList()));

                    Page<CampaignPageVo> pageListCompare;
                    if (function != null) {
                        pageListCompare = function.apply(puid, param);
                    } else {
                        // TODO 比对替换掉
                        pageListCompare = cpcSpCampaignService.getPageList(puid, param, new Page(1, rows.size()));
                    }
                    List<CampaignPageVo> rowsCompare = pageListCompare.getRows();

                    compareCampaignMap = rowsCompare.stream().collect(Collectors.toMap(CampaignPageVo::getCampaignId, Function.identity(), (a, b) -> a));
                }

                Map<String, CampaignPageVo> finalCompareCampaignMap = compareCampaignMap;
                List<AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo> rpcVos = rows.stream()
                        .filter(Objects::nonNull).map(item -> {
                            AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder =
                                    AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.newBuilder();
                            voBuilder.setId(Int64Value.of(item.getId()));
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                            voBuilder.setCampaignId(item.getCampaignId());
                            if (item.getSbType() != null) {
                                voBuilder.setSbType(item.getSbType());
                            }
                            if (StringUtils.isNotBlank(item.getPortfolioId())) {
                                voBuilder.setPortfolioId(item.getPortfolioId());
                            }
                            if (StringUtils.isNotBlank(item.getPortfolioName())) {
                                voBuilder.setPortfolioName(item.getPortfolioName());
                            }
                            if (item.getIsHidden() != null) {
                                voBuilder.setIsHidden(item.getIsHidden());
                            }
                            if (StringUtils.isNotBlank(item.getName())) {
                                voBuilder.setName(item.getName());
                            }
                            if (StringUtils.isNotBlank(item.getState())) {
                                voBuilder.setState(item.getState());
                            }
                            if (StringUtils.isNotBlank(item.getServingStatus())) {
                                voBuilder.setServingStatus(item.getServingStatus());
                            }
                            if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                                voBuilder.setServingStatusDec(item.getServingStatusDec());
                            }
                            if (item.getOutOfBudget() != null) {
                                voBuilder.setOutOfBudget(BoolValue.of(item.getOutOfBudget()));
                            }
                            if (StringUtils.isNotBlank(item.getDailyBudget())) {
                                voBuilder.setDailyBudget(item.getDailyBudget());
                            }
                            if (StringUtils.isNotBlank(item.getBudgetType())) {
                                voBuilder.setBudgetType(item.getBudgetType());
                            }
                            if (StringUtils.isNotBlank(item.getCampaignType())) {
                                voBuilder.setCampaignType(item.getCampaignType());
                            }
                            if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                                voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                            }
                            if (StringUtils.isNotBlank(item.getTargetingType())) {
                                voBuilder.setTargetingType(item.getTargetingType());
                            }
                            if (StringUtils.isNotBlank(item.getStrategy())) {
                                voBuilder.setStrategy(item.getStrategy());
                            }
                            if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
                                voBuilder.setPlacementProductPage(item.getPlacementProductPage());
                            } else {
                                voBuilder.setPlacementProductPage("0");
                            }
                            if (StringUtils.isNotBlank(item.getPlacementTop())) {
                                voBuilder.setPlacementTop(item.getPlacementTop());
                            } else {
                                voBuilder.setPlacementTop("0");
                            }
                            if (StringUtils.isNotBlank(item.getPlacementRestOfSearch())) {
                                voBuilder.setPlacementRestOfSearch(item.getPlacementRestOfSearch());
                            } else {
                                voBuilder.setPlacementRestOfSearch("0");
                            }
                            if (StringUtils.isNotBlank(item.getCreator())) {
                                voBuilder.setCreator(item.getCreator());
                            }
                            if (StringUtils.isNotBlank(item.getStartDate())) {
                                voBuilder.setStartDate(item.getStartDate());
                            }
                            if (StringUtils.isNotBlank(item.getEndDate())) {
                                voBuilder.setEndDate(item.getEndDate());
                            }
                            if (StringUtils.isNotBlank(item.getType())) {
                                voBuilder.setType(item.getType());
                            }
                            if (item.getUpdateTime() != null) {
                                voBuilder.setUpdateTime(DateUtil.dateToStrWithFormat(item.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
                            }
                            if (item.getCreateTime() != null) {
                                voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                            }
                            if (item.getTargetType() != null) {
                                voBuilder.setTargetType(item.getTargetType());
                            }
                            if (item.getAdFormat() != null) {
                                voBuilder.setAdFormat(item.getAdFormat());
                            }

                            voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                            voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                            //广告订单量
                            voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                            voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                            voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                            voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                            voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                            voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                            voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                            voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                            voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                            //广告销售额
                            voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                            // 广告策略标签
                            if (CollectionUtils.isNotEmpty(item.getStrategyList())) {
                                voBuilder.addAllAdStrategys(buildStrategyList(item));
                            }
                            //分时调价设置
                            if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                                voBuilder.setMarketplaceId(item.getMarketplaceId());
                            }
                            if (item.getIsBudgetPricing() != null) {
                                voBuilder.setIsBudgetPricing(item.getIsBudgetPricing());
                            }
                            if (item.getIsStatePricing() != null) {
                                voBuilder.setIsStatePricing(item.getIsStatePricing());
                            }
                            if (item.getPricingStartStopState() != null) {
                                voBuilder.setPricingStartStopState(item.getPricingStartStopState());
                            }
                            if (item.getPricingBudgetState() != null) {
                                voBuilder.setPricingBudgetState(item.getPricingBudgetState());
                            }
                            if (item.getIsSpacePricing() != null) {
                                voBuilder.setIsSpacePricing(item.getIsSpacePricing());
                            }
                            if (item.getPricingSpaceState() != null) {
                                voBuilder.setPricingSpaceState(item.getPricingSpaceState());
                            }

                            if (item.getCostType() != null) {
                                voBuilder.setCostType(item.getCostType());
                            }
                            if (item.getBrandEntityId() != null) {
                                voBuilder.setBrandEntityId(item.getBrandEntityId());
                            }
                            if (item.getBidOptimization() != null) {
                                voBuilder.setBidOptimization(item.getBidOptimization());
                            }
                            if (item.getBidMultiplier() != null) {
                                voBuilder.setBidMultiplier(item.getBidMultiplier());
                            }
                            if (item.getServingStatusName() != null) {
                                voBuilder.setServingStatusName(item.getServingStatusName());
                            }
                            if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType()) || StringUtils.isEmpty(item.getCostType())) {
                                voBuilder.setCostType("cpc");
                            }
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(item.getAdTags())) {
                                item.getAdTags().forEach(e -> {
                                    AdTagVo.Builder adTagBuilder = AdTagVo.newBuilder();
                                    AdTagVo tagVo = adTagBuilder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                                    voBuilder.addAdTags(tagVo);
                                });
                            }
                            voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                            voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                            if (SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())) {
                                voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                            } else {
                                voBuilder.setVcpm("-");
                            }
                            voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                            voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));

                            voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));

                            voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                            //其他广告产品订单量
                            voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                            //本广告产品销售额
                            voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                            //其他广告产品销售额
                            voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                            //广告销量
                            voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                            voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                            voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                            voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                            voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                            voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                            voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                            // 花费占比
                            voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                            // 销售额占比
                            voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                            // 订单量占比
                            voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                            // 销量占比
                            voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                            voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                            voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                            voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                            voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                            voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                            voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                            //预算使用量
                            Map<String, List<AmazonAdBudgetUsage>> adTypeMap = budgetUsageMap.get(item.getType());
                            AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage todayBudgetUsage = null;

                            List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage> budgetUsageList = new ArrayList<>();
                            Map<LocalDate, List<AmazonAdBudgetUsage>> budgetUsageDataListMap = new HashMap<>();
                            if (MapUtils.isNotEmpty(adTypeMap)) {
                                List<AmazonAdBudgetUsage> amazonAdBudgetUsages = adTypeMap.get(item.getCampaignId());
                                if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
                                    budgetUsageDataListMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getUsageUpdatedSiteDate));
                                }
                            }
                            for (int x = 2; x >= 0; x--) {
                                String dayType = "";
                                if (x == 2) {
                                    dayType = "theDayBefore";
                                }
                                if (x == 1) {
                                    dayType = "yesterday";
                                }
                                if (x == 0) {
                                    dayType = "today";
                                }
                                LocalDate localDate = siteDate.minusDays(x);
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder buil = null;
                                if (MapUtils.isNotEmpty(adTypeMap)) {
                                    buil = builderBudgetUsage(budgetUsageDataListMap.get(localDate));
                                } else {
                                    buil = builderBudgetUsage(null);
                                }
                                buil.setDayType(dayType);
                                buil.setDate(localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage budgetUsage = buil.build();
                                if (x == 0) {
                                    todayBudgetUsage = budgetUsage;
                                }
                                budgetUsageList.add(budgetUsage);
                            }
                            if (todayBudgetUsage != null) {
                                voBuilder.setBudgetUsage(todayBudgetUsage);
                            }
                            voBuilder.addAllBudgetUsages(budgetUsageList);

                            if (MapUtils.isNotEmpty(missBudgetMap) && missBudgetMap.get(item.getCampaignId()) != null) {
                                voBuilder.setMissBudget(missBudgetMap.get(item.getCampaignId()));
                            }
                            //环比指标数据
                            if (MapUtils.isNotEmpty(finalCompareCampaignMap)) {
                                if (finalCompareCampaignMap.containsKey(item.getCampaignId())) {
                                    CampaignPageVo compareItem = finalCompareCampaignMap.get(item.getCampaignId());

                                    voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0));

                                    //曝光环比值
                                    voBuilder.setCompareImpressionsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getImpressions().getValue()), BigDecimal.valueOf(voBuilder.getCompareImpressions())).toString());

                                    voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" :
                                            String.valueOf(compareItem.getViewImpressions()));

                                    //可见展示环比值
                                    voBuilder.setCompareViewImpressionsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getViewImpressions().getValue()), BigDecimal.valueOf(Integer.parseInt(voBuilder.getCompareViewImpressions()))).toString());

                                    voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0));

                                    //点击量环比值
                                    voBuilder.setCompareClicksRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getClicks().getValue()), BigDecimal.valueOf(voBuilder.getCompareClicks())).toString());

                                    voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");

                                    //ctr环比值
                                    voBuilder.setCompareCtrRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getCtr()), new BigDecimal(voBuilder.getCompareCtr())).toString());

                                    voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");

                                    //cvr环比值
                                    voBuilder.setCompareCvrRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getCvr()), new BigDecimal(voBuilder.getCompareCvr())).toString());

                                    voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");

                                    //Acos环比值
                                    voBuilder.setCompareAcosRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAcos()), new BigDecimal(voBuilder.getCompareAcos())).toString());

                                    voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");

                                    //Acots环比值
                                    voBuilder.setCompareAcotsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAcots()), new BigDecimal(voBuilder.getCompareAcots())).toString());

                                    voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");

                                    //Asots环比值
                                    voBuilder.setCompareAsotsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAsots()), new BigDecimal(voBuilder.getCompareAsots())).toString());

                                    voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));

                                    //AdOrderNum环比值
                                    voBuilder.setCompareAdOrderNumRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getAdOrderNum().getValue()), BigDecimal.valueOf(voBuilder.getCompareAdOrderNum())).toString());

                                    voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");

                                    //AdCost环比值
                                    voBuilder.setCompareAdCostRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdCost()), new BigDecimal(voBuilder.getCompareAdCost())).toString());

                                    voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");

                                    //AdCostPerClick环比值
                                    voBuilder.setCompareAdCostPerClickRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdCostPerClick()), new BigDecimal(voBuilder.getCompareAdCostPerClick())).toString());

                                    voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");

                                    //AdSale环比值
                                    voBuilder.setCompareAdSaleRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdSale()), new BigDecimal(voBuilder.getCompareAdSale())).toString());

                                    voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");

                                    //Roas环比值
                                    voBuilder.setCompareRoasRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getRoas()), new BigDecimal(voBuilder.getCompareRoas())).toString());

                                    voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                                    //Cpa环比值
                                    BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                                    voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                                    //Vcpm环比值
                                    if (SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())) {
                                        BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                                        voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                                vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());
                                    } else {
                                        voBuilder.setCompareVcpmRate("-");
                                    }
                                    voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                    //AdSaleNum比值
                                    int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                                    voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                    //AdOtherOrderNum比值
                                    int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                                    voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                                    //AdSales环比值
                                    BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                                    voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                                    voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                                    //AdOtherSales环比值
                                    BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                                    voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                    //OrderNum比值
                                    int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                                    voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                            new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                    //AdSelfSaleNum比值
                                    int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                                    voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                    //AdOtherSaleNum比值
                                    int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                                    voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                                    //OrdersNewToBrandFTD比值
                                    int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                                    voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                            new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                            compareItem.getOrderRateNewToBrandFTD() : "0");
                                    //OrderRateNewToBrandFTD环比值
                                    BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                                    voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                            compareItem.getSalesNewToBrandFTD() : "0");
                                    //SalesNewToBrandFTD环比值
                                    BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                                    voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                            compareItem.getSalesRateNewToBrandFTD() : "0");
                                    //SalesRateNewToBrandFTD环比值
                                    BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                                    voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                                    //UnitsOrderedNewToBrandFTD比值
                                    int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                                    voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                            new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                            compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                                    //UnitsOrderedRateNewToBrandFTD环比值
                                    BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                                    voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                            compareItem.getAdCostPercentage() : "0");
                                    //AdCostPercentage环比值
                                    BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                                    voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                    //AdSalePercentage环比值
                                    BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                                    voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                    //AdOrderNumPercentage环比值
                                    BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                                    voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                    //OrderNumPercentage环比值
                                    BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                                    voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));

                                    voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));

                                    voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));

                                    voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));

                                    voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));

                                    voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));

                                    voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));

                                    voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));

                                    voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));

                                    voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));

                                    voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));

                                    voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));

                                    voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));

                                    voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));

                                    voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));

                                    voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));

                                    voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));

                                }
                            }

                            //设置预算、竞价日志
                            setCampaignHomeVoOperationLog(voBuilder, amazonAdOperationLogMap);
                            voBuilder.setTopImpressionShare(Optional.ofNullable(item.getTopImpressionShare()).orElse("-"));
                            if (isVc) {
                                voBuilder.clearAcots();
                                voBuilder.clearCompareAcotsRate();
                                voBuilder.clearCompareAcots();
                                voBuilder.clearAsots();
                                voBuilder.clearCompareAsotsRate();
                                voBuilder.clearCompareAsots();
                            }
                            return voBuilder.build();

                        }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
        }
        builder.setPage(pageBuilder.build());
        log.info("拼装总共花费 = {}", System.currentTimeMillis() - nowTime);
        return builder;
    }

    private static List<AdStrategy> buildStrategyList(CampaignPageVo item) {
        List<AdStrategy> strategyList = new ArrayList<>();
        for (AdStrategyVo strategyVo : item.getStrategyList()) {
            AdStrategy.Builder strategyBuilder = AdStrategy.newBuilder();
            strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
            strategyBuilder.setStatus(strategyVo.getStatus());
            strategyList.add(strategyBuilder.build());
        }
        return strategyList;
    }

    private AllCampaignDataResponse.CampaignHomeVo.Builder responseHandle(int puid, Page<CampaignPageVo> voPage, CampaignPageParam param, ShopAuth shopAuth) {
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        AllCampaignDataResponse.CampaignHomeVo.Builder builder = AllCampaignDataResponse.CampaignHomeVo.newBuilder();
        AllCampaignDataResponse.CampaignHomeVo.Page.Builder pageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<CampaignPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //批量查询预算使用量
            List<String> campaignIds = rows.stream().filter(item -> Arrays.asList("enabled", "paused").contains(item.getState()))
                    .map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
            LocalDate siteDate = LocalDate.now(Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone().toZoneId());
            List<AmazonAdBudgetUsage> budgetUsages = amazonAdBudgetUsageService
                    .listByCampaignIdsAndData(puid, shopAuth.getId(), "CAMPAIGN", siteDate.minusDays(3), siteDate, campaignIds);
            Map<String, Map<String, List<AmazonAdBudgetUsage>>> budgetUsageMap = budgetUsages.stream()
                    .collect(Collectors.groupingBy(AmazonAdBudgetUsage::getAdvertisingProductType,
                            Collectors.groupingBy(AmazonAdBudgetUsage::getBudgetScopeId)));

            // 批量获取建议预算数据
            long t1 = Instant.now().toEpochMilli();
            List<AmazonAdMissBudget> missBudgets = amazonAdMissBudgetService.listByCampaignIds(puid, shopAuth.getId(), campaignIds);
            log.info("批量获取建议预算数据 result: {}, 共耗时: {}", missBudgets, Instant.now().toEpochMilli() - t1);
            List<AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation> missBudgetList = Lists.newArrayList();
            if (!missBudgets.isEmpty()) {
                missBudgets.stream().forEach(succ -> {
                    AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.Builder succBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation.newBuilder();
                    succBuilder.setCampaignId(succ.getCampaignId());
                    if (succ.getSuggestedBudget() != null) {
                        succBuilder.setSuggestedBudget(succ.getSuggestedBudget().doubleValue());
                    }

                    if (succ.getEstimatedMissedSalesLower() != null) {
                        succBuilder.setEstimatedMissedSalesLower(succ.getEstimatedMissedSalesLower().doubleValue());
                    }
                    if (succ.getEstimatedMissedSalesUpper() != null) {
                        succBuilder.setEstimatedMissedSalesUpper(succ.getEstimatedMissedSalesUpper().doubleValue());
                    }
                    if (succ.getEstimatedMissedImpressionsLower() != null) {
                        succBuilder.setEstimatedMissedImpressionsLower(succ.getEstimatedMissedImpressionsLower());
                    }
                    if (succ.getEstimatedMissedImpressionsUpper() != null) {
                        succBuilder.setEstimatedMissedImpressionsUpper(succ.getEstimatedMissedImpressionsUpper());
                    }
                    if (succ.getEstimatedMissedClicksLower() != null) {
                        succBuilder.setEstimatedMissedClicksLower(succ.getEstimatedMissedClicksLower());
                    }
                    if (succ.getEstimatedMissedClicksUpper() != null) {
                        succBuilder.setEstimatedMissedClicksUpper(succ.getEstimatedMissedClicksUpper());
                    }
                    if (succ.getPercentTimeInBudget() != null) {
                        succBuilder.setPercentTimeInBudget(succ.getPercentTimeInBudget().doubleValue());
                    }
                    if (StringUtils.isNotBlank(succ.getStartDate())) {
                        succBuilder.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                    }
                    if (StringUtils.isNotBlank(succ.getEndDate())) {
                        succBuilder.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
                    }
                    if (StringUtils.isNotBlank(succ.getRuleId())) {
                        succBuilder.setRuleId(succ.getRuleId());
                    }
                    if (StringUtils.isNotBlank(succ.getRuleName())) {
                        succBuilder.setRuleName(succ.getRuleName());
                    }
                    if (succ.getSuggestedBudgetIncreasePercent() != null) {
                        succBuilder.setSuggestedBudgetIncreasePercent(succ.getSuggestedBudgetIncreasePercent().doubleValue());
                    }
                    missBudgetList.add(succBuilder.build());
                });
            }
            Map<String, AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation> missBudgetMap = missBudgetList.stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCampaignId()))
                    .collect(Collectors.toMap(AllCampaignDataResponse.CampaignHomeVo.Page.MissBudgetRecommendation::getCampaignId, Function.identity()));

            //批量获取日志
            List<String> allCampaignIds = rows.stream().map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
            //24小时前的日志
            Date lastDate = DateUtil.addDay(new Date(), -1);
            Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyId(puid, shopAuth.getId(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), null, allCampaignIds, lastDate);

            if (CollectionUtils.isNotEmpty(rows)) {
                //查询环比指标
                Map<String, CampaignPageVo> compareCampaignMap = null;

                if (param.getIsCompare()) {
                    //对比时无须高级搜索条件
                    param.setUseAdvanced(false);

                    param.setCampaignIdList(rows.stream().map(CampaignPageVo::getCampaignId).collect(Collectors.toList()));
                    StopWatch sw = new StopWatch();
                    sw.start();
                    BigDecimal shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                    if (shopSalesByDateCompare == null) {
                        shopSalesByDateCompare = BigDecimal.ZERO;
                    }
                    sw.stop();
                    log.info("广告管理--广告活动接口调用-获取店铺销售额- 花费时间 {}", sw.getTotalTimeMillis());
                    param.setShopSales(shopSalesByDateCompare);

                    param.setStartDate(param.getCompareStartDate());
                    param.setEndDate(param.getCompareEndDate());
                    param.setCampaignIdList(rows.stream().map(CampaignPageVo::getCampaignId).collect(Collectors.toList()));
                    List<CampaignPageVo> rowsCompare = getAllCampaignVoList(puid, param, new Page(1, rows.size()), true, false);
                    compareCampaignMap = rowsCompare.stream().collect(Collectors.toMap(CampaignPageVo::getCampaignId, Function.identity(), (a, b) -> a));
                }

                Map<String, CampaignPageVo> finalCompareCampaignMap = compareCampaignMap;
                List<AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo> rpcVos = rows.stream()
                        .filter(Objects::nonNull).map(item -> {
                            AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder =
                                    AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.newBuilder();
                            voBuilder.setId(Int64Value.of(item.getId()));
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                            voBuilder.setCampaignId(item.getCampaignId());

                            if (item.getSbType() != null) {
                                voBuilder.setSbType(item.getSbType());
                            }
                            if (StringUtils.isNotBlank(item.getPortfolioId())) {
                                voBuilder.setPortfolioId(item.getPortfolioId());
                            }
                            if (StringUtils.isNotBlank(item.getPortfolioName())) {
                                voBuilder.setPortfolioName(item.getPortfolioName());
                            }
                            if (item.getIsHidden() != null) {
                                voBuilder.setIsHidden(item.getIsHidden());
                            }
                            if (StringUtils.isNotBlank(item.getName())) {
                                voBuilder.setName(item.getName());
                            }
                            if (StringUtils.isNotBlank(item.getState())) {
                                voBuilder.setState(item.getState());
                            }
                            if (StringUtils.isNotBlank(item.getServingStatus())) {
                                voBuilder.setServingStatus(item.getServingStatus());
                            }
                            if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                                voBuilder.setServingStatusDec(item.getServingStatusDec());
                            }
                            if (item.getOutOfBudget() != null) {
                                voBuilder.setOutOfBudget(BoolValue.of(item.getOutOfBudget()));
                            }
                            if (StringUtils.isNotBlank(item.getDailyBudget())) {
                                voBuilder.setDailyBudget(item.getDailyBudget());
                            }
                            if (StringUtils.isNotBlank(item.getBudgetType())) {
                                voBuilder.setBudgetType(item.getBudgetType());
                            }
                            if (StringUtils.isNotBlank(item.getCampaignType())) {
                                voBuilder.setCampaignType(item.getCampaignType());
                            }
                            if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                                voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                            }
                            if (StringUtils.isNotBlank(item.getTargetingType())) {
                                voBuilder.setTargetingType(item.getTargetingType());
                            }
                            if (StringUtils.isNotBlank(item.getStrategy())) {
                                voBuilder.setStrategy(item.getStrategy());
                            }
                            if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
                                voBuilder.setPlacementProductPage(item.getPlacementProductPage());
                            } else {
                                voBuilder.setPlacementProductPage("0");
                            }
                            if (StringUtils.isNotBlank(item.getPlacementTop())) {
                                voBuilder.setPlacementTop(item.getPlacementTop());
                            } else {
                                voBuilder.setPlacementTop("0");
                            }
                            if (StringUtils.isNotBlank(item.getPlacementRestOfSearch())) {
                                voBuilder.setPlacementRestOfSearch(item.getPlacementRestOfSearch());
                            } else {
                                voBuilder.setPlacementRestOfSearch("0");
                            }
                            if (StringUtils.isNotBlank(item.getCreator())) {
                                voBuilder.setCreator(item.getCreator());
                            }
                            if (StringUtils.isNotBlank(item.getStartDate())) {
                                voBuilder.setStartDate(item.getStartDate());
                            }
                            if (StringUtils.isNotBlank(item.getEndDate())) {
                                voBuilder.setEndDate(item.getEndDate());
                            }
                            if (StringUtils.isNotBlank(item.getType())) {
                                voBuilder.setType(item.getType());
                            }
                            if (item.getUpdateTime() != null) {
                                voBuilder.setUpdateTime(DateUtil.dateToStrWithFormat(item.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
                            }
                            if (item.getCreateTime() != null) {
                                voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                            }
                            if (item.getTargetType() != null) {
                                voBuilder.setTargetType(item.getTargetType());
                            }
                            if (item.getAdFormat() != null) {
                                voBuilder.setAdFormat(item.getAdFormat());
                            }

                            voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                            voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                            //广告订单量
                            voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                            voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                            voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                            voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                            voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                            voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                            voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                            voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                            voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                            //广告销售额
                            voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");


                            //分时调价设置
                            if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                                voBuilder.setMarketplaceId(item.getMarketplaceId());
                            }
                            if (item.getIsBudgetPricing() != null) {
                                voBuilder.setIsBudgetPricing(item.getIsBudgetPricing());
                            }
                            if (item.getIsStatePricing() != null) {
                                voBuilder.setIsStatePricing(item.getIsStatePricing());
                            }
                            if (item.getPricingStartStopState() != null) {
                                voBuilder.setPricingStartStopState(item.getPricingStartStopState());
                            }
                            if (item.getPricingBudgetState() != null) {
                                voBuilder.setPricingBudgetState(item.getPricingBudgetState());
                            }
                            if (item.getIsSpacePricing() != null) {
                                voBuilder.setIsSpacePricing(item.getIsSpacePricing());
                            }
                            if (item.getPricingSpaceState() != null) {
                                voBuilder.setPricingSpaceState(item.getPricingSpaceState());
                            }

                            if (item.getCostType() != null) {
                                voBuilder.setCostType(item.getCostType());
                            }
                            if (item.getBrandEntityId() != null) {
                                voBuilder.setBrandEntityId(item.getBrandEntityId());
                            }
                            if (item.getBidOptimization() != null) {
                                voBuilder.setBidOptimization(item.getBidOptimization());
                            }
                            if (item.getBidMultiplier() != null) {
                                voBuilder.setBidMultiplier(item.getBidMultiplier());
                            }
                            if (item.getServingStatusName() != null) {
                                voBuilder.setServingStatusName(item.getServingStatusName());
                            }
                            if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType()) || CampaignTypeEnum.sb.getCampaignType().equals(item.getType())) {
                                voBuilder.setCostType("cpc");
                            }
                            voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                            voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                            voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");

                            voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                            voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));

                            voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));

                            voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                            //其他广告产品订单量
                            voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                            //本广告产品销售额
                            voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                            //其他广告产品销售额
                            voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                            //广告销量
                            voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                            voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                            voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                            voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                            voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                            voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                            voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                            // 花费占比
                            voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                            // 销售额占比
                            voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                            // 订单量占比
                            voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                            // 销量占比
                            voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                            voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                            voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                            voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                            voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                            voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                            voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                            voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                            voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                            //预算使用量
                            Map<String, List<AmazonAdBudgetUsage>> adTypeMap = budgetUsageMap.get(item.getType());
                            AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage todayBudgetUsage = null;

                            List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage> budgetUsageList = new ArrayList<>();
                            Map<LocalDate, List<AmazonAdBudgetUsage>> budgetUsageDataListMap = new HashMap<>();
                            if (MapUtils.isNotEmpty(adTypeMap)) {
                                List<AmazonAdBudgetUsage> amazonAdBudgetUsages = adTypeMap.get(item.getCampaignId());
                                if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
                                    budgetUsageDataListMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getUsageUpdatedSiteDate));
                                }
                            }
                            for (int x = 2; x >= 0; x--) {
                                String dayType = "";
                                if (x == 2) {
                                    dayType = "theDayBefore";
                                }
                                if (x == 1) {
                                    dayType = "yesterday";
                                }
                                if (x == 0) {
                                    dayType = "today";
                                }
                                LocalDate localDate = siteDate.minusDays(x);
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder buil = null;
                                if (MapUtils.isNotEmpty(adTypeMap)) {
                                    buil = builderBudgetUsage(budgetUsageDataListMap.get(localDate));
                                } else {
                                    buil = builderBudgetUsage(null);
                                }
                                buil.setDayType(dayType);
                                buil.setDate(localDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage budgetUsage = buil.build();
                                if (x == 0) {
                                    todayBudgetUsage = budgetUsage;
                                }
                                budgetUsageList.add(budgetUsage);
                            }
                            if (todayBudgetUsage != null) {
                                voBuilder.setBudgetUsage(todayBudgetUsage);
                            }
                            voBuilder.addAllBudgetUsages(budgetUsageList);

                            if (MapUtils.isNotEmpty(missBudgetMap) && missBudgetMap.get(item.getCampaignId()) != null) {
                                voBuilder.setMissBudget(missBudgetMap.get(item.getCampaignId()));
                            }
                            //环比指标数据
                            if (MapUtils.isNotEmpty(finalCompareCampaignMap)) {
                                if (finalCompareCampaignMap.containsKey(item.getCampaignId())) {
                                    CampaignPageVo compareItem = finalCompareCampaignMap.get(item.getCampaignId());

                                    voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0));

                                    //曝光环比值
                                    voBuilder.setCompareImpressionsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getImpressions().getValue()), BigDecimal.valueOf(voBuilder.getCompareImpressions())).toString());

                                    voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" :
                                            String.valueOf(compareItem.getViewImpressions()));

                                    //可见展示环比值
                                    voBuilder.setCompareViewImpressionsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getViewImpressions().getValue()), BigDecimal.valueOf(Integer.parseInt(voBuilder.getCompareViewImpressions()))).toString());

                                    voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0));

                                    //点击量环比值
                                    voBuilder.setCompareClicksRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getClicks().getValue()), BigDecimal.valueOf(voBuilder.getCompareClicks())).toString());

                                    voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");

                                    //ctr环比值
                                    voBuilder.setCompareCtrRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getCtr()), new BigDecimal(voBuilder.getCompareCtr())).toString());

                                    voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");

                                    //cvr环比值
                                    voBuilder.setCompareCvrRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getCvr()), new BigDecimal(voBuilder.getCompareCvr())).toString());

                                    voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");

                                    //Acos环比值
                                    voBuilder.setCompareAcosRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAcos()), new BigDecimal(voBuilder.getCompareAcos())).toString());

                                    voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");

                                    //Acots环比值
                                    voBuilder.setCompareAcotsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAcots()), new BigDecimal(voBuilder.getCompareAcots())).toString());

                                    voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");

                                    //Asots环比值
                                    voBuilder.setCompareAsotsRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAsots()), new BigDecimal(voBuilder.getCompareAsots())).toString());

                                    voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));

                                    //AdOrderNum环比值
                                    voBuilder.setCompareAdOrderNumRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(BigDecimal.valueOf(voBuilder.getAdOrderNum().getValue()), BigDecimal.valueOf(voBuilder.getCompareAdOrderNum())).toString());

                                    voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");

                                    //AdCost环比值
                                    voBuilder.setCompareAdCostRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdCost()), new BigDecimal(voBuilder.getCompareAdCost())).toString());

                                    voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");

                                    //AdCostPerClick环比值
                                    voBuilder.setCompareAdCostPerClickRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdCostPerClick()), new BigDecimal(voBuilder.getCompareAdCostPerClick())).toString());

                                    voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");

                                    //AdSale环比值
                                    voBuilder.setCompareAdSaleRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getAdSale()), new BigDecimal(voBuilder.getCompareAdSale())).toString());

                                    voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");

                                    //Roas环比值
                                    voBuilder.setCompareRoasRate(AdAnalysisAndCompareVo.
                                            calculateCompareRete(new BigDecimal(voBuilder.getRoas()), new BigDecimal(voBuilder.getCompareRoas())).toString());

                                    voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                                    //Cpa环比值
                                    BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                                    voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                                    //Vcpm环比值
                                    BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                                    voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                                    voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                    //AdSaleNum比值
                                    int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                                    voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                    //AdOtherOrderNum比值
                                    int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                                    voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                                    //AdSales环比值
                                    BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                                    voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                                    voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                                    //AdOtherSales环比值
                                    BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                                    voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                    //OrderNum比值
                                    int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                                    voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                            new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                    //AdSelfSaleNum比值
                                    int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                                    voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                    //AdOtherSaleNum比值
                                    int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                                    voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                            new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                                    //OrdersNewToBrandFTD比值
                                    int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                                    voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                            new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                            compareItem.getOrderRateNewToBrandFTD() : "0");
                                    //OrderRateNewToBrandFTD环比值
                                    BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                                    voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                            compareItem.getSalesNewToBrandFTD() : "0");
                                    //SalesNewToBrandFTD环比值
                                    BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                                    voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                            compareItem.getSalesRateNewToBrandFTD() : "0");
                                    //SalesRateNewToBrandFTD环比值
                                    BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                                    voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                                    //UnitsOrderedNewToBrandFTD比值
                                    int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                                    voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                            new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                                    2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                            compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                                    //UnitsOrderedRateNewToBrandFTD环比值
                                    BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                                    voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                            compareItem.getAdCostPercentage() : "0");
                                    //AdCostPercentage环比值
                                    BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                                    voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                    //AdSalePercentage环比值
                                    BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                                    voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                    //AdOrderNumPercentage环比值
                                    BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                                    voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                    //OrderNumPercentage环比值
                                    BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                                    voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                    //0228add
                                    voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));

                                    voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));

                                    voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));

                                    voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));

                                    voBuilder.setCompareVideo5SecondViews(Optional.ofNullable(compareItem.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideo5SecondViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViews(), voBuilder.getCompareVideo5SecondViews(), 4), 100)));

                                    voBuilder.setCompareVideo5SecondViewRate(Optional.ofNullable(compareItem.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideo5SecondViewRateRate(MathUtil.isNullOrZero(voBuilder.getCompareVideo5SecondViewRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideo5SecondViewRate(), voBuilder.getCompareVideo5SecondViewRate(), 4), 100)));

                                    voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));

                                    voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));

                                    voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));

                                    voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));

                                    voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));

                                    voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));

                                    voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));

                                    voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));

                                    voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));

                                    voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));

                                    voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                                    voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                                }
                            }

                            //设置预算、竞价日志
                            setCampaignHomeVoOperationLog(voBuilder, amazonAdOperationLogMap);
                            voBuilder.setTopImpressionShare(Optional.ofNullable(item.getTopImpressionShare()).orElse("-"));
                            if (isVc) {
                                voBuilder.setAcots("-");
                                voBuilder.setCompareAcotsRate("-");
                                voBuilder.setCompareAcots("-");
                                voBuilder.setAsots("-");
                                voBuilder.setCompareAsotsRate("-");
                                voBuilder.setCompareAsots("-");
                            }
                            return voBuilder.build();

                        }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
        }
        builder.setPage(pageBuilder.build());
        return builder;
    }

    @RateLimit(key = "#puid", count = 10)
    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getOldAllCampaignAggregateData(Integer puid, CampaignPageParam param) {
        ShopAuth shopAuth = checkShopAuth(param);
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 查询指标数据 每天指标
        List<AdHomePerformancedto> reportList;
        // 最新的数据，没有环比
//        List<AdHomePerformancedto> latestReports;
        //对比周期汇总数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        List<String> campaignsList = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = new ArrayList<>();  //每日汇总数据
        List<String> campaignIds = new ArrayList<>();

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        boolean isNull = false;  // 查询的数据为空
        //获取不同类型数据 sp、sb、sd
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);

            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            campaignIds = adMarkupTagDao.getCampaignIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.CAMPAIGN.getType(), param.getAdTagIdList(), campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        if (!isNull && StringUtils.isNotBlank(param.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType()) && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> listProductValues = param.getListProductValues();
            //广告组合id不为空
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMsku(puid, param.getShopId(), param.getProductType(), listProductValues, param.getType(), campaignIds);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }

        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder builder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();

        if (isNull) {
            reportList = new ArrayList<>();
//            reportDayList = new ArrayList<>();
        } else {
            reportList = amazonAdCampaignAllReportDao.getReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, false);
            reportList.forEach(e -> e.setViewImpressions(CampaignTypeEnum.sb.getCampaignType().equals(e.getType()) ? e.getViewableImpressions() : e.getViewImpressions()));

            if (param.getIsCompare()) {
                reportListCompare = amazonAdCampaignAllReportDao.getReportByDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), param);
                reportListCompare.forEach(e -> e.setViewImpressions(CampaignTypeEnum.sb.getCampaignType().equals(e.getType()) ? e.getViewableImpressions() : e.getViewImpressions()));
            }
            campaignsList = reportList.stream().map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());
            //查询图表数据
            if (searchChartData) {
                reportDayList = amazonAdCampaignAllReportDao.getSpReportByCampaignIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), campaignsList, param.getType());
            }
        }
        // 获取每日预算汇总字段
        BigDecimal dailyBudgetSum = BigDecimal.ZERO;
        if (!isNull) {
            dailyBudgetSum = amazonAdCampaignAllDao.getSumDailyBudget(puid, param);
        }
        //环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getCampaignAggregateDataVo(reportList, reportListCompare, param.getShopSales(), shopSalesByDateCompare, dailyBudgetSum, isVc);

        builder.setAggregateDataVo(aggregateDataVo);

        if (searchChartData) {
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //获取chart数据
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, param.getShopSales());
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, param.getShopSales());
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, param.getShopSales());
            builder.addAllDay(dayPerformanceVos);
            builder.addAllWeek(weekPerformanceVos);
            builder.addAllMonth(monthPerformanceVos);
        }

        //需要将得到的Id进行过滤，过滤掉近30天没有报告的活动Id
        filterAndSaveIdTemporary(param.getPageSign(), puid, param.getShopId(), shopAuth.getMarketplaceId(), campaignsList);

        return builder.build();
    }

    @RateLimit(key = "#puid", count = 10)
    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getDorisAllCampaignAggregateData(Integer puid, CampaignPageParam param) {
        ShopAuth shopAuth = checkShopAuth(param);
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 查询指标数据 每天指标
        List<AdHomePerformancedto> reportList;
        //对比周期汇总数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        List<String> campaignsList = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<String> campaignIdFilterBySbCreativeType = amazonSbAdsDao.getCampaignIdBySbCreativeType(puid, param.getShopId(), param.getFilterTargetType(), param.getUseAdvanced());
        param.setCreativeIds(campaignIdFilterBySbCreativeType);
        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);
        // 筛选过滤活动id
        boolean isNull = filterCampaignIds(puid, param);
        if (isNull) {
            reportList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            // 直接根据日期分组的逻辑暂时去掉 因为汇总数据时 使用了类型判断  高级查询 暂时去掉
            reportList = amazonAdCampaignAllDorisDao.listTotalAmazonAdCampaignAllGroupCampaignId(puid, param);
            if (param.getIsCompare()) {
                String startDate = param.getStartDate();
                String endDate = param.getEndDate();
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                reportListCompare = amazonAdCampaignAllDorisDao.listTotalAmazonAdCampaignAllGroupCampaignId(puid, param);
                param.setStartDate(startDate);
                param.setEndDate(endDate);
            }
            // TODO 这里如果数量过大 需要多线程查询
            campaignsList = reportList.stream().map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());
            //查询图表数据
            if (searchChartData) {
                // 不使用多线程 直接bitmap
                if (campaignsList.size() > dynamicRefreshConfiguration.getDorisPagePartition()) {
                    log.info("汇总开启多线程查询");
                    reportDayList = getDorisThreadAllCampaignAggregate(puid, param, campaignsList);
                } else {
                    reportDayList = amazonAdCampaignAllDorisDao.listTotalAmazonAdCampaignAllGroupDateById(puid, param, campaignsList);
                }
            }
        }
        filterAndSaveIdTemporary(param.getPageSign(), puid, param.getShopId(), shopAuth.getMarketplaceId(), campaignsList);
        // 获取每日预算汇总字段
        BigDecimal dailyBudgetSum = BigDecimal.ZERO;
        if (!isNull) {
            dailyBudgetSum = amazonAdCampaignAllDao.getSumDailyBudget(puid, param);
        }
        //环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder builder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();
        if (searchChartData) {
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //获取chart数据
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, param.getShopSales());
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, param.getShopSales());
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, param.getShopSales());
            builder.addAllDay(dayPerformanceVos);
            builder.addAllWeek(weekPerformanceVos);
            builder.addAllMonth(monthPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getCampaignAggregateDataVo(reportList, reportListCompare, param.getShopSales(), shopSalesByDateCompare, dailyBudgetSum, isVc);
        builder.setAggregateDataVo(aggregateDataVo);
        return builder.build();
    }

    /**
     * 过滤活动id
     */
    private boolean filterCampaignIds(Integer puid, CampaignPageParam param) {
        List<String> campaignIds = new ArrayList<>();
        boolean isNull = false;  // 查询的数据为空
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            //层级类型-广告活动
            int type = 0;
            List<String> tagIds = param.getAdTagIdList().stream().map(String::valueOf).collect(Collectors.toList());
            List<Integer> shopIds = Collections.singletonList(param.getShopId());
            campaignIds = odsAdManageTagRelationDao.getRelationIdByTagIds(param.getPuid(), tagIds, type, shopIds, campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }
        if (!isNull && StringUtils.isNotBlank(param.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType()) && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> listProductValues = param.getListProductValues();
            //广告组合id不为空
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMsku(puid, param.getShopId(), param.getProductType(), listProductValues, param.getType(), campaignIds);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }
        // 自动化规则筛选活动id集合
        List<Integer> operationTypeList = AdCampaignStrategyTypeEnum.operationTypeList(param.getAdStrategyTypeList());
        if (!isNull && CollectionUtils.isNotEmpty(operationTypeList)) {
            List<String> campaignIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(param.getPuid(), CollectionUtil.newArrayList(param.getShopId()), AutoRuleItemTypeEnum.CAMPAIGN.getName(), operationTypeList, null, null, null, null);
            param.setAutoRuleIds(campaignIdList);
            if (CollectionUtils.isEmpty(campaignIdList) && !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.NONE.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.BUDGET_PRICING.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.STATE_PRICING.getCode()) &&
                    !param.getAdStrategyTypeList().contains(AdCampaignStrategyTypeEnum.SPACE_PRICING.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                isNull = true;
            }
        }
        return isNull;
    }

    /**
     * 活动Id 数量过大 切分
     */
    private List<AdHomePerformancedto> getDorisThreadAllCampaignAggregate(int puid, CampaignPageParam param, List<String> list) {
        ThreadPoolExecutor campaignReportPool = ThreadPoolUtil.getCampaignDorisPagePool();
        List<List<String>> campaignIdPartitionList = Lists.partition(list, dynamicRefreshConfiguration.getDorisPagePartition());
        List<AdHomePerformancedto> reportResult = Collections.synchronizedList(new ArrayList<>());
        try {
            List<CompletableFuture<List<AdHomePerformancedto>>> reportFutures = new ArrayList<>(campaignIdPartitionList.size());
            campaignIdPartitionList.forEach(k -> reportFutures.add(CompletableFuture.supplyAsync(() -> amazonAdCampaignAllDorisDao.listTotalAmazonAdCampaignAllGroupDateById(puid, param, k), campaignReportPool)));
            CompletableFuture<Void> all = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
            CompletableFuture<List<List<AdHomePerformancedto>>> results = all.thenApply(v -> reportFutures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
            results.get().stream().filter(Objects::nonNull).forEach(reportResult::addAll);
        } catch (Exception e) {
            log.error("query ad campaign report partition error", e);
            throw new BizServiceException("查询广告活动报告数据异常，请联系管理员");
        }
        log.info("汇总长度: {}", reportResult.size());
        return reportResult;
    }

    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getAllCampaignAggregateData(Integer puid, CampaignPageParam param) {
        StopWatch sw = new StopWatch();
        ShopAuth shopAuth = checkShopAuth(param);
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        //对比周期汇总数据
        List<AdHomePerformancedto> compareReportList = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        List<AdHomePerformancedto> latestReportList; // 最新汇总数据，不受时间影响
        List<String> campaignIds = new ArrayList<>();
        boolean isNull = false;  // 查询的数据为空
        sw.start("aggregate-filter query id");
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }
        //标签过滤
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            //层级类型-广告活动
            int type = 0;
            List<String> tagIds = param.getAdTagIdList().stream().map(String::valueOf).collect(Collectors.toList());
            List<Integer> shopIds = Collections.singletonList(param.getShopId());
            campaignIds = odsAdManageTagRelationDao.getRelationIdByTagIds(param.getPuid(), tagIds, type, shopIds, campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }
        if (!isNull && StringUtils.isNotBlank(param.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType()) && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> listProductValues = param.getListProductValues();
            //广告组合id不为空
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMsku(puid, param.getShopId(), param.getProductType(), listProductValues, param.getType(), campaignIds);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            } else {
                param.setCampaignIdList(campaignIds);
            }
        }
        sw.stop();
        sw.start("aggregate-query aggregate report data");
        if (isNull) {
            reportDayList = new ArrayList<>();
            latestReportList = new ArrayList<>();
        } else {
            //查询所有广告活动报告数据
            CampaignReportDto reportDto = getAllCampaignAggregate(puid, param);
            log.info("getAllCampaignAggregate result {}", JSON.toJSONString(reportDto));
            if (param.getIsCompare()) {
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                compareReportList = Optional.of(getAllCampaignAggregate(puid, param)).map(CampaignReportDto::getReportList).orElse(Lists.newArrayList());
            }
            reportDayList = Optional.ofNullable(reportDto.getReportList()).orElse(Lists.newArrayList());
            latestReportList = Optional.ofNullable(reportDto.getLatestReportList()).orElse(Lists.newArrayList());
            campaignIds = reportDto.getIdList();
        }
        sw.stop();
        sw.start("过滤有效33天内有效的campaign id并将根据pageSign进行暂存");
        filterAndSaveIdTemporary(param.getPageSign(), puid, param.getShopId(), shopAuth.getMarketplaceId(), campaignIds);
        sw.stop();
        // 获取每日预算汇总字段
        BigDecimal dailyBudgetSum = BigDecimal.ZERO;
        sw.start("aggregate-query budget data");
        if (!isNull && CollectionUtils.isNotEmpty(reportDayList)) {
            dailyBudgetSum = amazonAdCampaignAllDao.getSumDailyBudget(puid, param);
        }
        //环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        sw.stop();
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getCampaignAggregateDataVo(reportDayList, compareReportList, param.getShopSales(), shopSalesByDateCompare, dailyBudgetSum, latestReportList, isVc);
        AdMetricCompareDto adMetricDto = new AdMetricCompareDto();
        adMetricDto.setSumOrderNum(new BigDecimal(aggregateDataVo.getOrderNum().getValue()));
        adMetricDto.setSumAdOrderNum(new BigDecimal(aggregateDataVo.getAdOrderNum().getValue()));
        adMetricDto.setSumAdSale(new BigDecimal(aggregateDataVo.getAdSale()));
        adMetricDto.setSumCost(new BigDecimal(aggregateDataVo.getAdCost()));
        //对比没有广告销量
        adMetricDto.setSumCompareAdOrderNum(new BigDecimal(aggregateDataVo.getCompareAdOrderNum().getValue()));
        adMetricDto.setSumCompareAdSale(new BigDecimal(aggregateDataVo.getCompareAdSale()));
        adMetricDto.setSumCompareCost(new BigDecimal(aggregateDataVo.getCompareAdCost()));
        redisService.set(Constants.CAMPAIGN_PAGE_SUM_METRIC + param.getPageSign(), JSONUtil.objectToJson(adMetricDto), Constants.KEYWORD_PAGE_SUM_METRIC_TIME);
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        //获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, param.getShopSales());
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, param.getShopSales());
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, param.getShopSales());
        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder builder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();
        builder.setAggregateDataVo(aggregateDataVo);
        builder.addAllDay(dayPerformanceVos);
        builder.addAllWeek(weekPerformanceVos);
        builder.addAllMonth(monthPerformanceVos);
        log.info(sw.prettyPrint());
        return builder.build();
    }

    private ShopAuth checkShopAuth(CampaignPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        param.setMarketplaceId(shopAuth.getMarketplaceId());
        return shopAuth;
    }

    private CampaignReportDto getAllCampaignAggregate(int puid, CampaignPageParam param) {
        CampaignReportDto dto = new CampaignReportDto();
        List<String> campaignIds;
        StopWatch sw = new StopWatch();
        sw.start("aggregate-get all report campaign id between date");
        //查询在查询日期范围内所有的campaignId
        campaignIds = amazonAdCampaignAllReportDao.getCampaignIdListByParam(puid, param);
        sw.stop();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return dto;
        }
        //根据有报告数据的广告活动id，查询campaignAll表，并过滤查询条件
        sw.start("aggregate-get all campaign id by search");
        campaignIds = amazonAdCampaignAllDao.getCampaignIdListByParamAndIds(puid, param, campaignIds);
        sw.stop();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return dto;
        }
        //开始多线程分片获取广告活动的聚合报告数据
        sw.start("aggregate-query campaign report data partition");
        log.debug("query campaign id num :{}, partition num: {}", campaignIds.size(), dynamicRefreshConfiguration.getCampaignPagePartition());
        List<AdHomePerformancedto> reportDayList = Lists.newArrayList();
        List<AdHomePerformancedto> latestReports = Lists.newArrayList();
        if (campaignIds.size() > dynamicRefreshConfiguration.getCampaignPagePartition()) {
            List<AdHomePerformancedto> reportResult = Collections.synchronizedList(new ArrayList<>());
            List<AdHomePerformancedto> latestReportResult = Collections.synchronizedList(new ArrayList<>());
            ThreadPoolExecutor campaignReportPool = ThreadPoolUtil.getCampaignPagePool();
            List<List<String>> campaignIdPartitionList = Lists.partition(campaignIds, dynamicRefreshConfiguration.getCampaignPagePartition());
            try {
                List<CompletableFuture<Void>> reportFutures = campaignIdPartitionList.stream().map(l ->
                        CompletableFuture.runAsync(() -> reportResult.addAll(amazonAdCampaignAllReportDao.getReportDataByCampaignIdList(puid, param, l)), campaignReportPool).exceptionally((e) -> {
                            log.error("query aggregate campaign report error", e);
                            return null;
                        })).collect(Collectors.toList());
                reportFutures.addAll(campaignIdPartitionList.stream().map(l ->
                        CompletableFuture.runAsync(() -> latestReportResult.addAll(amazonAdCampaignAllReportDao.listLatestReports(puid, param.getShopId(), l, true)), campaignReportPool).exceptionally((e) -> {
                            log.error("query aggregate campaign latest report error", e);
                            return null;
                        })).collect(Collectors.toList()));
                CompletableFuture<Void> allReport = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
                allReport.join();//等待全部分片查询完成
            } catch (Exception e) {
                log.error("query ad campaign report partition error", e);
                throw new BizServiceException("查询广告活动报告数据异常，请联系管理员");
            }
            reportDayList.addAll(reportResult);
            latestReports.addAll(latestReportResult);
        } else {
            reportDayList = amazonAdCampaignAllReportDao.getReportDataByCampaignIdList(puid, param, campaignIds);
            latestReports = amazonAdCampaignAllReportDao.listLatestReports(puid, param.getShopId(), campaignIds, true);
        }
        sw.stop();
        dto.setReportList(reportDayList);
        dto.setIdList(campaignIds);
        dto.setLatestReportList(latestReports);
        log.info(sw.prettyPrint());
        return dto;
    }

    @Override
    public void getAllCampaignAggregateHour(Integer puid, GetCampaignHourReportRequest request, StreamObserver<GetCampaignHourReportResponse> responseObserver) {

    }

    private String getPageSearchStr(CampaignPageParam param) {
        return param.toString();
    }

    private AdHomeAggregateDataRpcVo getCampaignAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, BigDecimal dailyBudgetSum, boolean isVc) {
        return this.getCampaignAggregateDataVo(rows, rowsCompare, shopSales, shopSalesCompare, dailyBudgetSum, Collections.emptyList(), isVc);
    }

    private AdHomeAggregateDataRpcVo getCampaignAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, BigDecimal dailyBudgetSum, List<AdHomePerformancedto> latestReports, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // sb广告花费
        BigDecimal sbSumAdcost = rows.stream().filter(item -> item.getType().equalsIgnoreCase(AdTypeEnum.sb.getCampaignType())).filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        // sd广告花费
        BigDecimal sdSumAdcost = rows.stream().filter(item -> item.getType().equalsIgnoreCase(AdTypeEnum.sd.getCampaignType())).filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        //可见展示次数
        int sumViewImpressions = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        //sb可见展示次数
        int sbSumViewImpressions = rows.stream().filter(item -> item.getType().equalsIgnoreCase(AdTypeEnum.sb.getCampaignType())).filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        //sd可见展示次数
        int sdSumViewImpressions = rows.stream().filter(item -> item.getType().equalsIgnoreCase(AdTypeEnum.sd.getCampaignType())).filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //sb
        int sbSumSalesNum = rows.stream().filter(item -> item.getType().equalsIgnoreCase("sb")).filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //“品牌新买家”订单量
        int sumOrdersNewToBrand14d = rows.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14d = rows.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(e -> e.getSalesNewToBrand14d()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //“品牌新买家”销量
        int sumUnitsOrderedNewToBrand14d = rows.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getUnitsOrderedNewToBrand14d).sum();
        //每笔订单花费
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //vcpm （sb可见展示次数不为0的广告花费+sd可见展示次数不为0的广告花费)/(sb可见展示次数+sd可见展示次数)*1000
        //BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : (sumAdcost.divide(new BigDecimal(sumViewImpressions), 2, BigDecimal.ROUND_HALF_UP)).multiply(new BigDecimal("1000"));
        //BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumAdcost, new BigDecimal(sumViewImpressions), 6), BigDecimal.valueOf(1000));
        int sumVcpmViewImpressions = sbSumViewImpressions + sdSumViewImpressions;
        BigDecimal sumVcpmAdcost = (sbSumViewImpressions == 0 ? BigDecimal.ZERO : sbSumAdcost).add((sdSumViewImpressions == 0 ? BigDecimal.ZERO : sdSumAdcost));
        BigDecimal sumVcpm = sumVcpmViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumVcpmAdcost, new BigDecimal(sumVcpmViewImpressions), 6), BigDecimal.valueOf(1000));
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量（sb、sd没有广告销量）
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null && item.getType().equalsIgnoreCase(Constants.SP)).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //sd广告销量
        int sdSumSalesNum = rows.stream().filter(item -> item != null && item.getType().equalsIgnoreCase(Constants.SD) && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //其他产品广告销量 = 总广告销量 - sp本产品广告销量（sb、sd没有广告销量） - sb广告销量 - sd广告销量(sd没有本产品广告销量和其他产品广告销量字段)
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum - sbSumSalesNum - sdSumSalesNum;
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14d = sumAdOrderNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14d = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14d.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销量百分比
        BigDecimal sumUnitsOrderedRateNewToBrand14d = sumSalesNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumUnitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumSalesNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”订单转化率
        BigDecimal sumOrdersNewToBrandPercentage14d = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";

        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCart = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCart, 100), sumImpressions, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCart = sumAddToCart == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcost, sumAddToCart, 2);
        // 5秒观看次数
        int sumVideo5SecondViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViews, 100), sumImpressions, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutes = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressions, 100), sumImpressions, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRate = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicks, 100), sumViewImpressions, 2);
        // 品牌搜索次数
        int sumBrandedSearches = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 累计触达用户
        int sumCumulativeReach = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getCumulativeReach).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 平均触达次数
        BigDecimal sumImpressionsFrequencyAverage = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getImpressionsFrequencyAverage).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);


        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);

        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCartCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCartCompare, 100), sumImpressionsCompare, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCartCompare = sumAddToCartCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcostCompare, sumAddToCartCompare, 2);
        // 5秒观看次数
        int sumVideo5SecondViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideo5SecondViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 5秒观看率
        BigDecimal sumVideo5SecondViewRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumVideo5SecondViewsCompare, 100), sumImpressionsCompare, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        //可见展示次数
        int sumViewImpressionsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getViewImpressions).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressionsCompare, 100), sumImpressionsCompare, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRateCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicksCompare, 100), sumViewImpressionsCompare, 2);
        // 品牌搜索次数
        int sumBrandedSearchesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder();
        builder.setAcos(sumAcos.toPlainString());
        builder.setRoas(roas.toPlainString());
        builder.setAcots(acots.toPlainString());
        builder.setAsots(asots.toPlainString());
        builder.setAdCost(sumAdcost.toPlainString());
        builder.setAdCostPerClick(sumAdCostPerClick.toPlainString());
        builder.setAdOrderNum(Int32Value.of(sumAdOrderNum));
        builder.setCvr(sumCVr.toPlainString());
        builder.setCtr(sumCtr.toPlainString());
        builder.setAdSale(sumAdSale.toPlainString());
        builder.setClicks(Int32Value.of(sumClicks));
        builder.setImpressions(Int32Value.of(sumImpressions));

        builder.setViewImpressions(Int32Value.of(sumViewImpressions));
        builder.setCpa(sumCpa.toPlainString());
        builder.setVcpm(sumVcpm.toPlainString());
        builder.setAdSaleNum(Int32Value.of(sumAdSaleNum));
        builder.setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum));
        builder.setAdSales(sumAdSales.toPlainString());
        builder.setAdOtherSales(sumAdOtherSales.toPlainString());
        builder.setOrderNum(Int32Value.of(sumSalesNum));
        builder.setAdSelfSaleNum(Int32Value.of(sumOrderNum));
        builder.setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum));
        builder.setOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14d));
        builder.setOrderRateNewToBrandFTD(sumOrderRateNewToBrand14d.toPlainString());
        builder.setSalesNewToBrandFTD(sumSalesNewToBrand14d.toPlainString());
        builder.setSalesRateNewToBrandFTD((sumSalesRateNewToBrand14d.toPlainString()));
        builder.setUnitsOrderedNewToBrandFTD(Int32Value.of(sumUnitsOrderedNewToBrand14d));
        builder.setUnitsOrderedRateNewToBrandFTD(sumUnitsOrderedRateNewToBrand14d.toPlainString());
        builder.setOrdersNewToBrandPercentageFTD(sumOrdersNewToBrandPercentage14d.toPlainString());
        builder.setAdCostPercentage(sumAdCostPercentage);
        builder.setAdSalePercentage(sumAdSalePercentage);
        builder.setAdOrderNumPercentage(sumAdOrderNumPercentage);
        builder.setOrderNumPercentage(sumOrderNumPercentage);
        if (dailyBudgetSum != null) {
            builder.setDailyBudget(dailyBudgetSum.toPlainString());
        }
        builder.setNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViews));
        builder.setAddToCart(String.valueOf(sumAddToCart));
        builder.setAddToCartRate(String.valueOf(sumAddToCartRate));
        builder.setECPAddToCart(String.valueOf(sumECPAddToCart));
        builder.setVideo5SecondViews(String.valueOf(sumVideo5SecondViews));
        builder.setVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRate));
        builder.setVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViews));
        builder.setVideoMidpointViews(String.valueOf(sumVideoMidpointViews));
        builder.setVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViews));
        builder.setVideoCompleteViews(String.valueOf(sumVideoCompleteViews));
        builder.setVideoUnmutes(String.valueOf(sumVideoUnmutes));
        builder.setViewabilityRate(String.valueOf(sumViewabilityRate));
        builder.setViewClickThroughRate(String.valueOf(sumViewClickThroughRate));
        builder.setBrandedSearches(String.valueOf(sumBrandedSearches));
        builder.setDetailPageViews(String.valueOf(sumDetailPageViews));
        builder.setCumulativeReach(String.valueOf(sumCumulativeReach));
        builder.setImpressionsFrequencyAverage(String.valueOf(sumImpressionsFrequencyAverage));
        builder.setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice));

        //环比数据
        builder.setCompareAcos(sumAcosCompare.toPlainString());
        builder.setCompareRoas(roasCompare.toPlainString());
        builder.setCompareAdCost(sumAdcostCompare.toPlainString());
        builder.setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString());
        builder.setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare));
        builder.setCompareCvr(sumCvrCompare.toPlainString());
        builder.setCompareCtr(sumCtrCompare.toPlainString());
        builder.setCompareAdSale(sumAdSaleCompare.toPlainString());
        builder.setCompareClicks(Int32Value.of(sumClicksCompare));
        builder.setCompareImpressions(Int32Value.of(sumImpressionsCompare));
        builder.setCompareAcots(acotsCompare.toPlainString());
        builder.setCompareAsots(asotsCompare.toPlainString());

        builder.setCompareNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViewsCompare));
        builder.setCompareAddToCart(String.valueOf(sumAddToCartCompare));
        builder.setCompareAddToCartRate(String.valueOf(sumAddToCartRateCompare));
        builder.setCompareECPAddToCart(String.valueOf(sumECPAddToCartCompare));
        builder.setCompareVideo5SecondViews(String.valueOf(sumVideo5SecondViewsCompare));
        builder.setCompareVideo5SecondViewRate(String.valueOf(sumVideo5SecondViewRateCompare));
        builder.setCompareVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViewsCompare));
        builder.setCompareVideoMidpointViews(String.valueOf(sumVideoMidpointViewsCompare));
        builder.setCompareVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViewsCompare));
        builder.setCompareVideoCompleteViews(String.valueOf(sumVideoCompleteViewsCompare));
        builder.setCompareVideoUnmutes(String.valueOf(sumVideoUnmutesCompare));
        builder.setCompareViewImpressions(Int32Value.of(sumViewImpressionsCompare));
        builder.setCompareViewabilityRate(String.valueOf(sumViewabilityRateCompare));
        builder.setCompareViewClickThroughRate(String.valueOf(sumViewClickThroughRateCompare));
        builder.setCompareBrandedSearches(String.valueOf(sumBrandedSearchesCompare));
        builder.setCompareDetailPageViews(String.valueOf(sumDetailPageViewsCompare));
        builder.setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare));


        //环比值
        builder.setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString());


        builder.setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString());

        builder.setCompareNewToBrandDetailPageViewsRate(sumNewToBrandDetailPageViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumNewToBrandDetailPageViews, sumNewToBrandDetailPageViewsCompare, 4), 100)));
        builder.setCompareAddToCartRates(sumAddToCartCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCart, sumAddToCartCompare, 4), 100)));
        builder.setCompareAddToCartRateRate(sumAddToCartRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCartRate, sumAddToCartRateCompare, 4), 100)));
        builder.setCompareECPAddToCartRate(sumECPAddToCartCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumECPAddToCart, sumECPAddToCartCompare, 4), 100)));
        builder.setCompareVideo5SecondViewsRate(sumVideo5SecondViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViews, sumVideo5SecondViewsCompare, 4), 100)));
        builder.setCompareVideo5SecondViewRateRate(sumVideo5SecondViewRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideo5SecondViewRate, sumVideo5SecondViewRateCompare, 4), 100)));
        builder.setCompareVideoFirstQuartileViewsRate(sumVideoFirstQuartileViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoFirstQuartileViews, sumVideoFirstQuartileViewsCompare, 4), 100)));
        builder.setCompareVideoMidpointViewsRate(sumVideoMidpointViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoMidpointViews, sumVideoMidpointViewsCompare, 4), 100)));
        builder.setCompareVideoThirdQuartileViewsRate(sumVideoThirdQuartileViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoThirdQuartileViews, sumVideoThirdQuartileViewsCompare, 4), 100)));
        builder.setCompareVideoCompleteViewsRate(sumVideoCompleteViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoCompleteViews, sumVideoCompleteViewsCompare, 4), 100)));
        builder.setCompareVideoUnmutesRate(sumVideoUnmutesCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoUnmutes, sumVideoUnmutesCompare, 4), 100)));
        builder.setCompareViewImpressionsRate(sumViewImpressionsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewImpressions, sumViewImpressionsCompare, 4), 100)));
        builder.setCompareViewabilityRateRate(sumViewabilityRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewabilityRate, sumViewabilityRateCompare, 4), 100)));
        builder.setCompareViewClickThroughRateRate(sumViewClickThroughRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewClickThroughRate, sumViewClickThroughRateCompare, 4), 100)));
        builder.setCompareBrandedSearchesRate(sumBrandedSearchesCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumBrandedSearches, sumBrandedSearchesCompare, 4), 100)));
        builder.setCompareDetailPageViewsRate(sumDetailPageViewsCompare == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumDetailPageViews, sumDetailPageViewsCompare, 4), 100)));
        builder.setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)));
        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }
        return builder.build();
    }

    @Override
    public List<CampaignPageVo> getExportAllCampaignVoList(Integer puid, CampaignPageParam param) {
//        long t = Instant.now().toEpochMilli();
//        param.setPageNo(1);
//        param.setPageSize(Constants.EXPORT_MAX_SIZE);
//        handlePercentParam(param);
//        List<CampaignPageVo> voList = getDorisCampaignDataExport(puid, param);
//        log.info("广告管理--广告活动接口调用-获取sp数据- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t), JSONUtil.objectToJson(param));
//        if (CollectionUtils.isNotEmpty(voList) && voList.size() > Constants.EXPORT_MAX_SIZE) {
//            return voList.subList(0, Constants.EXPORT_MAX_SIZE);
//        }
//        return voList;
        List<CampaignPageVo> voList = new ArrayList<>();
        long t = Instant.now().toEpochMilli();
        List<CampaignPageVo> spCampaignVoList;
        Page<CampaignPageVo> voPage = new Page<>(1, Constants.EXPORT_MAX_SIZE);
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        spCampaignVoList = getOldCampaignVoList(puid, param, null, true);
        voList.addAll(spCampaignVoList);
        log.info("广告管理--广告活动接口调用-获取sp数据- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli() - t), JSONUtil.objectToJson(param));
        if (CollectionUtils.isNotEmpty(voList) && voList.size() > Constants.EXPORT_MAX_SIZE) {
            voList = voList.subList(0, Constants.EXPORT_MAX_SIZE);
        }
        //填充标签
        this.fillAdTagData(param.getPuid(), param.getShopId(), param, voList);
        return voList;
    }

    private void handlePercentParam(CampaignPageParam campaignPageParam) {
        campaignPageParam.setClickRateMin(campaignPageParam.getClickRateMin() != null ? MathUtil.divide(campaignPageParam.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setClickRateMax(campaignPageParam.getClickRateMax() != null ? MathUtil.divide(campaignPageParam.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMin(campaignPageParam.getAcosMin() != null ? MathUtil.divide(campaignPageParam.getAcosMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMax(campaignPageParam.getAcosMax() != null ? MathUtil.divide(campaignPageParam.getAcosMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMin(campaignPageParam.getSalesConversionRateMin() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMax(campaignPageParam.getSalesConversionRateMax() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMin(campaignPageParam.getAcotsMin() != null ? MathUtil.divide(campaignPageParam.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMax(campaignPageParam.getAcotsMax() != null ? MathUtil.divide(campaignPageParam.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMin(campaignPageParam.getAsotsMin() != null ? MathUtil.divide(campaignPageParam.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMax(campaignPageParam.getAsotsMax() != null ? MathUtil.divide(campaignPageParam.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMin(campaignPageParam.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMax(campaignPageParam.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMin(campaignPageParam.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMax(campaignPageParam.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
    }


    @Override
    public List<CampaignPageVo> getOldAllCampaignVoPageList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport) {

        List<CampaignPageVo> voList;

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                return new ArrayList<>();
            }
            param.setCampaignIdList(campaignIds);
        }

        if (isExport) {  // 导出
            voList = cpcSpCampaignService.getList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            return voList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高级筛选 程序排序
            voList = cpcSpCampaignService.getList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class);
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {  // 走活动管理表分页处理
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = cpcSpCampaignService.getPageList(puid, param, page);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    @Override
    public List<CampaignPageVo> getAllCampaignVoPageList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        param.setMarketplaceId(shopAuth.getMarketplaceId());
        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                return new ArrayList<>();
            }
            param.setCampaignIdList(campaignIds);
        }
        return getAllCampaignVoList(puid, param, voPage, false, true);
    }

    @Override
    public boolean syncCampaign(int puid, Integer shopId, List<SyncCampaignsRequest.CampaignId> campaignIds) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }
        try {
            Map<String, List<SyncCampaignsRequest.CampaignId>> map = campaignIds.stream().collect(Collectors.groupingBy(k -> k.getType() + "-" + k.getSyncType()));
            for (Map.Entry<String, List<SyncCampaignsRequest.CampaignId>> entry : map.entrySet()) {
                String ids = Joiner.on(",").join(entry.getValue().stream().map(SyncCampaignsRequest.CampaignId::getCampaignId).collect(Collectors.toList()));
                String[] key = entry.getKey().split("-");
                String type = key[0];
                String syncType = key[1];
                if (Constants.SP.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSpByShop(shopAuth, ids, null);
                    } else {
                        cpcAdSyncService.syncSpByShopAndId(shopAuth, ids, syncType);
                    }
                } else if (Constants.SD.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSdByShop(shopAuth, ids, null);
                    } else {
                        cpcAdSyncService.syncSdByShopAndId(shopAuth, ids, syncType);
                    }
                } else if (Constants.SB.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSbByShop(shopAuth, ids, null);
                    } else {
                        cpcAdSyncService.syncSbByShopAndId(shopAuth, ids, syncType);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.info("同步广告活动信息异常", e);
        }
        return false;
    }

    @Override
    public Result<String> syncBasicInfo(int puid, Integer shopId, List<SyncCampaignsRequest.CampaignId> campaignIds) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("amazonAdProfile无效");
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return ResultUtil.error("店铺授权无效");
        }
        List<AmazonServingStatusDto> list = new ArrayList<>();
        List<String> stringList = campaignIds.stream().map(SyncCampaignsRequest.CampaignId::getCampaignId).collect(Collectors.toList());
        String redisKey = String.format(RedisConstant.AMAZON_SYNC_REDIS_KEY, MD5Util.getMD5(String.join(",", stringList)));
        Boolean boolRedis = redisService.lock(redisKey, "key", 10L, TimeUnit.SECONDS);
        if (boolRedis != null && !boolRedis) {
            return ResultUtil.error("相同Id十秒内请勿重复请求同步!");
        }
        try {
            Map<String, List<SyncCampaignsRequest.CampaignId>> map = campaignIds.stream().collect(Collectors.groupingBy(k -> k.getType() + "-" + k.getSyncType()));
            for (Map.Entry<String, List<SyncCampaignsRequest.CampaignId>> entry : map.entrySet()) {
                String ids = Joiner.on(",").join(entry.getValue().stream().map(SyncCampaignsRequest.CampaignId::getCampaignId).collect(Collectors.toList()));
                String[] key = entry.getKey().split("-");
                String type = key[0];
                String syncType = key[1];
                if (Constants.SP.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSpByShop(shopAuth, ids, null);
                    } else {
                        list.addAll(cpcAdSyncService.syncSpByShopAndId(shopAuth, ids, syncType));
                    }
                } else if (Constants.SD.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSdByShop(shopAuth, ids, null);
                    } else {
                        list.addAll(cpcAdSyncService.syncSdByShopAndId(shopAuth, ids, syncType));
                    }
                } else if (Constants.SB.equalsIgnoreCase(type)) {
                    if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                        cpcAdSyncService.syncSbByShop(shopAuth, ids, null);
                    } else {
                        list.addAll(cpcAdSyncService.syncSbByShopAndId(shopAuth, ids, syncType));
                    }
                }
            }
        } catch (Exception e) {
            log.info("同步广告活动信息异常", e);
            return ResultUtil.error("同步信息异常");
        }
        return ResultUtil.success("同步成功", JSONUtil.objectToJson(list));
    }

    @Override
    public Result<String> syncMultipleBasicInfo(int puid, List<SyncBasicDto> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return ResultUtil.error("参数无效");
        }
        List<String> stringList = campaignIds.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
        String redisKey = String.format(RedisConstant.AMAZON_SYNC_REDIS_KEY, MD5Util.getMD5(String.join(",", stringList)));
        Boolean boolRedis = redisService.lock(redisKey, "key", 10L, TimeUnit.SECONDS);
        if (boolRedis != null && !boolRedis) {
            return ResultUtil.error("相同Id十秒内请勿重复请求同步!");
        }
        Map<Integer, List<SyncBasicDto>> basicMap = campaignIds.stream().collect(Collectors.groupingBy(SyncBasicDto::getShopId));
        List<AmazonServingStatusDto> list = new ArrayList<>();
        try {
            basicMap.forEach((shopId,value) -> {
                ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
                if (shopAuth == null) {
                    return;
                }
                Map<String, List<SyncBasicDto>> map = value.stream().collect(Collectors.groupingBy(k -> k.getType() + "-" + k.getSyncType()));
                for (Map.Entry<String, List<SyncBasicDto>> entry : map.entrySet()) {
                    String ids = Joiner.on(",").join(entry.getValue().stream().map(SyncBasicDto::getId).collect(Collectors.toList()));
                    String[] key = entry.getKey().split("-");
                    String type = key[0];
                    String syncType = key[1];
                    if (Constants.SP.equalsIgnoreCase(type)) {
                        if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                            cpcAdSyncService.syncSpByShop(shopAuth, ids, null);
                        } else {
                            list.addAll(cpcAdSyncService.syncSpByShopAndId(shopAuth, ids, syncType));
                        }
                    } else if (Constants.SD.equalsIgnoreCase(type)) {
                        if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                            cpcAdSyncService.syncSdByShop(shopAuth, ids, null);
                        } else {
                            list.addAll(cpcAdSyncService.syncSdByShopAndId(shopAuth, ids, syncType));
                        }
                    } else if (Constants.SB.equalsIgnoreCase(type)) {
                        if (Constants.PLACEMENT_ALL.equalsIgnoreCase(syncType)) {
                            cpcAdSyncService.syncSbByShop(shopAuth, ids, null);
                        } else {
                            list.addAll(cpcAdSyncService.syncSbByShopAndId(shopAuth, ids, syncType));
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.info("同步广告活动信息异常", e);
            return ResultUtil.error("同步信息异常");
        }
        return ResultUtil.success("同步成功", JSONUtil.objectToJson(list));
    }

    @Override
    public boolean autoRuleSyncCampaign(int puid, Integer shopId, String type, String campaignId) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }
        try {
            if (Constants.SP.equalsIgnoreCase(type)) {
                cpcCampaignApiService.syncCampaigns(shopAuth, campaignId, false);
            } else if (Constants.SD.equalsIgnoreCase(type)) {
                cpcSdCampaignApiService.syncCampaigns(shopAuth, campaignId, false);
            } else if (Constants.SB.equalsIgnoreCase(type)) {
                cpcSbCampaignApiService.syncCampaignsV4(shopAuth, campaignId, false);
            }
            return true;
        } catch (Exception e) {
            log.info("自动化规则同步广告活动信息异常", e);
        }
        return false;
    }

    @Override
    public boolean autoRuleSyncAdGroup(int puid, Integer shopId, String type, String campaignId, String adGroupId) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }

        try {

            if (Constants.SP.equalsIgnoreCase(type)) {
                cpcAdGroupApiService.syncAdGroups(shopAuth, campaignId, adGroupId);
            } else if (Constants.SD.equalsIgnoreCase(type)) {

            } else if (Constants.SB.equalsIgnoreCase(type)) {

            }

            return true;
        } catch (Exception e) {
            log.info("自动化规则同步广告活动信息异常", e);
        }
        return false;
    }

    @Override
    public boolean autoRuleSyncKeyword(int puid, Integer shopId, String type, String keywordId) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }

        try {

            if (Constants.SP.equalsIgnoreCase(type)) {
                cpcAdSyncService.syncSpKeyword(shopAuth, keywordId);
            } else if (Constants.SD.equalsIgnoreCase(type)) {

            } else if (Constants.SB.equalsIgnoreCase(type)) {
                cpcSbKeywordApiService.syncKeywordsIds(shopAuth, keywordId);
            }

            return true;
        } catch (Exception e) {
            log.info("自动化规则同步关键词信息异常", e);
        }
        return false;
    }

    @Override
    public boolean autoRuleSyncTarget(int puid, Integer shopId, String type, String targetId) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }

        try {
            if (Constants.SP.equalsIgnoreCase(type)) {
                cpcAdSyncService.syncSpTarget(shopAuth, targetId);
                return true;
            }
            if (Constants.SD.equalsIgnoreCase(type)) {
                cpcSdTargetingApiService.syncByTargetId(shopAuth, targetId);
                return true;
            }
            if (Constants.SB.equalsIgnoreCase(type)) {
                AmazonSbAdTargeting amazonSbAdTargeting = amazonSbAdTargetingDao.getByTargetId(puid, shopId, targetId);
                if (amazonSbAdTargeting != null) {
                    cpcSbTargetApiService.syncTargets(shopAuth, null, amazonSbAdTargeting.getAdGroupId(), false);
                }
            }
            return true;
        } catch (Exception e) {
            log.info("自动化规则同步关键词信息异常", e);
        }
        return false;
    }

    @Override
    public AllPlacementDataResponse.AdPlacementHomeVo getAllDorisPlacementData(int puid, PlacementPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        Page<PlacementPageVo> page = getAllDorisPlacementVoList(puid, param, shopAuth);

        AllPlacementDataResponse.AdPlacementHomeVo.Page.Builder pageBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));
        List<PlacementPageVo> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //环比指标
            Map<String, PlacementPageVo> comparePlacementMap = null;
            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                List<String> campaignIds = rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList());
                param.setCampaignIdList(campaignIds);

                // 取店铺销售额
                BigDecimal shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDateCompare == null) {
                    shopSalesByDateCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDateCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setCampaignIdList(rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList()));
                List<PlacementPageVo> placementVoListCompare = getAllDorisPlacementVoList(puid, param, shopAuth).getRows();
                comparePlacementMap = placementVoListCompare.stream().collect(Collectors.toMap(k -> k.getCampaignId()
                                .concat("#").concat(k.getPredicate()),
                        Function.identity(), (a, b) -> a));
            }
            //批量获取日志
            List<String> allCampaignIds = rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList());
            //24小时前的日志
            Date lastDate = DateUtil.addDay(new Date(), -1);
            Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyId(puid, shopAuth.getId(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), null, allCampaignIds, lastDate);

            Map<String, PlacementPageVo> finalComparePlacementMap = comparePlacementMap;
            List<AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.Builder voBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.newBuilder();
                voBuilder.setId(Int64Value.of(item.getId()));
                voBuilder.setCampaignId(item.getCampaignId());
                voBuilder.setType(item.getType());
                if (shopAuth != null) {
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getPredicate())) {
                    voBuilder.setPredicate(item.getPredicate());
                }
                if (StringUtils.isNotBlank(item.getPercentage())) {
                    voBuilder.setPercentage(item.getPercentage());
                } else {
                    voBuilder.setPercentage("0.0");
                }
                if (StringUtils.isNotBlank(item.getStrategy())) {
                    voBuilder.setStrategy(item.getStrategy());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }

                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");


                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalComparePlacementMap)) {
                    String mapKey = item.getCampaignId()
                            .concat("#").concat(item.getPredicate());
                    if (finalComparePlacementMap.containsKey(mapKey)) {
                        PlacementPageVo compareItem = finalComparePlacementMap.get(mapKey);

                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // 广告笔单价
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                    }
                }
                //设置竞价日志
                setPlacementDataOperationLog(voBuilder, amazonAdOperationLogMap);
                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllPlacementDataResponse.AdPlacementHomeVo.newBuilder()
                .setPage(pageBuilder.build()).build();
    }


    /**
     * 广告位首页数据(分页,拆线图,指标数据)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(int puid, PlacementPageParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //获取vo
        List<PlacementPageVo> placementVoList = getAllPlacementVoList(puid, param, shopAuth);


        Page<PlacementPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //分页数 //要排序
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            placementVoList = PageUtil.sort(placementVoList, param.getOrderField(), param.getOrderType());
        }

        PageUtil.getPage(voPage, placementVoList);
        if (voPage.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
            int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
            voPage.setTotalPage(totalPage);
            voPage.setTotalSize(Constants.TOTALSIZELIMIT);
        }


        //处理分页
        AllPlacementDataResponse.AdPlacementHomeVo.Page.Builder pageBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<PlacementPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //环比指标
            Map<String, PlacementPageVo> comparePlacementMap = null;
            if (param.getIsCompare()) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);
                List<String> campaignIds = rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList());
                param.setCampaignIdList(campaignIds);

                // 取店铺销售额
                BigDecimal shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDateCompare == null) {
                    shopSalesByDateCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDateCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setCampaignIdList(rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList()));
                List<PlacementPageVo> placementVoListCompare = getAllPlacementVoList(puid, param, shopAuth);
                comparePlacementMap = placementVoListCompare.stream().collect(Collectors.toMap(k -> k.getCampaignId()
                                .concat("#").concat(k.getPredicate()),
                        Function.identity(), (a, b) -> a));
            }

            //批量获取日志
            List<String> allCampaignIds = rows.stream().map(PlacementPageVo::getCampaignId).collect(Collectors.toList());
            //24小时前的日志
            Date lastDate = DateUtil.addDay(new Date(), -1);
            Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyId(puid, shopAuth.getId(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), null, allCampaignIds, lastDate);


            Map<String, PlacementPageVo> finalComparePlacementMap = comparePlacementMap;
            List<AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.Builder voBuilder = AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.newBuilder();
                voBuilder.setId(Int64Value.of(item.getId()));
                voBuilder.setCampaignId(item.getCampaignId());
                voBuilder.setType(item.getType());
                if (shopAuth != null) {
                    voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getPredicate())) {
                    voBuilder.setPredicate(item.getPredicate());
                }
                if (StringUtils.isNotBlank(item.getPercentage())) {
                    voBuilder.setPercentage(item.getPercentage());
                } else {
                    voBuilder.setPercentage("0.0");
                }
                if (StringUtils.isNotBlank(item.getStrategy())) {
                    voBuilder.setStrategy(item.getStrategy());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignState())) {
                    voBuilder.setCampaignState(item.getCampaignState());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }

                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }

                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");

                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");


                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalComparePlacementMap)) {
                    String mapKey = item.getCampaignId()
                            .concat("#").concat(item.getPredicate());
                    if (finalComparePlacementMap.containsKey(mapKey)) {
                        PlacementPageVo compareItem = finalComparePlacementMap.get(mapKey);

                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        //曝光环比值
                        int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        //点击量环比值
                        int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // 广告笔单价
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                    }
                }


                //设置竞价日志
                setPlacementDataOperationLog(voBuilder, amazonAdOperationLogMap);
                if (isVc) {
                    voBuilder.setAcots("-");
                    voBuilder.setCompareAcotsRate("-");
                    voBuilder.setCompareAcots("-");
                    voBuilder.setAsots("-");
                    voBuilder.setCompareAsotsRate("-");
                    voBuilder.setCompareAsots("-");
                }
                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        AllPlacementDataResponse.AdPlacementHomeVo buildVo = AllPlacementDataResponse.AdPlacementHomeVo.newBuilder()
                .setPage(pageBuilder.build()).build();
        return buildVo;

    }

    /**
     * 广告位指标数据
     *
     * @param puid
     * @param param
     * @return
     */
    @RateLimit(key = "#puid", count = 10)
    @Override
    public AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid, PlacementPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());

        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        boolean isNull = false;  // 查询的数据为空

        List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
        if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
            param.setCampaignIdList(campaignIds);
        } else {
            isNull = true;
        }

        // 增加竞价策略条件过滤广告活动
        if (StringUtils.isNotBlank(param.getStrategyType()) && !isNull) {

            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
                campaignIds = amazonAdCampaignAllDao.getByCampaignIdsAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                        param.getCampaignIdList(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());

                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignIdList(campaignIds);
                } else {
                    isNull = true;
                }
            } else {
                if (CollectionUtils.isNotEmpty(param.getCampaignIds()) && !isNull) {
                    List<String> campaignId = amazonAdCampaignAllDao.getCampaignIdByStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignIds(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {
                        isNull = true;
                    }
                } else if (StringUtils.isNotBlank(param.getCampaignId()) && !isNull) {
                    List<String> campaignId = amazonAdCampaignAllDao.getCampaignIdByStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    if (campaignId == null || campaignId.size() == 0) {
                        isNull = true;
                    }
                } else if (!isNull) {
                    List<String> campaignId = amazonAdCampaignAllDao.getCampaignIdByStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getState(), param.getServingStatus());
                    if (campaignId == null || campaignId.size() == 0) {
                        isNull = true;
                    } else {
                        param.setCampaignIdList(campaignId);
                    }
                }
            }
        }

        List<AdHomePerformancedto> list;
        //周期对比数据
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = null;
        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllPlacementAggregateDataResponse.AdPlacementHomeVo.Builder builder = AllPlacementAggregateDataResponse.AdPlacementHomeVo.newBuilder();

        if (isNull) {
            list = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            list = adCampaignPlacementReportRoutingService.listAllPlacementReportsByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param.getCampaignId(),
                    param.getPredicate(), param, param.getCampaignSite());
            if (param.getIsCompare()) {
                listCompare = adCampaignPlacementReportRoutingService.listAllPlacementReportsByDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), param.getCampaignId(),
                        param.getPredicate(), param, param.getCampaignSite());
            }
            List<String> campaignsList = list.stream().map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());

            //需要将得到的Id进行过滤，过滤掉近30天没有报告的活动Id
            filterAndSaveIdTemporary(param.getPageSign(), puid, param.getShopId(), shopAuth.getMarketplaceId(), campaignsList, param.getCampaignSite());
            //查询图表数据
            if (searchChartData) {
                //每日汇总数据
                reportDayList = adCampaignPlacementReportRoutingService.listAllPlacementReportsByCampaignIdList(puid, param.getShopId(), param.getStartDate(),
                        param.getEndDate(), param.getPredicate(), campaignsList, param.getCampaignSite());
            }
        }

        //填充环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            // 取店铺销售额
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        if (searchChartData) {
            //货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

            //chart图数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总数据
        AdHomeAggregateDataRpcVo placementAggregateDataVo = getPlacementAggregateDataVo(list, listCompare, shopSalesByDate, shopSalesByDateCompare, isVc);

        return builder.setAggregateDataVo(placementAggregateDataVo)
                .build();
    }

    @Override
    public AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllDorisPlacementAggregateData(Integer puid, PlacementPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        boolean isNull = false;  // 查询的数据为空
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
            if (!CollectionUtils.isEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                // 查询符合操作状态运行状态的campaignId
                param.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }
        List<AdHomePerformancedto> list;
        //周期对比数据
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = null;

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllPlacementAggregateDataResponse.AdPlacementHomeVo.Builder builder = AllPlacementAggregateDataResponse.AdPlacementHomeVo.newBuilder();

        if (isNull) {
            list = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            list = odsAmazonAdCampaignPlacementReportDao.listTotalAmazonAdCampaignAllPlacementGroupCampaignId(puid, param);
            List<String> campaignsList = list.stream().map(AdHomePerformancedto::getCampaignId).distinct().collect(Collectors.toList());
            if (param.getIsCompare()) {
                listCompare.add(odsAmazonAdCampaignPlacementReportDao.listTotalPlacementCompareData(puid, param, param.getCompareStartDate(), param.getCompareEndDate()));
            }
            //需要将得到的Id进行过滤，过滤掉近30天没有报告的活动Id
            filterAndSaveIdTemporary(param.getPageSign(), puid, param.getShopId(), shopAuth.getMarketplaceId(), campaignsList);
            if (searchChartData) {
                if (campaignsList.size() > dynamicRefreshConfiguration.getDorisPagePartition()) {
                    reportDayList = getDorisThreadAllPlacementCampaignAggregate(puid, param, campaignsList);
                } else {
                    //每日汇总数据
                    reportDayList = odsAmazonAdCampaignPlacementReportDao.listTotalAmazonAdCampaignAllPlacementGroupDateById(puid,param, campaignsList);
                }
            }
        }

        //填充环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            // 取店铺销售额
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        if (searchChartData) {
            //货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //chart图数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总数据
        AdHomeAggregateDataRpcVo placementAggregateDataVo = getPlacementAggregateDataVo(list, listCompare, shopSalesByDate, shopSalesByDateCompare, isVc);

        return builder
                .setAggregateDataVo(placementAggregateDataVo)
                .build();

    }

    /**
     * 活动Id 数量过大 切分
     */
    private List<AdHomePerformancedto> getDorisThreadAllPlacementCampaignAggregate(int puid, PlacementPageParam param, List<String> list) {
        ThreadPoolExecutor campaignReportPool = ThreadPoolUtil.getAggregatePlacementDorisSyncPool();
        List<List<String>> campaignIdPartitionList = Lists.partition(list, dynamicRefreshConfiguration.getDorisPagePartition());
        List<AdHomePerformancedto> reportResult = Collections.synchronizedList(new ArrayList<>());
        try {
            List<CompletableFuture<List<AdHomePerformancedto>>> reportFutures = new ArrayList<>(campaignIdPartitionList.size());
            campaignIdPartitionList.forEach(k -> reportFutures.add(CompletableFuture.supplyAsync(() -> odsAmazonAdCampaignPlacementReportDao.listTotalAmazonAdCampaignAllPlacementGroupDateById(puid, param, k), campaignReportPool)));
            CompletableFuture<Void> all = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
            CompletableFuture<List<List<AdHomePerformancedto>>> results = all.thenApply(v -> reportFutures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
            results.get().stream().filter(Objects::nonNull).forEach(reportResult::addAll);
        } catch (Exception e) {
            log.error("query ad campaign placement report partition error", e);
            throw new BizServiceException("查询广告位活动报告数据异常，请联系管理员");
        }
        log.info("汇总长度: {}", reportResult.size());
        return reportResult;
    }

    @Override
    public Page<PlacementPageVo> getAllDorisPlacementVoList(Integer puid, PlacementPageParam param, ShopAuth shopAuth) {
        Page<PlacementPageVo> placementPageVos = new Page<>();
        placementPageVos.setPageSize(param.getPageSize());
        placementPageVos.setPageNo(param.getPageNo());
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getState(), param.getServingStatus());
            if (CollectionUtils.isEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                // 查询符合操作状态运行状态的campaignId
                return placementPageVos;
            }
            param.setCampaignIdList(campaignIds);
        }
        // 处理只查询 数量
        if (param.getOnlyCount() != null && param.getOnlyCount()) {
            int count = odsAmazonAdCampaignPlacementReportDao.listAmazonAdCampaignAllPlacementAllCount(puid, param);
            return new Page<>(param.getPageNo(), param.getPageSize(), 0, count);
        }
        StopWatchUtil.start();
        Page<AmazonAdCampaignDorisAllReport> page = odsAmazonAdCampaignPlacementReportDao.listAmazonAdCampaignAllPlacementPage(puid, param);
        log.info("getAllDorisPlacementVoList time = {}", StopWatchUtil.getAndStart());
        if (page != null && CollectionUtils.isNotEmpty(page.getRows())) {
            AdMetricDto adMetricDto = odsAmazonAdCampaignPlacementReportDao.getPlacementSumAdMetric(puid, param);
            log.info("getPlacementSumAdMetric time = {}", StopWatchUtil.getAndStart());
            List<AmazonAdCampaignDorisAllReport> amazonAdCampaignDorisAllReports = page.getRows();
            List<AmazonAdCampaignAll> list = amazonAdCampaignAllDao.listByCampaignIds(puid, shopAuth.getId(),
                    amazonAdCampaignDorisAllReports.stream().map(AmazonAdCampaignDorisAllReport::getCampaignId).distinct().collect(Collectors.toList()));
            Map<String, AmazonAdCampaignAll> map = list.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            List<String> portfolioIds = list.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
            ShopSaleDto finalShopSaleDto = new ShopSaleDto();
            finalShopSaleDto.setSumRange(param.getShopSales());
            List<PlacementPageVo> voList = new ArrayList<>(amazonAdCampaignDorisAllReports.size());
            for (AmazonAdCampaignDorisAllReport amazonAdCampaignDorisAllReport : amazonAdCampaignDorisAllReports) {
                AmazonAdCampaignAll item = map.get(amazonAdCampaignDorisAllReport.getCampaignId());
                PlacementPageVo placementPageVo = new PlacementPageVo();
                placementPageVo.setType(Constants.SP);
                placementPageVo.setCampaignId(item.getCampaignId());
                placementPageVo.setCampaignState(item.getState());
                placementPageVo.setStrategy(item.getStrategy());
                placementPageVo.setCampaignTargetingType(item.getTargetingType());
                placementPageVo.setCampaignName(item.getName());
                placementPageVo.setId(item.getId());
                placementPageVo.setPortfolioId(item.getPortfolioId());
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(item.getPortfolioId())) {
                        placementPageVo.setPortfolioName(portfolioMap.get(item.getPortfolioId()).getName());
                    } else {
                        placementPageVo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    placementPageVo.setPortfolioName("-");
                }
                String placement = amazonAdCampaignDorisAllReport.getCampaignType();

                if (Constants.PLACEMENT_TOP.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTTOP.value());
                } else if (Constants.PLACEMENT_DETAIL_PAGE.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
                } else if (Constants.PLACEMENT_OTHER.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
                }

                Map<String, Adjustment> adjustmentMap = null;
                if (StringUtils.isNotBlank(item.getAdjustments())) {
                    List<Adjustment> adjustments = JSONUtil.jsonToArray(item.getAdjustments(), Adjustment.class);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adjustments)) {
                        adjustmentMap = adjustments.stream().filter(Objects::nonNull).collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
                    }
                }

                if (adjustmentMap != null && adjustmentMap.containsKey(placementPageVo.getPredicate())) {
                    placementPageVo.setPercentage(String.valueOf(adjustmentMap.get(placementPageVo.getPredicate()).getPercentage()));
                }
                ReportBase reportBase = build(amazonAdCampaignDorisAllReport);
                cpcCommService.fillReportDataIntoPageVo(placementPageVo, reportBase, finalShopSaleDto);
                voList.add(placementPageVo);
            }
            filterAdMetricData(voList, adMetricDto);
            placementPageVos.setRows(voList);
            placementPageVos.setTotalPage(page.getTotalPage());
            placementPageVos.setTotalSize(page.getTotalSize());
        }
        StopWatchUtil.remove();
        return placementPageVos;
    }

    private ReportBase build(AmazonAdCampaignDorisAllReport amazonAdCampaignDorisAllReport) {
        ReportBase reportBase = new ReportBase();
        reportBase.setCountDate("");
        reportBase.setCost(amazonAdCampaignDorisAllReport.getCost());
        reportBase.setTotalSales(amazonAdCampaignDorisAllReport.getTotalSales());
        reportBase.setAdSales(amazonAdCampaignDorisAllReport.getAdSales());
        reportBase.setImpressions(amazonAdCampaignDorisAllReport.getImpressions());
        reportBase.setClicks(amazonAdCampaignDorisAllReport.getClicks());
        reportBase.setOrderNum(amazonAdCampaignDorisAllReport.getOrderNum());
        reportBase.setSaleNum(amazonAdCampaignDorisAllReport.getSaleNum());
        reportBase.setAdSaleNum(amazonAdCampaignDorisAllReport.getAdSaleNum());
        reportBase.setAdOrderNum(amazonAdCampaignDorisAllReport.getAdOrderNum());
        reportBase.setSalesNewToBrand14d(BigDecimal.ZERO);
        reportBase.setOrdersNewToBrand14d(0);
        reportBase.setUnitsOrderedNewToBrand14d(0);
        reportBase.setViewImpressions(0);
        reportBase.setType("sp");
        reportBase.setMaxTopIs(BigDecimal.ZERO);
        reportBase.setMinTopIs(BigDecimal.ZERO);
        reportBase.setNewToBrandDetailPageViews(0);
        reportBase.setAddToCart(0);
        reportBase.setAddToCartRate(null);
        reportBase.setVideo5SecondViews(0);
        reportBase.setVideo5SecondViewRate(null);
        reportBase.setVideoFirstQuartileViews(0);
        reportBase.setVideoMidpointViews(0);
        reportBase.setVideoThirdQuartileViews(0);
        reportBase.setVideoCompleteViews(0);
        reportBase.setVideoUnmutes(0);
        reportBase.setViewableImpressions(0);
        reportBase.setViewabilityRate(null);
        reportBase.setViewClickThroughRate(null);
        reportBase.setBrandedSearches(0);
        reportBase.setDetailPageViews(0);
        reportBase.setCumulativeReach(0);
        reportBase.setImpressionsFrequencyAverage(BigDecimal.ZERO);
        return reportBase;
    }

    /**
     * 每个活动固定广告位三条,查询出所有广告位,报告中存在数据则填充数据,否则为0
     *
     * @param puid
     * @param param
     * @param shopAuth
     * @return
     */
    @Override
    public List<PlacementPageVo> getAllPlacementVoList(int puid, PlacementPageParam param, ShopAuth shopAuth) {
        List<PlacementPageVo> placementPageVos = new ArrayList<>();
        List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus());
        if (CollectionUtils.isEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
            // 查询符合操作状态运行状态的campaignId
            return placementPageVos;
        }
        param.setCampaignIdList(campaignIds);
        //先查询报告数据
        long placementTime = System.currentTimeMillis();
        List<AmazonAdCampaignAllReport> poList = adCampaignPlacementReportRoutingService.listSumPlacementReports(puid, param, param.getCampaignSite());
        log.info("==============================查询广告位数据花费时间 {} ==============================", System.currentTimeMillis() - placementTime);


        //日期报告数据 按活动ID分组
        Map<String, List<AmazonAdCampaignAllReport>> reportsMap = null;
        if (CollectionUtils.isNotEmpty(poList)) {
            reportsMap = poList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCampaignId));
        }

        //查询所有活动信息  传活动ID,则查具体的活动ID,否则查询所有活动
        List<AmazonAdCampaignAll> campaigns = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
                if (param.getCampaignIdList().containsAll(param.getCampaignIds())) {
                    List<AmazonAdCampaignAll> campaign = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignIds(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getStatus(), param.getServingStatus());
                    campaigns.addAll(campaign);
                } else if (StringUtils.isBlank(param.getPortfolioId())) {
                    List<AmazonAdCampaignAll> campaign = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            param.getCampaignIds(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getStatus(), param.getServingStatus());
                    campaigns.addAll(campaign);
                }
            } else {
                List<AmazonAdCampaignAll> campaign = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                        param.getCampaignIds(), CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getStatus(), param.getServingStatus());
                campaigns.addAll(campaign);
            }
        } else if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIdList = Arrays.stream(StringUtils.split(param.getCampaignId(), ",")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
                campaignIdList.retainAll(param.getCampaignIdList());
                if (CollectionUtils.isNotEmpty(campaignIdList)) {
                    List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                            campaignIdList, CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getStatus(), param.getServingStatus());
                    campaigns.addAll(campaignList);
                }
            } else {
                List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByCampaignIdAndStrategy(puid, shopAuth.getId(), shopAuth.getMarketplaceId(),
                        campaignIdList, CampaignTypeEnum.sp.getCampaignType(), param.getStrategyType(), param.getStatus(), param.getServingStatus());
                campaigns.addAll(campaignList);
            }
        } else {
            long placementTime2 = System.currentTimeMillis();
            List<AmazonAdCampaignAll> allCampaignsByShop = amazonAdCampaignAllDao.getAllCampaignsByShop(puid, shopAuth.getId(), param.getStrategyType(),
                    param.getCampaignIdList(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus());
            log.info("==============================查询广告位数据花费时间2 {} ==============================", System.currentTimeMillis() - placementTime2);
            campaigns.addAll(allCampaignsByShop);
        }


        List<String> portfolioIds = campaigns.stream().filter(Objects::nonNull).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
        }

        // 取店铺销售额
        ShopSaleDto finalShopSaleDto = new ShopSaleDto();
        BigDecimal shopSales = param.getShopSales();
        finalShopSaleDto.setSumRange(shopSales);

        Map<String, List<AmazonAdCampaignAllReport>> finalReportsMap = reportsMap;
        Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
        campaigns.stream().filter(Objects::nonNull).forEach(item -> {
            Map<String, Adjustment> adjustmentMap = null;
            if (StringUtils.isNotBlank(item.getAdjustments())) {
                List<Adjustment> adjustments = JSONUtil.jsonToArray(item.getAdjustments(), Adjustment.class);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adjustments)) {
                    adjustmentMap = adjustments.stream().filter(Objects::nonNull).collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
                }
            }

            //处理位置搜索
            List<String> placements = null;
            if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(param.getCampaignSite()) && !param.isQuerySublist()) {
                placements = Collections.singletonList(Constants.PLACEMENT_AMAZON_BUSINESS);
            } else {
                if (StringUtils.isNotBlank(param.getPredicate())) {
                    PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
                    placements = Lists.newArrayList(predicateEnum.getContent());
                } else {
                    //不传广告位置条件,则搜索所有位置的
                    placements = Lists.newArrayList(Constants.PLACEMENT_TOP, Constants.PLACEMENT_OTHER, Constants.PLACEMENT_DETAIL_PAGE);
                }
            }

            PlacementPageVo placementPageVo;

            Map<String, AmazonAdCampaignAllReport> placementMap = null;
            //活动对应报告数据
            if (MapUtils.isNotEmpty(finalReportsMap) && finalReportsMap.containsKey(item.getCampaignId())) {
                List<AmazonAdCampaignAllReport> reports = finalReportsMap.get(item.getCampaignId());
                //再按投放位置分组
                if (CollectionUtils.isNotEmpty(reports)) {
                    placementMap = reports.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignType, e -> e));
                }
            }

            for (String placement : placements) {
                placementPageVo = new PlacementPageVo();
                placementPageVo.setType(Constants.SP);
                placementPageVo.setCampaignId(item.getCampaignId());
                placementPageVo.setStrategy(item.getStrategy());
                placementPageVo.setCampaignTargetingType(item.getTargetingType());
                placementPageVo.setCampaignName(item.getName());
                placementPageVo.setCampaignState(item.getState());
                placementPageVo.setId(item.getId());
                placementPageVo.setPortfolioId(item.getPortfolioId());
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    if (finalPortfolioMap != null && finalPortfolioMap.containsKey(item.getPortfolioId())) {
                        placementPageVo.setPortfolioName(finalPortfolioMap.get(item.getPortfolioId()).getName());
                    } else {
                        placementPageVo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    placementPageVo.setPortfolioName("-");
                }

                if (Constants.PLACEMENT_TOP.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTTOP.value());
                } else if (Constants.PLACEMENT_DETAIL_PAGE.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
                } else if (Constants.PLACEMENT_OTHER.equals(placement)) {
                    placementPageVo.setPredicate("placementRestOfSearch");
                } else if (Constants.PLACEMENT_AMAZON_BUSINESS.equals(placement)) {
                    placementPageVo.setPredicate(PredicateEnum.SITEAMAZONBUSINESS.value());
                }

                if (adjustmentMap != null && adjustmentMap.containsKey(placementPageVo.getPredicate())) {
                    placementPageVo.setPercentage(String.valueOf(adjustmentMap.get(placementPageVo.getPredicate()).getPercentage()));
                }
                if (adjustmentMap != null && PredicateEnum.SITEAMAZONBUSINESS.value().equals(param.getCampaignSite())) {
                    Adjustment adjustment = adjustmentMap.get(PredicateEnum.SITEAMAZONBUSINESS.value());
                    String amazonBusinessPercentage = Optional.ofNullable(adjustment).map(Adjustment::getPercentage).map(String::valueOf).orElse("0");
                    placementPageVo.setAmazonBusinessPercentage(amazonBusinessPercentage);
                }

                if (MapUtils.isNotEmpty(placementMap) && placementMap.containsKey(placement)) {
                    // 填充报告数据
                    cpcCommService.fillReportDataIntoPageVo(placementPageVo, build(placementMap.get(placement)), finalShopSaleDto);
                }

                placementPageVos.add(placementPageVo);
            }

        });

        if (param.getUseAdvanced() != null && param.getUseAdvanced() && !param.isQuerySublist()) {  //开启了高级搜索,需要过滤
            cpcCommService.filterPlacementAdVanceData(placementPageVos, param);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(placementPageVos, adMetricDto);
        // 填充占比
        filterAdMetricData(placementPageVos, adMetricDto);

        log.info("==============================查询广告位花费时间 {} ==============================", System.currentTimeMillis() - placementTime);
        return placementPageVos;
    }

    private ReportBase build(AmazonAdCampaignAllReport amazonAdCampaignAllReport) {
        ReportBase reportBase = new ReportBase();
        reportBase.setCountDate("");
        reportBase.setCost(amazonAdCampaignAllReport.getCost());
        reportBase.setTotalSales(amazonAdCampaignAllReport.getTotalSales());
        reportBase.setAdSales(amazonAdCampaignAllReport.getAdSales());
        reportBase.setImpressions(amazonAdCampaignAllReport.getImpressions());
        reportBase.setClicks(amazonAdCampaignAllReport.getClicks());
        reportBase.setOrderNum(amazonAdCampaignAllReport.getOrderNum());
        reportBase.setSaleNum(amazonAdCampaignAllReport.getSaleNum());
        reportBase.setAdSaleNum(amazonAdCampaignAllReport.getAdSaleNum());
        reportBase.setAdOrderNum(amazonAdCampaignAllReport.getAdOrderNum());
        reportBase.setSalesNewToBrand14d(BigDecimal.ZERO);
        reportBase.setOrdersNewToBrand14d(0);
        reportBase.setUnitsOrderedNewToBrand14d(0);
        reportBase.setViewImpressions(0);
        reportBase.setType("sp");
        reportBase.setMaxTopIs(BigDecimal.ZERO);
        reportBase.setMinTopIs(BigDecimal.ZERO);
        reportBase.setNewToBrandDetailPageViews(0);
        reportBase.setAddToCart(0);
        reportBase.setAddToCartRate(null);
        reportBase.setVideo5SecondViews(0);
        reportBase.setVideo5SecondViewRate(null);
        reportBase.setVideoFirstQuartileViews(0);
        reportBase.setVideoMidpointViews(0);
        reportBase.setVideoThirdQuartileViews(0);
        reportBase.setVideoCompleteViews(0);
        reportBase.setVideoUnmutes(0);
        reportBase.setViewableImpressions(0);
        reportBase.setViewabilityRate(null);
        reportBase.setViewClickThroughRate(null);
        reportBase.setBrandedSearches(0);
        reportBase.setDetailPageViews(0);
        reportBase.setCumulativeReach(0);
        reportBase.setImpressionsFrequencyAverage(BigDecimal.ZERO);
        return reportBase;
    }

    private void fillAdTagData(Integer puid, Integer shopId, CampaignPageParam param, List<CampaignPageVo> rows) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(rows)) {
            return;
        }
        List<String> campaignIds = rows.stream().map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
        //根据广告活动id反查标签
        int type = AdManageTagTypeEnum.CAMPAIGN.getCode();
        //权限过滤，根据uid去查询出有权限的groupId
        List<Long> groupId;
        if (param.getIsAdmin() != null && param.getIsAdmin()) {
            groupId = adManageTagDaoGroupUser.listGroupIdByPuid(puid, type);
        } else {
            groupId = adManageTagDaoGroupUser.getTagGroup(puid, param.getUid(), type);
        }
        //根据groupId去过滤出标签
        List<AdMarkupTagVo> relationVos = adManageTagRelationService.getTagIdByRelationId(puid, type, campaignIds, groupId);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> tagIdList = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagIdList)) {
            return;
        }
        //根据标签Ids获取相关信息
        List<AdManageTag> tagList = adManageTagDao.getListByIdList(puid, type, tagIdList);
        if (CollectionUtils.isEmpty(tagList)) {
            return;
        }
        Map<Long, AdManageTag> adTagMap = tagList.stream().collect(Collectors.toMap(AdManageTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (CampaignPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getCampaignId());
            if (adMarkupTagVo == null) {
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if (tagIds == null) {
                continue;
            }
            List<AdManageTag> adManageTagList = tagIds.stream().map(adTagMap::get).collect(Collectors.toList());
            List<AdTag> adTagList = new ArrayList<>();
            adManageTagList.stream().filter(Objects::nonNull).forEach(i -> {
                AdTag adTag = new AdTag();
                BeanUtils.copyProperties(i, adTag);
                adTagList.add(adTag);
            });
            vo.setAdTags(adTagList);
        }
    }

    // 获取指标总数据
    private void filterSumMetricData(List<PlacementPageVo> voList, AdMetricDto adMetricDto) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> new BigDecimal(item.getAdCost())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> new BigDecimal(item.getAdSale())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(PlacementPageVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(PlacementPageVo::getOrderNum).sum()));
    }


    // 填充指标占比数据
    private void filterAdMetricData(List<PlacementPageVo> voList, AdMetricDto adMetricDto) {
        for (PlacementPageVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage("0");
                vo.setAdSalePercentage("0");
                vo.setAdOrderNumPercentage("0");
                vo.setOrderNumPercentage("0");
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, PlacementPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }


    private AdHomeAggregateDataRpcVo getPlacementAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //CPA
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);

        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //广告销量
        int sumSalesNumCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品订单量
        int sumAdSaleNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNumCompare = sumAdOrderNumCompare - sumAdSaleNumCompare;
        //本广告产品销售额
        BigDecimal sumAdSalesCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSales() != null).map(AdHomePerformancedto::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSalesCompare = sumAdSaleCompare.subtract(sumAdSalesCompare);
        //本广告产品销量
        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNumCompare = sumSalesNumCompare - sumOrderNumCompare;


        AdHomeAggregateDataRpcVo.Builder voBuilder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.toPlainString())
                .setRoas(roas.toPlainString())
                .setAcots(acots.toPlainString())
                .setAsots(asots.toPlainString())
                .setAdCost(sumAdcost.toPlainString())
                .setAdCostPerClick(sumAdCostPerClick.toPlainString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.toPlainString())
                .setCtr(sumCtr.toPlainString())
                .setAdSale(sumAdSale.toPlainString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setAdSaleNum(Int32Value.of(sumAdSaleNum))
                .setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum))
                .setAdSales(sumAdSales.toPlainString())
                .setAdOtherSales(sumAdOtherSales.toPlainString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .setAdSelfSaleNum(Int32Value.of(sumOrderNum))
                .setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum))
                .setCpa(sumCpa.toPlainString())
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setOrderNumPercentage(sumOrderNumPercentage)
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrderNum(Int32Value.of(sumSalesNumCompare))
                .setCompareAdSaleNum(Int32Value.of(sumAdSaleNumCompare))
                .setCompareAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNumCompare))
                .setCompareAdSales(sumAdSalesCompare.toPlainString())
                .setCompareAdOtherSales(sumAdOtherSalesCompare.toPlainString())
                .setCompareAdSelfSaleNum(Int32Value.of(sumOrderNumCompare))
                .setCompareAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNumCompare))

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))
                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderNumRate(sumSalesNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumSalesNum, sumSalesNumCompare, 4), 100)))
                .setCompareAdSaleNumRate(sumAdSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdSaleNum, sumAdSaleNumCompare, 4), 100)))
                .setCompareAdOtherOrderNumRate(sumAdOtherOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherOrderNum, sumAdOtherOrderNumCompare, 4), 100)))
                .setCompareAdSalesRate(sumAdSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSales.subtract(sumAdSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdOtherSalesRate(sumAdOtherSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdOtherSales.subtract(sumAdOtherSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdOtherSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdSelfSaleNumRate(sumOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrderNum, sumOrderNumCompare, 4), 100)))
                .setCompareAdOtherSaleNumRate(sumAdOtherSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherSaleNum, sumAdOtherSaleNumCompare, 4), 100)));
        if (isVc) {
            voBuilder.setAcots("-");
            voBuilder.setCompareAcotsRate("-");
            voBuilder.setCompareAcots("-");
            voBuilder.setAsots("-");
            voBuilder.setCompareAsotsRate("-");
            voBuilder.setCompareAsots("-");
        }
        return voBuilder.build();
    }


    @Override
    public List<CampaignPageVo> getOldCampaignVoList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isExport) {
        List<CampaignPageVo> voList = new ArrayList<>();
        //查询广告组合及标签对应的广告活动id
        List<String> campaignIds = getQueryIdsByPageFilter(puid, param);
        if (Objects.nonNull(campaignIds) && campaignIds.isEmpty()) {
            return Lists.newArrayList();
        }

        if (isExport) {  // 导出
            voList = amazonAdCampaignAllService.getList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            return voList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高级筛选 程序排序
            voList = amazonAdCampaignAllService.getList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class);
                if (isSorted) {
                    //TODO 根据名称排序
                    if ("name".equals(param.getOrderField())) {
                        PageUtil.sortedByOrderField(voList, param.getOrderField(), param.getOrderType());
                    } else {
                        voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    }
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {  // 走活动管理表分页处理
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = cpcSpCampaignService.getPageList(puid, param, page);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }
        return voList;
    }

    private List<CampaignPageVo> getAllCampaignVoList(Integer puid, CampaignPageParam param, Page<CampaignPageVo> voPage, boolean isCompare, boolean isExport) {
        StopWatch sw = new StopWatch("getAllCampaignVoList");
        List<CampaignPageVo> voList = new ArrayList<>();
        sw.start("获取所有广告活动类型列表分页数据");
        //查询广告组合及标签对应的广告活动id
        List<String> campaignIds = getQueryIdsByPageFilter(puid, param);
        if (Objects.nonNull(campaignIds) && campaignIds.isEmpty()) {
            return Lists.newArrayList();
        }
        CampaignPageMetricVo<CampaignInfoPageVo> campaignPageMetricVo;
        AdCampaignDefaultOrderEnum orderField = AdCampaignDefaultOrderEnum.getAdCampaignDefaultOrderEnumByKey(param.getOrderField());
        //列表按指定的报告指标进行排序，可进行高级筛选
        if (StringUtils.isNotBlank(param.getOrderType()) && StringUtils.isNotBlank(param.getOrderField()) && Objects.isNull(orderField)) {
            campaignPageMetricVo = getAllCampaignPageOrderByReport(puid, param, isExport);
        } else {
            if (isUseReportDataAdvanced(param)) {
                //有高级筛选，默认排序
                campaignPageMetricVo = getAllCampaignPageFilterAndOrderByCampaign(puid, param, isExport);
            } else {
                //无高级筛选,默认排序
                campaignPageMetricVo = getAllCampaignPageNoFilterAndOrder(puid, param, isExport);
            }
        }
        if (Objects.nonNull(campaignPageMetricVo.getPage()) && CollectionUtils.isEmpty(campaignPageMetricVo.getPage().getRows())) {
            return voList;
        }
        List<CampaignInfoPageVo> poList = campaignPageMetricVo.getPage().getRows();
        List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(CampaignInfoPageVo::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
        }

        Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));//判断user表中id是否存在
        Page<CampaignInfoPageVo> page = campaignPageMetricVo.getPage();
        AdMetricCompareDto adMetricCompareDto = campaignPageMetricVo.getAdMetricDto();
        AdMetricDto adMetricDto = new AdMetricDto();
        if (Objects.nonNull(adMetricCompareDto)) {
            if (isCompare) {
                adMetricDto.setSumCost(adMetricCompareDto.getSumCompareCost());
                adMetricDto.setSumAdSale(adMetricCompareDto.getSumCompareAdSale());
                adMetricDto.setSumAdOrderNum(adMetricCompareDto.getSumCompareAdOrderNum());
                adMetricDto.setSumOrderNum(adMetricCompareDto.getSumCompareOrderNum());
            } else {
                adMetricDto.setSumCost(adMetricCompareDto.getSumCost());
                adMetricDto.setSumAdSale(adMetricCompareDto.getSumAdSale());
                adMetricDto.setSumAdOrderNum(adMetricCompareDto.getSumAdOrderNum());
                adMetricDto.setSumOrderNum(adMetricCompareDto.getSumOrderNum());
            }
        } else {
            adMetricDto = null;
        }
        sw.stop();
        voPage.setTotalSize(page.getTotalSize());
        voPage.setTotalPage(page.getTotalPage());
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            CampaignPageVo vo;
            sw.start("拼装数据");
            for (CampaignInfoPageVo campaignInfoPageVo : page.getRows()) {
                vo = new CampaignPageVo();
                //填充广告活动数据
                this.fillCampaignPageVo(campaignInfoPageVo, vo);
                //填充关键词报告数据
                cpcCommService.fillReportDataIntoPageVo(vo, campaignInfoPageVo, new ShopSaleDto(param.getShopSales()));
                //填充占比
                cpcCommService.filterMetricData(vo, adMetricDto);

                if (StringUtils.isNotBlank(campaignInfoPageVo.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(campaignInfoPageVo.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(campaignInfoPageVo.getPortfolioId()).getName());
                        vo.setIsHidden(portfolioMap.get(campaignInfoPageVo.getPortfolioId()).getIsHidden());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }
                // 创建人
                if (userMap.containsKey(campaignInfoPageVo.getCreateId())) {
                    User user = userMap.get(campaignInfoPageVo.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                //兼容本产品广告销量（sd无广告销量）
                if (CampaignTypeEnum.sb.getCampaignType().equals(vo.getType()) || CampaignTypeEnum.sd.getCampaignType().equals(vo.getType())) {
                    vo.setAdSelfSaleNum(0);
                    vo.setAdOtherSaleNum(0);
                }
                voList.add(vo);
            }
            sw.stop();
        }
        voPage.setRows(voList);
        log.info(sw.prettyPrint());
        return voList;
    }

    @Override
    public boolean syncShop(int puid, Integer shopId, String type) {

        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return false;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return false;
        }

        try {
            if (Constants.SP.equalsIgnoreCase(type)) {
                cpcAdSyncService.syncSpByShop(shopAuth, null, null);
            } else if (Constants.SD.equalsIgnoreCase(type)) {
                cpcAdSyncService.syncSdByShop(shopAuth, null, null);
            } else if (Constants.SB.equalsIgnoreCase(type)) {
                cpcAdSyncService.syncSbByShop(shopAuth, null, null);
            }
            return true;
        } catch (Exception e) {
            log.info("同步广告活动信息异常", e);
        }
        return false;

    }


    /**
     * 微信接口改动很少且很多字段不需要，单独拎出来做个方法避免改动时两边都需要测试
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllCampaignDataResponse.CampaignHomeVo getAllWxCampaignData(Integer puid, CampaignPageParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理wx {} --广告活动接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();
        Page<CampaignPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        log.info("广告管理wx{}--广告活动接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        log.info("广告管理wx{}--广告活动接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        param.setShopSales(shopSalesByDate);
        getOldCampaignVoList(puid, param, voPage, false);

        AllCampaignDataResponse.CampaignHomeVo.Builder builder = AllCampaignDataResponse.CampaignHomeVo.newBuilder();
        AllCampaignDataResponse.CampaignHomeVo.Page.Builder pageBuilder = AllCampaignDataResponse.CampaignHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<CampaignPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //批量查询预算使用量
            List<String> campaignIds = rows.stream().filter(item -> Arrays.asList("enabled", "paused").contains(item.getState()))
                    .map(CampaignPageVo::getCampaignId).distinct().collect(Collectors.toList());
            LocalDate siteDate = LocalDate.now(Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone().toZoneId());
            List<AmazonAdBudgetUsage> budgetUsages = amazonAdBudgetUsageService
                    .listByCampaignIds(puid, shopAuth.getId(), "CAMPAIGN", siteDate, campaignIds);
            Map<String, Map<String, List<AmazonAdBudgetUsage>>> budgetUsageMap = budgetUsages.stream()
                    .collect(Collectors.groupingBy(AmazonAdBudgetUsage::getAdvertisingProductType,
                            Collectors.groupingBy(AmazonAdBudgetUsage::getBudgetScopeId)));

            List<AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo> rpcVos = rows.stream()
                    .filter(Objects::nonNull).map(item -> {
                        AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder =
                                AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.newBuilder();
                        voBuilder.setId(Int64Value.of(item.getId()));
                        voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        voBuilder.setCampaignId(item.getCampaignId());

                        if (StringUtils.isNotBlank(item.getPortfolioId())) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (StringUtils.isNotBlank(item.getPortfolioName())) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }
                        if (StringUtils.isNotBlank(item.getName())) {
                            voBuilder.setName(item.getName());
                        }
                        if (StringUtils.isNotBlank(item.getState())) {
                            voBuilder.setState(item.getState());
                        }
                        if (StringUtils.isNotBlank(item.getServingStatus())) {
                            voBuilder.setServingStatus(item.getServingStatus());
                        }
                        if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                            voBuilder.setServingStatusDec(item.getServingStatusDec());
                        }
                        if (item.getOutOfBudget() != null) {
                            voBuilder.setOutOfBudget(BoolValue.of(item.getOutOfBudget()));
                        }
                        if (StringUtils.isNotBlank(item.getDailyBudget())) {
                            voBuilder.setDailyBudget(item.getDailyBudget());
                        }
                        if (StringUtils.isNotBlank(item.getBudgetType())) {
                            voBuilder.setBudgetType(item.getBudgetType());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignType())) {
                            voBuilder.setCampaignType(item.getCampaignType());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                            voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingType())) {
                            voBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getStrategy())) {
                            voBuilder.setStrategy(item.getStrategy());
                        }
                        if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
                            voBuilder.setPlacementProductPage(item.getPlacementProductPage());
                        } else {
                            voBuilder.setPlacementProductPage("0");
                        }
                        if (StringUtils.isNotBlank(item.getPlacementTop())) {
                            voBuilder.setPlacementTop(item.getPlacementTop());
                        } else {
                            voBuilder.setPlacementTop("0");
                        }
                        if (StringUtils.isNotBlank(item.getCreator())) {
                            voBuilder.setCreator(item.getCreator());
                        }
                        if (StringUtils.isNotBlank(item.getStartDate())) {
                            voBuilder.setStartDate(item.getStartDate());
                        }
                        if (StringUtils.isNotBlank(item.getEndDate())) {
                            voBuilder.setEndDate(item.getEndDate());
                        }
                        if (StringUtils.isNotBlank(item.getType())) {
                            voBuilder.setType(item.getType());
                        }
                        if (item.getUpdateTime() != null) {
                            voBuilder.setUpdateTime(DateUtil.dateToStrWithFormat(item.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
                        }
                        if (item.getCreateTime() != null) {
                            voBuilder.setCreateTime(DateUtil.dateToStrWithFormat(item.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
                        }
                        if (item.getTargetType() != null) {
                            voBuilder.setTargetType(item.getTargetType());
                        }
                        if (item.getAdFormat() != null) {
                            voBuilder.setAdFormat(item.getAdFormat());
                        }

                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        //广告订单量
                        voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                        voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                        voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                        voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                        voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                        voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                        voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                        voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                        voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                        //广告销售额
                        voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");


                        //分时调价设置
                        if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                            voBuilder.setMarketplaceId(item.getMarketplaceId());
                        }
                        if (item.getIsBudgetPricing() != null) {
                            voBuilder.setIsBudgetPricing(item.getIsBudgetPricing());
                        }
                        if (item.getPricingBudgetState() != null) {
                            voBuilder.setPricingBudgetState(item.getPricingBudgetState());
                        }
                        if (item.getIsSpacePricing() != null) {
                            voBuilder.setIsSpacePricing(item.getIsSpacePricing());
                        }
                        if (item.getPricingSpaceState() != null) {
                            voBuilder.setPricingSpaceState(item.getPricingSpaceState());
                        }

                        if (item.getCostType() != null) {
                            voBuilder.setCostType(item.getCostType());
                        }
                        if (item.getServingStatusName() != null) {
                            voBuilder.setServingStatusName(item.getServingStatusName());
                        }
                        if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType()) || CampaignTypeEnum.sb.getCampaignType().equals(item.getType())) {
                            voBuilder.setCostType("cpc");
                        }
                        voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                        voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                        voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                        //本广告产品订单量
                        voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                        //其他广告产品订单量
                        voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                        //本广告产品销售额
                        voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                        //其他广告产品销售额
                        voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                        //广告销量
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        //本广告产品销量
                        voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                        //其他广告产品销量
                        voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                        voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                        voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                        voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                        voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                        voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                        // 花费占比
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        // 销售额占比
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        // 订单量占比
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        // 销量占比
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        //预算使用量
                        Map<String, List<AmazonAdBudgetUsage>> adTypeMap = budgetUsageMap.get(item.getType());
                        AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder budgetUsageBuilder =
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.newBuilder();
                        if (MapUtils.isNotEmpty(adTypeMap)) {
                            List<AmazonAdBudgetUsage> amazonAdBudgetUsages = adTypeMap.get(item.getCampaignId());

                            if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
                                List<AmazonAdBudgetUsage> sortedBudgetUsageList = amazonAdBudgetUsages.stream()
                                        .sorted(Comparator.comparing(AmazonAdBudgetUsage::getUsageUpdatedSiteDate))
                                        .collect(Collectors.toList());
                                //最后更新记录
                                AmazonAdBudgetUsage amazonAdBudgetUsage = sortedBudgetUsageList.get(sortedBudgetUsageList.size() - 1);
                                budgetUsageBuilder.setIsNoData(false);
                                budgetUsageBuilder.setCurrentBudget(amazonAdBudgetUsage.getBudget().doubleValue());
                                budgetUsageBuilder.setLastUpdateAt(amazonAdBudgetUsage.getUsageUpdatedSiteTime()
                                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                budgetUsageBuilder.setPercent(amazonAdBudgetUsage.getBudgetUsagePercentage().doubleValue());
                                //变化记录
                                List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item> budgetUsageItems =
                                        sortedBudgetUsageList.stream().map(o -> {
                                            AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.Builder itemBuilder =
                                                    AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.newBuilder();
                                            itemBuilder.setCurrentBudget(o.getBudget().doubleValue());
                                            itemBuilder.setPercent(o.getBudgetUsagePercentage().doubleValue());
                                            itemBuilder.setUpdateAt(o.getUsageUpdatedSiteTime()
                                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                                            return itemBuilder.build();
                                        }).collect(Collectors.toList());
                                budgetUsageBuilder.addAllItem(budgetUsageItems);

                            } else {
                                budgetUsageBuilder.setIsNoData(true);
                            }
                        } else {
                            budgetUsageBuilder.setIsNoData(true);
                        }

                        voBuilder.setBudgetUsage(budgetUsageBuilder.build());

                        if (isVc) {
                            voBuilder.setAcots("-");
                            voBuilder.setCompareAcotsRate("-");
                            voBuilder.setCompareAcots("-");
                            voBuilder.setAsots("-");
                            voBuilder.setCompareAsotsRate("-");
                            voBuilder.setCompareAsots("-");
                        }
                        return voBuilder.build();

                    }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }


        builder.setPage(pageBuilder.build());
        log.info("广告管理{}--广告活动接口调用-排序,分页,汇总,日月周数据统计- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);

        return builder.build();
    }

    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getOldAllWxCampaignAggregateData(Integer puid, CampaignPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);


        //先取出所有数据包括对比周期
        LocalDate startDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        LocalDate compareStartDate = startDate.minus(between).minusDays(1L);
        LocalDate compareEndDate = startDate.minusDays(1L);
        List<AdHomePerformancedto> reportListCompare;
        // 查询指标数据 每天指标
        List<AdHomePerformancedto> reportList;
        List<String> campaignsList;
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        List<String> campaignIds;
        boolean isNull = false;  // 查询的数据为空
        //获取不同类型数据 sp、sb、sd
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {

            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);

            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            }
            param.setCampaignIdList(campaignIds);
        }
        if (isNull) {
            reportList = new ArrayList<>();
            reportListCompare = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {

            reportList = amazonAdCampaignAllReportDao.getReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
            reportListCompare = amazonAdCampaignAllReportDao.getReportByDate(puid, param.getShopId(), compareStartDate.format(DateTimeFormatter.BASIC_ISO_DATE), compareEndDate.format(DateTimeFormatter.BASIC_ISO_DATE), param);
            campaignsList = reportList.stream().map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());
            reportDayList = amazonAdCampaignAllReportDao.getSpReportByCampaignIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), campaignsList, param.getType());

        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getCampaignAggregateDataVo(reportList, new ArrayList<>(), shopSalesByDate, BigDecimal.ZERO, null, isVc);

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataCompareVo = getCampaignAggregateDataVo(reportListCompare, new ArrayList<>(), shopSalesByDate, BigDecimal.ZERO, null, isVc);

        AdHomeAggregateDataRpcVo campaignAggregateDataChainVo = getCampaignAggregateDataChainVo(aggregateDataVo, aggregateDataCompareVo);
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
        //List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
        //List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);

        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder builder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();
        builder.setAggregateDataVo(campaignAggregateDataChainVo);
        builder.addAllDay(dayPerformanceVos);
        //builder.addAllWeek(weekPerformanceVos);
        //builder.addAllMonth(monthPerformanceVos);
        return builder.build();
    }

    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getAllWxCampaignAggregateData(Integer puid, CampaignPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);


        //先取出所有数据包括对比周期
        LocalDate startDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        LocalDate compareStartDate = startDate.minus(between).minusDays(1L);
        LocalDate compareEndDate = startDate.minusDays(1L);

        List<AdHomePerformancedto> reportListCompare;
        // 查询指标数据 每天指标
        List<AdHomePerformancedto> reportList;
        List<String> campaignsList;
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        List<String> campaignIds;
        boolean isNull = false;  // 查询的数据为空
        //获取不同类型数据 sp、sb、sd
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {

            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);

            if (CollectionUtils.isEmpty(campaignIds)) {  // 为空直接返回结果
                isNull = true;
            }
            param.setCampaignIdList(campaignIds);
        }
        if (isNull) {
            reportList = new ArrayList<>();
            reportListCompare = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            CampaignReportDto reportResult = getAllCampaignAggregate(puid, param);

            //获取对比数据
            param.setStartDate(compareStartDate.format(DateTimeFormatter.BASIC_ISO_DATE));
            param.setEndDate(compareEndDate.format(DateTimeFormatter.BASIC_ISO_DATE));
            CampaignReportDto compareReportResult = getAllCampaignAggregate(puid, param);
            reportList = reportResult.getReportList();
            reportListCompare = compareReportResult.getReportList();
            campaignsList = reportResult.getIdList();
            reportDayList = amazonAdCampaignAllReportDao.getSpReportByCampaignIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), campaignsList, param.getType());
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getCampaignAggregateDataVo(reportList, new ArrayList<>(), shopSalesByDate, BigDecimal.ZERO, null, isVc);

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataCompareVo = getCampaignAggregateDataVo(reportListCompare, new ArrayList<>(), shopSalesByDate, BigDecimal.ZERO, null, isVc);

        AdHomeAggregateDataRpcVo campaignAggregateDataChainVo = getCampaignAggregateDataChainVo(aggregateDataVo, aggregateDataCompareVo);
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);

        AllCampaignAggregateDataResponse.CampaignHomeVo.Builder builder = AllCampaignAggregateDataResponse.CampaignHomeVo.newBuilder();
        builder.setAggregateDataVo(campaignAggregateDataChainVo);
        builder.addAllDay(dayPerformanceVos);
        return builder.build();
    }

    private AdHomeAggregateDataRpcVo getCampaignAggregateDataChainVo(AdHomeAggregateDataRpcVo groupAggregateDataVo, AdHomeAggregateDataRpcVo groupAggregateDataVoCompare) {
        AdHomeAggregateDataRpcVo.Builder builder = groupAggregateDataVo.toBuilder();
        if (groupAggregateDataVoCompare == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(groupAggregateDataVoCompare.getAdCost())
                .setClicksCompare(groupAggregateDataVoCompare.getClicks().getValue())
                .setCtrCompare(groupAggregateDataVoCompare.getCtr())
                .setCvrCompare(groupAggregateDataVoCompare.getCvr())
                .setImpressionsCompare(groupAggregateDataVoCompare.getImpressions().getValue())
                .setAdCostCompare(groupAggregateDataVoCompare.getAdCost())
                .setAdSaleCompare(groupAggregateDataVoCompare.getAdSale())
                .setAdOrderNumCompare(groupAggregateDataVoCompare.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(groupAggregateDataVoCompare.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAcos()), new BigDecimal(groupAggregateDataVoCompare.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getClicks().getValue()), BigDecimal.valueOf(groupAggregateDataVoCompare.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(groupAggregateDataVo.getCtr()), new BigDecimal(groupAggregateDataVoCompare.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(groupAggregateDataVo.getCvr()), new BigDecimal(groupAggregateDataVoCompare.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getImpressions().getValue()), BigDecimal.valueOf(groupAggregateDataVoCompare.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdCost()), new BigDecimal(groupAggregateDataVoCompare.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdSale()), new BigDecimal(groupAggregateDataVoCompare.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(groupAggregateDataVo.getAdOrderNum().getValue()), BigDecimal.valueOf(groupAggregateDataVoCompare.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(groupAggregateDataVo.getAdCostPerClick()), new BigDecimal(groupAggregateDataVoCompare.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data, BigDecimal dataCompare) {
        if ((dataCompare == null || dataCompare.compareTo(BigDecimal.ZERO) == 0) && (data != null && data.compareTo(BigDecimal.ZERO) != 0)) {
            return "100.00";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data, dataCompare).multiply(BigDecimal.valueOf(100)), dataCompare).toString();
    }

    @Override
    @Async
    public void syncAmazonAdByShopId(int puid, int shopId, String uuid) {
        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            ProcessMsg pm = new ProcessMsg(1, 0, "同步成功");
            stringRedisService.set(uuid, pm);
            return;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            ProcessMsg pm = new ProcessMsg(1, 0, "同步成功");
            stringRedisService.set(uuid, pm);
            return;
        }
        try {
            iSyncAmazonAdService.syncAmazonAdsByShop(shopAuth, AdSyncRecord.TriggerChannelEnum.USER_TRIGGER.getChannel());
            ProcessMsg pm = new ProcessMsg(1, 0, "同步成功");
            stringRedisService.set(uuid, pm);
        } catch (Exception e) {
            ProcessMsg pm = new ProcessMsg(-1, 0, "同步异常");
            stringRedisService.set(uuid, pm);
            log.info("同步店铺广告信息异常", e);
        }
    }

    @Override
    public AmazonAdCampaignAll getCampaignInfo(Integer puid, Integer shopId, String campaignId) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        return amazonAdCampaignAllDao.getByCampaignId(puid, shopId, campaignId);
    }

    @Override
    public Page<AmazonAdCampaignAll> getMultiShopCampaignList(MultiShopCampaignListParam param) {
        return amazonAdCampaignAllDao.getMultiShopCampaignList(param.getPuid(), param);
    }

    /**
     * 广告活动列表页设置竞价、预算日志
     *
     * @param voBuilder
     * @param amazonAdOperationLogMap
     */
    private void setCampaignHomeVoOperationLog(AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo.Builder voBuilder, Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap) {
        //预算日志
        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    CampaignDataLog.Builder budgetLog = CampaignDataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setBudgetLog(budgetLog);
                }
            }
            voBuilder.setIsUpdateBudget(true);
        } else {
            voBuilder.setIsUpdateBudget(stringRedisService.get(logKey) != null);
        }
        //搜索结果顶部竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(), voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    CampaignDataLog.Builder budgetLog = CampaignDataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setPlacementTopLog(budgetLog);
                }
            }
            voBuilder.setIsUpdatePlacementTop(true);
        } else {
            voBuilder.setIsUpdatePlacementTop(stringRedisService.get(logKey) != null);
        }
        //产品页面竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    CampaignDataLog.Builder budgetLog = CampaignDataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setPlacementProductPageLog(budgetLog);
                }
            }
            voBuilder.setIsUpdatePlacementProductPage(true);
        } else {
            voBuilder.setIsUpdatePlacementProductPage(stringRedisService.get(logKey) != null);
        }
        //产品页面竞价日志
        logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode(), voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    CampaignDataLog.Builder budgetLog = CampaignDataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setPlacementRestOfSearchLog(budgetLog);
                }
            }
            voBuilder.setIsUpdatePlacementRestOfSearch(true);
        } else {
            voBuilder.setIsUpdatePlacementRestOfSearch(stringRedisService.get(logKey) != null);
        }
    }


    /**
     * 广告位竞价日志
     *
     * @param voBuilder
     * @param amazonAdOperationLogMap
     */
    private void setPlacementDataOperationLog(AllPlacementDataResponse.AdPlacementHomeVo.Page.PlacementPageVo.Builder voBuilder, Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap) {
        Integer code = null;
        if (PlacementPageParam.placementPredicateEnum.placementTop.getCode().equals(voBuilder.getPredicate())) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode();
        } else if (PlacementPageParam.placementPredicateEnum.placementProductPage.getCode().equals(voBuilder.getPredicate())) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode();
        } else if (PlacementPageParam.placementPredicateEnum.Other.getCode().equals(voBuilder.getPredicate())) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode();
        } else if (PlacementPageParam.placementPredicateEnum.siteAmazonBusiness.getCode().equals(voBuilder.getPredicate())){
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode();
        } else {
            return;
        }

        String logKey = String.format("%s-%s", code, voBuilder.getCampaignId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    CampaignDataLog.Builder budgetLog = CampaignDataLog.newBuilder();
                    budgetLog.setCount(logBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    voBuilder.setBidLog(budgetLog);
                }
            }
            voBuilder.setIsUpdateBid(true);
        } else {
            voBuilder.setIsUpdateBid(stringRedisService.get(logKey) != null);
        }
    }

    private void filterAndSaveIdTemporary(String pageSign, Integer puid,
                                          Integer shopId, String marketId, List<String> idList) {
        filterAndSaveIdTemporary(pageSign, puid, shopId, marketId, idList, null);
    }

    private void filterAndSaveIdTemporary(String pageSign, Integer puid,
                                          Integer shopId, String marketId, List<String> idList, String campaignSite) {
        //需要将得到的Id进行过滤，过滤掉近30天没有报告的活动Id
        List<String> queryIdList = idList;
        ThreadPoolUtil.getCpcAggregateIdsSyncPool().execute(() -> {
            List<String> validRecordIdList = new ArrayList<>();
            try {
                if (CollectionUtils.isNotEmpty(idList)) {
                    validRecordIdList = adCampaignPlacementReportRoutingService.getValidRecordByDate(puid, shopId,
                            marketId, LocalDate.now().minusDays(63).format(DateTimeFormatter.BASIC_ISO_DATE), queryIdList, campaignSite);
                }
            } catch (Exception e) {
                log.error("query valid campaign id error, puid:{}, shopId:{}, pageSign:{}, e:{}", puid, shopId, pageSign, e.getMessage());
            }
            cpcPageIdsHandler.addIdsTemporarySynchronize(puid, validRecordIdList, pageSign, "");
        });
    }


    private AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder builderBudgetUsage(List<AmazonAdBudgetUsage> amazonAdBudgetUsages) {
        AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Builder budgetUsageBuilder =
                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.newBuilder();
        if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
            Map<LocalDate, List<AmazonAdBudgetUsage>> dateListMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getUsageUpdatedSiteDate));

            List<AmazonAdBudgetUsage> sortedBudgetUsageList = amazonAdBudgetUsages.stream()
                    .sorted(Comparator.comparing(AmazonAdBudgetUsage::getUsageUpdatedSiteDate))
                    .collect(Collectors.toList());
            //最后更新记录
            AmazonAdBudgetUsage amazonAdBudgetUsage = sortedBudgetUsageList.get(sortedBudgetUsageList.size() - 1);
            budgetUsageBuilder.setIsNoData(false);
            budgetUsageBuilder.setCurrentBudget(amazonAdBudgetUsage.getBudget().doubleValue());
            budgetUsageBuilder.setLastUpdateAt(amazonAdBudgetUsage.getUsageUpdatedSiteTime()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            budgetUsageBuilder.setPercent(amazonAdBudgetUsage.getBudgetUsagePercentage().doubleValue());
            //变化记录
            List<AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item> budgetUsageItems =
                    sortedBudgetUsageList.stream().map(o -> {
                        AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.Builder itemBuilder =
                                AllCampaignDataResponse.CampaignHomeVo.Page.BudgetUsage.Item.newBuilder();
                        itemBuilder.setCurrentBudget(o.getBudget().doubleValue());
                        itemBuilder.setPercent(o.getBudgetUsagePercentage().doubleValue());
                        itemBuilder.setUpdateAt(o.getUsageUpdatedSiteTime()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        return itemBuilder.build();
                    }).collect(Collectors.toList());
            budgetUsageBuilder.addAllItem(budgetUsageItems);

        } else {
            budgetUsageBuilder.setIsNoData(true);
        }
        return budgetUsageBuilder;
    }

    private Page<CampaignPageVo> checkParamAndShop(Integer puid, CampaignPageParam param, ShopAuth shopAuth) {
        String uuid = UUID.randomUUID().toString();
        log.info("广告管理 {} --广告活动接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();
        Page<CampaignPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        param.setMarketplaceId(shopAuth.getMarketplaceId());
        // 取店铺销售额
        log.info("广告管理{}--广告活动接口调用-获取门店信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        log.info("广告管理{}--广告活动接口调用-获取店铺销售额- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        param.setShopSales(shopSalesByDate);
        return voPage;
    }

    private List<String> getQueryIdsByPageFilter(Integer puid, CampaignPageParam param) {
        List<String> campaignIds = null;
        if (StringUtils.isNotBlank(param.getPortfolioId()) && !param.getPortfolioId().equals("-1")) {
            //广告组合id不为空
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(), param.getType(), null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                campaignIds = new ArrayList<>();
                return campaignIds;
            }
        }
        //标签过滤
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            //通过标签Ids筛选广告活动Ids
            int type = 0;
            List<String> tagIds = param.getAdTagIdList().stream().map(String::valueOf).collect(Collectors.toList());
            List<Integer> shopIds = Collections.singletonList(param.getShopId());
            campaignIds = odsAdManageTagRelationDao.getRelationIdByTagIds(param.getPuid(), tagIds, type, shopIds, campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                campaignIds = Collections.emptyList();
                return campaignIds;
            }
        }

        if ((Objects.isNull(campaignIds) || CollectionUtils.isNotEmpty(campaignIds)) && StringUtils.isNotBlank(param.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType()) && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> listProductValues = param.getListProductValues();
            //查询asin/msku不为空
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMsku(puid, param.getShopId(), param.getProductType(), listProductValues, param.getType(), campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 为空直接返回结果
                param.setCampaignIdList(campaignIds);
            } else {
                campaignIds = new ArrayList<>();
            }
        }
        return campaignIds;
    }

    private CampaignPageMetricVo<CampaignInfoPageVo> getAllCampaignPageOrderByReport(Integer puid, CampaignPageParam param, boolean isExport) {
        CampaignPageMetricVo<CampaignInfoPageVo> campaignPageMetricVo = new CampaignPageMetricVo<>();
        Page<CampaignInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        campaignPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch(GET_CAMPAIGN_PAGE_LOG);
        //查询report表中的campaignIdList和指标数据
        sw.start("查询report表中对应的campaign id和指标数据");
        List<AllCampaignOrderBo> adOrderBoList;
        boolean isLatest = SqlStringReportUtil.isLatest(param.getOrderField(), param.getOrderType());
        if (isUseReportDataAdvanced(param)) {
            // 先进行高级筛选
            adOrderBoList = amazonAdCampaignAllReportDao.getCampaignIdAndIndexList(puid, param, false, true);
            // 使用最新的数据进行排序，需要多查一次，不需要在进行高级筛选
            if (isLatest) {
                param.setCampaignIdList(adOrderBoList.stream().map(AllCampaignOrderBo::getId).collect(Collectors.toList()));
                adOrderBoList = amazonAdCampaignAllReportDao.getCampaignIdAndIndexList(puid, param, true, false);
            }
        } else {
            adOrderBoList = amazonAdCampaignAllReportDao.getCampaignIdAndIndexList(puid, param, isLatest, false);
        }

        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return campaignPageMetricVo;
        }
        sw.stop();
        //查询campaignall表获取出列表页所有的广告活动
        sw.start("查询campaign all表获取出列表页所有的campaignId");
        List<String> campaignIdList = adOrderBoList.parallelStream().map(AllCampaignOrderBo::getId).collect(Collectors.toList());
        List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdListByParamAndIds(puid, param, campaignIdList);
        adOrderBoList = adOrderBoList.stream().filter(e -> campaignIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return campaignPageMetricVo;
        }
        //获取列表页所有的campaignId，用于查询占比数据
        List<String> allCampaignIdList = adOrderBoList.parallelStream().map(AllCampaignOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        //将广告活动根据campaignIdList排序分页，获取列表页数据
        sw.start("将campaignId根据campaignIdList排序分页，获取列表页数据");
        campaignPageMetricVo.setPage(this.getAllCampaignPageByOrderBos(puid, param, adOrderBoList));
        sw.stop();
        //占比数据
        sw.start("获取占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getCampaignAdMetricDto(puid, param, allCampaignIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            campaignPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            campaignPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return campaignPageMetricVo;
    }


    @Override
    public CampaignPageMetricVo<CampaignInfoPageVo> getAllCampaignPageNoFilterAndOrder(Integer puid, CampaignPageParam param, boolean isExport) {
        CampaignPageMetricVo<CampaignInfoPageVo> campaignPageMetricVo = new CampaignPageMetricVo<>();
        StopWatch sw = new StopWatch(GET_CAMPAIGN_PAGE_LOG);
        sw.start("获取广告活动列表页");
        //分页获取广告活动
        Page<CampaignInfoPageVo> campaignPage = amazonAdCampaignAllDao.getAllCampaignPage(puid, param);
        campaignPageMetricVo.setPage(campaignPage);
        sw.stop();
        log.info("query campaign basic info spend time:{} /ms", sw.getTotalTimeMillis());
        if (CollectionUtils.isEmpty(campaignPage.getRows())) {
            return campaignPageMetricVo;
        }
        //查询报告数据
        sw.start("获取广告活动报告数据");
        List<CampaignInfoPageVo> rows = campaignPage.getRows();
        List<String> campaignIdList = rows.stream().map(CampaignInfoPageVo::getCampaignId).collect(Collectors.toList());
        Map<String, CampaignInfoPageVo> reportMap = amazonAdCampaignAllReportDao.getReportByCampaignIds(puid, param, campaignIdList)
                .parallelStream().collect(Collectors.toMap(CampaignInfoPageVo::getCampaignId, Function.identity()));
        Map<String, AdHomePerformancedto> latestReportMap = amazonAdCampaignAllReportDao.listLatestReports(puid, param.getShopId(), campaignIdList, false)
                .stream().collect(Collectors.toMap(AdHomePerformancedto::getCampaignId, Function.identity()));
        sw.stop();
        log.info("query campaign report info spend time:{} /ms", sw.getTotalTimeMillis());
        //组装数据
        sw.start("数据广告活动组装");
        rows.forEach(e -> {
            if (reportMap.containsKey(e.getCampaignId())) {
                this.campaignPageVoReportDataCopy(reportMap.get(e.getCampaignId()), e);
            }
            if (latestReportMap.containsKey(e.getCampaignId())) {
                this.campaignPageVoLatestReportDataCopy(latestReportMap.get(e.getCampaignId()), e);
            }
        });
        sw.stop();
        log.info("basic info fill with report spend time:{} /ms", sw.getTotalTimeMillis());
        //占比数据
        sw.start("获取广告活动占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getCampaignAdMetricDto(puid, param, null);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            campaignPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            campaignPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info("get campaign metric spend time:{} /ms", sw.getTotalTimeMillis());
        log.info(sw.prettyPrint());
        return campaignPageMetricVo;
    }

    private CampaignPageMetricVo<CampaignInfoPageVo> getAllCampaignPageFilterAndOrderByCampaign(Integer puid, CampaignPageParam param, boolean isExport) {
        CampaignPageMetricVo<CampaignInfoPageVo> campaignPageMetricVo = new CampaignPageMetricVo<>();
        Page<CampaignInfoPageVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        campaignPageMetricVo.setPage(page);
        StopWatch sw = new StopWatch(GET_CAMPAIGN_PAGE_LOG);
        //查询report表获取高级筛选过滤出的campaignIdList
        sw.start("查询report表获取高级筛选过滤出的campaignIdList");
        List<String> campaignIdList = amazonAdCampaignAllReportDao.getCampaignIdListByParam(puid, param);
        sw.stop();
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return campaignPageMetricVo;
        }
        //查keyword表获取所有的campaignId、bid
        sw.start("查campaignAll表获取所有的campaignId、bid");
        AdCampaignDefaultOrderEnum orderEnum = AdCampaignDefaultOrderEnum.getAdCampaignDefaultOrderEnumByKey(param.getOrderField());
        List<AllCampaignOrderBo> adOrderBoList = amazonAdCampaignAllDao.getCampaignIdAndOrderFieldList(puid, param, campaignIdList,
                (Objects.nonNull(orderEnum)) ? orderEnum.getOrderField() : SqlStringReportUtil.ID);
        if (CollectionUtils.isEmpty(adOrderBoList)) {
            return campaignPageMetricVo;
        }
        //获取列表页所有的campaignId，用于查询占比数据
        List<String> allCampaignIdList = adOrderBoList.stream().map(AllCampaignOrderBo::getId).collect(Collectors.toList());
        sw.stop();
        //内存中根据默认、竞价排序，获取列表页
        sw.start("内存中根据默认、竞价排序，获取列表页");
        campaignPageMetricVo.setPage(this.getAllCampaignPageByOrderBos(puid, param, adOrderBoList));
        sw.stop();
        //占比数据
        sw.start("获取占比数据");
        if (isExport) {
            AdMetricDto spAdMetricDto = this.getCampaignAdMetricDto(puid, param, allCampaignIdList);
            AdMetricCompareDto adMetricCompareDto = new AdMetricCompareDto();
            adMetricCompareDto.setSumAdOrderNum(spAdMetricDto.getSumAdOrderNum());
            adMetricCompareDto.setSumAdSale(spAdMetricDto.getSumAdSale());
            adMetricCompareDto.setSumCost(spAdMetricDto.getSumCost());
            adMetricCompareDto.setSumOrderNum(spAdMetricDto.getSumOrderNum());
            campaignPageMetricVo.setAdMetricDto(adMetricCompareDto);
        } else {
            campaignPageMetricVo.setAdMetricDto(this.waitAggregateMetric(param.getPageSign()));
        }
        sw.stop();
        log.info(sw.prettyPrint());
        return campaignPageMetricVo;
    }


    private void campaignPageVoReportDataCopy(CampaignInfoPageVo report, CampaignInfoPageVo target) {
        target.setImpressions(report.getImpressions());
        target.setClicks(report.getClicks());
        target.setCost(report.getCost());
        target.setSaleNum(report.getSaleNum());
        target.setAdOrderNum(report.getAdOrderNum());
        target.setTotalSales(report.getTotalSales());
        target.setAdSales(report.getAdSales());
        target.setAdSaleNum(report.getAdSaleNum());
        target.setOrderNum(report.getOrderNum());
        target.setMaxTopIs(report.getMaxTopIs());
        target.setMinTopIs(report.getMinTopIs());
        target.setViewImpressions(report.getViewImpressions());
        target.setOrdersNewToBrand14d(report.getOrdersNewToBrand14d());
        target.setSalesNewToBrand14d(report.getSalesNewToBrand14d());
        target.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14d());
        target.setNewToBrandDetailPageViews(report.getNewToBrandDetailPageViews());
        target.setAddToCart(report.getAddToCart());
        target.setVideo5SecondViews(report.getVideo5SecondViews());
        target.setVideoFirstQuartileViews(report.getVideoFirstQuartileViews());
        target.setVideoMidpointViews(report.getVideoMidpointViews());
        target.setVideoThirdQuartileViews(report.getVideoThirdQuartileViews());
        target.setVideoCompleteViews(report.getVideoCompleteViews());
        target.setVideoUnmutes(report.getVideoUnmutes());
        target.setViewableImpressions(report.getViewableImpressions());
        target.setBrandedSearches(report.getBrandedSearches());
        target.setDetailPageViews(report.getDetailPageViews());
    }

    private void campaignPageVoLatestReportDataCopy(AdHomePerformancedto latestReport, CampaignInfoPageVo target) {
        target.setCumulativeReach(latestReport.getCumulativeReach());
        target.setImpressionsFrequencyAverage(latestReport.getImpressionsFrequencyAverage());
    }

    private AdMetricDto getCampaignAdMetricDto(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        AdMetricDto adMetricDto = new AdMetricDto();
        if (CollectionUtils.isEmpty(campaignIdList)) {
            campaignIdList = amazonAdCampaignAllReportDao.getCampaignIdListByParam(puid, param);
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return adMetricDto;
            }
            campaignIdList = amazonAdCampaignAllDao.getCampaignIdListByParamAndIds(puid, param, campaignIdList);
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return adMetricDto;
            }
        }
        return amazonAdCampaignAllReportDao.getCampaignPageSumMetricDataByCampaignIdList(puid, param, campaignIdList);
    }

    private AdMetricCompareDto waitAggregateMetric(String pageSign) {
        for (int i = 0; i < 50; i++) {
            Object objectAdMetricDto = redisService.get(Constants.CAMPAIGN_PAGE_SUM_METRIC + pageSign);
            if (objectAdMetricDto == null) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error(String.format("wait campaign aggregate metric error, pageSign:%s", pageSign), e);
                    throw new RuntimeException(e);
                }
                continue;
            }
            return JSONUtil.jsonToObject((String) objectAdMetricDto, AdMetricCompareDto.class);
        }
        return null;
    }

    private Page<CampaignInfoPageVo> getAllCampaignPageByOrderBos(Integer puid, CampaignPageParam param, List<AllCampaignOrderBo> adOrderBos) {
        StopWatch sw = new StopWatch("getPageByCampaignOrderBos");
        Page<CampaignInfoPageVo> page = new Page<>();
        boolean isOrder = StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType());
        sw.start("排序并分页");
        adOrderBos = AdPageUtil.getAllCampaignOrderPage(param.getPageNo(), param.getPageSize(), adOrderBos, (isOrder ? param.getOrderType() : null), page);
        sw.stop();
        //获取列表页数据
        sw.start("获取列表页数据");
        List<CampaignInfoPageVo> voList = this.getAllCampaignPageByCampaignIdList(puid, param, adOrderBos.stream().map(AllCampaignOrderBo::getId).collect(Collectors.toList()));
        sw.stop();
        if (CollectionUtils.isEmpty(voList)) {
            return page;
        }
        page.setRows(voList);
        return page;
    }

    private List<CampaignInfoPageVo> getAllCampaignPageByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList) {
        List<CampaignInfoPageVo> campaignPageVoList = amazonAdCampaignAllDao.getCampaignPageVoListByCampaignIdList(puid, param, campaignIdList);
        if (CollectionUtils.isEmpty(campaignPageVoList)) {
            return campaignPageVoList;
        }
        List<String> campaignIds = campaignPageVoList.parallelStream().map(CampaignInfoPageVo::getCampaignId).collect(Collectors.toList());
        Map<String, CampaignInfoPageVo> reportMap = amazonAdCampaignAllReportDao.getReportByCampaignIds(puid, param, campaignIds)
                .stream().collect(Collectors.toMap(CampaignInfoPageVo::getCampaignId, Function.identity()));
        Map<String, AdHomePerformancedto> latestReportMap = amazonAdCampaignAllReportDao.listLatestReports(puid, param.getShopId(), campaignIds, false)
                .stream().collect(Collectors.toMap(AdHomePerformancedto::getCampaignId, Function.identity()));
        campaignPageVoList.forEach(e -> {
            if (reportMap.containsKey(e.getCampaignId())) {
                this.campaignPageVoReportDataCopy(reportMap.get(e.getCampaignId()), e);
            }
            if (latestReportMap.containsKey(e.getCampaignId())) {
                this.campaignPageVoLatestReportDataCopy(latestReportMap.get(e.getCampaignId()), e);
            }
        });
        return campaignPageVoList;
    }

    public boolean isUseReportDataAdvanced(CampaignPageParam param) {
        if (param.getUseAdvanced()) {
            if (param.getImpressionsMin() == null && param.getImpressionsMax() == null && param.getClicksMin() == null && param.getClicksMax() == null
                    && param.getClickRateMin() == null && param.getClickRateMax() == null && param.getCostMin() == null && param.getCostMax() == null
                    && param.getCpcMin() == null && param.getCpcMax() == null && param.getOrderNumMin() == null && param.getOrderNumMax() == null &&
                    param.getSalesMin() == null && param.getSalesMax() == null && param.getAcosMin() == null && param.getAcosMax() == null &&
                    param.getRoasMin() == null && param.getRoasMax() == null && param.getViewImpressionsMin() == null && param.getViewImpressionsMax() == null &&
                    param.getAdSaleNumMin() == null && param.getAdSaleNumMax() == null && param.getAdOtherOrderNumMin() == null && param.getAdOtherOrderNumMax() == null &&
                    param.getAdSelfSaleNumMin() == null && param.getAdSelfSaleNumMax() == null && param.getAdOtherSaleNumMin() == null && param.getAdOtherSaleNumMax() == null &&
                    param.getAdSalesTotalMin() == null && param.getAdSalesTotalMax() == null && param.getOrdersNewToBrandFTDMin() == null && param.getOrdersNewToBrandFTDMax() == null &&
                    param.getUnitsOrderedNewToBrandFTDMin() == null && param.getUnitsOrderedNewToBrandFTDMax() == null && param.getSalesConversionRateMin() == null && param.getSalesConversionRateMax() == null &&
                    param.getAcotsMin() == null && param.getAcotsMax() == null && param.getAsotsMin() == null && param.getAsotsMax() == null &&
                    param.getAdCostPercentageMin() == null && param.getAdCostPercentageMax() == null && param.getAdSalePercentageMin() == null && param.getAdSalePercentageMax() == null &&
                    param.getAdOrderNumPercentageMin() == null && param.getAdOrderNumPercentageMax() == null && param.getOrderNumPercentageMin() == null && param.getOrderNumPercentageMax() == null &&
                    param.getCpaMin() == null && param.getCpaMax() == null && param.getVcpmMin() == null && param.getVcpmMax() == null &&
                    param.getAdSalesMin() == null && param.getAdSalesMax() == null && param.getAdOtherSalesMin() == null && param.getAdOtherSalesMax() == null &&
                    param.getOrderRateNewToBrandFTDMin() == null && param.getOrderRateNewToBrandFTDMax() == null && param.getSalesNewToBrandFTDMin() == null && param.getSalesNewToBrandFTDMax() == null &&
                    param.getSalesRateNewToBrandFTDMin() == null && param.getSalesRateNewToBrandFTDMax() == null && param.getUnitsOrderedRateNewToBrandFTDMin() == null && param.getUnitsOrderedRateNewToBrandFTDMax() == null
                    && param.getAddToCartMin() == null && param.getAddToCartMax() == null
                    && param.getVideo5SecondViewsMin() == null && param.getVideo5SecondViewsMax() == null
                    && param.getVideoCompleteViewsMin() == null && param.getVideoCompleteViewsMax() == null
                    && param.getViewabilityRateMin() == null && param.getViewabilityRateMax() == null
                    && param.getViewClickThroughRateMin() == null && param.getViewClickThroughRateMax() == null
                    && param.getBrandedSearchesMin() == null && param.getBrandedSearchesMax() == null
                    && param.getAdvertisingUnitPriceMin() == null && param.getAdvertisingUnitPriceMax() == null
                    && param.getTopImpressionShareMin() == null && param.getTopImpressionShareMax() == null
            ) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public void setServingStatus(CampaignInfoPageVo campaignInfoPageVo, String servingStatus) {
        if (StringUtils.isNotBlank(servingStatus)) {
            AmazonAdCampaignAll.servingStatusEnum byCode = UCommonUtil.getByCode(servingStatus, AmazonAdCampaignAll.servingStatusEnum.class);
            if (byCode != null) {
                campaignInfoPageVo.setServingStatusName(byCode.getName());
                campaignInfoPageVo.setServingStatusDec(byCode.getDescription());
            } else {
                campaignInfoPageVo.setServingStatusName(servingStatus);
                campaignInfoPageVo.setServingStatusDec(servingStatus);
            }
        }
    }

    private void fillCampaignPageVo(CampaignInfoPageVo campaignInfoPageVo, CampaignPageVo vo) {
        setServingStatus(campaignInfoPageVo, campaignInfoPageVo.getServingStatus());
        BeanUtils.copyProperties(campaignInfoPageVo, vo, ParamCopyUtil.checkPropertiesNullOrEmpty(campaignInfoPageVo));
        vo.setSbType(campaignInfoPageVo.getIsMultiAdGroupsEnabled());
        Optional.ofNullable(campaignInfoPageVo.getTotalSales()).map(BigDecimal::toString).ifPresent(vo::setAdSales);
        //根据状态code查询状态描述
        if (AmazonAdCampaign.stateEnum.enabled.getCode().equals(campaignInfoPageVo.getState())) {
            AmazonAdCampaign.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(campaignInfoPageVo.getServingStatus(), AmazonAdCampaign.servingStatusEnum.class);
            vo.setServingStatusDec(null == servingStatusEnum ? StringUtils.EMPTY : servingStatusEnum.getDescription());
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(campaignInfoPageVo.getServingStatus())) {
                vo.setServingStatus("CAMPAIGN_OUT_OF_BUDGET");
            }
            if (AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(campaignInfoPageVo.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(campaignInfoPageVo.getServingStatus())) {
                //如果状态是超过预算
                String outOfTimeStr = "";
                if (campaignInfoPageVo.getOutOfBudgetTime() != null) {
                    try {
                        LocalDateTime localDateTime = LocalDateTimeUtil.ofEpochSecondToDateTime(campaignInfoPageVo.getOutOfBudgetTime());
                        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(campaignInfoPageVo.getMarketplaceId());
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                        outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                    } catch (Exception e) {
                        log.error("转换超预算时间错误", e);
                    }
                }
                vo.setServingStatusName(vo.getServingStatusName() + " " + outOfTimeStr);
                vo.setOutOfBudget(true);
            }
        }

        vo.setDailyBudget(String.valueOf(campaignInfoPageVo.getBudget()));
        vo.setBudgetType(campaignInfoPageVo.getBudgetType());
        vo.setCampaignType(campaignInfoPageVo.getCampaignType());
        vo.setTargetingType(campaignInfoPageVo.getTargetingType());
        vo.setStrategy(campaignInfoPageVo.getStrategy());
        vo.setStartDate(campaignInfoPageVo.getStartDateStr());
        vo.setEndDate(campaignInfoPageVo.getEndDateStr());
        vo.setCreateTime(campaignInfoPageVo.getCreateTime());
        vo.setUpdateTime(campaignInfoPageVo.getUpdateTime());
        vo.setType(campaignInfoPageVo.getType());
        if (CampaignTypeEnum.sp.getCampaignType().equals(campaignInfoPageVo.getType())) {
            vo.setCampaignTargetingType(campaignInfoPageVo.getTargetingType());
        }
        if (CampaignTypeEnum.sb.getCampaignType().equals(campaignInfoPageVo.getType())) {
            vo.setCampaignTargetingType(Constants.MANUAL);
            vo.setTargetType(campaignInfoPageVo.getTargetType());
        }

        if (CampaignTypeEnum.sd.getCampaignType().equals(campaignInfoPageVo.getType())) {
            vo.setCampaignTargetingType(campaignInfoPageVo.getTactic());
        }

        //分时调价
        vo.setIsBudgetPricing(campaignInfoPageVo.getIsBudgetPricing());
        vo.setPricingBudgetState(campaignInfoPageVo.getPricingBudgetState());
        vo.setIsSpacePricing(campaignInfoPageVo.getIsSpacePricing());
        vo.setPricingSpaceState(campaignInfoPageVo.getPricingSpaceState());
        vo.setIsStatePricing(campaignInfoPageVo.getIsStatePricing());
        vo.setPricingStartStopState(campaignInfoPageVo.getPricingStartStopState());
        vo.setCostType(campaignInfoPageVo.getCostType());
        if (CampaignTypeEnum.sb.getCampaignType().equals(campaignInfoPageVo.getType())) {
            vo.setBrandEntityId(campaignInfoPageVo.getBrandEntityId());
            vo.setBidOptimization(campaignInfoPageVo.getBidOptimization());
            if (campaignInfoPageVo.getBidMultiplier() != null) {
                vo.setBidMultiplier(campaignInfoPageVo.getBidMultiplier().doubleValue());
            }
        }
        vo.setMarketplaceId(campaignInfoPageVo.getMarketplaceId());

        // 广告位调整价
        if (StringUtils.isNotBlank(campaignInfoPageVo.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(campaignInfoPageVo.getAdjustments(), new TypeReference<List<Adjustment>>() {
                });
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementProductPage(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementTop(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementRestOfSearch(String.valueOf(adjustment.getPercentage()));
                    }
                }
            }
        }
    }

}
