package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.bidRecommendation.*;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by lm on 2021/8/4.
 * 获取关键词、投放建议竞价
 */
@Component
@Slf4j
public class CpcSbBidApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 获取关键词或投放下的分类和asin的建议竞价
     */
    public Result<BidRecommendationResult> getKeywordAndTargetBids(ShopAuth shop, AmazonAdProfile amazonAdProfile, Long campaignId, String adFormat,
                                          List<SBBidRecommendationKeyword> keywordList, List<List<SBTargetingExpressions>> targetList, String goal) {

        SBBidRecommendationResponse response = cpcApiHelper.call(shop, () -> BidRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), campaignId, adFormat, null, keywordList, targetList, null, goal));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResult());
        }

        String errMsg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                errMsg = response.getResult().getDetails();
            } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                errMsg = response.getResult().getDescription();
            }
        }
        return ResultUtil.returnErr(errMsg);
    }

    public Result<BidRecommendationResult> getKeywordAndTargetBidsNew(ShopAuth shop, AmazonAdProfile amazonAdProfile,
                                                                      Long campaignId, String adFormat,String costType,
                                                                      List<SBBidRecommendationKeyword> keywordList, List<List<SBTargetingExpressions>> targetList,
                                                                      List<String> themeTypeList, String goal) {

        SBBidRecommendationResponse response = cpcApiHelper.call(shop, () -> BidRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), campaignId, adFormat, costType, keywordList, targetList, themeTypeList, goal));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResult());
        }

        String errMsg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                errMsg = response.getResult().getDetails();
            } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                errMsg = response.getResult().getDescription();
            }
        }
        return ResultUtil.returnErr(errMsg);
    }

}
