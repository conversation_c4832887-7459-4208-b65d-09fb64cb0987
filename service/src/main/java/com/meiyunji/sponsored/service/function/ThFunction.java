package com.meiyunji.sponsored.service.function;

import java.util.Objects;
import java.util.function.Function;

/**
 * 3参数有返回值的函数接口
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-02-05  16:40
 */

@FunctionalInterface
public interface ThFunction<P, T, U, R> {


    R apply(P p, T t, U u);


    default <V> ThFunction<P, T, U, V> andThen(Function<? super R, ? extends V> after) {
        Objects.requireNonNull(after);
        return (P p, T t, U u) -> after.apply(apply(p, t, u));
    }
}
