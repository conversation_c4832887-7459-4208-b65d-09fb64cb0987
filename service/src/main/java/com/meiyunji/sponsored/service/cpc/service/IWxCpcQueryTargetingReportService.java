package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetDataResponse;
import com.meiyunji.sponsored.service.cpc.vo.*;


/**
 * <AUTHOR>
 * @date 2023/1/3
 */
public interface IWxCpcQueryTargetingReportService {

    /**
     * 查询所有搜索词(投放产生)
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 查询所有搜索词汇总信息(投放产生)
     * @param puid
     * @param dto
     * @return
     */
    AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto);


}
