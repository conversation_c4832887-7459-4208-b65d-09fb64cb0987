package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-01  16:27
 * 商品投放、自动投放公用
 */

@Data
@ApiModel
public class TargetViewParam extends ViewBaseParam {
    @ApiModelProperty("asin值、类目")
    private String targetText;

    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty(value = "广告组id")
    private String groupId;

    @ApiModelProperty("匹配方式")
    private String targetType;

    @ApiModelProperty("筛选类型，仅商品投放用")
    private String selectType;

    @ApiModelProperty(value = "有效状态")
    private String status;

    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    @ApiModelProperty("搜索字段")
    private String queryField;

    @ApiModelProperty("搜索值")
    private String queryValue;

    @ApiModelProperty("搜索类型，模糊、精确")
    private String queryType;

    private List<String> adGroupIdList;
    //用于小时分析
    private List<String> targetIdList;

    private String uuid;

    public enum OrderFieldEnum implements BaseEnum {
        BID("bid", "竞价");
        private String field;
        private String desc;

        OrderFieldEnum(String field, String desc) {
            this.field = field;
            this.desc = desc;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }
}
