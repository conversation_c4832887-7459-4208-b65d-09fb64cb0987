package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupReport;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AmazonAdGroupReport
 * <AUTHOR>
 */
@Repository
@Slf4j
public class  AmazonAdGroupReportDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdGroupReport> implements IAmazonAdGroupReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertList(Integer puid, List<AmazonAdGroupReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //筛选出95天内的数据
            List<AmazonAdGroupReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertListOriginAndHotTable(puid, hotList, getHotTableName());
            }
        }

    }

    @Override
    public void insertDorisList(Integer puid, List<AmazonAdGroupReport> list) {
        if (!dynamicRefreshConfiguration.verifyGroupAndAdReport(puid)){
            return;
        }
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(),LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap,k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_group_report" ,map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    private void insertListOriginAndHotTable(Integer puid, List<AmazonAdGroupReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,")
                .append("`ad_group_id`,`count_date`,`ad_group_name`,`campaign_name`,`cost`,`cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`,")
                .append("`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for(AmazonAdGroupReport amazonAdGroupReport : list){
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(amazonAdGroupReport.getPuid());
            argsList.add(amazonAdGroupReport.getShopId());
            argsList.add(amazonAdGroupReport.getMarketplaceId());
            argsList.add(amazonAdGroupReport.getCampaignId());
            argsList.add(amazonAdGroupReport.getAdGroupId());
            argsList.add(amazonAdGroupReport.getCountDate());
            argsList.add(amazonAdGroupReport.getAdGroupName());
            argsList.add(amazonAdGroupReport.getCampaignName());
            argsList.add(amazonAdGroupReport.getCost());
            argsList.add(amazonAdGroupReport.getCostRmb());
            argsList.add(amazonAdGroupReport.getCostUsd());
            argsList.add(amazonAdGroupReport.getTotalSales());
            argsList.add(amazonAdGroupReport.getTotalSalesRmb());
            argsList.add(amazonAdGroupReport.getTotalSalesUsd());
            argsList.add(amazonAdGroupReport.getAdSales());
            argsList.add(amazonAdGroupReport.getAdSalesRmb());
            argsList.add(amazonAdGroupReport.getAdSalesUsd());
            argsList.add(amazonAdGroupReport.getImpressions());
            argsList.add(amazonAdGroupReport.getClicks());
            argsList.add(amazonAdGroupReport.getOrderNum());
            argsList.add(amazonAdGroupReport.getAdOrderNum());
            argsList.add(amazonAdGroupReport.getSaleNum());
            argsList.add(amazonAdGroupReport.getAdSaleNum());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),");
        sql.append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`total_sales`=values(total_sales),`total_sales_rmb`=values(total_sales_rmb),`total_sales_usd`=values(total_sales_usd),");
        sql.append("`ad_sales`=values(ad_sales),`ad_sales_rmb`=values(ad_sales_rmb),`ad_sales_usd`=values(ad_sales_usd),`impressions`=values(impressions),`clicks`=values(clicks),");
        sql.append("`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),`ad_sale_num`=values(ad_sale_num)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(),argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdGroupReport> getSumByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT ad_group_id,sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`order_num`) order_num,sum(`ad_order_num`) ad_order_num,sum(`sale_num`) sale_num,sum(`ad_sale_num`) ad_sale_num ")
                .append("FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where`puid`=? and`shop_id`=? and`marketplace_id`=? and `count_date`>=? and `count_date`<=? group by ad_group_id");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,startDate,endDate},getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        String tableName = getTableNameByStartDate(search.getStart());
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,ad_group_id,ad_group_name,campaign_id,campaign_name,count_date,sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`order_num`) order_num,sum(`ad_order_num`) ad_order_num,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd,")
                .append("sum(`sale_num`) sale_num,sum(`ad_sale_num`) ad_sale_num FROM ")
                .append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select ad_group_id FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(search.getGroupIds())) {
            whereSql.append("and ad_group_id in ('").append(StringUtils.join(search.getGroupIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if (StringUtils.isNotBlank(search.getCampaignId())) {
            whereSql.append(" and campaign_id = ?");
            argsList.add(search.getCampaignId());
        }
        if(StringUtils.isNotBlank(search.getSearchValue())){
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and ad_group_name like ?");
            argsList.add("%" +search.getSearchValue()+"%");
        }
        whereSql.append("group by shop_id,ad_group_id ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){
            String orderField = ReportService.getOrderField(search.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,ad_group_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdGroupReport.class);
    }

    @Override
    public AmazonAdGroupReport getSumReportByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String adGroupId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`order_num`) order_num,sum(`ad_order_num`) ad_order_num,")
                .append("sum(`sale_num`) sale_num,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd,sum(`ad_sale_num`) ad_sale_num FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where`puid`=? and`shop_id`=?")
                .append("  and`marketplace_id`=? and ad_group_id=? and `count_date`>=? and count_date<=? ");

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdGroupReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,adGroupId,startStr,endStr},getMapper());
            return list!=null && list.size()>0?list.get(0):null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdGroupReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String adGroupId) {
        StringBuilder sql = new StringBuilder(" SELECT `count_date`,`cost`,`cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`, ")
                .append("`total_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`,`ad_sale_num` FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid=? and shop_id=? and marketplace_id=? and ad_group_id=? and count_date>=? and count_date<=? order by count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,adGroupId,startStr,endStr},getMapper());

        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        String tableName = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        StringBuilder selectSql = new StringBuilder(" SELECT shop_id,`ad_group_name`,`campaign_name`,`count_date`,`cost`,`cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`, ")
                .append("`total_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sales`,`ad_sales_rmb`,`ad_sales_usd`,`ad_sale_num` FROM ")
                .append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*)").append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and ad_group_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getGroupId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String orderField = ReportService.getOrderField(param.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }else{
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdGroupReport.class);
    }

    @Override
    public AmazonAdGroupReport getDetailInfo(int puid, Integer shopId, String marketplaceId, String adGroupId) {
        StringBuilder sql = new StringBuilder("select ad_group_name,campaign_name from t_amazon_ad_group_report where puid=? and shop_id=? and marketplace_id=? and ad_group_id=?")
                .append(" order by count_date desc limit 1");
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdGroupReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),new Object[]{puid,shopId,marketplaceId,adGroupId},getMapper());
            return list!=null&&list.size()>0?list.get(0):null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdGroupReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> groupIds) {
        String sql = "SELECT ad_group_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .in("ad_group_id", groupIds.toArray())
                .groupBy("ad_group_id")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> listSumReportByGroupIds(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param, List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select  'sp' as type, ad_group_id,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,sum(clicks)  clicks,sum(sale_num)  order_num,sum(ad_order_num)  ad_order_num,sum(ad_sale_num) ad_sale_num, ");
        sql.append(" sum(order_num) sales_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by ad_group_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .groupId(re.getString("ad_group_id"))
                            .type(re.getString("type"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdGroupReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String groupId) {
        String sql = "SELECT count_date,campaign_id,ad_group_id,shop_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", groupId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId) {
        String sql = "select campaign_id id,campaign_name `name` from t_amazon_ad_group_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql,arg.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getSpReportByDate(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sp' `type`,c.ad_group_id ad_group_id, count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(" t_amazon_ad_group c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        //广告组
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", StringUtil.splitStr(param.getMultiGroupId()), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        //广告标签管理
        if (CollectionUtils.isNotEmpty(param.getGroupIds())){
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and c.name = ? ");
                argsList.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and c.name like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("lc.name", param.getSearchValueList(), argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and c.default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.ad_group_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid,hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .groupId(re.getString("ad_group_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AdMetricDto getSumMetric(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sp' `type`,sum(cost) cost, sum(total_sales) total_sales,");
        sql.append(" sum(order_num)  order_num,sum(sale_num)  sale_num from  ");
        sql.append(" t_amazon_ad_group c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        //广告组
        if (StringUtils.isNotBlank(param.getGroupId())) {
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }


        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        //广告标签管理
        if (CollectionUtils.isNotEmpty(param.getGroupIds())){
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and c.default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdMetricDto>() {
                @Override
                public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                    AdMetricDto dto = AdMetricDto.builder()
                            .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .sumOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                            .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                            .build();
                    return dto;
                }
            }, argsList.toArray());

            return list != null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByGroupIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id=? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getGroupListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select ad_group_id from (select sum(`impressions`) impressions, ad_group_id from t_amazon_ad_group_report where " +
                " puid = ? and shop_id=?  and update_time > ? group by ad_group_id having impressions > 0) a ";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdReportData> getAllReportByGroupIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder selectSql = new StringBuilder("select ad_group_id,count_date,")
                .append("IFNULL(sum(cost),0) `cost`,")
                .append("IFNULL(sum(total_sales),0) adSale,")
                .append("IFNULL(sum(ad_sales),0) adSelfSale,")
                .append("IFNULL(sum(`impressions`),0) impressions,")
                .append("IFNULL(sum(`clicks`),0) clicks,")
                .append("IFNULL(sum(sale_num),0) adOrderNum,")
                .append("IFNULL(sum(ad_order_num),0) adSelfSaleNum,")
                .append("IFNULL(sum(order_num),0) adSaleNum,")
                .append("IFNULL(sum(ad_sale_num),0) adSelfOrderNum from ");
        startStr = startStr.contains("-") ? startStr.replaceAll("-", "") : startStr;
        endStr = endStr.contains("-") ? endStr.replaceAll("-", "") : endStr;
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
//        selectSql.append(" t_amazon_ad_group_report ");
        selectSql.append(" WHERE ");
        selectSql.append(" puid = ?");
        argsList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            argsList.add(shopId);
        }
        selectSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        selectSql.append(" and count_date >= ?");
        argsList.add(startStr);
        selectSql.append(" and count_date <= ?");
        argsList.add(endStr);
        selectSql.append(" group by ad_group_id,count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            // 也可以直接在对象 写注解
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), (re, i) -> {
                AdReportData dto = new AdReportData();
                dto.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                dto.setItemId(re.getString("ad_group_id"));
                dto.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
                dto.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
                dto.setAdOrderNum(Optional.of(re.getInt("adOrderNum")).orElse(0));
                dto.setAdSelfOrderNum(Optional.of(re.getInt("adSelfOrderNum")).orElse(0));
                dto.setAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO));
                dto.setAdSelfSale(Optional.ofNullable(re.getBigDecimal("adSelfSale")).orElse(BigDecimal.ZERO));
                dto.setAdSaleNum(Optional.of(re.getInt("adSaleNum")).orElse(0));
                dto.setAdSelfSaleNum(Optional.of(re.getInt("adSelfSaleNum")).orElse(0));
                dto.setType(Constants.SP);
                dto.setCountDate(re.getString("count_date"));
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdGroupReportHourlyDTO> getGroupReportByGroupId(Integer puid, Integer shopId, String startDate, String endDate, List<String> adGroupIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT ad_group_id, shop_id, count_date, marketplace_id, campaign_id, campaign_name, sum(cost) as cost, sum(total_sales) as total_sales, ")
                .append(" sum(ad_sales) as ad_sales, sum(`impressions`) as `impressions`, sum(clicks) as clicks, sum(order_num) as order_num, sum(ad_order_num) as ad_order_num, sum(sale_num) as sale_num,")
                .append(" sum(ad_sale_num) as ad_sale_num ")
                .append(" FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        selectSql.append(" WHERE ");
        List<Object> paramList = Lists.newArrayList();
        selectSql.append(" puid = ?");
        paramList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            paramList.add(shopId);
        }
        selectSql.append(" and count_date >= ?");
        paramList.add(startDate);
        selectSql.append(" and count_date <= ?");
        paramList.add(endDate);
        if (CollectionUtils.isNotEmpty(adGroupIdList)) {
            selectSql.append(" and ad_group_id in ( '").append(StringUtils.join(adGroupIdList, "','")).append("') ");
        }
        selectSql.append(" group by count_date");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), paramList.toArray(), (re, i) -> {
                AdGroupReportHourlyDTO dto = AdGroupReportHourlyDTO.builder()
                        .adGroupId(re.getString("ad_group_id"))
                        .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        //订单量
                        .orderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //订单字段，此字段与销量字段在报告同步入库层面就写反了，所以，saleNum所对应的应该是本广告总订单量
                        //本广告订单量
                        .adOrderNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))  //本广告订单字段，此字段与销量字段在报告同步入库层面就写反了，所以，adSaleNum所对应的应该是本广告订单量
                        .totalSales(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        //广告销量
                        .saleNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))//销量字段，此字段与订单字段在报告同步入库层面就写反了，所以，orderNum所对应的应该是本广告总销量
                        ////本广告销量
                        .adSaleNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))//本产品销量字段，此字段与订单字段在报告同步入库层面就写反了，所以，adOrderNum对应的是本产品的销量
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            });
        } finally {
            hintManager.close();
        }
    }

    private StringBuilder subWhereSql(GroupPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /*******************************广告管理高级搜索新增查询指标**************************************/
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(param.getCpaMax());
            }

            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }

            //本广告产品销量
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他广告产品销量
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) >= ?");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) <= ?");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 广告销售额÷广告订单量×100%
            if (param.getAdvertisingUnitPriceMin() != null){
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null){
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

        }
        return subWhereSql;
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<GroupInfoPageVo> getReportByGroupIds(Integer puid, GroupPageParam param, List<String> groupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("SELECT ad_group_id, shop_id, count_date, marketplace_id, campaign_id, campaign_name, sum(cost) as cost, sum(total_sales) as total_sales, ")
                .append(" sum(ad_sales) as ad_sales, sum(`impressions`) as `impressions`, sum(clicks) as clicks, sum(order_num) as order_num, sum(ad_order_num) as ad_order_num, sum(sale_num) as sale_num,")
                .append(" sum(ad_sale_num) as ad_sale_num ")
                .append(" FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)) + " where ");

        StringBuffer whereSql = new StringBuffer();
        whereSql.append("puid = ? ");
        argsList.add(param.getPuid());
        whereSql.append(" and shop_id = ? ");
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        whereSql.append("  and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        whereSql.append(" group by ad_group_id");
        selectSql.append(whereSql);
        if (param.getUseAdvanced()) {//高级搜索sql拼接
            selectSql.append(getGroupPageHavingSql(param, argsList));
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GroupInfoPageVo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getGroupIdListByParam(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id adGroupId from ")
                .append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD))).append(" g ");
        sb.append(" where puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealInList("g.ad_group_id", param.getGroupIds(), argsList));
        }
        sb.append(" group by g.ad_group_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getGroupPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.GROUP_PAGE_SUM_METRIC_TIME);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AdMetricDto getGroupPageSumMetricDataByCampaignIdList(Integer puid, GroupPageParam param, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select  'sp' as type, ad_group_id,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,sum(clicks)  clicks,sum(sale_num)  order_num,sum(ad_order_num)  ad_order_num,sum(ad_sale_num) ad_sale_num, ");
        sql.append(" sum(order_num) sales_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        whereSql.append("  and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> list = getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }, argsList.toArray());
            return list!= null && list.size() > 0 ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AllGroupOrderBo> getAdGroupIdAndIndexList(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id, ")
                .append(SqlStringReportUtil.getGroupSpOrderField(param.getOrderField())).append(" orderField ")
                .append(" from ").append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD))).append(" g ");

        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealInList("g.ad_group_id", param.getGroupIds(), argsList));
        }
        sb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sb.append(" group by g.ad_group_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getGroupPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.GROUP_PAGE_QUERY_REPORT_ID_LIMIT);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getReportDataByGroupIdList(Integer puid, GroupPageParam param, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id=? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    private String getGroupPageHavingSql(GroupPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getSpGroupPageHavingSql(qo, argsList);
    }

    @Override
    public List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopIdList, List<String> adGroupIdList, Set<String> fields, String startDate, String endState) {
        if (CollectionUtils.isEmpty(adGroupIdList)) {
            return Lists.newArrayList();
        }
        StringBuilder sql = new StringBuilder("SELECT ad_group_id ");
        List<Object> args = Lists.newArrayList();
        Set<String> fieldKey = fields.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(fieldKey)) {
            sql.append(",");
            sql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        }
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN)));
//        sql.append(" t_amazon_ad_group_report ");
        sql.append(" where `puid`=? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(adGroupIdList)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIdList, args));
        }
        sql.append(" and `count_date` >= ? and `count_date` <= ? ");
        args.add(startDate.contains("-") ? startDate.replaceAll("-","") : startDate);
        args.add(endState.contains("-") ? endState.replaceAll("-","") : endState);
        sql.append(" group by ad_group_id ");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new ObjectMapper<>(SponsoredIndexCampaignData.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    private static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costTotal", "sum(cost) `costTotal`");
            put("costAvg", "avg(cost) `costAvg`");
            put("clicksTotal", "sum(`clicks`) clicksTotal");
            put("clicksAvg", "avg(`clicks`) clicksAvg");
            put("impressionsTotal", "sum(`impressions`) impressionsTotal");
            put("impressionsAvg", "avg(`impressions`) impressionsAvg");
            put("adSalesTotal", "sum(total_sales) adSalesTotal"); // 广告销售额
            put("adSalesAvg", "avg(total_sales) adSalesAvg");
            put("adOrderNumTotal", "sum(sale_num) adOrderNumTotal"); // 广告订单量
            put("adOrderNumAvg", "avg(sale_num) adOrderNumAvg");
            put("adSalesNumTotal", "sum(order_num) adSalesNumTotal"); // 广告销量
            put("adSalesNumAvg", "avg(order_num) adSalesNumAvg");
        }
    });

    @Override
    public List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopDTOS, AdTypeEnum adType, String startCountDate, String endCountDate) {
        List<ReportMonitorBo> resultBolist = new ArrayList<>();
        String groupTable;
        //通过广告类型得到对应表名
        switch (adType) {
            case sp: {
                groupTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_group_report");
            } break;
            case sb: {
                groupTable = "t_amazon_ad_sb_group_report";
            } break;
            case sd: {
                groupTable = "t_amazon_ad_sd_group_report";
            } break;
            default:
                return resultBolist;
        }

        Map<Integer, List<Integer>> puidShopIdsMap = shopDTOS.stream().collect(Collectors.groupingBy(ShopDTO::getPuid, Collectors.mapping(ShopDTO::getShopId, Collectors.toList())));
        String finalGroupTable = groupTable;
        puidShopIdsMap.forEach((puid, shopIds) -> {
            if (CollectionUtils.isEmpty(shopIds)) {
                return;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select sum(cost) total_cost,sum(clicks) total_clicks,sum(impressions) total_impressions, puid, shop_id from ")
                .append(finalGroupTable)
                .append(" where puid = ? and count_date between ? and ? ");
            List<Object> args = Lists.newArrayList(puid, startCountDate, endCountDate);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args))
                .append(" group by shop_id ");
            HintManager hintManager = HintManager.getInstance();
            try {
                List<ReportMonitorBo> reportMonitorBoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), (row, i) -> ReportMonitorBo.builder()
                    .totalCost(row.getBigDecimal("total_cost"))
                    .totalClicks(row.getLong("total_clicks"))
                    .totalImpressions(row.getLong("total_impressions"))
                    .puid(row.getInt("puid"))
                    .shopId(row.getInt("shop_id"))
                    .build());
                resultBolist.addAll(reportMonitorBoList);
            } finally {
                hintManager.close();
            }
        });
        return resultBolist;

    }
}