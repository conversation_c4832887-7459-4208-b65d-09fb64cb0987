package com.meiyunji.sponsored.service.sellfoxApi.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.enums.SellfoxApiEnum;
import com.meiyunji.sponsored.service.sellfoxApi.IFbaInventoryManageApi;
import com.meiyunji.sponsored.service.sellfoxApi.qo.AsinInventoryInfoReq;
import com.meiyunji.sponsored.service.sellfoxApi.qo.FbaReplenishmentApiQo;
import com.meiyunji.sponsored.service.sellfoxApi.vo.AsinInventoryInfo;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaReplenishmentApiVO;
import com.meiyunji.sponsored.service.util.OkHttpClientUtil;
import com.meiyunji.sponsored.service.vo.AdFbaInventoryVo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;
import java.util.TreeMap;


/**
 * FbaInventoryManage
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FbaInventoryManageApiImpl implements IFbaInventoryManageApi {


    @Value("${services.amzup.prefix}")
    private String amzupPrefix;

    @Override
    public AdFbaInventoryVo getByAsinAndSku(Integer puid, Integer shopId, String marketplaceId, String asin, String sku) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("asin",asin);
        map.put("sku",sku);
        log.info("======================开始请求商品fba接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getFbaInventoryByAsinAndSku,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<AdFbaInventoryVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<AdFbaInventoryVo>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求商品fba评价接口出现异常======================", e);

        }
        log.info("======================结束请求商品fba评价接口======================");
        return null;
    }

    @Override
    public List<FbaReplenishmentApiVO> getAdReplenishList(FbaReplenishmentApiQo qo) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        TreeMap<String, Object> map = objectToTreeMap(qo);
        log.info("======================开始请求FBA库存不足接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.postRequest(amzupPrefix,SellfoxApiEnum.getAdReplenishList,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<FbaReplenishmentApiVO>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<FbaReplenishmentApiVO>>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求FBA库存不足出现异常======================", e);

        }
        log.info("======================结束请求FBA库存不足接口======================");
        return null;
    }

    @Override
    public AsinInventoryInfo getAsinInventoryInfo(AsinInventoryInfoReq asinInventoryInfoReq) {
        if (asinInventoryInfoReq == null || CollectionUtils.isEmpty(asinInventoryInfoReq.getAsins())) {
            return null;
        }
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        TreeMap<String, Object> map = objectToTreeMap(asinInventoryInfoReq);
        log.info("======================开始请求ASIN库存指标接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.postRequest(amzupPrefix,SellfoxApiEnum.getAsinInventoryInfo,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<AsinInventoryInfo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<AsinInventoryInfo>>() {
            });
            return result == null ? null : result.getData();
        } catch (IOException e) {
            log.error("======================请求ASIN库存指标出现异常======================", e);

        }
        log.info("======================结束请求ASIN库存指标接口======================");
        return null;
    }

    public static TreeMap<String, Object> objectToTreeMap(FbaReplenishmentApiQo qo) {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        Class<?> clazz = qo.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(qo);
                if(value != null){
                    treeMap.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return treeMap;
    }

    public static TreeMap<String, Object> objectToTreeMap(AsinInventoryInfoReq qo) {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        Class<?> clazz = qo.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(qo);
                if(value != null){
                    treeMap.put(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return treeMap;
    }


}