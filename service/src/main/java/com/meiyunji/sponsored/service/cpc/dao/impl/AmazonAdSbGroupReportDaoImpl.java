package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbGroupReportDao;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbGroupReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.GroupInfoPageVo;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
@Slf4j
public class AmazonAdSbGroupReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSbGroupReport> implements IAmazonAdSbGroupReportDao {

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSbGroupReport> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_sb_group_report` (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_format`,`campaign_name`,")
                .append("`campaign_id`,`campaign_status`,`campaign_budget`,`campaign_budget_type`,`campaign_rule_based_budget`,`applicable_budget_rule_id`,`applicable_budget_rule_name`,")
                .append("`ad_group_name`,`ad_group_id`,")
                .append("`impressions`,`clicks`,`currency`,`cost`,`sales14d`,`sales14d_same_sku`,`conversions14d`,`conversions14d_same_sku`,`detail_page_views_clicks14d`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,")
                .append("`units_ordered_new_to_brand14d`,`units_ordered_new_to_brand_percentage14d`,`units_sold14d`,`dpv14d`,")
                .append("`vctr`,`video5second_view_rate`,`video5second_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views` ,")
                .append("`video_unmutes`,`viewable_impressions`,`video_complete_views`,`vtr`,`branded_searches14d`,`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSbGroupReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdFormat());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getCampaignRuleBasedBudget());
            argsList.add(report.getApplicableBudgetRuleId());
            argsList.add(report.getApplicableBudgetRuleName());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getDetailPageViewsClicks14d());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage14d());
            argsList.add(report.getOrderRateNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getSalesNewToBrandPercentage14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getUnitsSold14d());
            argsList.add(report.getDpv14d());
            argsList.add(report.getVctr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVtr());
            argsList.add(report.getBrandedSearches14d());
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),`campaign_rule_based_budget`=values(campaign_rule_based_budget),");
        sql.append("`applicable_budget_rule_id`=values(applicable_budget_rule_id),`applicable_budget_rule_name`=values(applicable_budget_rule_name),");
        sql.append("`ad_group_name`=values(ad_group_name),`ad_group_id`=values(ad_group_id),");
        sql.append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`sales14d`=values(sales14d),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`conversions14d`=values(conversions14d),`conversions14d_same_sku`=values(conversions14d_same_sku),`detail_page_views_clicks14d`=values(detail_page_views_clicks14d),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`orders_new_to_brand_percentage14d`=values(orders_new_to_brand_percentage14d),`order_rate_new_to_brand14d`=values(order_rate_new_to_brand14d),");
        sql.append("`sales_new_to_brand14d`=values(sales_new_to_brand14d),`sales_new_to_brand_percentage14d`=values(sales_new_to_brand_percentage14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`units_ordered_new_to_brand_percentage14d`=values(units_ordered_new_to_brand_percentage14d),`units_sold14d`=values(units_sold14d),`dpv14d`=values(dpv14d),");
        sql.append("`vctr`=values(vctr),`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_first_quartile_views`=values(video_first_quartile_views),");
        sql.append("`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),`video_unmutes`=values(video_unmutes),");
        sql.append("`viewable_impressions`=values(viewable_impressions),`video_complete_views`=values(video_complete_views),`vtr`=values(vtr),`branded_searches14d` = values(branded_searches14d)");

        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public void insertDorisList(Integer puid, List<AmazonAdSbGroupReport> list) {
        if (!dynamicRefreshConfiguration.verifyGroupAndAdReport(puid)){
            return;
        }
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap, k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_sb_group_report", map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    @Override
    public List<AmazonAdSbGroupReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String groupId) {
        String sql = "select * from t_amazon_ad_sb_group_report where puid=? and shop_id=? and marketplace_id=? and ad_group_id=? and count_date>=? and count_date<=?  order by count_date";
        return getJdbcTemplate(puid).query(sql,new Object[]{puid,shopId,marketplaceId,groupId,startStr,endStr},getMapper());
    }

    @Override
    public AmazonAdSbGroupReport getSumReportByGroupId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String groupId) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM `t_amazon_ad_sb_group_report` where`puid`=? and`shop_id`=? ")
                .append("  and`marketplace_id`=? and ad_group_id=? and `count_date`>=? and count_date<=?  ");
        List<AmazonAdSbGroupReport> list = getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, marketplaceId, groupId, startStr, endStr}, getMapper());
        return list!= null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId) {
        String sql = "select campaign_id id,campaign_name `name` from t_amazon_ad_sb_group_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        return getJdbcTemplate(puid).queryForList(sql,arg.toArray());
    }

    @Override
    public List<AmazonAdSbGroupReport> listSumReports(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> groupList) {
        String sql = "SELECT puid, shop_id, keyword_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sb_group_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .in("ad_group_id", groupList.toArray())
                .groupBy("ad_group_id")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdSbGroupReport> listReports(int puid, Integer shopId, String startDate, String endDate, String adGroupId) {
        String sql = "SELECT puid, count_date,campaign_id,ad_group_id,shop_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sb_group_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("ad_group_id", adGroupId)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }




    private StringBuilder subWhereSql(GroupPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /*******************************sb广告组广告管理高级搜索新增查询指标**************************************/
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(param.getCpaMax());
            }

            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(conversions14d_same_sku, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - conversions14d_same_sku, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - conversions14d_same_sku, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }


            //本广告产品销量
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) >= ?");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(units_sold14d, 0) <= ?");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他广告产品销量
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - units_sold14d, 0) >= ?");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - units_sold14d, 0) <= ?");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 5秒观看次数 筛选
            if (param.getVideo5SecondViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video5second_views , 0) >= ? ");
                argsList.add(param.getVideo5SecondViewsMin());
            }
            if (param.getVideo5SecondViewsMax() != null) {
                subWhereSql.append(" and ifnull(video5second_views , 0) <= ? ");
                argsList.add(param.getVideo5SecondViewsMax());
            }

            // 视频完整播放次数 筛选
            if (param.getVideoCompleteViewsMin() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) >= ? ");
                argsList.add(param.getVideoCompleteViewsMin());
            }
            if (param.getVideoCompleteViewsMax() != null ) {
                subWhereSql.append(" and ifnull(video_complete_views , 0) <= ? ");
                argsList.add(param.getVideoCompleteViewsMax());
            }

            //可见展示次数
            if (param.getViewImpressionsMin() != null) {
                subWhereSql.append(" and viewable_impressions >= ?");
                argsList.add(param.getViewImpressionsMin());
            }
            if (param.getViewImpressionsMax() != null) {
                subWhereSql.append(" and viewable_impressions <= ?");
                argsList.add(param.getViewImpressionsMax());
            }

            // 观看率 筛选
            if (param.getViewabilityRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewabilityRateMin());
            }
            if (param.getViewabilityRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (param.getViewClickThroughRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) >= ? ");
                argsList.add(param.getViewClickThroughRateMin());
            }
            if (param.getViewClickThroughRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) <= ? ");
                argsList.add(param.getViewClickThroughRateMax());
            }

            // 品牌搜索次数 筛选
            if (param.getBrandedSearchesMin() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) >= ? ");
                argsList.add(param.getBrandedSearchesMin()); }
            if (param.getBrandedSearchesMax() != null) {
                subWhereSql.append(" and ifnull(branded_searches14d , 0) <= ? ");
                argsList.add(param.getBrandedSearchesMax()); }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
        }
        return subWhereSql;
    }

    @Override
    public List<AdHomePerformancedto> listSumReportByGroupIds(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param, List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select   'sb' as type, ad_group_id,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions, ");
        sql.append(" sum(clicks)  clicks,sum(conversions14d) order_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(units_sold14d) `units_sold14d`, sum(sales14d_same_sku) ad_sales,");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d`,");
        sql.append(" sum(`video5second_views`) AS `video5second_views`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`viewable_impressions`) AS `viewable_impressions`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`");
        sql.append("  FROM `t_amazon_ad_sb_group_report` where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by ad_group_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        /**
                         * TODO 广告报告重构
                         * CPC,VCPM广告订单量
                         */
                        .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                        //CPC,VCPM广告销售额
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .groupId(re.getString("ad_group_id"))
                        .type(re.getString("type"))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getSbReportByGroupIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' `type`,count_date,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions,sum(clicks)  clicks, ");
        sql.append(" sum(conversions14d) sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`, sum(sales14d_same_sku) ad_sales,sum(units_sold14d) `units_sold14d`, ");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d` from ");
        sql.append("  t_amazon_ad_sb_group_report  where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> {
            AdHomePerformancedto dto = AdHomePerformancedto.builder()
                    .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                    .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))
                    .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                    .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                    //本广告产品销售额
                    .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                    //“品牌新买家”订单量
                    .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                    //“品牌新买家”销售额
                    .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                    .type(re.getString("type"))
                    .build();
            return dto;
        }, argsList.toArray());
    }

    @Override
    public List<AdHomePerformancedto> getSbReportByDate(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' `type`,r.ad_group_id ad_group_id,count_date,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions,sum(clicks)  clicks, ");
        sql.append(" sum(conversions14d) sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`, sum(units_sold14d) `units_sold14d`,  sum(sales14d_same_sku) ad_sales,");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d`,");
        sql.append(" sum(`video5second_views`) AS `video5second_views`,");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`,");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`,");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`,");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`,");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`,");
        sql.append(" sum(`viewable_impressions`) AS `viewable_impressions`,");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d`");
        sql.append(" from t_amazon_ad_group_sb c join t_amazon_ad_sb_group_report r on r.puid=c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getMultiGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }
        //广告标签筛选
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String subSql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "c.ad_group_id", false);
            if(StringUtils.isNotEmpty(subSql)){
                whereSql.append(subSql);
            }
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and c.name = ? ");
                argsList.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and c.name like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("c.name", param.getSearchValueList(), argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }


        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.ad_group_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);


        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .groupId(re.getString("ad_group_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                         //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //sb广告组本广告产品销量是0
                        //.orderNum(0)
                        ////本广告产品订单量
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .type(re.getString("type"))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public AdMetricDto getSumMetric(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select 'sb' `type`,sum(cost) cost, sum(sales14d)  total_sales, ");
        sql.append(" sum(conversions14d) sale_num,sum(conversions14d_same_sku) sales_num ");
        sql.append(" from t_amazon_ad_group_sb c join t_amazon_ad_sb_group_report r on r.puid=c.puid and r.shop_id=c.shop_id and r.ad_group_id=c.ad_group_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        //广告标签筛选
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and c.serving_status = 'AD_GROUP_STATUS_ENABLED' ");
        }


        whereSql.append("  and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        List<AdMetricDto> list = getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(BigDecimal.ZERO)
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());

        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<AdGroupReportHourlyDTO> getSbGroupReportByGroupId(Integer puid, Integer shopId, String startDate, String endDate, List<String> adGroupIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT ad_group_id, shop_id, count_date, marketplace_id, campaign_id, campaign_name, sum(cost) as cost, sum(sales14d) as total_sales,")
                .append(" sum(sales14d_same_sku) as ad_sales, sum(`impressions`) as `impressions`, sum(clicks) as clicks, sum(conversions14d) as order_num, sum(conversions14d_same_sku) as ad_order_num, sum(units_sold14d) as sale_num," +
                        " sum(viewable_impressions) as viewable_impressions, sum(orders_new_to_brand14d) as orders_new_to_brand14d, sum(units_ordered_new_to_brand14d) as units_ordered_new_to_brand14d, " +
                        " sum(sales_new_to_brand14d) as sales_new_to_brand14d")
                .append(" FROM t_amazon_ad_sb_group_report where ");
        List<Object> paramList = Lists.newArrayList();
        selectSql.append(" puid = ?");
        paramList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            paramList.add(shopId);
        }
        selectSql.append(" and count_date >= ?");
        paramList.add(startDate);
        selectSql.append(" and count_date <= ?");
        paramList.add(endDate);
        if (CollectionUtils.isNotEmpty(adGroupIdList)) {
            selectSql.append(" and ad_group_id in ( '").append(StringUtils.join(adGroupIdList, "','")).append("') ");
        }
        selectSql.append(" group by count_date");
        return getJdbcTemplate(puid).query(selectSql.toString(), paramList.toArray(), (re, i) -> {
            AdGroupReportHourlyDTO dto = AdGroupReportHourlyDTO.builder()
                    .adGroupId(re.getString("ad_group_id"))
                    .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))

                    .totalSales(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))

                    //广告销量
                    .saleNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                    ////本广告产品订单量
                    .adSaleNum(0)//sb广告组没有本产品销量，直接设置为0

                    .orderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                    .adOrderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))  //销量字段订单

                    //本广告产品销售额
                    .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                    .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                    .vcpmImpressions(re.getInt("viewable_impressions"))
                    .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                    .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
                    .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                    .build();
            return dto;
        });
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AllGroupOrderBo> getSbGroupIdAndIndexList(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id, ")
                .append(SqlStringReportUtil.getGroupSbOrderField(param.getOrderField())).append(" orderField ")
                .append(" from t_amazon_ad_sb_group_report g ");

        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sb.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }
        sb.append(" group by g.ad_group_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getGroupPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.GROUP_PAGE_QUERY_REPORT_ID_LIMIT);
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
    }

    @Override
    public List<String> getSbGroupIdListByParam(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id adGroupId from t_amazon_ad_sb_group_report g ");
        sb.append(" where puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {
            sb.append(SqlStringUtil.dealInList("g.ad_group_id", param.getGroupIds(), argsList));
        }
        sb.append(" group by g.ad_group_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getGroupPageHavingSql(param, argsList));
        }
        sb.append(" limit " + Constants.GROUP_PAGE_SUM_METRIC_TIME);
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<GroupInfoPageVo> getSbReportByGroupIds(Integer puid, GroupPageParam param, List<String> groupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("SELECT ad_group_id, shop_id, count_date, marketplace_id, campaign_id, campaign_name, sum(cost) as cost, sum(`sales14d`) as total_sales, ")
                .append(" sum(`sales14d_same_sku`) as ad_sales, sum(`impressions`) as `impressions`, sum(clicks) as clicks, sum(`conversions14d`) as sale_num, sum(`conversions14d_same_sku`) as ad_sale_num, sum(`units_sold14d`) as order_num,")
                .append(" sum(0) as ad_order_num, ")
                .append("sum(`video5second_views`) AS video5SecondViews, ")
                .append("sum(`video_first_quartile_views`) AS videoFirstQuartileViews, ")
                .append("sum(`video_Midpoint_Views`) AS videoMidpointViews, ")
                .append("sum(`video_third_quartile_views`) AS videoThirdQuartileViews, ")
                .append("sum(`video_complete_views`) AS videoCompleteViews, ")
                .append("sum(`video_unmutes`) AS videoUnmutes, ")
                .append("sum(`viewable_impressions`) AS viewableImpressions, ")
                .append("sum(`branded_searches14d`) AS brandedSearches ")
                .append(" FROM t_amazon_ad_sb_group_report where ");
        StringBuffer whereSql = new StringBuffer();
        whereSql.append("puid = ? ");
        argsList.add(param.getPuid());
        whereSql.append(" and shop_id = ? ");
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        whereSql.append("  and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        whereSql.append(" group by ad_group_id");
        selectSql.append(whereSql);
        if (param.getUseAdvanced()) {//高级搜索sql拼接
            selectSql.append(getGroupPageHavingSql(param, argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GroupInfoPageVo.class));
    }

    @Override
    public List<AdHomePerformancedto> getSbReportByGroupIdList(Integer puid, GroupPageParam param, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' `type`,count_date,sum(cost) cost, sum(sales14d)  total_sales,sum(impressions) impressions,sum(clicks)  clicks, ");
        sql.append(" sum(conversions14d) sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`, sum(sales14d_same_sku) ad_sales,sum(units_sold14d) `units_sold14d`, ");
        sql.append(" sum(sales14d_same_sku) `sales14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_ordered_new_to_brand14d) `units_ordered_new_to_brand14d`, ");
        sql.append(" sum(`video5second_views`) AS `video5second_views`, ");
        sql.append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ");
        sql.append(" sum(`video_Midpoint_Views`) AS `video_Midpoint_Views`, ");
        sql.append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ");
        sql.append(" sum(`video_complete_views`) AS `video_complete_views`, ");
        sql.append(" sum(`video_unmutes`) AS `video_unmutes`, ");
        sql.append(" sum(`viewable_impressions`) AS `view_impressions`, ");
        sql.append(" sum(`branded_searches14d`) AS `branded_searches14d` ");

        sql.append(" from t_amazon_ad_sb_group_report  where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());

        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> {
            AdHomePerformancedto dto = AdHomePerformancedto.builder()
                    .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                    .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))
                    .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                    .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                    //本广告产品销售额
                    .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                    //“品牌新买家”订单量
                    .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                    //“品牌新买家”销售额
                    .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                    .type(re.getString("type"))
                    .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                    .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                    .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                    .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                    .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                    .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                    //可见展示次数
                    .viewImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
                    .brandedSearches(Optional.ofNullable(re.getInt("branded_searches14d")).orElse(0))
                    .build();
            return dto;
        }, argsList.toArray());
    }

    @Override
    public AdMetricDto getSbGroupPageSumMetricDataByCampaignIdList(Integer puid, GroupPageParam param, List<String> groupIdList) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select  'sb' as type, ad_group_id,sum(cost) cost, sum(sales14d)  total_sales, sum(sales14d_same_sku) ad_sales,sum(impressions) impressions,sum(clicks)  clicks,sum(`units_sold14d`) as order_num, sum(0) as ad_order_num,sum(`conversions14d_same_sku`) as ad_sale_num, ");
        sql.append(" sum(`conversions14d`) as sale_num FROM t_amazon_ad_sb_group_report ");
        sql.append(" where puid= ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        whereSql.append("  and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        sql.append(whereSql);
        List<AdMetricDto> list = getJdbcTemplate(puid).query(sql.toString(), (re, i) -> {
            AdMetricDto dto = AdMetricDto.builder()
                    .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                    .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                    .build();
            return dto;
        }, argsList.toArray());
        return list!= null && list.size() > 0 ? list.get(0) : null;
    }

    private String getGroupPageHavingSql(GroupPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getSbGroupPageHavingSql(qo, argsList);
    }

    @Override
    public List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopIdList, List<String> adGroupIdList, Set<String> fields, String startDate, String endState) {
        if (CollectionUtils.isEmpty(adGroupIdList)) {
            return Lists.newArrayList();
        }
        StringBuilder sql = new StringBuilder("SELECT ad_group_id ");
        List<Object> args = Lists.newArrayList();
        Set<String> fieldKey = fields.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(fieldKey)) {
            sql.append(",");
            sql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        }
        sql.append(" FROM t_amazon_ad_sb_group_report ");
        sql.append(" where `puid`=? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (CollectionUtils.isNotEmpty(adGroupIdList)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIdList, args));
        }
        sql.append( " and `count_date` >= ? and `count_date` <= ? ");
        args.add(startDate.contains("-") ? startDate.replaceAll("-","") : startDate);
        args.add(endState.contains("-") ? endState.replaceAll("-","") : endState);
        sql.append(" group by ad_group_id ");
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(SponsoredIndexCampaignData.class), args.toArray());
    }

    @Override
    public List<AdReportData> getAllReportByGroupIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder selectSql = new StringBuilder("select ad_group_id,count_date,")
                .append("IFNULL(sum(cost),0) `cost`,")
                .append("IFNULL(sum(sales14d),0) adSale,")
                .append("IFNULL(sum(sales14d_same_sku),0) adSelfSale,")
                .append("IFNULL(sum(`impressions`),0) impressions,")
                .append("IFNULL(sum(`clicks`),0) clicks,")
                .append("IFNULL(sum(conversions14d),0) adOrderNum,")
                .append("sum(0) adSelfSaleNum,")
                .append("IFNULL(sum(units_sold14d),0) adSaleNum,")
                .append("IFNULL(sum(conversions14d_same_sku),0) adSelfOrderNum from ");
        startStr = startStr.contains("-") ? startStr.replaceAll("-", "") : startStr;
        endStr = endStr.contains("-") ? endStr.replaceAll("-", "") : endStr;
        selectSql.append(" t_amazon_ad_sb_group_report ");
        selectSql.append(" WHERE ");
        selectSql.append(" puid = ?");
        argsList.add(puid);
        if (Objects.nonNull(shopId)) {
            selectSql.append(" and shop_id = ?");
            argsList.add(shopId);
        }
        selectSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        selectSql.append(" and count_date >= ?");
        argsList.add(startStr);
        selectSql.append(" and count_date <= ?");
        argsList.add(endStr);
        selectSql.append(" group by ad_group_id,count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            // 也可以直接在对象 写注解
            return getJdbcTemplate(puid).query(selectSql.toString(), (re, i) -> {
                AdReportData dto = new AdReportData();
                dto.setCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                dto.setItemId(re.getString("ad_group_id"));
                dto.setImpressions(Optional.of(re.getLong("impressions")).orElse(0L));
                dto.setClicks(Optional.of(re.getLong("clicks")).orElse(0L));
                dto.setAdOrderNum(Optional.of(re.getInt("adOrderNum")).orElse(0));
                dto.setAdSelfOrderNum(Optional.of(re.getInt("adSelfOrderNum")).orElse(0));
                dto.setAdSale(Optional.ofNullable(re.getBigDecimal("adSale")).orElse(BigDecimal.ZERO));
                dto.setAdSelfSale(Optional.ofNullable(re.getBigDecimal("adSelfSale")).orElse(BigDecimal.ZERO));
                dto.setAdSaleNum(Optional.of(re.getInt("adSaleNum")).orElse(0));
                dto.setAdSelfSaleNum(Optional.of(re.getInt("adSelfSaleNum")).orElse(0));
                dto.setType(Constants.SB);
                dto.setCountDate(re.getString("count_date"));
                return dto;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    private static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costTotal", "sum(cost) `costTotal`");
            put("costAvg", "avg(cost) `costAvg`");
            put("clicksTotal", "sum(`clicks`) clicksTotal");
            put("clicksAvg", "avg(`clicks`) clicksAvg");
            put("impressionsTotal", "sum(`impressions`) impressionsTotal");
            put("impressionsAvg", "avg(`impressions`) impressionsAvg");
            put("adSalesTotal", "sum(`sales14d`) adSalesTotal");
            put("adSalesAvg", "avg(`sales14d`) adSalesAvg");
            put("adOrderNumTotal", "sum(`conversions14d`) adOrderNumTotal");
            put("adOrderNumAvg", "avg(`conversions14d`) adOrderNumAvg");
            put("adSalesNumTotal", "sum(`units_sold14d`) adSalesNumTotal");
            put("adSalesNumAvg", "avg(`units_sold14d`) adSalesNumAvg");
        }
    });

}
