package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 广告占比类型指标
 * <AUTHOR>
 * @date 2022/12/08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdMetricCompareDto {
    //广告花费
    private BigDecimal sumCost;
    //销售额
    private BigDecimal sumAdSale;
    //广告订单量
    private BigDecimal sumAdOrderNum;
    //广告销量
    private BigDecimal sumOrderNum;

    //对比时页面不会显示对比的占比值
    //广告花费
    private BigDecimal sumCompareCost;
    //销售额
    private BigDecimal sumCompareAdSale;
    //广告订单量
    private BigDecimal sumCompareAdOrderNum;
    //广告销量
    private BigDecimal sumCompareOrderNum;
}
