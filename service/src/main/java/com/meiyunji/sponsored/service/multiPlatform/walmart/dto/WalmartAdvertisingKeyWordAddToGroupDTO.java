package com.meiyunji.sponsored.service.multiPlatform.walmart.dto;

import java.util.List;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public class WalmartAdvertisingKeyWordAddToGroupDTO {

    private Long adGroupId;
    private List<KeyWord> keyWords;

    public static class KeyWord {
        private String state;
        private String keywordText;
        private String matchType;
        private Double bid;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getKeywordText() {
            return keywordText;
        }

        public void setKeywordText(String keywordText) {
            this.keywordText = keywordText;
        }

        public String getMatchType() {
            return matchType;
        }

        public void setMatchType(String matchType) {
            this.matchType = matchType;
        }

        public Double getBid() {
            return bid;
        }

        public void setBid(Double bid) {
            this.bid = bid;
        }
    }

    public Long getAdGroupId() {
        return adGroupId;
    }

    public void setAdGroupId(Long adGroupId) {
        this.adGroupId = adGroupId;
    }

    public List<KeyWord> getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(List<KeyWord> keyWords) {
        this.keyWords = keyWords;
    }
}
