package com.meiyunji.sponsored.service.cpc.constants;

import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
public enum AdTargetObjectTypeEnum {
    KEYWORD(1, "关键词"),
    ASIN(2, "ASIN"),
    CATEGORY(3, "类目"),
    BRAND(4, "品牌"),
    ;
    private int code;
    private String desc;

    AdTargetObjectTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static int getCodeByTargetType(String targetType) {
        if (TargetTypeEnum.category.name().equals(targetType)) {
            return CATEGORY.getCode();
        } else if (TargetTypeEnum.asin.name().equals(targetType)) {
            return ASIN.getCode();
        } else if (TargetTypeEnum.brand.name().equals(targetType)) {
            return BRAND.getCode();
        }
        return 0;
    }

    public static String getTargetTypeByCode(int code) {
        if (CATEGORY.getCode() == code) {
            return TargetTypeEnum.category.name();
        } else if (ASIN.getCode() == code) {
            return TargetTypeEnum.asin.name();
        } else if (BRAND.getCode() == code) {
            return TargetTypeEnum.brand.name();
        }
        return null;
    }

    public static List<String> TARGET_SUPPORT_TYPES = Arrays.asList(TargetTypeEnum.category.name(), TargetTypeEnum.asin.name());

    public static List<String> SP_NE_TARGET_SUPPORT_TYPES = Arrays.asList(TargetTypeEnum.brand.name(), TargetTypeEnum.asin.name());
    public static List<String> SB_NE_TARGET_SUPPORT_TYPES = Arrays.asList(TargetTypeEnum.brand.name(), TargetTypeEnum.asin.name());
    // 目前赛狐还不支持sd的品牌否定投放,但是产品侧决定忽略对这种情况的限制处理
    public static List<String> SD_NE_TARGET_SUPPORT_TYPES = Arrays.asList(TargetTypeEnum.brand.name(), TargetTypeEnum.asin.name());
}
