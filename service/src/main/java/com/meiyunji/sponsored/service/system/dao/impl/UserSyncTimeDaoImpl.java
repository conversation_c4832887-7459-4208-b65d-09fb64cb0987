package com.meiyunji.sponsored.service.system.dao.impl;


import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.system.dao.IUserSyncTimeDao;
import com.meiyunji.sponsored.service.system.po.UserSyncTime;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * UserSyncTime
 * <AUTHOR>
 */
@Repository
public class UserSyncTimeDaoImpl extends BaseDaoImpl<UserSyncTime> implements IUserSyncTimeDao {

    @Override
    public int updateSyncTime(Integer puid, Integer shopId, String mid, Integer type) {
        String sql = "UPDATE t_user_sync_time SET marketplace_id = ?, sync_last_time = NOW(), update_time = NOW() WHERE puid = ? AND shop_id = ? AND sync_type = ?";
        return getJdbcTemplate().update(sql, mid, puid, shopId, type);
    }

    @Override
    public int saveSyncTime(Integer puid, Integer shopId, String mid, Integer type) {
        String sql = "INSERT INTO t_user_sync_time(puid, shop_id, marketplace_id, sync_type, sync_last_time) VALUES(?,?,?,?,NOW())";
        return getJdbcTemplate().update(sql, puid, shopId, mid, type);
    }

    @Override
    public List<UserSyncTime> getUserSyncTime(Integer puid, Integer shopId, Integer type) {
        ConditionBuilder.Builder condition =  new ConditionBuilder.Builder();
        condition.equalTo("puid", puid);
        if (shopId != null) {
            condition.equalTo("shop_id", shopId);
        }
        if (type != null) {
            condition.equalTo("sync_type", type);
        }

        return listByCondition(condition.build());
    }

    @Override
    public int updateSyncTimeDate(Integer puid, Integer shopId, String mid, Integer type, Date lastDate) {
        String sql = "UPDATE t_user_sync_time SET marketplace_id = ?, sync_last_time = ?, update_time = NOW() WHERE puid = ? AND shop_id = ? AND sync_type = ?";
        return getJdbcTemplate().update(sql, mid,lastDate, puid, shopId, type);
    }

    @Override
    public int saveSyncTimeDate(Integer puid, Integer shopId, String mid, Integer type,Date lastDate) {
        String sql = "INSERT INTO t_user_sync_time(puid, shop_id, marketplace_id, sync_type, sync_last_time) VALUES(?,?,?,?,?)";
        return getJdbcTemplate().update(sql, puid, shopId, mid, type,lastDate);
    }
}