package com.meiyunji.sponsored.service.reportImport.processor;

import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazonaws.util.json.Jackson;
import com.beust.jcommander.internal.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdTargeting;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.BaseLxReport;
import com.meiyunji.sponsored.service.reportImport.model.LxAudienceReport;
import com.meiyunji.sponsored.service.reportImport.vo.CategoryVo;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import com.meiyunji.sponsored.service.reportImport.vo.ExpressionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * lx受众报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAudienceReportImportProcessor extends AbstractLxReportImportProcessor<LxAudienceReport> {

    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    private final IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;

    private final String audience = "audience";

    private final String purchases = "purchases";

    private final String views = "views";

    private final String  classification = "分类:";

    private final String  priceRange = "价格范围:";

    private final String brand = "品牌:";

    private final String score = "评分:";

    private final String distribution = "配送:";

    private final String star = "星";

    private final String asinReviewRatingLessThan = "asinReviewRatingLessThan:";

    private final String asinPriceGreaterThan = "asinPriceGreaterThan:";

    private final String asin = "asin";

    private final String lookbackLx = "回溯期:";

    private final String lookbackAm = "lookback";


    protected LxAudienceReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonSdAdTargetingDao amazonSdAdTargetingDao, IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao);
        this.amazonSdAdTargetingDao = amazonSdAdTargetingDao;
        this.amazonAdSdTargetingReportDao = amazonAdSdTargetingReportDao;
    }

    /**
     * 导入报告
     *
     * @param importMessage           导入消息
     * @param reports                 报告
     * @param shopAuthMap    店铺数据
     */
    @Override
    public void importReport(AdReportImportMessage importMessage, List<LxAudienceReport> reports, Map<String, ShopAuth> shopAuthMap) {
        Integer puid = importMessage.getPuid();
        Long taskId = importMessage.getScheduleId();

        List<Integer> shopIds = shopAuthMap.values().stream().map(ShopAuth::getId).collect(Collectors.toList());
        //按活动名称查询所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = listByTypeAndCampaignNames(puid, reports, shopIds);

        //记录重复活动
        Map<String, List<AmazonAdCampaignAll>> mapList = allCampaigns.stream()
                .collect(Collectors.groupingBy(k -> getCampaignKeyFormat(
                        k.getShopId(), k.getType(), k.getName())));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : mapList.entrySet()) {
            if (entry.getValue().size() > 1) {
                Map<Integer, ShopAuth> shopIdMap = shopAuthMap.values().stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
                Map<String, String> map = new HashMap<>();
                map.put("campaignName", entry.getValue().get(0).getName());
                map.put("country", Marketplace.fromId(shopIdMap.get(entry.getValue().get(0).getShopId()).getMarketplaceId()).getCountryCode());
                map.put("shopName", shopIdMap.get(entry.getValue().get(0).getShopId()).getName());
                throw new BizServiceException(ReportImportErrType.DUPLICATION_CAMPAIGN.name(), Jackson.toJsonString(map));             }
        }

        Map<String, AmazonAdCampaignAll> campaignNameMap = getCampaignNameMap(allCampaigns);
        Map<String, AmazonAdCampaignAll> campaignIdMap = getCampaignIdMap(allCampaigns);

        Map<String, List<LxAudienceReport>> lxAudienceReportMap = reports.stream().collect(
                Collectors.groupingBy(LxAudienceReport::getAdType));

        //填充campaignId,用于更精确查询ad-group
        lxAudienceReportMap.forEach((k, v) -> {
            fillCampaignIdForReport(puid, campaignNameMap, v);
        });


        Map<String, AmazonSdAdGroup> sdAdGroupMap = getSdAdGroupMap(puid, reports);

        //填充groupId,用于更精确查询
        lxAudienceReportMap.forEach((k, v) -> {
            for (LxAudienceReport report : v) {
                if (report.getCampaignId() == null) {
                    throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                }
                String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                if (!sdAdGroupMap.containsKey(mapKey)) {
                    throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                }
                AmazonSdAdGroup adGroup = sdAdGroupMap.get(mapKey);
                report.setGroupId(adGroup.getAdGroupId());

                //判断投放字符串判断类型
                if (report.getTarget().contains("您推广的商品")) {
                    report.setTargetType("exactProduct");
                }
                if (report.getTarget().contains("与您推广商品类似的商品")) {
                    report.setTargetType("similarProduct");
                }
                if (report.getTarget().contains("再营销浏览定向:分类:")) {
                    report.setTargetType("category");
                    //获取类目名称
                    String cName = report.getTarget().split(",")[0].split(":")[2];
                    report.setCategoryName(cName);
                }
                if(report.getTarget().contains("与推广商品相关")){
                    report.setTargetType("lookback");
                }
                if(report.getTarget().contains("亚马逊消费者:")){
                    report.setTargetType("audienceSameAs");
                }
            }
        });

        List<String> groupIds = reports.stream().map(BaseLxReport::getGroupId).collect(Collectors.toList());
        List<String> targetTypes = Lists.newArrayList("audienceSameAs","lookback","category","similarProduct","exactProduct");

        List<AmazonSdAdTargeting> targets = amazonSdAdTargetingDao.getByGroupIdsAndTargetTypes(puid, groupIds, targetTypes);
        Map<String, AmazonSdAdTargeting> targetingMap = targets.stream().collect(Collectors.toMap(k -> String.valueOf(k.getShopId())
                        .concat("#").concat(k.getAdGroupId()).concat("#").concat(getType(k.getResolvedExpression()))
                , Function.identity(), (a, b) -> a));
        log.info("import lxAudience map :{}", JSONUtil.objectToJson(targetingMap.keySet()));
        List<AmazonAdSdTargetingReport> sdReports = new ArrayList<>();
        for (LxAudienceReport report : reports) {
            String campaignMapKey = getCampaignKeyFormat(
                    report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignId());
            AmazonAdCampaignAll campaignAll = campaignIdMap.get(campaignMapKey);

            String mapKey = String.valueOf(report.getShopId())
                    .concat("#").concat(report.getGroupId()).concat("#").concat(getLxType(report.getTarget()));
            log.info("import lxAudience mapKey :{}", mapKey);
            if (!targetingMap.containsKey(mapKey)) {
                throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "投放不存在 (广告组名称: " + report.getAdGroupName() + "; 投放: "+ report.getTarget()+")");
            }
            AmazonSdAdTargeting amazonSdAdTargeting = targetingMap.get(mapKey);

            AmazonAdSdTargetingReport  sdTargetingReport = new AmazonAdSdTargetingReport();
            sdTargetingReport.setPuid(report.getPuid());
            sdTargetingReport.setShopId(report.getShopId());
            sdTargetingReport.setMarketplaceId(report.getMarketplaceId());
            sdTargetingReport.setCountDate(report.getSfCountDate());
            sdTargetingReport.setTacticType(campaignAll.getTactic());
            sdTargetingReport.setCurrency(Marketplace.fromId(report.getMarketplaceId()).getCurrencyCode().name());
            sdTargetingReport.setCampaignName(report.getCampaignName());
            sdTargetingReport.setCampaignId(report.getCampaignId());
            sdTargetingReport.setAdGroupName(report.getAdGroupName());
            sdTargetingReport.setAdGroupId(report.getGroupId());
            sdTargetingReport.setTargetId(amazonSdAdTargeting.getTargetId());
            sdTargetingReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
            sdTargetingReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
            sdTargetingReport.setConversions14dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
            sdTargetingReport.setSales14dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
            sdTargetingReport.setUnitsOrderedNewToBrand14d(isDxmNumeric(report.getUnitsOrderedNewToBrand14d()) ? Integer.valueOf(report.getUnitsOrderedNewToBrand14d()) : 0);
            sdTargetingReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
            sdTargetingReport.setCostType(campaignAll.getCostType());
            sdTargetingReport.setUnitsOrdered14d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
            sdTargetingReport.setConversions14d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
            sdTargetingReport.setSales14d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);

            sdTargetingReport.setViewImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.parseInt(report.getViewImpressions()) : 0);
            sdTargetingReport.setOrdersNewToBrand14d(isDxmNumeric(report.getOrdersNewToBrand14d()) ? Integer.parseInt(report.getOrdersNewToBrand14d()) : 0);
            sdTargetingReport.setSalesNewToBrand14d(isDxmNumeric(report.getSalesNewToBrand14d()) ? new BigDecimal(report.getSalesNewToBrand14d()) : BigDecimal.ZERO);
            sdTargetingReport.setUnitsOrderedNewToBrand14d(isDxmNumeric(report.getUnitsOrderedNewToBrand14d()) ? Integer.parseInt(report.getUnitsOrderedNewToBrand14d()) : 0);

            sdReports.add(sdTargetingReport);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdTargetingReportDao.insertOrUpdateList(puid, sdReports);
        }


    }

    private void fillCampaignIdForReport(Integer puid, Map<String, AmazonAdCampaignAll> campaignNameMap, List<LxAudienceReport> v) {
        for (LxAudienceReport report : v) {
            String mapKey = getCampaignKeyFormat(
                    report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignName());
            if (!campaignNameMap.containsKey(mapKey)) {
                throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
            }
            AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);
            report.setCampaignId(campaignAll.getCampaignId());
        }
    }

    private String getType(String expression) {
        List<ExpressionVo> expressionVoList = JSONUtil.jsonToArray(expression, ExpressionVo.class);
        if (CollectionUtils.isEmpty(expressionVoList)) {
            return "";
        }
        ExpressionVo expressionVo = expressionVoList.get(0);
        String type = expressionVo.getType();
        if (this.audience.equals(type)) {
            return type + "#" + expressionVo.getValue().get(0).getValue();
        } else {
            if (CollectionUtils.isNotEmpty(expressionVo.getValue())) {
                String valueType = expressionVo.getValue().get(0).getType();

                if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(valueType) || SpV3ExpressionEnum.asinCategorySameAs.getValue().equals(valueType)) {
                    return type + "#" + amzCategoryMatching(expressionVo.getValue());
                } else {
                    return type + "#" + expressionVo.getValue().get(0).getType();
                }
            } else {
                return type;
            }

        }
    }

    private String getLxType(String target) {
        if (target.contains("亚马逊消费者:")) {
            List<String> splitStr = StringUtil.splitStr(target, ":");
            return "audience"+"#"+splitStr.get(splitStr.size() - 1);
        } else if (target.contains("再营销浏览定向:")) {
            if (target.contains("再营销浏览定向:分类:")) {
                String replace = target.replace("再营销浏览定向:", "");
                return "views" + "#" + lxCategoryMatching(replace);
            } else {
                return target.contains("您推广的商品,") ? "views" + "#" + "exactProduct" : "views" + "#" + "similarProduct";
            }
        } else if (target.contains("购买再营销:")) {
            if (target.contains("购买再营销:分类:")) {
                String replace = target.replace("购买再营销:", "");
                return "purchases" + "#" + lxCategoryMatching(replace);
            } else {
                return target.contains("与推广商品相关,") ? "purchases" + "#" + "relatedProduct" : "purchases" + "#" + "exactProduct";
            }
        }
        return null;
    }


    private String lxCategoryMatching(String target) {
        List<String> segmentations = StringUtil.splitStr(target, StringUtil.SPLIT_COMMA);
        //分类
        String classification = "";
        //品牌
        String brand = "";
        //价格范围
        String priceRange = "";
        //配送
        String score = "";
        String asinReviewRatingLessThan = "";
        String asinPriceGreaterThan = "";
        String lookbook = "";

        for (String segmentation : segmentations) {
            if (segmentation.contains(this.classification)) {
                classification = segmentation.replace(this.classification, "").trim();
            } else if (segmentation.contains(this.priceRange)) {
                priceRange = segmentation.replace(this.priceRange, "").trim();
            } else if (segmentation.contains(this.brand)) {
                brand = segmentation.replace(this.brand, "").trim();
            } else if (segmentation.contains(this.score)) {
                score = segmentation.replace(this.score, "").replace(this.star, "").trim();
            } else if (segmentation.contains(this.asinPriceGreaterThan)) {
                asinPriceGreaterThan = segmentation.replace(this.asinPriceGreaterThan, "").trim();
            } else if (segmentation.contains(this.asinReviewRatingLessThan)) {
                asinReviewRatingLessThan = segmentation.replace(this.asinReviewRatingLessThan, "").trim();
            } else if (segmentation.contains(this.lookbackLx)) {
                lookbook = segmentation.replace(this.lookbackLx, "").replace("天","").trim();
            }

        }
        //固定顺序拼接
        return classification +"#"+ brand +"#"+ priceRange +"#"+ score +"#"+ asinPriceGreaterThan +"#"+ asinReviewRatingLessThan +"#"+ lookbook;
    }


    private String amzCategoryMatching(List<CategoryVo> categoryVos) {
        Map<String, String> categoryMap = null;
        if (CollectionUtils.isNotEmpty(categoryVos)) {
            categoryMap = categoryVos.stream().collect(Collectors.toMap(CategoryVo::getType, CategoryVo::getValue));
        }
        if (categoryMap == null) {
            return "";
        }
        String asinCategorySameAs = "";
        String asinBrandSameAs = "";
        String asinPriceBetween = "";
        String asinReviewRatingBetween = "";
        String asinPriceGreaterThan = "";
        String asinReviewRatingLessThan = "";
        String lookback = "";
        for (Map.Entry<String, String> entry : categoryMap.entrySet()) {
            if (ExpressionEnum.asinCategorySameAs.value().equals(entry.getKey())) {
                asinCategorySameAs = entry.getValue();
            } else if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(entry.getKey())) {
                asinCategorySameAs = entry.getValue();
            } else if (ExpressionEnum.asinBrandSameAs.value().equals(entry.getKey())) {
                asinBrandSameAs = entry.getValue();
            } else if (SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(entry.getKey())) {
                asinBrandSameAs = entry.getValue();
            } else if (ExpressionEnum.asinPriceBetween.value().equals(entry.getKey())) {
                asinPriceBetween = entry.getValue();
            } else if (SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(entry.getKey())) {
                asinPriceBetween = entry.getValue();
            } else if (ExpressionEnum.asinReviewRatingBetween.value().equals(entry.getKey())) {
                asinReviewRatingBetween = entry.getValue();
            } else if (SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(entry.getKey())) {
                asinReviewRatingBetween = entry.getValue();
            } else if (ExpressionEnum.asinPriceGreaterThan.value().equals(entry.getKey())) {
                asinPriceGreaterThan = entry.getValue();
            } else if (SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(entry.getKey())) {
                asinPriceGreaterThan = entry.getValue();
            } else if (ExpressionEnum.asinReviewRatingLessThan.value().equals(entry.getKey())) {
                asinReviewRatingLessThan = entry.getValue();
            } else if (SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(entry.getKey())) {
                asinReviewRatingLessThan = entry.getValue();
            } else if (this.lookbackAm.equals(entry.getKey())) {
                lookback = entry.getValue();
            }
        }

        return asinCategorySameAs.trim() +"#"+ asinBrandSameAs.trim()
                +"#"+ asinPriceBetween.trim() +"#"+ asinReviewRatingBetween.trim() +"#"+ asinPriceGreaterThan.trim()
                +"#"+ asinReviewRatingLessThan.trim() +"#"+ lookback.trim();
    }
}
