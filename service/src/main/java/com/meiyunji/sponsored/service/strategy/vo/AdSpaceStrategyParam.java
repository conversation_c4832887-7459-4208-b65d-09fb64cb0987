package com.meiyunji.sponsored.service.strategy.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import lombok.Data;

import java.util.List;

@Data
public class AdSpaceStrategyParam {
    //puid
    private Integer puid;
    //店铺id
    private Integer shopId;
    //站点id
    private String marketplaceId;
    //广告组合id
    private List<String> portfolioIds;
    //广告活动有效状态
    private String state;
    //广告活动名称
    private String campaignName;
    //当前页数
    private Integer pageNo;
    //每页查询条数
    private Integer pageSize;
    //位置
    private String predicate;
    //广告组合id
    private List<String> servingStatusList;
    //traceId
    private String traceId;

    public enum placementPredicateEnum implements BaseEnum {

        placementProductPage("placementProductPage","Detail Page on-Amazon","产品页面"),
        placementTop("placementTop","Top of Search on-Amazon","搜索结果顶部(首页)");
        ;
        private String code;
        private String content;
        private String desc;

        placementPredicateEnum(String code, String content, String desc) {
            this.code = code;
            this.content = content;
            this.desc = desc;
        }

        public String getContent() {
            return content;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return content;
        }
    }
}
