package com.meiyunji.sponsored.service.cpc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告产品明细dto
 *
 * @Author: zzh
 * @Date: 2024/9/26 16:20
 */
@Data
public class AdProductDetailDto implements Serializable {

    @ApiModelProperty("用户id")
    private Integer puid;

    @ApiModelProperty("店铺id")
    private Integer shopId;

    @ApiModelProperty("产品id")
    private Long id;

    @ApiModelProperty("组id")
    private String adGroupId;

    @ApiModelProperty("sku")
    private String sku;

    @ApiModelProperty("asin")
    private String asin;

    @ApiModelProperty("图片")
    private String mainImage;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("在线状态")
    private String onlineStatus;

    @ApiModelProperty("站点")
    private String marketplaceId;

    @ApiModelProperty("在线产品表是否存在")
    private Boolean exist = true;

}
