package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdCampaignReport;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.Date;
import java.util.List;

/**
 * Created by lm on 2021/5/14.
 *
 */
public interface IAmazonAdSdCampaignReportDao extends IBaseShardingDao<AmazonAdCampaignAllReport> {

    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdSdCampaignReport> list);

    Page getPageList(int puid, SearchVo search, Page page);

    Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param , Page page);

    AmazonAdCampaignAllReport getSumReportByCampaignId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    List<AmazonAdCampaignAllReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    AmazonAdCampaignAllReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String tacticType);

    List<AmazonAdCampaignAllReport> listSumReports(int puid, Integer shopId, String startDate, String endDate, List<String> campaignIds);

    List<AmazonAdCampaignAllReport> listReports(int puid, Integer shopId, String startDate, String endDate, String campaignId);

    /**
     * 统计指定时间段的数据
     * @param puid :
     * @param shopId :
     * @param start :
     * @param end :
     * @return
     */
    AmazonAdCampaignAllReport statByDateRange(int puid, Integer shopId, String start, String end);

    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getCampaignListByUpdateTime(Integer puid, Integer shopId, Date date);

    AmazonAdCampaignAllReport getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo);

    AmazonAdCampaignAllReport getDetailsSumVo(Integer puid, CampaignReportDetails detailsVo);

    List<AmazonAdCampaignAllReport> getListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo);
}
