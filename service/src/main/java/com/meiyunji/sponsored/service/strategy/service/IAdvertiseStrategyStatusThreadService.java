package com.meiyunji.sponsored.service.strategy.service;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.strategy.vo.AddStrategyVo;
import com.meiyunji.sponsored.service.strategy.vo.SubmitStrategyVo;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2023-07-27  09:25
 */
public interface IAdvertiseStrategyStatusThreadService {

    /**
     * 提交任务
     *
     * @param puid
     * @param submitStrategyVo
     * @param templateId
     * @param updateId
     * @param loginIp
     * @return
     */
    AddStrategyVo campaignSubmitStrategy(Integer puid, SubmitStrategyVo submitStrategyVo, Long templateId, Integer updateId, String loginIp);
}
