package com.meiyunji.sponsored.service.stream.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2023/12/1 9:12
 * @describe:
 */

@Getter
public enum AmazonStreamTaskTypeEnum {
    CAMPAIGN((byte)0,"<PERSON><PERSON>AIG<PERSON>","广告活动"),
    GROUP((byte)1,"GROUP","广告组"),
    AD((byte)2,"AD","广告产品")  ,
    TARGET((byte)3,"TARGET","商品投放"),
    KEYWORD((byte)4,"KEYWORD","关键词投放"),
    NE_TARGET((byte)5,"NE_TARGET","否定商品投放"),
    NE_KEYWORD((byte)6,"NE_KEYWORD","否定关键词投放")  ,
    CAMPAIGN_NE_TARGET((byte)7,"CAMPAIGN_NE_TARGET","广告活动否定商品"),
    CAMPAIGN_NE_KEYWORD((byte)8,"CAMPAIGN_NE_KEYWORD","广告活动否定关键词")  ,
    ;

    private Byte code;

    private String type;

    private String desc;


    AmazonStreamTaskTypeEnum(Byte code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    public static AmazonStreamTaskTypeEnum getAmazonStreamTaskTypeEnumByCode(Byte code) {
        for(AmazonStreamTaskTypeEnum en : AmazonStreamTaskTypeEnum.values()) {
            if(en.code.equals(code)) {
                return en;
            }
        }
        return null;
    }
}
