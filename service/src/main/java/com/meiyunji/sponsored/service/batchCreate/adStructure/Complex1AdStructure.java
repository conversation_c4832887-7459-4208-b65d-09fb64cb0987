package com.meiyunji.sponsored.service.batchCreate.adStructure;

import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewBaseInfoRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewBatchDataRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewVo;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreateSubmitRequest;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureBeanId;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-13  19:49
 */

/**
 * 精细化1广告结构：成熟产品高预算
 */

@Component(AdStructureBeanId.COMPLEX_1)
public class Complex1AdStructure extends AdStructure {

    /**
     * 精细化1广告结构json数据：成熟产品高预算
     * 也可以直接使用AdStructureCampaignTypeDto进行封装
     * 1个自动广告活动，活动下1个广告组(自动)
     * 1个手动广告活动，3个广告组，1个关键词投放广告组且为精确匹配，1个关键词投放广告组且为广泛匹配，1个关键词投放广告组且为词组匹配
     * 1个手动广告活动，2个广告组，1个商品投放广告组且为精确匹配，1个商品投放广告组且为拓展匹配
     *
     */
    private String complex1Json = "[{\"targetingType\":\"auto\",\"groupTypeList\":[{\"type\":\"auto\",\"matchType\":\"auto\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"keyword\",\"matchType\":\"exact\"},{\"type\":\"keyword\",\"matchType\":\"broad\"},{\"type\":\"keyword\",\"matchType\":\"phrase\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"targeting\",\"matchType\":\"asinSameAs\"},{\"type\":\"targeting\",\"matchType\":\"asinExpandedFrom\"}]}]";

    @PostConstruct
    @Override
    public void init() {
        STRUCTURE_JSON = complex1Json;
        super.init();
    }

    @Override
    public List<SpBatchCreatePreviewVo> generatePreview(List<SpBatchCreatePreviewBatchDataRequest> batchDataRequestList) {
        return super.generatePreview(batchDataRequestList);
    }

    @Override
    public boolean check(SpBatchCreateSubmitRequest request) {
        //return super.check(request);
        //2024.11.27：不需要check，由用户随便怎么造
        return true;
    }
    
}
