package com.meiyunji.sponsored.service.batchCreate.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchCampaignDao;
import com.meiyunji.sponsored.service.batchCreate.dto.campaign.CampaignInfoDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.group.GroupInfoInTaskDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.productView.AsinViewBatchSpListDTO;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */

@Repository
public class AmazonAdBatchCampaignDaoImpl extends BaseShardingDaoImpl<AmazonAdBatchCampaign> implements IAmazonAdBatchCampaignDao {

    @Override
    public void insertList(int puid, List<AmazonAdBatchCampaign> campaignList) {
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (id, puid, shop_id, profile_id, marketplace_id, task_id, portfolio_id, name, campaign_type, targeting_type, budget, start_date, end_date, strategy, adjustments_top, adjustments_product, adjustments_other, adjustments, tags, task_status, execute_count, next_retry_time, create_time, create_id) values ");
        List<Object> argsList = new ArrayList<>(campaignList.size());
        for (AmazonAdBatchCampaign campaign : campaignList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(campaign.getId());
            argsList.add(campaign.getPuid());
            argsList.add(campaign.getShopId());
            argsList.add(campaign.getProfileId());
            argsList.add(campaign.getMarketplaceId());
            argsList.add(campaign.getTaskId());
            argsList.add(campaign.getPortfolioId());
            argsList.add(campaign.getName());
            argsList.add(campaign.getCampaignType());
            argsList.add(campaign.getTargetingType());
            argsList.add(campaign.getBudget());
            argsList.add(campaign.getStartDate());
            argsList.add(campaign.getEndDate());
            argsList.add(campaign.getStrategy());
            argsList.add(campaign.getAdjustmentsTop());
            argsList.add(campaign.getAdjustmentsProduct());
            argsList.add(campaign.getAdjustmentsOther());
            argsList.add(campaign.getAdjustments());
            argsList.add(campaign.getTags());
            argsList.add(campaign.getTaskStatus());
            argsList.add(campaign.getExecuteCount());
            argsList.add(campaign.getNextRetryTime());
            argsList.add(campaign.getCreateTime());
            argsList.add(campaign.getCreateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateStatusAndRetryByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> idList, Byte status, Date nextRetryTime) {
        StringBuilder sb = new StringBuilder("update ");
        sb.append(getJdbcHelper().getTable());
        sb.append(" set task_status = ? , execute_count = execute_count + 1, next_retry_time = ?, update_time = now() where puid = ? and shop_id = ? and task_id = ? ");
        sb.append(" and id in (").append(StringUtils.join(idList, ",")).append(") ");
        sb.append(" and task_status in (0, 2) ");
        getJdbcTemplate(puid).update(sb.toString(), status, nextRetryTime, puid, shopId, taskId);
    }

    @Override
    public AsinViewBatchSpListDTO getCampaignAndTaskInfoById(Integer puid, Long id) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("select b.id as id, b.shop_id as shopId, b.marketplace_id as marketplaceId, b.name as campaignName, b.task_id as taskId, b.shop_id as shopId, ");
        selectSql.append(" b.create_time as createTime, b.create_id  as creatorId, b.task_status as taskStatus from t_amazon_ad_batch_campaign b ");
        StringBuilder whereSql = new StringBuilder(" where b.id = ? ");
        argsList.add(id);
        selectSql.append(whereSql);
        List<AsinViewBatchSpListDTO> result = getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), (row, i) -> AsinViewBatchSpListDTO.builder()
                .id(row.getLong("id"))
                .shopId(row.getInt("shopId"))
                .marketplaceId(row.getString("marketplaceId"))
                .campaignName(row.getString("campaignName"))
                .taskId(row.getLong("taskId"))
                .build());
        return Optional.of(result).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).map(r -> r.get(0)).orElse(null);
    }

    @Override
    public void updateErrTaskStatusByIdList(Integer puid, Map<Long, String> idErrMsgMap, boolean updateExecuteCount) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `err_msg` = ?, `task_status` = 3, `update_time` = now() ");
        if (updateExecuteCount) {
            sql.append(", `execute_count` = execute_count + 1");
        }
        sql.append(" where puid = ? and id = ? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (Map.Entry<Long, String> entry : idErrMsgMap.entrySet()) {
            batchArg = new Object[]{
                    entry.getValue(),
                    puid,
                    entry.getKey()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public void batchUpdateStatusByIdList(Integer puid, List<AmazonAdBatchCampaign> successBatchCampaignList, Byte status) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `task_status` = ?, `execute_count` = execute_count + 1, `amazon_campaign_id` = ?, `update_time` = now() ")
                .append(" where puid = ? and id = ? and task_status in (0, 2, 4)");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdBatchCampaign campaign : successBatchCampaignList) {
            batchArg = new Object[]{status, campaign.getAmazonCampaignId(), puid, campaign.getId()};
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<AmazonAdBatchCampaign> selectNeedSubmitAmazon(Integer puid, Integer shopId, List<Long> idList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("id", idList.toArray())
                .in("task_status", new Object[]{SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()});
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<CampaignInfoDTO> getCampaignListByTaskId(Integer puid, String marketplaceId,
                                                         Integer shopId, Long taskId) {
        StringBuilder sql = new StringBuilder("select id, name, targeting_type as type, strategy, budget, task_status, err_msg as errMsg from ");
        List<Object> argsList = Lists.newArrayList();
        sql.append(getJdbcHelper().getTable());
        sql.append(" where 1 = 1 ");
        if (Objects.nonNull(puid)) {
            sql.append(" and puid = ? ");
            argsList.add(puid);
        }
        if (Objects.nonNull(marketplaceId)) {
            sql.append(" and marketplace_id = ? ");
            argsList.add(marketplaceId);
        }
        if (Objects.nonNull(shopId)) {
            sql.append(" and shop_id = ? ");
            argsList.add(shopId);
        }
        if (Objects.nonNull(taskId)) {
            sql.append(" and task_id = ? ");
            argsList.add(taskId);
        }
        sql.append(" order by id desc");
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), (row, i) -> CampaignInfoDTO.builder()
                .id(row.getLong("id"))
                .name(row.getString("name"))
                .type(row.getString("type"))
                .strategy(row.getString("strategy"))
                .budget(row.getDouble("budget"))
                .taskStatus(row.getInt("task_status"))
                .errMsg(row.getString("errMsg"))
                .build());
    }

    @Override
    public Set<Byte> distinctStatusByTaskId(Integer puid, Integer shopId, Long taskId) {
        StringBuilder sql = new StringBuilder("select distinct task_status from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? and task_id = ?");
        List<Byte> taskStatus = getJdbcTemplate(puid).query(sql.toString(), (rs, rowNum) -> rs.getByte("task_status"), puid, shopId, taskId);
        return new HashSet<>(taskStatus);
    }

    @Override
    public int terminateCampaigns(Integer puid, Long taskId, Byte status,
                                  List<Byte> includeStatus, String errMsg) {
        if (Objects.isNull(taskId)) {
            return 0;
        }
        StringBuilder sql = new StringBuilder("UPDATE ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" set task_status = ?, err_msg = ?, update_time = now() ");
        StringBuilder whereSql = new StringBuilder(" where task_id = ? ");
        if (CollectionUtils.isNotEmpty(includeStatus)) {
            whereSql.append(" and task_status in (").append(StringUtils.join(includeStatus, ",")).append(") ");
        }
        sql.append(whereSql);
        return getJdbcTemplate(puid).update(sql.toString(), status, errMsg, taskId);
    }

    @Override
    public List<AmazonAdBatchCampaign> selectNeedRetryAmazon(Integer puid, Integer shopId, Long taskId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .in("task_status", new Object[]{SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()})
                .lessThan("next_retry_time", LocalDateTime.now());

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdBatchCampaign> listByTaskId(Integer puid, Integer shopId, Long taskId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId);
        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateStatusByIdBatch(Integer puid, Map<Long, Byte> campaignStatusMap) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `status` = ?, `update_time` = now() ")
                .append(" where puid = ? and id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (Map.Entry<Long, Byte> entry : campaignStatusMap.entrySet()) {
            batchArg = new Object[]{entry.getValue(), puid, entry.getKey()};
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdBatchCampaign> getCampaignBasicInfoByIds(Integer puid, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        StringBuilder sql = new StringBuilder(" SELECT id, name from ").append(getJdbcHelper().getTable());
        sql.append(" WHERE ");
        sql.append(" id in ( \"").append(StringUtils.join(ids, "\",\"")).append("\" ) ");
        return getJdbcTemplate(puid).query(sql.toString(), new Object[]{}, new ObjectMapper<>(AmazonAdBatchCampaign.class));
    }
}