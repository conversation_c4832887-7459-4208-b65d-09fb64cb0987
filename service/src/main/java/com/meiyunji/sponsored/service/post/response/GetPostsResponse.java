package com.meiyunji.sponsored.service.post.response;

import com.meiyunji.sponsored.common.base.Page;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 帖子列表页查询resp
 * @Author: heqiwen
 * @Date: 2025/03/31 16:33
 */
@Data
public class GetPostsResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    Page<Posts> page;

    @Data
    public static class Posts {
        /**
         * id
         */
        private Long id;
        /**
         * puid
         */
        private Integer puid;
        /**
         * 帖子配置id
         */
        private String postProfileId;

        /**
         * 帖子id
         */
        private String postId;

        /**
         * 图片或视频地址
         */
        private String mediaUrl;

        /**
         * 媒体类型
         */
        private String mediaType;

        /**
         * 媒体Id
         */
        private String mediaId;

        /**
         * 店铺id
         */
        private Integer shopId;

        /**
         * 店铺名称
         */
        private String shopName;

        /**
         * 帖子标题
         */
        private String caption;

        /**
         * 品牌名称
         */
        private String brandName;

        /**
         * asin信息
         */
        private List<String> asinList;

        /**
         * 发布时间
         */
        private String liveDate;

        /**
         * 可见展示次数
         */
        private String impressions;

        /**
         * 点击量
         */
        private String clicks;

        /**
         * 点击率
         */
        private String ctr;

        /**
         * 品牌旗舰点点击量
         */
        private String clicksToBrandStore;

        /**
         * 商品详情页点击量
         */
        private String clicksToDetailPage;

        /**
         * 关注量
         */
        private String clicksToFollow;
        /**
         * 触达用户数
         */
        private String reach;

        /**
         * 创建时间
         */
        private String postCreatedDate;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 备注
         */
        private String remark;

        /**
         * 状态
         */
        private String status;

        /**
         * 未获批准原因
         */
        private String statusMetadata;

        private String rejectionReasons;

        private Integer uid;
    }

}
