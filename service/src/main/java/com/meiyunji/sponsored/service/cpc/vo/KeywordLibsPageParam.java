package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 关键词列表页入参
 */
@Data
@ApiModel
public class KeywordLibsPageParam {

    @ApiModelProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty("商户ID")
    private Integer puid;
    @ApiModelProperty(value = "用户id")
    private Integer uid;
    @ApiModelProperty("uid下的所有店铺id")
    private List<Integer> shopIds;
    @ApiModelProperty(value = "标签id")
    private List<Integer> adTagIds;
    @ApiModelProperty(value = "关键词")
    private String keywordText;
    @ApiModelProperty("关键词添加来源")
    private String source;
    @ApiModelProperty("关键词类型：否定，非否定")
    private String targetType;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("开始时间")
    private String addStartDate;
    @ApiModelProperty("结束时间")
    private String addEndDate;
    @ApiModelProperty("开始时间")
    private String compareStartDate;
    @ApiModelProperty("结束时间")
    private String compareEndDate;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("desc asc")
    private String orderType;
    @ApiModelProperty("搜索类型 exact blur")
    private String searchType;
    @ApiModelProperty("asin")
    private String asin;
    @ApiModelProperty("站点ID")
    private String marketplaceId;
    @ApiModelProperty("查询内容")
    private String searchVal;
    @ApiModelProperty("查询广告组合id列表")
    private List<String> searchPortfolioList;
    @ApiModelProperty("查询广告活动id列表")
    private List<String> searchCampaignList;
    @ApiModelProperty("查询广告组id列表")
    private List<String> searchAdGroupList;
    @ApiModelProperty("查询匹配方式列表")
    private List<String> searchMatchTypeList;
    @ApiModelProperty("币种")
    private String to;
    @ApiModelProperty("报告数据来源站点")
    private List<String> country;
    @ApiModelProperty("报告数据来源店铺")
    private List<String> shopIdList;

    private List<String> keyTagText;

    private List<Integer> uidList = new ArrayList<>();
    private Integer searchUid;
    @ApiModelProperty("aba排名对应的站点id")
    private String site;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("搜索类型 exact blur")
    private String remarkSearchType;
    @ApiModelProperty("标签搜索类型")
    private int adTagQueryType;
    private int creator;
}
