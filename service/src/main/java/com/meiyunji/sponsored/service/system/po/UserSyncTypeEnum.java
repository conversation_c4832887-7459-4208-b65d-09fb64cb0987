package com.meiyunji.sponsored.service.system.po;

/**
 * 用户上次同步时间
 */
public enum UserSyncTypeEnum {

    AD_SP_MANAGE(300, "广告-SP广告管理"),
    AD_SB_MANAGE(301, "广告-SB广告管理"),
    AD_SD_MANAGE(302, "广告-SD广告管理"),
    AD_SP_REPORT(303, "广告-SP报告"),
    AD_SB_REPORT(304, "广告-SB报告"),
    AD_SD_REPORT(305, "广告-SD报告"),
    AD_HISTORY_CHANGE(306,"广告-历史变更记录"),
    AD_SD_CREATIVE(307, "广告-SD广告创意"),
    ;

    private Integer type;
    private String desc;

    UserSyncTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static UserSyncTypeEnum getByType(Integer type){
        for (UserSyncTypeEnum unitEnum : UserSyncTypeEnum.values()) {
            if (unitEnum.type.equals(type)) {
                return unitEnum;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
