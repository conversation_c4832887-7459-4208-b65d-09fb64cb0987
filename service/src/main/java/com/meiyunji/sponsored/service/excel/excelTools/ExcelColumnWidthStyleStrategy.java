package com.meiyunji.sponsored.service.excel.excelTools;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractHeadColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.List;

public class ExcelColumnWidthStyleStrategy extends AbstractHeadColumnWidthStyleStrategy {

    private Integer columnWidth;

    /**
     * 可以隐藏的列
     */
    private List <Integer> list ;

    public ExcelColumnWidthStyleStrategy(Integer columnWidth ,List<Integer> list) {
        this.columnWidth = columnWidth;
        this.list = list ;
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cell<PERSON><PERSON><PERSON>ist,
                                  Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        boolean needSetWidth = relativeRowIndex != null && (isHead || relativeRowIndex == 0);
        if (needSetWidth) {

            if(list!=null && list.contains(cell.getColumnIndex())){
                writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 0);
            }else {
                Integer width = this.columnWidth(head, cell.getColumnIndex());
                if (width != null) {
                    width = width * 256;
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), width);
                }
            }

        }
    }

    @Override
    protected Integer columnWidth(Head head, Integer integer) {
        return this.columnWidth;
    }
}
