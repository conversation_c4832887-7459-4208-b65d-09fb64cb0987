package com.meiyunji.sponsored.service.autoRule.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.autorule.status.CheckOperationVo;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.AutoRuleQueryKeyword;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;

import java.util.List;
import java.util.Set;

public interface AdvertiseAutoRuleStatusService {

    /**
     * 自动化规则查询广告活动
     *
     * @param param
     * @return
     */
    Result<Page<AmazonAdCampaignAll>> pageCampaignList(AdCampaignAutoRuleParam param);


    /**
     * 自动化规则查询投放
     *
     * @param adKeywordTargetAutoRuleParam
     * @return
     */
    Result<Page<AdKeywordTargetAutoRuleVo>> pageKeywordTargetList(AdKeywordTargetAutoRuleParam adKeywordTargetAutoRuleParam);

    /**
     * 自动化规则查询广告组
     *
     * @param adGroupAutoRuleParam
     * @return
     */
    Result<Page<AutoRuleAdGroup>> pageAdGroupList(AdGroupAutoRuleParam adGroupAutoRuleParam);

    /**
     * 自动化规则查询搜索词
     *
     * @param adQueryKeywordAutoRuleParam
     * @return
     */
    Result<Page<AutoRuleQueryKeyword>> pageAdQueryKeywordList(AdQueryKeywordAutoRuleParam adQueryKeywordAutoRuleParam);

    /**
     * 自动化规则查询广告目标商品
     *
     * @param adProductAutoRuleParam
     * @return
     */
    Result<Page<AmazonAdProduct>> getAdProductList(AdProductAutoRuleParam adProductAutoRuleParam);

    /**
     * 关键词卡位搜索关键词
     *
     * @param param
     * @return
     */
    Result<Page<AmazonAdKeyword>> getAdKeywordCardList(AdKeywordCardAutoRuleParam param);

    /**
     * 受控活动分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledCampaign(AutoRuleObjectParam param);

    /**
     * 受控关键词分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledTarget(AutoRuleObjectParam param);

    /**
     * 受控关键词分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledSearchQuery(AutoRuleObjectParam param);

    /**
     * 受控广告组分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledGroup(AutoRuleObjectParam param);

    /**
     * 受控关键词分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseAutoRuleStatus>> pageAdControlledKeywordCard(AutoRuleObjectParam param);

    /**
     * 提交任务
     *
     * @param puid
     * @param submitAutoRuleVos
     * @param templateId
     * @param updateId
     * @return
     */
    Result<List<AddAutoRuleVo>> submitAutoRule(Integer puid, List<SubmitAutoRuleVo> submitAutoRuleVos, Long templateId, AdvertiseAutoRuleTemplate vo,Integer updateId, String traceId);

    /**
     * 更新策略
     *
     * @param puid
     * @param templateId
     * @param updateAutoRuleVoList
     * @param vo
     * @return
     */
    Result<String> updateAutoRule(Integer puid, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, AdvertiseAutoRuleTemplate vo, String traceId);

    /**
     * 更新策略
     *
     * @param puid
     * @param templateId
     * @param updateAutoRuleVoList
     * @param vo
     * @return
     */
    Result<String> updateAutoRuleBid(Integer puid, Integer updateId, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, String traceId);

    /**
     * 更新状态
     *
     * @param puid
     * @param statusId
     * @param status
     * @param updateId
     * @param loginIp
     * @return
     */
    Result<String> updateAutoRuleStatus(Integer puid, Long statusId, String enableStatus, Integer updateId, String traceId, AdvertiseAutoRuleStatus autoRuleStatus);

    /**
     * 移除策略
     *
     * @param puid
     * @param removeAutoRuleVoList
     * @return
     */
    Result<String> removeAutoRule(Integer puid, List<RemoveAutoRuleVo> removeAutoRuleVoList, String traceId, List<Integer> authedShopIdList);

    /**
     * 归档记录删除
     */
    void removeArchiveRecord (Integer puid,Integer shopId,Long taskId);

    /**
     * 相似规则列表
     */
    Result<Page<SimilarRulePageVo>> pageSimilarRuleList(SimilarRulePageParam param);

    /**
     * 相似规则列表
     */
    Result<Page<SimilarRuleVo>> pageAllSimilarRuleList(Integer puid, Integer shopId,Long templateId, List<String> itemIdList, List<Long> excludedStatusIdList, Integer pageNo, Integer pageSize);

    /**
     * 查询广告组近7天不含当日的cpc数据接口
     */
    Result<String> queryGoalAdGroupCpc(Integer puid, Integer shopId,String marketplaceId,String adType, String adGroupId);

    void updateNextExecuteTime(int puid, int shopId, String itemType, List<UpdateNextExecuteTimeVo> nextExecuteTimeVoList);

    Result<CheckOperationVo> checkOperation4Grpc(int puid, List<Integer> shopIdList, long templateId, String ruleAction);

    CheckRuleIndexAndActionStatusVo checkOperation4Template(AdvertiseAutoRuleTemplate existTemplate, String ruleAction);

    CheckRuleIndexAndActionStatusVo checkOperationBase(AutoRuleItemTypeEnum itemType,
                                                Set<String> adTypeSet,
                                                Set<String> targetTypeSet,
                                                String ruleAction);

    CheckRuleIndexAndActionStatusVo checkTargetType(Integer puid, String itemId, Integer shopId, AutoRuleItemTypeEnum itemType, String adType, String targetType, String ruleAction);

    CheckRuleIndexAndActionStatusVo checkRuleIndexBase(List<String> ruleIndexList, Set<String> adTypeSet);

    /**
     * 分页获取受控对象规则列表
     *
     * @param param 请求参数
     * @return 响应参数
     */
    Page<AdAutoRuleStatusVo> pageAutoRuleStatus(PageAutoRuleStatusParam param);

    /**
     * 根据主键id获取受控对象记录
     *
     * @param puid 用户id
     * @param statusId 主键id
     * @return 响应参数
     */
    AdAutoRuleStatusVo getStatusByStatusId(int puid, Long statusId);

    /**
     * 编辑受控对象
     *
     * @param param 请求参数
     * @return 响应参数
     */
    Boolean editAutoRuleStatus(EditAutoRuleStatusParam param);

    /**
     * 添加受控对象校验受控对象是否合法
     *
     * @param param 请求参数
     * @return 响应参数
     */
    List<CheckItemTemplateVo> checkItemTemplate(CheckItemTemplateParam param);
}
