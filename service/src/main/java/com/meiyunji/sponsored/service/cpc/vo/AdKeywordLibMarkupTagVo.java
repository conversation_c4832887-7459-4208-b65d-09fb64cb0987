package com.meiyunji.sponsored.service.cpc.vo;


import com.meiyunji.sponsored.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AdKeywordLibMarkupTagVo {

    private Integer puid;
    private Integer uid;
    private String relationId;
    private String tagIdsStr;
    private List<Long> tagIds;
    private String error;

    public void setTagIdsList(String tagIdsStr){

        if(StringUtils.isNotBlank(tagIdsStr)){
            List<String> list = StringUtil.splitStr(tagIdsStr,",");
            if(CollectionUtils.isNotEmpty(list)){
                tagIds =  list.stream().filter(StringUtils::isNotBlank).map(Long::valueOf).collect(Collectors.toList());
            }
        }
    }

}
