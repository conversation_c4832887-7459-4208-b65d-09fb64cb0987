package com.meiyunji.sponsored.service.batchCreate.enums;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-13  18:27
 */
public class AdStructureBeanId {

    public static final String SIMPLE_1 = "simple1AdStructure";
    public static final String SIMPLE_2 = "simple2AdStructure";
    public static final String SIMPLE_3 = "simple3AdStructure";

    public static final String COMPLEX_1 = "complex1AdStructure";
    public static final String COMPLEX_2 = "complex2AdStructure";
    public static final String CUSTOM = "customAdStructure";

    public static final String COMPLEX_3 = "complex3AdStructure";

}
