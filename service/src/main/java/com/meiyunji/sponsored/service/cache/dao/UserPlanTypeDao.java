package com.meiyunji.sponsored.service.cache.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplatePuid;
import com.meiyunji.sponsored.service.cache.enums.PayPackTypeEnum;
import com.meiyunji.sponsored.service.cache.po.UserPlanType;

import java.util.List;

public interface UserPlanTypeDao extends IBaseDao<UserPlanType> {

    List<Integer> selectPuidByPlanType(PayPackTypeEnum type, List<Integer> puids);
}
