package com.meiyunji.sponsored.service.config;

import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.*;

/**
 * 新的kafka配置
 *
 * <AUTHOR>
 * @date 2023/07/21
 */
@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@Slf4j
public class ScheduleKafkaNewConfiguration {

    @Value("${spring.kafka.schedule-new.bootstrap-servers}")
    private String bootstrapServers;
    @Value("${spring.kafka.schedule-new.consumer.group-id}")
    private String groupId;
    @Value("${spring.kafka.schedule-new.consumer.enable-auto-commit}")
    private boolean enableAutoCommit;

    // kafka消费者配置
    @Bean("scheduleNewTaskFinishedConsumer")
    @ConditionalOnProperty(name = "spring.kafka.schedule-new.enabled", havingValue = "true")
    public KafkaConsumer ScheduleNewTaskFinishedConsumer(KafkaProperties kafkaProperties) {
        KafkaProperties.consumerProperties consumerProperties = kafkaProperties.getConsumers().get("sf-aadas-schedule-task");
        KafkaConsumer consumer = new KafkaConsumer(consumerConfigs());
        List<String> topics = Lists.newArrayList();
        topics.add(consumerProperties.getTopic());
        consumer.subscribe(topics);
        return consumer;
    }

    private Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 100);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 15000);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
        return props;
    }


}
