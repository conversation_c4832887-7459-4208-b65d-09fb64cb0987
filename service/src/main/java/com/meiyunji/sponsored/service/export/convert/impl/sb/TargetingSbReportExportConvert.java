package com.meiyunji.sponsored.service.export.convert.impl.sb;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.meiyunji.sponsored.service.export.AdReportExportExecutorHelper;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportDataVoExportConvert;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.enums.AdReportExportAdFormatEnum;
import com.meiyunji.sponsored.service.vo.AdSbTargetExportVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.SB_TARGETING)
public class TargetingSbReportExportConvert implements ReportDataVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.SB_TARGETING;
    }

    @Autowired
    private AdReportExportExecutorHelper exportExecutorHelper;

    @Override
    public void processExcelDataList(ReportDataVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdSbTargetExportVo vo = new AdSbTargetExportVo();
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            Date dailyDate = DateUtil.strToDate(reportVo.getCountDate(), "yyyyMMdd");
            vo.setDailyDate(DateUtil.dateToStrWithFormat(dailyDate, "yyyy-MM-dd"));
        }

        vo.setCampaignStartDate(reportVo.getCampaignStartDate());
        vo.setCampaignEndDate(reportVo.getCampaignEndDate());
        vo.setTargetState(reportVo.getState());

        vo.setMatchType(reportVo.getMatchType());
        vo.setPut(reportVo.getTargetingText());
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setAdGroupName(reportVo.getAdGroupName());
        vo.setCost(reportVo.getCost());
        vo.setImpressions(reportVo.getImpressions());
        vo.setClicks(reportVo.getClicks());
        vo.setCpc(reportVo.getCpc());
        vo.setClickRate(reportVo.getClickRate());
        vo.setSalesConversionRate(reportVo.getSalesConversionRate());
        vo.setAcos(reportVo.getAcos());
        vo.setRoas(reportVo.getRoas());
        vo.setAttributedConversions14d(reportVo.getAttributedConversions14d());
        vo.setAttributedConversions14dSameSKU(reportVo.getAttributedConversions14dSameSKU());
        vo.setAttributedSales14d(reportVo.getAttributedSales14d());
        vo.setAttributedSales14dSameSKU(reportVo.getAttributedSales14dSameSKU());
        vo.setAttributedSales14dOtherSameSKU(reportVo.getAttributedSales14dOtherSameSKU());
        if (AdReportExportAdFormatEnum.video.getAdFormat().equals(reportVo.getAdFormat())) {
            vo.setAttributedOrdersNewToBrand14d(reportVo.getAttributedOrdersNewToBrand14d());
            vo.setAttributedOrdersNewToBrandPercentage14d(reportVo.getAttributedOrdersNewToBrandPercentage14d());
            vo.setAttributedSalesNewToBrand14d(reportVo.getAttributedSalesNewToBrand14d());
            vo.setAttributedSalesNewToBrandPercentage14d(reportVo.getAttributedSalesNewToBrandPercentage14d());
            vo.setAttributedUnitsOrderedNewToBrand14d(reportVo.getAttributedUnitsOrderedNewToBrand14d());
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(reportVo.getAttributedUnitsOrderedNewToBrandPercentage14d());
            vo.setVctr(reportVo.getVctr());
            vo.setVideo5SecondViewRate(reportVo.getVideo5SecondViewRate());
            vo.setVideo5SecondViews(reportVo.getVideo5SecondViews());
            vo.setVideoFirstQuartileViews(reportVo.getVideoFirstQuartileViews());
            vo.setVideoMidpointViews(reportVo.getVideoMidpointViews());
            vo.setVideoThirdQuartileViews(reportVo.getVideoThirdQuartileViews());
            vo.setVideoUnmutes(reportVo.getVideoUnmutes());
            vo.setViewableImpressions(reportVo.getViewableImpressions());
            vo.setVideoCompleteViews(reportVo.getVideoCompleteViews());
            vo.setVtr(reportVo.getVtr());

            //“品牌新买家”销量百分比
            if (reportVo.getAttributedUnitsOrderedNewToBrand14d() != null && reportVo.getAttributedUnitsOrdered14d() != null) {
                BigDecimal bigDecimal = exportExecutorHelper.calculationRateBigDecimal(BigDecimal.valueOf(reportVo.getAttributedUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(reportVo.getAttributedUnitsOrdered14d()), true);
                vo.setAttributedUnitsOrderedNewToBrandPercentage14d(bigDecimal.doubleValue());
            } else {
                vo.setAttributedUnitsOrderedNewToBrandPercentage14d(BigDecimal.ZERO.doubleValue());
            }
        }
        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shop.getMarketplaceId());
            if (null != m) {
                vo.setCurrency(m.getCurrencyCode());
            }
        }
        //广告销量
        vo.setAdSalesNumTotal(reportVo.getAttributedUnitsOrdered14d());

        //品牌新买家订单转化率=品牌新买家订单量除以广告点击量
        BigDecimal calculationRate = exportExecutorHelper.calculationRateBigDecimal(BigDecimal.valueOf(reportVo.getAttributedOrdersNewToBrand14d()), BigDecimal.valueOf(reportVo.getClicks()), true);
        vo.setAttributedOrderRateNewToBrand14d(calculationRate.doubleValue());

        vo.setAttributedOrdersNewToBrand14d(reportVo.getAttributedOrdersNewToBrand14d());

        //品牌新买家订单百分比
        if (reportVo.getAttributedOrdersNewToBrand14d() != null && reportVo.getAttributedConversions14d() != null) {
            BigDecimal rateBigDecimal = exportExecutorHelper.calculationRateBigDecimal(BigDecimal.valueOf(reportVo.getAttributedOrdersNewToBrand14d()), BigDecimal.valueOf(reportVo.getAttributedConversions14d()), true);
            vo.setAttributedOrdersNewToBrandPercentage14d(rateBigDecimal.doubleValue());
        } else {
            vo.setAttributedOrdersNewToBrandPercentage14d(BigDecimal.ZERO.doubleValue());
        }


        vo.setAttributedSalesNewToBrand14d(reportVo.getAttributedSalesNewToBrand14d());

        //“品牌新买家”销售额百分比
        if (reportVo.getAttributedSalesNewToBrand14d() != null && reportVo.getAttributedSales14d() != null) {
            BigDecimal bigDecimal = exportExecutorHelper.calculationRateBigDecimal(reportVo.getAttributedSalesNewToBrand14d(), reportVo.getAttributedSales14d(), true);
            vo.setAttributedSalesNewToBrandPercentage14d(bigDecimal.doubleValue());
        } else {
            vo.setAttributedSalesNewToBrandPercentage14d(BigDecimal.ZERO.doubleValue());
        }
        list.add(vo);
    }
}
