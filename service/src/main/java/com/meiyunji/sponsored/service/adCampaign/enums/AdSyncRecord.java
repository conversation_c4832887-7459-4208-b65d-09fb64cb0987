package com.meiyunji.sponsored.service.adCampaign.enums;

/**
 * @author: zhuluku<PERSON>
 * @email: <EMAIL>
 * @date: 2023-06-15  18:49
 */
public class AdSyncRecord {
    public enum TriggerStatusEnum {
        DOING(0),  // 执行中
        DONE(1);  // 已完成
        private int status;
        TriggerStatusEnum(int status) {
            this.status = status;
        }
        public int getStatus() {
            return status;
        }
    }

    public enum TriggerChannelEnum {
        USER_TRIGGER(1),  // 用户端触发同步
        SCHEDULE_TRIGGER(2);  // 定时调度任务触发同步
        private int channel;
        TriggerChannelEnum(int channel) {
            this.channel = channel;
        }
        public int getChannel() {
            return channel;
        }
    }

    public enum AdChannelEnum {
        USER_TRIGGER(1, "用户手动触发同步"),
        SCHEDULE_TRIGGER(2, "任务调度触发同步");
        private int channelType;
        private String channelName;
        AdChannelEnum(int channelType, String channelName) {
            this.channelType = channelType;
            this.channelName = channelName;
        }
        public int getChannelType() {
            return channelType;
        }

        public String getChannelName() {
            return channelName;
        }
        public static AdChannelEnum fromModuleType(int channelType) {
            AdChannelEnum[] var1 = values();
            for (AdChannelEnum c : var1) {
                if (c.channelType == channelType) {
                    return c;
                }
            }
            return null;
        }
    }

    public enum AdTypeEnum {
        SP(1),  // sp广告
        SB(2),  // sb广告
        SD(3),  // sd广告
        SHOP(4), // 店铺粒度
        PUID(5); // 用户粒度

        private int type;
        AdTypeEnum(int type) {
            this.type = type;
        }
        public int getType() {
            return type;
        }
    }

    public enum AdModuleTypeEnum {
        // SP广告
        SP_PORTFOLIO(AdTypeEnum.SP.getType(), 10),
        SP_CAMPAIGNS(AdTypeEnum.SP.getType(), 11),
        SP_AD_GROUPS(AdTypeEnum.SP.getType(), 12),
        SP_KEYWORDS(AdTypeEnum.SP.getType(), 13),
        SP_CAMPAIGN_NE_KEYWORDS(AdTypeEnum.SP.getType(), 14),
        SP_NE_KEYWORD(AdTypeEnum.SP.getType(), 15),
        SP_TARGETING(AdTypeEnum.SP.getType(), 16),
        SP_NE_TARGETING(AdTypeEnum.SP.getType(), 17),
        SP_ADS(AdTypeEnum.SP.getType(), 18),
        SP_CAMPAIGN_NE_TARGETING(AdTypeEnum.SP.getType(), 19),

        // SB广告
        SB_CAMPAIGNS_REPLENISH(AdTypeEnum.SB.getType(), 20),
        SB_CAMPAIGNS(AdTypeEnum.SB.getType(), 21),
        SB_AD_GROUPS(AdTypeEnum.SB.getType(), 22),
        SB_NE_KEYWORDS(AdTypeEnum.SB.getType(), 23),
        SB_KEYWORDS(AdTypeEnum.SB.getType(), 24),
        SB_TARGETS(AdTypeEnum.SB.getType(), 25),
        SB_NE_TARGETS(AdTypeEnum.SB.getType(), 26),
        SB_ADS(AdTypeEnum.SB.getType(), 27),
        SB_CAMPAIGNS_V4(AdTypeEnum.SB.getType(), 28),
        SB_CAMPAIGNS_AD_FORMAT(AdTypeEnum.SB.getType(), 29),

        // SD广告
        SD_CAMPAIGNS(AdTypeEnum.SD.getType(), 31),
        SD_ADGROUP(AdTypeEnum.SD.getType(), 32),
        SD_TARGETING(AdTypeEnum.SD.getType(), 33),
        SD_NE_TARGETING(AdTypeEnum.SD.getType(), 34),
        SD_PRODUCT_ADS(AdTypeEnum.SD.getType(), 35);

        private int adType;
        private int moduleType;
        AdModuleTypeEnum(int adType, int moduleType) {
            this.adType = adType;
            this.moduleType = moduleType;
        }
        public int getAdType() {
            return adType;
        }

        public int getModuleType() {
            return moduleType;
        }
        public static AdModuleTypeEnum fromModuleType(int moduleType) {
            AdModuleTypeEnum[] var1 = values();
            for (AdModuleTypeEnum c : var1) {
                if (c.moduleType == moduleType) {
                    return c;
                }
            }
            return null;
        }
    }


}
