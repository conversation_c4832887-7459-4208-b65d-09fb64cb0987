package com.meiyunji.sponsored.service.aadrasGrpcApi.autoRule.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.api.entry.RuleSchedulePb;
import com.meiyunji.sellfox.aadras.api.enumeration.*;
import com.meiyunji.sellfox.aadras.api.service.*;
import com.meiyunji.sellfox.aadras.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AadrasStubConfig;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AbstractAdAutoRuleApi;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleCallAadrasGrayHelper;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleConsistencyDto;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleJson;
import com.meiyunji.sponsored.service.autoRule.vo.PerformOperationJson;
import com.meiyunji.sponsored.service.autoRule.vo.TimeRuleJson;
import com.meiyunji.sponsored.service.kafka.AutoRuleConsistencyMessageKafkaProducer;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class AdGroupSearchQueryTaskScheduleApi extends AbstractAdAutoRuleApi {

    @Autowired
    private AadrasStubConfig aadrasStubConfig;

    @Autowired
    private AutoRuleConsistencyMessageKafkaProducer autoRuleConsistencyMessageKafkaProducer;

    @Autowired
    private AutoRuleCallAadrasGrayHelper autoRuleCallAadrasGrayHelper;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    protected AdGroupSearchQueryTaskScheduleApi(ShopAuthServiceImpl shopAuthService, ManagedChannel aadrasApiManagedChannel) {
        super(shopAuthService, aadrasApiManagedChannel);
    }

    @Override
    public boolean checkValid(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType advertiseRuleTaskType) {
        return advertiseRuleTaskType == AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY;
    }

    @Override
    public void setAutoRuleTask(Long taskId, AdvertiseAutoRuleStatus advertiseAutoRuleStatus, ShopAuth shopAuth) throws Exception {
        String adType = advertiseAutoRuleStatus.getAdType();
        String groupId = advertiseAutoRuleStatus.getItemId();
        SetSearchQueryRuleTaskScheduleRequestPb.SetSearchQueryRuleTaskScheduleRequest.Builder
                builder = SetSearchQueryRuleTaskScheduleRequestPb.SetSearchQueryRuleTaskScheduleRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        if ("SB".equals(adType)) {
            builder.setGroupType(GroupTypePb.GroupType.group_keyword);
        } else {
            if ("keyword".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_keyword);
            } else if ("auto".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_auto);
            } else if ("targeting".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_targeting);
            }
        }
        builder.setTaskId(taskId);
        builder.setPuid(advertiseAutoRuleStatus.getPuid());
        builder.setShopId(advertiseAutoRuleStatus.getShopId());
        builder.setProfileId(advertiseAutoRuleStatus.getProfileId());
        builder.setTemplateId(advertiseAutoRuleStatus.getTemplateId());
        builder.setRuleType(advertiseAutoRuleStatus.getRuleType());
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RuleSchedulePb.RuleSearchQueryTaskSchedule.Builder taskScheduler = RuleSchedulePb.RuleSearchQueryTaskSchedule.newBuilder();
        taskScheduler.setGroupId(groupId);
        taskScheduler.setCampaignId(advertiseAutoRuleStatus.getCampaignId());
        taskScheduler.setExecuteType(RuleExecuteTypePb.RuleExecuteType.valueOf(advertiseAutoRuleStatus.getExecuteType()));
        List<AutoRuleJson> autoRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getRule(),AutoRuleJson.class);
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getPerformOperation(),PerformOperationJson.class);
        autoRuleJsonList.forEach(e->{
            RuleSchedulePb.RuleSearchQueryTaskSchedule.RuleItem.Builder ruleItem = RuleSchedulePb.RuleSearchQueryTaskSchedule.RuleItem.newBuilder();
            ruleItem.setDay(e.getDay());
            if (e.getExcludeDay() != null) {
                ruleItem.setExcludeDay(e.getExcludeDay());
            } else {
                ruleItem.setExcludeDay(0);
            }
            ruleItem.setRuleIndex(RuleIndexTypePb.RuleIndexType.valueOf(e.getRuleIndex()));
            ruleItem.setRuleOperatorType(RuleOperatorTypePb.RuleOperatorType.valueOf(e.getRuleOperator()));
            if (StringUtils.isNotBlank(e.getRuleStatisticalModeType())) {
                ruleItem.setRuleStatisticalModeType(RuleStatisticalModeTypePb.RuleStatisticalModeType.valueOf(e.getRuleStatisticalModeType()));
            }
            ruleItem.setRuleValue(e.getRuleValue());
            if (StringUtils.isNotBlank(e.getAfterRuleValue())) {
                ruleItem.setAfterRuleValue(e.getAfterRuleValue());
            }
            taskScheduler.addRuleItem(ruleItem.build());
        });
        performOperationJsonList.forEach(e->{
            RuleSchedulePb.RuleSearchQueryTaskSchedule.PerformOperation.Builder performOperation = RuleSchedulePb.RuleSearchQueryTaskSchedule.PerformOperation.newBuilder();
            performOperation.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(e.getRuleAction()));
            performOperation.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(e.getRuleAdjust()));
            if (StringUtils.isNotBlank(e.getExpressionType())) {
                performOperation.setExpressionType(e.getExpressionType());
            }
            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getTargetAdType())) {
                performOperation.setTargetAdType(advertiseAutoRuleStatus.getTargetAdType());
            }
            if (StringUtils.isNotBlank(e.getNegativeType())) {
                performOperation.setNegativeType(NegativeTypePb.NegativeType.valueOf(e.getNegativeType()));
            }
            if ("1".equals(e.getAppointAdGroupType()) && StringUtils.isNotBlank(e.getAdGroupId())) {
                performOperation.setTargetAdGroupId(e.getAdGroupId());
                performOperation.setTargetCampaignId(e.getCampaignId());
                if ("SB".equals(adType)) {
                    performOperation.setGroupType(GroupTypePb.GroupType.group_keyword);
                } else {
                    if ("keyword".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_keyword);
                    } else if ("auto".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_auto);
                    } else if ("targeting".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_targeting);
                    }
                }
            } else {
                performOperation.setGroupType(builder.getGroupType());
                performOperation.setTargetAdGroupId(groupId);
                performOperation.setTargetCampaignId(advertiseAutoRuleStatus.getCampaignId());
            }
            if ("adjustFixed".equals(e.getBidType())) {
                performOperation.setBiddingType(BiddingTypePb.BiddingType.selfDefineBiddingPrice);
                if (StringUtils.isNotBlank(e.getBidValue())) {
                    performOperation.setBiddingPrice(e.getBidValue());
                }
            } else if ("defaultbid".equals(e.getBidType())) {
                performOperation.setBiddingType(BiddingTypePb.BiddingType.defaultAdGroupBiddingPrice);
                if (advertiseAutoRuleStatus.getDefaultBid() != null) {
                    performOperation.setBiddingPrice(advertiseAutoRuleStatus.getDefaultBid().toString());
                }
            }
            if (StringUtils.isNotBlank(e.getMatchType())) {
                performOperation.setMatchType(MatchTypePb.MatchType.valueOf(e.getMatchType()));
            }
            taskScheduler.addPerformOperation(performOperation.build());
        });
        if (advertiseAutoRuleStatus.getStartDate() != null) {
            taskScheduler.setStartDate(advertiseAutoRuleStatus.getStartDate().format(DateTimeFormatter.ISO_DATE));
        }
        if (advertiseAutoRuleStatus.getEndDate() != null) {
            taskScheduler.setEndDate(advertiseAutoRuleStatus.getEndDate().format(DateTimeFormatter.ISO_DATE));
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getSetRelation())) {
            taskScheduler.setSetRelation(advertiseAutoRuleStatus.getSetRelation());
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getExecuteTimeSpaceUnit())) {
            taskScheduler.setExecuteTimeSpaceUnit(advertiseAutoRuleStatus.getExecuteTimeSpaceUnit());
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getExecuteTimeSpaceValue())) {
            taskScheduler.setExecuteTimeSpaceValue(advertiseAutoRuleStatus.getExecuteTimeSpaceValue());
        }
        List<TimeRuleJson> timeRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getTimeRule(),TimeRuleJson.class);
        if (CollectionUtils.isNotEmpty(timeRuleJsonList)) {
            timeRuleJsonList.forEach(e -> {
                RuleSchedulePb.RuleSearchQueryTaskSchedule.TimeRuleItem.Builder builder2 = RuleSchedulePb.RuleSearchQueryTaskSchedule.TimeRuleItem.newBuilder();
                builder2.setSiteDate(e.getSiteDate());
                builder2.setStartTimeSite(e.getStartTimeSite());
                builder2.setEndTimeSite(e.getEndTimeSite());
                taskScheduler.addTimeRuleItem(builder2.build());
            });
        }
        builder.setTaskScheduler(taskScheduler.build());
        SetSearchQueryRuleTaskScheduleRequestPb.SetSearchQueryRuleTaskScheduleRequest request = builder.build();
        if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
            try {
                log.info("自动化规则-广告组搜索词提交请求参数: {}", request);
                AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                SetSearchQueryRuleTaskScheduleResponsePb.SetSearchQueryRuleTaskScheduleResponse response =
                        stub.setSearchQueryRuleTaskSchedule(request);
            } catch (StatusRuntimeException e) {
                throw GrpcExceptionUtil.unWrapException(e);
            }
        } else {
            try {
                if (!autoRuleCallAadrasGrayHelper.isGray(advertiseAutoRuleStatus.getPuid())) {
                    log.info("自动化规则-广告组搜索词提交请求参数: {}", request);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    SetSearchQueryRuleTaskScheduleResponsePb.SetSearchQueryRuleTaskScheduleResponse response =
                            stub.setSearchQueryRuleTaskSchedule(request);
                }
            } catch (Exception e) {
                log.error("异步更新受控对象异常", e);
            } finally {
                String jsonMessage = ProtoBufUtil.toJsonStr(request);
                AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(advertiseAutoRuleStatus.getShopId(),
                        AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name(), false, false, jsonMessage);
                log.info("异步更新受控对象, 发送异步消息, message: {}", consistencyDto);
                autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
            }
        }
    }

    @Override
    public void removeAutoRuleTask(Long taskId, String targetType, String itemStatus, ShopAuth shopAuth) throws Exception {
        RemoveSearchQueryRuleTaskScheduleRequestPb.RemoveSearchQueryRuleTaskScheduleRequest.Builder builder = RemoveSearchQueryRuleTaskScheduleRequestPb.RemoveSearchQueryRuleTaskScheduleRequest.newBuilder();
        builder.setPuid(shopAuth.getPuid());
        builder.setShopId(shopAuth.getId());
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setTaskId(taskId);
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RemoveSearchQueryRuleTaskScheduleRequestPb.RemoveSearchQueryRuleTaskScheduleRequest request = builder.build();
        if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
            try {
                log.info("自动化规则-广告组搜索词删除请求参数: {}", request);
                AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                RemoveSearchQueryRuleTaskScheduleResponsePb.RemoveSearchQueryRuleTaskScheduleResponse response =
                        stub.removeSearchQueryRuleTaskSchedule(request);
            } catch (StatusRuntimeException e) {
                throw GrpcExceptionUtil.unWrapException(e);
            }
        } else {
            try {
                if (!autoRuleCallAadrasGrayHelper.isGray(shopAuth.getPuid())) {
                    log.info("自动化规则-广告组搜索词删除请求参数: {}", request);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    RemoveSearchQueryRuleTaskScheduleResponsePb.RemoveSearchQueryRuleTaskScheduleResponse response =
                            stub.removeSearchQueryRuleTaskSchedule(request);
                }
            } catch (Exception e) {
                log.error("异步删除受控对象异常", e);
            } finally {
                String jsonMessage = ProtoBufUtil.toJsonStr(request);
                AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(shopAuth.getId(),
                        AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name(), true, false, jsonMessage);
                log.info("异步删除受控对象, 发送异步消息, message: {}", consistencyDto);
                autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
            }
        }
    }

    @Override
    public void executeRuleTask(Integer puid, Integer shopId, String marketplaceId, Long recordId, Long taskId, AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType advertiseRuleTaskType) throws Exception {
        ExecuteSearchQueryRuleTaskRequestPb.ExecuteSearchQueryRuleTaskRequest.Builder builder = ExecuteSearchQueryRuleTaskRequestPb.ExecuteSearchQueryRuleTaskRequest.newBuilder();
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(shopId,puid);
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setSearchQueryTaskRecordId(recordId);
        builder.setTaskId(taskId);
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        ExecuteSearchQueryRuleTaskRequestPb.ExecuteSearchQueryRuleTaskRequest request = builder.build();
        try {
            log.info("自动化规则-广告组搜索词记录请求参数: {}", request);
            AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
            ExecuteSearchQueryRuleTaskResponsePb.ExecuteSearchQueryRuleTaskResponse response = stub.executeSearchQueryRuleTask(request);
        } catch (StatusRuntimeException e) {
            throw GrpcExceptionUtil.unWrapException(e);
        }
    }

    @Override
    public void batchSetAutoRuleTask(List<AdvertiseAutoRuleStatus> statusList, ShopAuth shopAuth) throws Exception {
        //计算切割大小，避免超过kafka限制
        int kafkaMaxSize = dynamicRefreshNacosConfiguration.getAutoruleCallAadrasUseKafkaConsistencyMaxRequestSize() * 1024 * 1024;
        int thresholdPercent = dynamicRefreshNacosConfiguration.getAutoruleCallAadrasUseKafkaMessageRequestSizeThresholdPercent();
        int threshold = kafkaMaxSize * Math.min(Math.max(thresholdPercent, 50), 80) / 100; // x%阈值，预留(100-x)%其他字段和json分隔符等
        BatchSetSearchQueryRuleTaskScheduleRequestPb.SearchQueryRuleTaskSchedule task = searchQueryRuleTaskScheduleDataAssembly(shopAuth, statusList.get(0));
        int singleLength = ProtoBufUtil.toJsonStr(task).getBytes(StandardCharsets.UTF_8).length;
        int batchSize = threshold / singleLength;
        List<List<AdvertiseAutoRuleStatus>> partitionList = Lists.partition(statusList, batchSize);
        log.info("自动化规则批量提交广告组搜索词, 受控对象总数量: {}, kafka max request size byte: {}, 首个受控对象byte: {}, 一批受控对象数量: {},拆分批数: {}",
                statusList.size(), kafkaMaxSize, singleLength, batchSize, partitionList.size());

        for (List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList : partitionList) {
            BatchSetSearchQueryRuleTaskScheduleRequestPb.BatchSetSearchQueryRuleTaskScheduleRequest.Builder request = BatchSetSearchQueryRuleTaskScheduleRequestPb.BatchSetSearchQueryRuleTaskScheduleRequest.newBuilder();
            for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : advertiseAutoRuleStatusList) {
                request.addSearchQueryRuleTaskSchedule(searchQueryRuleTaskScheduleDataAssembly(shopAuth, advertiseAutoRuleStatus));
            }

            BatchSetSearchQueryRuleTaskScheduleRequestPb.BatchSetSearchQueryRuleTaskScheduleRequest requestMsg = request.build();
            if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
                try {
                    log.info("自动化规则批量提交广告组搜索词: {}", requestMsg);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    BatchSetSearchQueryRuleTaskScheduleResponsePb.BatchSetSearchQueryRuleTaskScheduleResponse response =
                            stub.batchSetSearchQueryRuleTaskSchedule(requestMsg);
                } catch (StatusRuntimeException e) {
                    throw GrpcExceptionUtil.unWrapException(e);
                }
            } else {
                try {
                    if (!autoRuleCallAadrasGrayHelper.isGray(shopAuth.getPuid())) {
                        log.info("自动化规则批量提交广告组搜索词: {}", requestMsg);
                        AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                        BatchSetSearchQueryRuleTaskScheduleResponsePb.BatchSetSearchQueryRuleTaskScheduleResponse response =
                                stub.batchSetSearchQueryRuleTaskSchedule(requestMsg);
                    }
                } catch (Exception e) {
                    log.error("自动化规则批量提交广告组搜索词异常", e);
                } finally {
                    String jsonMessage = ProtoBufUtil.toJsonStr(requestMsg);
                    AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(shopAuth.getId(),
                            AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name(), false, true, jsonMessage);
                    log.info("批量提交受控对象, 发送异步消息, message: {}", consistencyDto);
                    try {
                        autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
                    } catch (Exception e) {
                        log.info("批量提交受控对象, 发送异步消息异常", e);
                    }
                }
            }
        }
    }

    private BatchSetSearchQueryRuleTaskScheduleRequestPb.SearchQueryRuleTaskSchedule searchQueryRuleTaskScheduleDataAssembly (ShopAuth shopAuth, AdvertiseAutoRuleStatus advertiseAutoRuleStatus) {
        String adType = advertiseAutoRuleStatus.getAdType();
        String groupId = advertiseAutoRuleStatus.getItemId();
        BatchSetSearchQueryRuleTaskScheduleRequestPb.SearchQueryRuleTaskSchedule.Builder
                builder = BatchSetSearchQueryRuleTaskScheduleRequestPb.SearchQueryRuleTaskSchedule.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        if ("SB".equals(adType)) {
            builder.setGroupType(GroupTypePb.GroupType.group_keyword);
        } else {
            if ("keyword".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_keyword);
            } else if ("auto".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_auto);
            } else if ("targeting".equals(advertiseAutoRuleStatus.getGroupType())) {
                builder.setGroupType(GroupTypePb.GroupType.group_targeting);
            }
        }
        builder.setTaskId(advertiseAutoRuleStatus.getTaskId());
        builder.setPuid(advertiseAutoRuleStatus.getPuid());
        builder.setShopId(advertiseAutoRuleStatus.getShopId());
        builder.setProfileId(advertiseAutoRuleStatus.getProfileId());
        builder.setTemplateId(advertiseAutoRuleStatus.getTemplateId());
        builder.setRuleType(advertiseAutoRuleStatus.getRuleType());
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RuleSchedulePb.RuleSearchQueryTaskSchedule.Builder taskScheduler = RuleSchedulePb.RuleSearchQueryTaskSchedule.newBuilder();
        taskScheduler.setGroupId(groupId);
        taskScheduler.setCampaignId(advertiseAutoRuleStatus.getCampaignId());
        taskScheduler.setExecuteType(RuleExecuteTypePb.RuleExecuteType.valueOf(advertiseAutoRuleStatus.getExecuteType()));
        List<AutoRuleJson> autoRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getRule(),AutoRuleJson.class);
        List<PerformOperationJson> performOperationJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getPerformOperation(),PerformOperationJson.class);
        autoRuleJsonList.forEach(e->{
            RuleSchedulePb.RuleSearchQueryTaskSchedule.RuleItem.Builder ruleItem = RuleSchedulePb.RuleSearchQueryTaskSchedule.RuleItem.newBuilder();
            ruleItem.setDay(e.getDay());
            if (e.getExcludeDay() != null) {
                ruleItem.setExcludeDay(e.getExcludeDay());
            } else {
                ruleItem.setExcludeDay(0);
            }
            ruleItem.setRuleIndex(RuleIndexTypePb.RuleIndexType.valueOf(e.getRuleIndex()));
            ruleItem.setRuleOperatorType(RuleOperatorTypePb.RuleOperatorType.valueOf(e.getRuleOperator()));
            if (StringUtils.isNotBlank(e.getRuleStatisticalModeType())) {
                ruleItem.setRuleStatisticalModeType(RuleStatisticalModeTypePb.RuleStatisticalModeType.valueOf(e.getRuleStatisticalModeType()));
            }
            ruleItem.setRuleValue(e.getRuleValue());
            if (StringUtils.isNotBlank(e.getAfterRuleValue())) {
                ruleItem.setAfterRuleValue(e.getAfterRuleValue());
            }
            taskScheduler.addRuleItem(ruleItem.build());
        });
        performOperationJsonList.forEach(e->{
            RuleSchedulePb.RuleSearchQueryTaskSchedule.PerformOperation.Builder performOperation = RuleSchedulePb.RuleSearchQueryTaskSchedule.PerformOperation.newBuilder();
            performOperation.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(e.getRuleAction()));
            performOperation.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(e.getRuleAdjust()));
            if (StringUtils.isNotBlank(e.getExpressionType())) {
                performOperation.setExpressionType(e.getExpressionType());
            }
            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getTargetAdType())) {
                performOperation.setTargetAdType(advertiseAutoRuleStatus.getTargetAdType());
            }
            if (StringUtils.isNotBlank(e.getNegativeType())) {
                performOperation.setNegativeType(NegativeTypePb.NegativeType.valueOf(e.getNegativeType()));
            }
            if ("1".equals(e.getAppointAdGroupType()) && StringUtils.isNotBlank(e.getAdGroupId())) {
                performOperation.setTargetAdGroupId(e.getAdGroupId());
                performOperation.setTargetCampaignId(e.getCampaignId());
                if ("SB".equals(adType)) {
                    performOperation.setGroupType(GroupTypePb.GroupType.group_keyword);
                } else {
                    if ("keyword".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_keyword);
                    } else if ("auto".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_auto);
                    } else if ("targeting".equals(advertiseAutoRuleStatus.getTarGroupType())) {
                        performOperation.setGroupType(GroupTypePb.GroupType.group_targeting);
                    }
                }
            } else {
                performOperation.setGroupType(builder.getGroupType());
                performOperation.setTargetAdGroupId(groupId);
                performOperation.setTargetCampaignId(advertiseAutoRuleStatus.getCampaignId());
            }
            if ("adjustFixed".equals(e.getBidType())) {
                performOperation.setBiddingType(BiddingTypePb.BiddingType.selfDefineBiddingPrice);
                if (StringUtils.isNotBlank(e.getBidValue())) {
                    performOperation.setBiddingPrice(e.getBidValue());
                }
            } else if ("defaultbid".equals(e.getBidType())) {
                performOperation.setBiddingType(BiddingTypePb.BiddingType.defaultAdGroupBiddingPrice);
                if (advertiseAutoRuleStatus.getDefaultBid() != null) {
                    performOperation.setBiddingPrice(advertiseAutoRuleStatus.getDefaultBid().toString());
                }
            }
            if (StringUtils.isNotBlank(e.getMatchType())) {
                performOperation.setMatchType(MatchTypePb.MatchType.valueOf(e.getMatchType()));
            }
            taskScheduler.addPerformOperation(performOperation.build());
        });
        if (advertiseAutoRuleStatus.getStartDate() != null) {
            taskScheduler.setStartDate(advertiseAutoRuleStatus.getStartDate().format(DateTimeFormatter.ISO_DATE));
        }
        if (advertiseAutoRuleStatus.getEndDate() != null) {
            taskScheduler.setEndDate(advertiseAutoRuleStatus.getEndDate().format(DateTimeFormatter.ISO_DATE));
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getSetRelation())) {
            taskScheduler.setSetRelation(advertiseAutoRuleStatus.getSetRelation());
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getExecuteTimeSpaceUnit())) {
            taskScheduler.setExecuteTimeSpaceUnit(advertiseAutoRuleStatus.getExecuteTimeSpaceUnit());
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getExecuteTimeSpaceValue())) {
            taskScheduler.setExecuteTimeSpaceValue(advertiseAutoRuleStatus.getExecuteTimeSpaceValue());
        }
        List<TimeRuleJson> timeRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getTimeRule(),TimeRuleJson.class);
        if (CollectionUtils.isNotEmpty(timeRuleJsonList)) {
            timeRuleJsonList.forEach(e -> {
                RuleSchedulePb.RuleSearchQueryTaskSchedule.TimeRuleItem.Builder builder2 = RuleSchedulePb.RuleSearchQueryTaskSchedule.TimeRuleItem.newBuilder();
                builder2.setSiteDate(e.getSiteDate());
                builder2.setStartTimeSite(e.getStartTimeSite());
                builder2.setEndTimeSite(e.getEndTimeSite());
                taskScheduler.addTimeRuleItem(builder2.build());
            });
        }
        builder.setTaskScheduler(taskScheduler.build());
        return builder.build();
    }
}
