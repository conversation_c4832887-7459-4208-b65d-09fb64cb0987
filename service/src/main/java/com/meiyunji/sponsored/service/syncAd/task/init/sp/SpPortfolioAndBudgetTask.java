package com.meiyunji.sponsored.service.syncAd.task.init.sp;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.impl.AmazonAdProfileApiService;
import com.meiyunji.sponsored.service.cpc.service2.impl.AmazonAdPortfolioApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.service.IAmazonAdShopDataInitTaskService;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sp + ShopDataSyncConstant.portfolioBudget)
public class SpPortfolioAndBudgetTask extends AdShopDataSyncTask {


    @Autowired
    private IAmazonAdShopDataInitTaskService amazonAdShopDataInitTaskService;

    @Autowired
    private AmazonAdPortfolioApiService portfolioApiService;

    @Autowired
    private AmazonAdProfileApiService profileApiService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sp);
        setTaskType(ShopDataSyncTaskTypeEnum.PORTFOLIO_BUDGET);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步广告组合
        portfolioApiService.syncPortfolio(shop, profile, null, true);
        //同步预算
        profileApiService.getProfile(shop, profile, true);
    }
}
