package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AllCampaignOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.dto.CampaignAndReportSearchDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdShopReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;


public interface IAmazonAdCampaignAllReportDao extends IBaseShardingSphereDao<AmazonAdCampaignAllReport> {

    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdCampaignAllReport> list);

    /**
     * 插入doris
     */
    void insertDorisList(List<AmazonAdCampaignAllReport> list);

    AmazonAdCampaignAllReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String type);

    List<AmazonAdCampaignAllReport> getSumDailyReportByDateRange(Integer puid, Integer shopId, String marketplaceId, String start, String end, String type);

    List<AmazonAdCampaignAllReport> getSumByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate,String type);

    Page getPageList(Integer puid, SearchVo search, Page page);

    List<AmazonAdCampaignAllReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds);

    List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> campaignIds, boolean isAggregation);

    AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param);

    AdMetricDto getCampaignPageSumMetricDataByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList);

    List<AdHomePerformancedto> getReportByDate(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param);

    List<AdHomePerformancedto> getReportByDate(Integer puid, Integer shopId, String startStr, String endStr, CampaignPageParam param, boolean isLatest);

    List<AdHomePerformancedto> getReportBySearchParam(Integer puid, CampaignAndReportSearchDTO searchParam);

    List<AdHomePerformancedto> getReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList);

    List<AdReportData> getAllReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList);

    List<CampaignInfoPageVo> getReportByCampaignIds(Integer puid, CampaignPageParam param, List<String> campaignIdList);

    List<AdHomePerformancedto> getSpReportByCampaignIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList, String type);

    Page sdDetailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    AmazonAdCampaignAllReport getSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    List<AmazonAdCampaignAllReport> getSumReportByAllCampaignIds(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> campaignIds);

    List<AdHomePerformancedto> getAllDayReportByAllCampaignIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> campaignIdList);

    List<AmazonAdCampaignAllReport> listReports(Integer puid, Integer shopId, String startStr, String endStr, String campaignId, String type);

    List<AmazonAdCampaignAllReport> listPlacementReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    List<AmazonAdCampaignAllReport> listPlacementReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId, String placement);

    List<AmazonAdCampaignAllReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId, String type);

    Page getSdPageList(Integer puid, SearchVo search, Page page);

    Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    AmazonAdCampaignAllReport getDetailInfo(Integer puid, Integer shopId, String marketplaceId, String campaignId);

    AmazonAdCampaignAllReport statByDateRange(Integer puid, Integer shopId, String start, String end, String type);

    AmazonAdCampaignAllReport getSdSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    List<AmazonAdCampaignAllReport> getSdChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    AmazonAdCampaignAllReport getSdSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String tacticType);

    List<AmazonAdCampaignAllReport> sdListSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> campaignIds);

    List<AmazonAdCampaignAllReport> sdListReports(Integer puid, Integer shopId, String startDate, String endDate, String campaignId);

    AmazonAdCampaignAllReport sdStatByDateRange(Integer puid, Integer shopId, String start, String end);

    List<String> getSdCampaignListByUpdateTime(Integer puid, Integer shopId, Date date);

    AmazonAdCampaignAllReport getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo);

    AmazonAdCampaignAllReport getSdReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo);

    AmazonAdCampaignAllReport getDetailsSumVo(Integer puid, CampaignReportDetails detailsVo);

    AmazonAdCampaignAllReport getSdDetailsSumVo(Integer puid, CampaignReportDetails detailsVo);

    List<AmazonAdCampaignAllReport> getListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo);

    AmazonAdCampaignAllReport getSbSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String adFormat);

    List<AmazonAdCampaignAllReport> getSbTaskSumReport(Integer puid, Integer shopId, String startDate, String endDate);

    AmazonAdCampaignAllReport getSbSumReportByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate);

    AmazonAdCampaignAllReport getSbSumReportByDateAndType(Integer puid, Integer shopId, String type, String startDate, String endDate);

    List<AmazonAdCampaignAllReport> getSbVideoReportSumByDateAndType(Integer puid, Integer shopId, String type, String startDate, String endDate);

    Page getSbPageList(Integer puid, SearchVo search, Page page);

    Page sbDetailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    List<AmazonAdCampaignAllReport> getSbChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    AmazonAdCampaignAllReport getSbSumReportByCampaignId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    AmazonAdCampaignAllReport sbStatByDateRange(Integer puid, Integer shopId, String start, String end);

    List<AmazonAdCampaignAllReport> sbListReports(Integer puid, Integer shopId, String startDate, String endDate, String campaignId);

    List<AmazonAdCampaignAllReport> sbListSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> campaignIds);

    List<AmazonAdCampaignAllReport> getSdListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo);

    List<AmazonAdCampaignAllReport> getReportByCampaignId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String campaignId, String type);

    List<AmazonAdShopReport> getNewHomeCpcData(Integer puid, List<Integer> shopIds, String countDate);

    List<AmazonAdCampaignAllReport> getReportByCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList);

    List<AmazonAdCampaignAllReport> getReportByCampaignIdListAll(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList);

    List<String> getDiagnoseCountCampaignId(DiagnoseCountParam param);

    public List<String> getValidRecordByDate(int puid, int shopId, String marketId, String startDate, List<String> campaignIds);

    List<AmazonAdCampaignAllReport> getFirstPlaceIsByCampaignId(int puid, int shopId, String marketId, String startDate, String endDate, String campaignId, String adType);

    List<AmazonAdCampaignAllReport> getFirstPlaceIsByCampaignId(int puid, List<Integer> shopIdList, String startDate, String endDate, List<String> campaignId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<AmazonAdCampaignAllReport> getSdSumReportGroupByCountDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate, String tacticType);

    List<AmazonAdCampaignAllReport> getSbSumReportGroupByCountDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate, String adFormat);

    List<String> getCampaignIdListByParam(Integer puid, CampaignPageParam param);

    List<AllCampaignOrderBo> getCampaignIdAndIndexList(Integer puid, CampaignPageParam param);

    List<AllCampaignOrderBo> getCampaignIdAndIndexList(Integer puid, CampaignPageParam param, boolean isLatest, boolean isUseAdvanced);

    List<AdHomePerformancedto> getReportDataByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList);

    List<AmazonAdCampaignAllReport> getShopCost(Integer puid, List<Integer> shopIds, String reportDate, String type);

    List<AmazonAdCampaignAllReport> getShopCampaignOrderByCostLimit(Integer puid, List<Integer> shopIds, String reportDate, String type);

    List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopId, List<String> campaignIdList, Set<String> fields, String startDate, String endState);

    List<SponsoredIndexCampaignData> listSponsoredIndexPortfolio(Integer puid, List<Integer> shopId, List<String> campaignIdList, Set<String> fields, String startDate, String endState);

    /**
     * 查询某种类型广告在某个时间段内的活动指标汇总
     *
     * @param shopList       shopList
     * @param adType         广告类型
     * @param startCountDate 开始时间
     * @param endCountDate   结束时间
     * @return 汇总指标
     */
    List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopList, AdTypeEnum adType, String startCountDate, String endCountDate);

    /**
     * 查询币种为空的数据
     */
    List<Long> listNoCurrencyData(Integer puid, Integer shopId, String marketplaceId, String countDay);

    /**
     * 根据Id 更新币种
     */
    void updateCurrencyByIds(Integer puid, List<Long> ids, String currency);
}
