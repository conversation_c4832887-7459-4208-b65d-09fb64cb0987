package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdCampaignDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdCampaign;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageParam;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by lm on 2021/7/29.
 */
@Repository
public class AmazonSbAdCampaignDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignAll> implements IAmazonSbAdCampaignDao {


    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;


    @Override
    public void batchAdd(int puid, List<AmazonSbAdCampaign> list,boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonSbAdCampaign campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(campaign.getMarketplaceId());
            arg.add(campaign.getProfileId());
            arg.add(campaign.getName());
            arg.add(campaign.getCampaignId());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getAdFormat());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getBrandEntityId());
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getCreative());
            arg.add(campaign.getLandingPage());
            arg.add(campaign.getCreateInAmzup());
            arg.add(campaign.getCreateId());
            arg.add(campaign.getUpdateId());
            argList.add(arg.toArray());
        }

        StringBuilder sql = new StringBuilder();
        sql.append("insert into t_amazon_ad_campaign_sb (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`name`,`campaign_id`,");
        sql.append("`budget`,`budget_type`,`start_date`,`end_date`,`ad_format`,`state`,`serving_status`,");
        if (syncHistory) {
            sql.append("out_of_budget_time,");
            sql.append("sync_out_of_budget_time_state,");
        }
        sql.append("`brand_entity_id`,`portfolio_id`,`bid_optimization`,`bid_multiplier`,`creative`,`landing_page`,`create_in_amzup`,`create_id`,");
        sql.append( "`update_id`,`create_time`,`update_time`) values (?,?, ?,?, ?,?, ?,?, ?,?, ?, ");
        if (syncHistory) {
            sql.append("?,");
            sql.append("?,");
        }
        sql.append("?,?, ?,?, ?,?, ?,?, ?,?,?, now(), now())");

        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSbAdCampaign> list ,boolean syncHistory) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonSbAdCampaign campaign : list) {
            if (campaign.getPuid() == null || campaign.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(campaign.getName());
            arg.add(campaign.getBudget());
            arg.add(campaign.getBudgetType());
            arg.add(campaign.getStartDate());
            arg.add(campaign.getEndDate());
            arg.add(campaign.getState());
            arg.add(campaign.getServingStatus());
            if (syncHistory) {
                arg.add(campaign.getOutOfBudgetTime());
                arg.add(campaign.getSyncOutOfBudgetTimeState());
            }
            arg.add(campaign.getPortfolioId());
            arg.add(campaign.getBidOptimization());
            arg.add(campaign.getBidMultiplier());
            arg.add(campaign.getCreative());
            arg.add(campaign.getLandingPage());
            arg.add(campaign.getUpdateId());
            arg.add(campaign.getPuid());
            arg.add(campaign.getShopId());
            arg.add(campaign.getCampaignId());
            argList.add(arg.toArray());
        }
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_sb` set `name` =?,`budget` =?,`budget_type`=?,`start_date`=?,`end_date`=?,`state`=?,`serving_status`=?,");
        if (syncHistory) {
            sql.append("`out_of_budget_time`=?,");
            sql.append("`sync_out_of_budget_time_state`=?,");
        }
        sql.append("`portfolio_id`=?,`bid_optimization`=?,`bid_multiplier`=?,`creative`=?, `landing_page`=?,update_id=?, update_time=now() where `puid` =? and `shop_id`=? and `campaign_id`=?");
        getJdbcTemplate(puid).batchUpdate(sql.toString(), argList);
    }

    @Override
    public void batchUpdateCampaignTargetType(Integer puid, List<AmazonAdCampaignAll> amazonSbAdCampaign) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdCampaignAll amazonAdGroup : amazonSbAdCampaign) {
            batchArg = new Object[]{
                    amazonAdGroup.getTargetType(),
                    amazonAdGroup.getId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_campaign_all` set `target_type` = ?, `update_time`=now() where id=?", batchArgs);
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String name) {
        return amazonAdCampaignAllDao.exist(puid,shopId,name, CampaignTypeEnum.sb.getCampaignType());
    }

    @Override
    public List<AmazonAdCampaignAll> listByCampaignId(int puid, Integer shopId, List<String> campaignIds) {
        return amazonAdCampaignAllDao.listByCampaignId(puid,shopId,campaignIds,CampaignTypeEnum.sb.getCampaignType());

    }

    @Override
    public AmazonAdCampaignAll getByCampaignId(int puid, Integer shopId, String campaignId) {
        return amazonAdCampaignAllDao.getByCampaignId(puid,shopId,null,campaignId,CampaignTypeEnum.sb.getCampaignType());
    }


    @Override
    public List<AmazonAdCampaignAll> listNoCampaignTargetType(Integer puid, Integer shopId, String campaignId) {
        return amazonAdCampaignAllDao.listNoCampaignTargetType(puid,shopId,campaignId,CampaignTypeEnum.sb.getCampaignType());
    }

    @Override
    public List<AmazonAdCampaignAll> getByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds) {
        return amazonAdCampaignAllDao.getByCampaignIds(puid,shopId,null,campaignIds,CampaignTypeEnum.sb.getCampaignType());
    }



    @Override
    public String getCreativeByCampaignId(Integer puid, Integer shopId, String campaignId) {
        return amazonAdCampaignAllDao.getSbCreativeByCampaignId(puid,shopId,campaignId);

    }



    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String campaignId, LocalDate localDate) {
        amazonAdCampaignAllDao.updateDataUpdateTime(puid,shopId,campaignId,localDate,CampaignTypeEnum.sb.getCampaignType());
    }

    @Override
    public List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, String portfolioId) {
        return amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid,shopId,portfolioId,CampaignTypeEnum.sb.getCampaignType(), null, null);

    }


    @Override
    public List<String> getPortfolioListByCampaignIds(Integer puid, Integer shopId, List<String> campaignId) {
        return amazonAdCampaignAllDao.getPortfolioListByCampaignIds(puid,shopId,campaignId,CampaignTypeEnum.sb.getCampaignType());
    }

}
