package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

@Data
@DbTable(value = "ods_t_ad_manage_tag")
public class OdsAdManageTag extends BasePo {
    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    //com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum
    @DbColumn(value = "type")
    private Integer type;

    @DbColumn(value = "group_id")
    private Long groupId;

    @DbColumn(value = "name")
    private String name;

    @DbColumn(value = "color")
    private String color;

    @DbColumn(value = "permission_type")
    private Integer permissionType;

    @DbColumn(value = "sort")
    private Integer sort;

    @DbColumn(value = "definition")
    private String definition;

    @DbColumn(value = "remark")
    private String remark;

    @DbColumn(value = "del_flag")
    private Integer delFlag;

    @DbColumn(value = "create_id")
    private Integer createId;

    @DbColumn(value = "update_id")
    private Integer updateId;

    public String toBaseString() {
        return "OdsAdManageTag{" +
                "id=" + id +
                ", puid=" + puid +
                ", type=" + type +
                ", groupId=" + groupId +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", permissionType=" + permissionType +
                ", sort=" + sort +
                ", delFlag=" + delFlag +
                ", createId=" + createId +
                ", updateId=" + updateId +
                "} " + super.toString();
    }
}
