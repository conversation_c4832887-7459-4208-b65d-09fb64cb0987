package com.meiyunji.sponsored.service.reportImport.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 导入广告报告任务表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DbTable("t_cpc_reports_import_task")
public class CpcReportsImportTask implements Serializable {

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    /**
    * 商户Id
    */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
    * 状态
    */
    @DbColumn(value = "status")
    private String status;

    /**
     * 错误类型(按产品需求先把错误任务上)
     */
    @DbColumn(value = "err_type")
    private String errType;

    /**
     * 错误信息json(按产品需求先把错误任务上)
     */
    @DbColumn(value = "err_info")
    private String errInfo;


    /**
     * 报告类型
     */
    @DbColumn(value = "report_type")
    private String reportType;

    /**
    * 数据来源
    */
    @DbColumn(value = "data_source")
    private String dataSource;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_at")
    private LocalDateTime createAt;

    /**
     * 最后更新时间
     */
    @DbColumn(value = "last_update_at")
    private LocalDateTime lastUpdateAt;
}