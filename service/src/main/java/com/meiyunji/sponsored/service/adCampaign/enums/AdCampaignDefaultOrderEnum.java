package com.meiyunji.sponsored.service.adCampaign.enums;

import com.alibaba.druid.support.spring.stat.SpringStatUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: ys
 * @date: 2024/2/4 9:30
 * @describe:
 */
@Getter
public enum AdCampaignDefaultOrderEnum {
    CAMPAIGN_NAME(1, "name", "name"),
    CAMPAIGN_DAILY_BUDGET(2, "dailyBudget", "budget"),
    CAMPAIGN_START_DATE(3, "startDate", "start_date"),
    CAMPAIGN_END_DATE(4, "endDate", "end_date")
    ;
    private int code;
    private String orderKey;
    private String orderField;

    AdCampaignDefaultOrderEnum(int code, String orderKey, String orderField) {
        this.code = code;
        this.orderKey = orderKey;
        this.orderField = orderField;
    }

    public static AdCampaignDefaultOrderEnum getAdCampaignDefaultOrderEnumByKey(String orderKey) {
        if (StringUtils.isEmpty(orderKey)) {
            return null;
        }
        for(AdCampaignDefaultOrderEnum en :AdCampaignDefaultOrderEnum.values()) {
            if (en.getOrderKey().equals(orderKey)) {
                return en;
            }
        }
        return null;
    }
}
