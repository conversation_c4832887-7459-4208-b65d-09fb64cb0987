package com.meiyunji.sponsored.service.grabRankings.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/8/16 14:29
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PostcodeBatchResult {
    private int code;
    private String msg;
    private List<PostcodeData> data;
}
