package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDiagnoseDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductDiagnose;
import com.meiyunji.sponsored.service.enums.BooleanStatusEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.DeleteDiagnoseReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.ListDiagnoseDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.ListDiagnoseReqVo;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */
@Repository
public class AmazonAdProductDiagnoseDaoImpl extends BaseShardingDaoImpl<AmazonAdProductDiagnose> implements IAmazonAdProductDiagnoseDao {


    @Override
    public int saveDiagnose(AmazonAdProductDiagnose diagnose) {
        String sql = "insert into " + getJdbcHelper().getTable() + " (puid, uid, module_type, filter_field, position_status, is_delete, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?, ?)";
        Object[] args = {diagnose.getPuid(), diagnose.getUid(), diagnose.getModuleType(), diagnose.getFilterField(), diagnose.getPositionStatus(), diagnose.getIsDelete(), diagnose.getCreateTime(), diagnose.getUpdate_time()};
        return getJdbcTemplate(diagnose.getPuid()).update(sql, args);
    }

    @Override
    public int deleteDiagnose(DeleteDiagnoseReqVo reqVo) {
        String sql = "update " + getJdbcHelper().getTable() + " set is_delete = 0, update_time = now() where id = ? and puid = ? and uid = ?";
        return getJdbcTemplate(reqVo.getPuid()).update(sql, reqVo.getId(), reqVo.getPuid(), reqVo.getUid());
    }

    @Override
    public int countByUid(Integer puid, Integer uid, Byte moduleType, boolean containDelete) {

        StringBuilder sql = new StringBuilder("select count(*) from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and uid = ? and module_type = ? ");
        List<Object> argList = new ArrayList<>(4);
        argList.add(puid);
        argList.add(uid);
        argList.add(moduleType);
        if (!containDelete) {
            sql.append(" and is_delete = ?");
            argList.add(BooleanStatusEnum.TRUE.getStatus());
        }

        return getJdbcTemplate(puid).queryForObject(sql.toString(), Integer.class, argList.toArray());
    }

    @Override
    public void batchSaveDiagnose(Integer puid, List<AmazonAdProductDiagnose> collect) {
        String sql = "insert into " + getJdbcHelper().getTable() + " (puid, uid, module_type, filter_field, position_status, is_delete, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?, ?)";
        List<Object[]> objectList = new ArrayList<>(collect.size());
        collect.forEach(x -> {
            Object[] args = {x.getPuid(), x.getUid(), x.getModuleType(), x.getFilterField(), x.getPositionStatus(), x.getIsDelete(), x.getCreateTime(), x.getUpdate_time()};
            objectList.add(args);
        });
        getJdbcTemplate(puid).batchUpdate(sql, objectList);
    }

    @Override
    public Page<ListDiagnoseDto> pageList(ListDiagnoseReqVo reqVo) {
        StringBuilder selectSql = new StringBuilder("select id, module_type as moduleType, filter_field as filterField, position_status as positionStatus from ").append(getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM (SELECT id FROM ").append(getJdbcHelper().getTable());
        List<Object> args = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and uid = ? and module_type = ? and is_delete = 1 ");
        args.add(reqVo.getPuid());
        args.add(reqVo.getUid());
        args.add(reqVo.getModuleType());
        selectSql.append(whereSql);
        countSql.append(whereSql).append(" ) t ");
        selectSql.append(" order by position_status desc, update_time desc, id desc ");
        return getPageResultByClass(reqVo.getPuid(), reqVo.getPageNo(), reqVo.getPageSize(), countSql.toString(), args.toArray(), selectSql.toString(), args.toArray(), ListDiagnoseDto.class);
    }

    @Override
    public void updatePositionStatus(Integer puid, Integer uid, Byte moduleType) {
        StringBuilder sql = new StringBuilder("update ").append(getJdbcHelper().getTable());
        sql.append(" set position_status = 0, update_time = now() where puid = ? and uid = ? and module_type = ? and position_status = 1 and is_delete = 1");
        getJdbcTemplate(puid).update(sql.toString(), puid, uid, moduleType);
    }

    @Override
    public int updatePositionStatusById(Integer puid, Integer uid, Long id, Byte positionStatus) {
        StringBuilder sql = new StringBuilder("update ").append(getJdbcHelper().getTable());
        sql.append(" set position_status = ?, update_time = now() where puid = ? and uid = ? and id = ? ");
        return getJdbcTemplate(puid).update(sql.toString(), positionStatus, puid, uid, id);
    }
}