package com.meiyunji.sponsored.service.strategy.po;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 广告顶级预算策略模板
 */
@Data
@DbTable(value = "t_advertise_strategy_top_budget_template")
public class AdvertiseStrategyTopBudgetTemplate implements Serializable {
    @DbColumn(value = "id")
    private Long id;

    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * profileId
     */
    @DbColumn(value = "profile_id")
    private String profileId;

    /**
     * 站点ID
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 策略状态ID
     */
    @DbColumn(value = "task_id")
    private Long taskId;

    /**
     * 状态
     */
    @DbColumn(value = "status")
    private String status;

    /**
     * 执行预览数量
     */
    private Integer executePreview;

    /**
     * 周期类型: DAILY->每日，WEEKLY->每周
     */
    @DbColumn(value = "type")
    private String type;

    /**
     * 规则(json数组)
     */
    @DbColumn(value = "rule")
    private String rule;

    /**
     * 原始值[json对象]
     */
    @DbColumn(value = "origin_value")
    private String originValue;

    /**
     * 还原值[json对象]
     */
    @DbColumn(value = "reduction_value")
    private String reductionValue;

    /**
     * 创建人ID
     */
    @DbColumn(value = "create_uid")
    private Integer createUid;

    /**
     * 更新人ID
     */
    @DbColumn(value = "update_uid")
    private Integer updateUid;

    /**
     * 创建人
     */
    @DbColumn(value = "create_name")
    private String createName;

    /**
     * 更新人
     */
    @DbColumn(value = "update_name")
    private String updateName;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_at",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @DbColumn(value = "last_update_at",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime lastUpdateAt;

    public String getMarketplaceName(){
        if (this.marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplaceCN();
        }
        return null;
    }
}
