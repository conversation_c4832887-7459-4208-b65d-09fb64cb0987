package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;

import java.util.List;

/**
 * CpcQueryTargetingReport
 * <AUTHOR>
 * @date 2023/1/4
 */
public interface IWxCpcQueryTargetingReportDao extends IBaseShardingSphereDao<CpcQueryTargetingReport> {

    /**
     * 获取搜索词ASIN投放报告信息
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageKeywordAndTargetManageList(int puid, CpcQueryWordDto dto, Page page);


    /**
     * 按日期获取搜索词ASIN投放报告信息
     * @param puid
     * @param dto
     * @return
     */
    List<AdHomePerformancedto> getKeywordAndTargetListAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto);


    /**
     * 获取搜索词ASIN投放每日汇总数据
     * @param puid
     * @param shopId
     * @param startStr
     * @param endStr
     * @param targetIdList
     * @param dto
     * @return
     */
    List<AdHomePerformancedto> getReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, CpcQueryWordDto dto);

}