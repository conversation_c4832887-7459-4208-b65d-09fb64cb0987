package com.meiyunji.sponsored.service.aadrasGrpcApi;

import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sponsored.service.aadrasGrpcApi.autoRule.AdAutoRuleApi;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import io.grpc.ManagedChannel;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AadrasApiFactory implements ApplicationContextAware {

    private Map<String, AdAutoRuleApi> adAutoRuleApiMap;
    public static ManagedChannel taskManagedChannel;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        adAutoRuleApiMap = applicationContext.getBeansOfType(AdAutoRuleApi.class);
    }

    @Autowired
    public static void setManagedChannel(@Qualifier("aadrasApiManagedChannel") ManagedChannel taskManagedChannel) {
        AadrasApiFactory.taskManagedChannel = taskManagedChannel;
    }


    public AadrasApiFactory( @Qualifier("aadrasApiManagedChannel") ManagedChannel taskManagedChannel) {
        this.taskManagedChannel = taskManagedChannel;
    }

    public AdAutoRuleApi getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType advertiseRuleTaskType) {
        for (Map.Entry<String, AdAutoRuleApi> entry : adAutoRuleApiMap.entrySet()) {
            if (entry.getValue().checkValid(advertiseRuleTaskType)) {
                return entry.getValue();
            }
        }
        throw new RuntimeException("auto rule not found process application");
    }
}
