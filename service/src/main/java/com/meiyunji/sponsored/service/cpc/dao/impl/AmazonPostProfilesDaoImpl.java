package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonPostProfilesDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonPostsProfiles;
import com.meiyunji.sponsored.service.post.po.BrandsPo;
import com.meiyunji.sponsored.service.post.request.GetPostsRequest;
import com.meiyunji.sponsored.service.post.response.GetBrandsResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class AmazonPostProfilesDaoImpl extends BaseShardingDaoImpl<AmazonPostsProfiles> implements IAmazonPostProfilesDao {
    @Override
    public List<GetBrandsResponse.BrandsVo> getBrands(Integer puid, List<Integer> shopIdList) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append("select distinct name, brand_id brandId, post_profile_id postProfileId from ").append(this.getJdbcHelper().getTable());
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetBrandsResponse.BrandsVo.class));
    }

    @Override
    public List<GetBrandsResponse.BrandsVo> multiShopGetBrands(Integer puid, List<Integer> shopIdList) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append("select distinct name, brand_id brandId from ").append(this.getJdbcHelper().getTable());
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetBrandsResponse.BrandsVo.class));
    }

    @Override
    public List<BrandsPo> getProfileByBrand(GetPostsRequest req, List<BrandsPo> brandsPoList) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append("select post_profile_id postProfileId, name ").append(" from ").append(this.getJdbcHelper().getTable());
        selectSql.append(" where puid =? ");
        argsList.add(req.getFilterDto().getPuid());
        selectSql.append(SqlStringUtil.dealInList("shop_id", req.getFilterDto().getShopIdList(), argsList));
        selectSql.append(SqlStringUtil.dealMultiInList(Arrays.asList("brand_id", "name"), brandsPoList, argsList, Arrays.asList(BrandsPo::getBrandId, BrandsPo::getBrandName)));
        return getJdbcTemplate(req.getFilterDto().getPuid()).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(BrandsPo.class));
    }

    @Override
    public void upsertPostProfiles(List<AmazonPostsProfiles> posts) {
        if (CollectionUtils.isEmpty(posts)) {
            return;
        }
        int puid = posts.get(0).getPuid();
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`, `shop_id`, `feed_url`, `is_authorized`, `brand_profile_id`, `browse_node_id`, `post_profile_id`, `brand_id`, `name`, `last_approval_date`, `logo_url`, `status`, `is_pay`) values ");
        List<Object> argsList = new ArrayList<>(posts.size());
        for (AmazonPostsProfiles post : posts) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(post.getPuid());
            argsList.add(post.getShopId());
            argsList.add(post.getFeedUrl());
            argsList.add(post.getIsAuthorized());
            argsList.add(post.getBrandProfileId());
            argsList.add(post.getBrowseNodeId());
            argsList.add(post.getPostProfileId());
            argsList.add(post.getBrandId());
            argsList.add(post.getName());
            argsList.add(post.getLastApprovalDate());
            argsList.add(post.getLogoUrl());
            argsList.add(post.getStatus());
            argsList.add(post.getIsPay());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE\n" +
                "`feed_url` = VALUES(`feed_url`),\n" +
                "`is_authorized` = VALUES(`is_authorized`),\n" +
                "`browse_node_id` = VALUES(`browse_node_id`),\n" +
                "`name` = VALUES(`name`),\n" +
                "`last_approval_date` = VALUES(`last_approval_date`),\n" +
                "`logo_url` = VALUES(`logo_url`),\n" +
                "`status` = VALUES(`status`),\n" +
                "`update_time`=now()");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonPostsProfiles> listByShopId(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        List<Object> argList = new ArrayList<>();
        argList.add(puid);
        argList.add(shopId);
        return getJdbcTemplate(puid).query(sql.toString(), argList.toArray(), getMapper());
    }

    @Override
    public List<BrandsPo> getBrandsByPostProfileIds(Integer puid, List<Integer> shopIds, Set<String> postProfileIds, List<String> postIds) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append("select any_value(a.post_profile_id) postProfileId, b.post_id postId, any_value(a.name) brandName ").append(" from ").append(this.getJdbcHelper().getTable()).append(" a ");
        selectSql.append(" left join t_amazon_ad_posts ").append(" b on a.puid = b.puid and a.shop_id = b.shop_id and a.post_profile_id = b.post_profile_id and b.puid = ?");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("b.shop_id", shopIds, argsList));
        selectSql.append(SqlStringUtil.dealInList("b.post_profile_id", new ArrayList<>(postProfileIds), argsList));
        selectSql.append(SqlStringUtil.dealInList("b.post_id", postIds, argsList));
        selectSql.append(" where a.puid =? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("a.shop_id", shopIds, argsList));
        selectSql.append(SqlStringUtil.dealInList("a.post_profile_id", new ArrayList<>(postProfileIds), argsList));
        selectSql.append(" order by postId ");
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(BrandsPo.class));
    }

    @Override
    public List<Integer> getShopIdByPuid(int puid, List<Integer> shopIdList) {
        StringBuilder selectSql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        selectSql.append("select shop_id ").append(" from ").append(this.getJdbcHelper().getTable());
        selectSql.append(" where puid =? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        return getJdbcTemplate(puid).queryForList(selectSql.toString(), argsList.toArray(), Integer.class);
    }
}
