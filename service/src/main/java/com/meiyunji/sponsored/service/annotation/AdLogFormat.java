package com.meiyunji.sponsored.service.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 广告收集日志：各字段更新操作说明
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AdLogFormat {
    String value() default "";
    String methodStr() default "";
}
