package com.meiyunji.sponsored.service.export.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 流量表缓存dto
 * @Author: hejh
 * @Date: 2024/5/24 16:44
 */
@Data
public class FlowCacheDto implements Serializable {
    /**
     * 报告任务表id
     */
    private long reportId;
    /**
     * puid
     */
    private int puid;
    /**
     * uuid，是否同步完成 映射
     * uuid代表唯一店铺
     */
    private Map<String, Boolean> uuidSyncedMap;
    /**
     * 产品使用(parentAsin,asin,sku)
     */
    private String tabType;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
    /**
     * shopIds
     */
    private List<Integer> shopIds;
    /**
     * 广告类型
     */
    private String adType;
    /**
     * 报告名称
     */
    private String reportName;
}
