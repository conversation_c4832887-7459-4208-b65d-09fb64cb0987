package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroupUser;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagRelation;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageTagRelationDao;
import com.meiyunji.sponsored.service.doris.po.OdsAdManageTagRelation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
public class OdsAdManageTagRelationDaoImpl extends DorisBaseDaoImpl<OdsAdManageTagRelation> implements IOdsAdManageTagRelationDao {

    @Override
    public List<String> getRelationIdByTagId(int puid, String tagId, int type, List<Integer> shopIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select relation_id from ods_t_ad_manage_tag_relation where puid = ? and type = ? and tag_id = ? ");
        argsList.add(puid);
        argsList.add(type);
        argsList.add(tagId);
        sql.append(" and del_flag = 0");
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        return getJdbcTemplate().queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<OdsAdManageTagRelation> getByTagIds(int puid, int type, List<Long> tagIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ods_t_ad_manage_tag_relation where puid = ? and type = ? and del_flag = 0 ");
        argsList.add(puid);
        argsList.add(type);
        if (CollectionUtils.isNotEmpty(tagIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("tag_id", tagIds, argsList));
        }
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsAdManageTagRelation.class), argsList.toArray());
    }

    @Override
    public List<String> getRelationIdByTagIds(Integer puid, List<String> temporaryIds, int type,List<Integer> shopIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select relation_id from ods_t_ad_manage_tag_relation where puid = ? and type = ? ");
        argsList.add(puid);
        argsList.add(type);
        if (CollectionUtils.isNotEmpty(temporaryIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("tag_id", temporaryIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        sql.append(" and del_flag = 0");
        return getJdbcTemplate().queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<String> getRelationIdByTagIds(Integer puid, List<String> tagIds, int type, List<Integer> shopIds, List<String> relationIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select relation_id from ods_t_ad_manage_tag_relation where puid = ? and type = ? ");
        argsList.add(puid);
        argsList.add(type);
        if (CollectionUtils.isNotEmpty(tagIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("tag_id", tagIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(relationIdList)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIdList, argsList));
        }
        sql.append(" and del_flag = 0");
        return getJdbcTemplate().queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<AdMarkupTagVo> getTagIdByRelationId(Integer puid, int type, List<String> campaignIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(puid) puid, any(type) type, relation_id relationId, GROUP_CONCAT(DISTINCT CAST(tag_id AS CHAR), ',') tagIdsStr from ods_t_ad_manage_tag_relation where puid = ? and type = ? ");
        argsList.add(puid);
        argsList.add(type);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("relation_id", campaignIds, argsList));
        }
        sql.append(" and del_flag = 0");
        sql.append( " group by relation_id ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdMarkupTagVo.class));
    }

    @Override
    public List<AdMarkupTagVo> getTagIdByRelationId(Integer puid, int type, List<String> campaignIds, List<Long> groupId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(puid) puid, any(type) type, relation_id relationId, GROUP_CONCAT(DISTINCT CAST(tag_id AS CHAR), ',') tagIdsStr from ods_t_ad_manage_tag_relation where del_flag = 0 and puid = ? and type = ? ");
        argsList.add(puid);
        argsList.add(type);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("relation_id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(groupId)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("group_id", groupId, argsList));
        }
        sql.append(" and del_flag = 0");
        sql.append( " group by relation_id ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdMarkupTagVo.class));
    }
}

