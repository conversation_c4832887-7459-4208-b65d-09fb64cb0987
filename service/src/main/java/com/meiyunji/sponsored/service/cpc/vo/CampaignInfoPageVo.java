package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.ReportBase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @author: ys
 * @date: 2024/1/27 11:29
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CampaignInfoPageVo extends ReportBase {

    private static final long serialVersionUID = 3630899967501022819L;

    /**
     * id
     */
    @DbColumn(value = "id",autoIncrement=true,key = true)
    private Long id;
    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;
    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 活动类型  sp sb sd
     */
    @DbColumn(value = "type")
    private String type;


    /**
     * 配置ID
     */
    @DbColumn(value = "profile_id")
    private String profileId;
    /**
     * 站点
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;
    /**
     * 活动名称
     */
    @DbColumn(value = "name")
    @AdLogFormat(value = "广告活动名称",methodStr = "getName")
    private String name;
    /**
     * 活动类型
     */
    @DbColumn(value = "campaign_type")
    private String campaignType;

    /**
     *  sb活动类型 商品集 视频集
     */
    @DbColumn(value = "ad_format")
    private String adFormat;

    /**
     * SB 品牌id, 接口同步不返回,只有在我们这边创建才有
     */
    @DbColumn("brand_entity_id")
    private String brandEntityId;

    /**
     * SP auto自动投放,manual手动投放
     */
    @DbColumn(value = "targeting_type")
    @AdLogFormat(value = "投放类型", methodStr = "getTargetingType")
    private String targetingType;

    /**
     *  SB 广告活动投放类型  keyword、product
     */
    @DbColumn(value = "target_type")
    private String targetType;

    /**
     *  sp,sb,sd 投放类型，冗余字段，方便查询，sp targeting_type ， sb tactic ，sd  ad_format
     */
    @DbColumn(value = "ad_target_type")
    private String adTargetType;

    /**
     * 活动状态（enabled，paused，archived）
     */
    @DbColumn(value = "state")
    @AdLogFormat(value = "广告活动状态", methodStr = "covertChState")
    private String state;
    /**
     * TODO 广告活动增加日志
     * 操作：日志打印状态需转换中文
     */
    public String covertChState() {
        return AmazonAdCampaignAll.stateEnum.valueOf(state).getDescription();
    }
    /**
     * 每日预算最小为1
     */
    @DbColumn(value = "budget")
    @AdLogFormat(value = "每日预算", methodStr = "getBudget")
    private BigDecimal budget;
    /**
     * 币种(USD,CAD,GBP,EUR)
     */
    @DbColumn(value = "currency_code")
    private String currencyCode;
    /**
     * 活动开始时间
     */
    @DbColumn(value = "start_date")
    @AdLogFormat(value = "活动开始时间", methodStr = "getStartDateStr")
    private Date startDate;
    public String getStartDateStr(){
        if(startDate != null){
            return DateUtil.dateToStrWithFormat(startDate,"yyyy-MM-dd");
        }
        return null;
    }

    /**
     * 活动结束时间
     */
    @DbColumn(value = "end_date")
    @AdLogFormat(value = "活动结束时间", methodStr = "getEndDateStr")
    private Date endDate;
    public String getEndDateStr(){
        if(endDate != null){
            return DateUtil.dateToStrWithFormat(endDate,"yyyy-MM-dd");
        }
        return null;
    }
    /**
     * 新的竞价策略(legacyForSales,autoForSales,manual)
     */
    @DbColumn(value = "strategy")
    @AdLogFormat(value = "竞价策略", methodStr = "getStrategy")
    private String strategy;
    /**
     * 广告展示位置([{"predicate": "placementProductPage","percentage": 850},{"predicate": "placementTop","percentage": 50}])
     */
    @DbColumn(value = "adjustments")
    @AdLogFormat(value = "广告展示位置", methodStr = "getAdjustments")
    private String adjustments;
    /**
     * 活动的具体状态,例:CAMPAIGN_ARCHIVED
     */
    @DbColumn(value = "serving_status")
    private String servingStatus;
    /**
     * 平台的创建时间
     */
    @DbColumn(value = "creation_date")
    private LocalDateTime creationDate;
    /**
     * 平台的更新时间
     */
    @DbColumn(value = "last_updated_date")
    private LocalDateTime lastUpdatedDate;


    /**
     * 超预算时间(站点时间戳)
     */
    @DbColumn(value = "out_of_budget_time")
    private Long outOfBudgetTime;


    /**
     * 同步超预算时间状态 1成功 0 失败
     */
    @DbColumn(value = "sync_out_of_budget_time_state")
    private Integer syncOutOfBudgetTimeState;


    /**
     * 1在amzup创建，0从amazon同步
     */
    @DbColumn(value = "create_in_amzup")
    private Integer createInAmzup;
    /**
     * 创建人id
     */
    @DbColumn(value = "create_id")
    private Integer createId;
    /**
     * 修改人id
     */
    @DbColumn(value = "update_id")
    private Integer updateId;

    /**
     * 是否开启分时调价启停
     */
    @DbColumn(value = "is_state_pricing")
    private Integer isStatePricing;

    /**
     * 分时启停任务状态
     */
    @DbColumn(value = "pricing_state_state")
    private Integer pricingStartStopState;

    /**
     * 是否开启预算分时调价
     */
    @DbColumn(value = "is_budget_pricing")
    private Integer isBudgetPricing;

    /**
     * 分时调价预算任务状态
     */
    @DbColumn(value = "pricing_budget_state")
    private Integer pricingBudgetState;

    /**
     * 是否开启广告位分时调价
     */
    @DbColumn(value = "is_space_pricing")
    private Integer isSpacePricing;

    /**
     * 分时调价广告位任务状态
     */
    @DbColumn(value = "pricing_space_state")
    private Integer pricingSpaceState;

    /**
     * 报告数据最新更新时间 yyyy-MM-dd
     */
    @DbColumn(value = "data_update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate dataUpdateTime;

    /**
     *  广告组合id
     */
    @DbColumn(value = "portfolio_id")
    @AdLogFormat(value = "广告组合", methodStr = "getPortfolioId")
    private String portfolioId;


    /**
     * sb 预算类型，lifetime, daily
     */
    @DbColumn(value = "budget_type")
    private String budgetType;


    /**
     * sb  设置为true，允许亚马逊自动优化放置在搜索顶部以下的出价
     */
    @DbColumn(value = "bid_optimization")
    private Boolean bidOptimization;

    /**
     *  sb 该字段只能在''bidOptimization''设置为false时设置
     */
    @DbColumn(value = "bid_multiplier")
    private BigDecimal bidMultiplier;

    /**
     *  sb 创意
     */
    @DbColumn(value = "creative")
    private String creative;

    /**
     *  sb 着陆页
     */
    @DbColumn(value = "landing_page")
    private String landingPage;

    private String servingStatusDec;

    /**
     * sd 投放策略：T00020(Product), T00030(audience)
     */
    @DbColumn(value = "tactic")
    private String tactic;


    /**
     * sd 扣费方式：cpc、vcpm [Coming Soon]
     */
    @DbColumn(value = "cost_type")
    private String costType;

    /**
     * sb 基于规则的预算信息 "ruleBasedBudget": {"isProcessing": true,"applicableRuleName": "string","value": 0,"applicableRuleId": "string"}
     */
    @DbColumn(value = "rule_based_budget")
    private String ruleBasedBudget;

    /**
     * sb 是否多广告组启用 0、1
     */
    @DbColumn(value = "is_multiAdGroups_enabled")
    private Boolean isMultiAdGroupsEnabled;

    /**
     * 按购物者细分调整出价 [{"percentage": 0,"shopperSegment": "NEW_TO_BRAND_PURCHASE"},{"percentage": 0,"shopperSegment": "NEW_TO_BRAND_PURCHASE"}]
     */
    @DbColumn(value = "bid_adjustments_by_shopper_segment")
    private String bidAdjustmentsByShopperSegment;

    /**
     * 按展示位置调整出价 [{"percentage": 0,"placement": "HOME"},{"percentage": 0,"placement": "DETAIL_PAGE"},{"percentage": 0,"placement": "OTHER"}]
     */
    @DbColumn(value = "bid_adjustments_by_placement")
    private String bidAdjustmentsByPlacement;

    /**
     * 竞价优化策略 MAXIMIZE_IMMEDIATE_SALES:默认出价策略  MAXIMIZE_NEW_TO_BRAND_CUSTOMERS:对广告活动进行了优化，以获得更多品牌新买家
     */
    @DbColumn(value = "bid_optimization_strategy")
    private String bidOptimizationStrategy;

    /**
     * SOLD_ON_AMAZON  NOT_SOLD_ON_AMAZON  SOLD_ON_DTC
     */
    @DbColumn(value = "product_location")
    private String productLocation;

    /**
     * 用户为广告系列指定的自定义标识符的列表，最多指定 50个键值对
     */
    @DbColumn(value = "tags")
    private String tags;

    //辅助字段
    private List<AmazonAdGroup> adGroups;

    private String  endTimeStr;  //修改活动结束时间为空的时候用到

    private String servingStatusName;

    //搜索词首页首位曝光率

    private Double topOfSearchIS;


    public void setServingStatus(String servingStatus) {
        this.servingStatus = servingStatus;
        if(StringUtils.isNotBlank(servingStatus)){
            servingStatusEnum byCode = UCommonUtil.getByCode(servingStatus, servingStatusEnum.class);
            if(byCode != null){
                this.servingStatusName = byCode.getName();
                this.servingStatusDec = byCode.getDescription();
            }else {
                this.servingStatusName =servingStatus;
                this.servingStatusDec = servingStatus;
            }
        }
    }

    /**
     * 广告组状态
     */
    public enum  stateEnum implements BaseEnum {
        enabled("enabled","开启"),
        paused("paused","停止"),
        archived("archived","归档"),
        ;

        private String code;
        private String desc;

        stateEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }

    /**
     * 广告二级状态枚举类
     */
    public enum servingStatusEnum implements BaseEnum {

        CAMPAIGN_ARCHIVED("CAMPAIGN_ARCHIVED","已归档","广告活动不符合条件，因为它被设置为已存档。"),  //已归档  前端不展示
        CAMPAIGN_STATUS_ENABLED("CAMPAIGN_STATUS_ENABLED","正在投放","广告活动处于活动状态，可以获得展示量。您的广告将展示在符合条件的广告位中。"),
        CAMPAIGN_OUT_OF_BUDGET("CAMPAIGN_OUT_OF_BUDGET","超出预算","广告活动处于活动状态，可以获得展示量。您的广告将展示在符合条件的广告位中。"),
        ACCOUNT_OUT_OF_BUDGET("ACCOUNT_OUT_OF_BUDGET","账户预算不足","广告活动处于活动状态，可以获得展示量。您的广告将展示在符合条件的广告位中。"),
        PENDING_START_DATE("PENDING_START_DATE","已安排","广告活动尚未开始，但已安排在一个未来的时间开始。"),
        CAMPAIGN_INCOMPLETE("CAMPAIGN_INCOMPLETE","不完整","广告活动未包含至少一个定向目标和要推广的一件商品，因此不符合展示广告的条件。此状态仅用于商品推广"),
        CAMPAIGN_PAUSED("CAMPAIGN_PAUSED","已暂停","广告活动不符合条件，因为它被设置为已暂停。"),   //暂停  前端不展示
        ENDED("ENDED","已结束","广告活动结束日期已过，广告活动已结束"),
        EXCEPTION_STATUS("EXCEPTION_STATUS","异常","广告异常活动（EXCEPTION_STATUS）"),
        ADVERTISER_PAYMENT_FAILURE("ADVERTISER_PAYMENT_FAILURE","付款失败","您的付款方式（如信用卡、借记卡或储蓄卡）已停用、金额不足或最近出现变动"),
        ADVERTISER_PAYMENT_FAILED("ADVERTISER_PAYMENT_FAILED","付款失败","您的付款方式（如信用卡、借记卡或储蓄卡）已停用、金额不足或最近出现变动"),
        PORTFOLIO_STATUS_ENABLED("PORTFOLIO_STATUS_ENABLED","广告组合-正在投放","广告组合状态被设置为“已启用”"),
        PORTFOLIO_STATUS_PAUSED("PORTFOLIO_STATUS_PAUSED","广告组合-已暂停","广告组合状态被设置为“已暂停”"),
        PORTFOLIO_ARCHIVED("PORTFOLIO_ARCHIVED","广告组合-已归档","广告组合状态被设置为“已归档"),
        PORTFOLIO_OUT_OF_BUDGET("PORTFOLIO_OUT_OF_BUDGET","广告组合-超预算","广告活动预算已达到广告组合的最高预算上限"),
        PORTFOLIO_PENDING_START_DATE("PORTFOLIO_PENDING_START_DATE","广告组合-未开始","广告组合的开始日期在将来，广告组合未开始"),
        PORTFOLIO_ENDED("PORTFOLIO_ENDED","广告组合-已结束","广告组合的结束日期是过去的，广告组合已结束"),
        // sd二级状态
        ADVERTISER_STATUS_ENABLED("ADVERTISER_STATUS_ENABLED", "广告商状态已启用","广告商已被设置为【已启动】"),

        // sb二级状态
        asinNotBuyable("asinNotBuyable", "产品不可售","产品中的产品不可售"),
        billingError("billingError", "账单错误","账单错误（BILLING ERROR）"),
        ended("ended", "已结束","广告活动结束日期已过，广告活动已结束"),
        landingPageNotAvailable("landingPageNotAvailable","着陆页不可用", "着陆页不可用（LANDING PAGE NOT AVAILABLE）"),
        outOfBudget("outOfBudget", "超出预算","广告活动已超出广告活动每日预算。此广告活动中的广告直到重置每日预算的午夜才有资格获得展示量"),
        paused("paused", "已暂停","广告活动不符合条件，因为它被设置为已暂停。"),
        pendingReview("pendingReview", "待审核","亚马逊正在审核广告活动，予以批准后会将其设置为开展。"),
        ready("ready", "已安排","广告活动尚未开始，但已安排在一个未来的时间开始。"),
        rejected("rejected", "未获得批准","您的广告活动素材已被拒绝，需要进行编辑。"),
        running("running", "投放中","广告活动处于活动状态，可以获得展示量。您的广告将展示在符合条件的广告位中。"),
        scheduled("scheduled", "已预定","已预定（SCHEDULED）"),
        terminated("terminated", "已归档","广告活动不符合条件，因为它被设置为已存档。");

        ;

        private String code;
        private String name;
        private String desc;

        servingStatusEnum(String code,String name,String desc) {
            this.code = code;
            this.name = name;
            this.desc = desc;
        }

        @Override
        public String getCode() {
            return code;
        }


        public String getName() {
            return name;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }
}
