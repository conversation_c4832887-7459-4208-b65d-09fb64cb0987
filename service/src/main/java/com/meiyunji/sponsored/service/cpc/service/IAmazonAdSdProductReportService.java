package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.vo.*;


import java.util.List;
import java.util.Map;

/**
 * Created by DXM_0081 on 2021/5/14.
 */
public interface IAmazonAdSdProductReportService {

    Page pageList(int puid, SearchVo search, Page page);

    Page pageList4DownloadCenterExport(int puid, SearchVo search, Page page);

    Page detailPageList(int puid, ReportParam param, Page page);

    SumReportDataVo getSumReport(int puid, Integer shopId, String marketplaceId, ReportParam param);

    List<ReportDataVo> getChartList(int puid, Integer shopId, String marketplaceId, ReportParam param);

    /**
     * 获取广告产品列表页对应的活动和广告组信息
     */
    Map<String, String> getAdProductMap(int puid, Integer shopId, String marketplaceId, String campaignId);

}
