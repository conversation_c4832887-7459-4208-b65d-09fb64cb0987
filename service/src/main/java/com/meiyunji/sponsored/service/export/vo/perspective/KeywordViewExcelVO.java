package com.meiyunji.sponsored.service.export.vo.perspective;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.StreamDataViewVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class KeywordViewExcelVO {

    @ExcelProperty(value = "有效状态")
    private String state;
    @ExcelProperty(value = "匹配类型")
    private String matchType;
    @ExcelProperty(value = "标签")
    private String adTags;
    @ExcelProperty(value = "服务状态")
    private String servingStatusName; // sb 无
    @ExcelProperty(value = "广告组")
    private String adGroupName;
    @ExcelProperty(value = "广告活动")
    private String campaignName;
    @ExcelProperty(value = "广告组合")
    private String portfolioName;
    @ExcelProperty(value = "建议竞价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String suggestBid;
    @ExcelProperty(value = "建议竞价范围")
    private String suggestBidScope;
    @ExcelProperty(value = "竞价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String bid;
    @ExcelProperty(value = "ABA搜索词排名")
    private Integer searchFrequencyRank;
    @ExcelProperty(value = "排名周变化率")
    private String weekRatio;

    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCost;
    @ExcelProperty(value = "广告花费占比")
    private String adCostPercentage;
    @ExcelProperty(value = "广告曝光量")
    private Long impressions;
    @ExcelProperty("搜索结果首页首位IS")
    private String topImpressionShare;
    @ExcelProperty(value = "广告点击量")
    private Long clicks;
    @ExcelProperty(value = "可见展示次数")
    private Long viewImpressions;
    @ExcelProperty(value = "CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;
    @ExcelProperty(value = "CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCostPerClick;
    @ExcelProperty(value = "VCPM")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String vcpm;
    @ExcelProperty(value = "广告点击率")
    private String ctr;
    @ExcelProperty(value = "广告转化率")
    private String cvr;
    @ExcelProperty(value = "ACoS")
    private String acos;
    @ExcelProperty(value = "ROAS")
    private String roas;
    @ExcelProperty(value = "ACoTS")
    private String acots;
    @ExcelProperty(value = "ASoTS")
    private String asots;
    @ExcelProperty(value = "广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String advertisingUnitPrice;
    @ExcelProperty(value = "广告订单量")
    private Integer adOrderNum;
    @ExcelProperty(value = "广告订单量占比")
    private String adOrderNumPercentage;
    @ExcelProperty(value = "本广告产品订单量")
    private Integer adSaleNum;
    @ExcelProperty(value = "其他产品广告订单量")
    private Integer adOtherOrderNum;
    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;
    @ExcelProperty(value = "广告销售额占比")
    private String adSalePercentage;
    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSales;
    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSales;
    @ExcelProperty(value = "广告销量")
    private Integer orderNum;
    @ExcelProperty(value = "广告销量占比")
    private String orderNumPercentage;
    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;
    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;
    @ExcelProperty(value = "“品牌新买家”订单量")
    private Integer ordersNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”订单百分比")
    private String orderRateNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String salesNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”销售额百分比")
    private String salesRateNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”销量")
    private Integer unitsOrderedNewToBrandFTD;
    @ExcelProperty(value = "“品牌新买家”销量百分比")
    private String unitsOrderedRateNewToBrandFTD;

    public static KeywordViewExcelVO getInstance(String currency, KeywordViewVo data) {
        KeywordViewExcelVO excel = new KeywordViewExcelVO();
        excel.setState(AllAdStateEnum.getStateValueIgnoreCase(data.getState()));
        excel.setMatchType(MatchValueEnum.getMatchValue(data.getMatchType()));
        if (CollectionUtils.isNotEmpty(data.getAdTags())) {
            excel.setAdTags(data.getAdTags().stream().map(AdTag::getName).filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        excel.setServingStatusName(data.getServingStatusName());
        excel.setAdGroupName(data.getAdGroupName());
        excel.setCampaignName(data.getCampaignName());
        excel.setPortfolioName(data.getPortfolioName());
        if (StringUtils.isNotBlank(data.getSuggestBid())) {
            excel.setSuggestBid(currency + data.getSuggestBid());
        }
        CurrencyUnitEnum unitEnum = CurrencyUnitEnum.getByCurrency(currency);
        excel.setSuggestBidScope(ExportStringUtil.getSuggestBidScope(data.getRangeStart(), data.getRangeEnd(), Objects.nonNull(unitEnum) ? unitEnum.getUnit() : ""));
        excel.setBid(currency + StringUtils.defaultIfBlank(data.getBid(), "0"));
        excel.setSearchFrequencyRank(data.getSearchFrequencyRank());
        excel.setWeekRatio((Objects.nonNull(data.getWeekRatio()) ? data.getWeekRatio() : BigDecimal.ZERO) + "%");
        excel.setTopImpressionShare(ExportStringUtil.parseTopImpressionShare(data.getTopImpressionShare()));
        buildData(currency, excel, data);
        if (!SBCampaignCostTypeEnum.VCPM.getCode().equalsIgnoreCase(data.getCostType())) {
            excel.setVcpm("-");
        }
        return excel;
    }

    public static void buildData(String currency, KeywordViewExcelVO instance, StreamDataViewVo data) {
        instance.setAdCost(currency + MathUtil.toBigDecimalStrWithScale(data.getAdCost(), 2));
        instance.setAdCostPercentage(MathUtil.toBigDecimalStrWithScale(data.getAdCostPercentage(), 2) + "%");
        instance.setImpressions(data.getImpressions());
        instance.setClicks(data.getClicks());
        instance.setViewImpressions(data.getViewImpressions());
        instance.setCpa(currency + MathUtil.toBigDecimalStrWithScale(data.getCpa(), 2));
        instance.setAdCostPerClick(currency + MathUtil.toBigDecimalStrWithScale(data.getAdCostPerClick(), 2));
        instance.setVcpm(currency + MathUtil.toBigDecimalStrWithScale(data.getVcpm(), 2));
        instance.setCtr(MathUtil.toBigDecimalStrWithScale(data.getCtr(), 2) + "%");
        instance.setCvr(MathUtil.toBigDecimalStrWithScale(data.getCvr(), 2) + "%");
        instance.setAcos(MathUtil.toBigDecimalStrWithScale(data.getAcos(), 2) + "%");
        instance.setRoas(MathUtil.toBigDecimalStrWithScale(data.getRoas(), 2));
        instance.setAcots(MathUtil.toBigDecimalStrWithScale(data.getAcots(), 2) + "%");
        instance.setAsots(MathUtil.toBigDecimalStrWithScale(data.getAsots(), 2) + "%");
        instance.setAdvertisingUnitPrice(Objects.nonNull(data.getAdvertisingUnitPrice()) ? currency + data.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP) : "");
        instance.setAdOrderNum(data.getAdOrderNum());
        instance.setAdOrderNumPercentage(MathUtil.toBigDecimalStrWithScale(data.getAdOrderNumPercentage(), 2) + "%");
        instance.setAdSaleNum(data.getAdSaleNum());
        instance.setAdOtherOrderNum(data.getAdOtherOrderNum());
        instance.setAdSale(currency + MathUtil.toBigDecimalStrWithScale(data.getAdSale(), 2));
        instance.setAdSalePercentage(MathUtil.toBigDecimalStrWithScale(data.getAdSalePercentage(), 2) + "%");
        instance.setAdSales(currency + MathUtil.toBigDecimalStrWithScale(data.getAdSales(), 2));
        instance.setAdOtherSales(currency + MathUtil.toBigDecimalStrWithScale(data.getAdOtherSales(), 2));
        instance.setOrderNum(data.getOrderNum());
        instance.setOrderNumPercentage(MathUtil.toBigDecimalStrWithScale(data.getOrderNumPercentage(), 2) + "%");
        instance.setAdSelfSaleNum(data.getAdSelfSaleNum());
        instance.setAdOtherSaleNum(data.getAdOtherSaleNum());
        instance.setOrdersNewToBrandFTD(data.getOrdersNewToBrandFTD());
        instance.setOrderRateNewToBrandFTD(MathUtil.toBigDecimalStrWithScale(data.getOrderRateNewToBrandFTD(), 2) + "%");
        instance.setSalesNewToBrandFTD(currency + MathUtil.toBigDecimalStrWithScale(data.getSalesNewToBrandFTD(), 2));
        instance.setSalesRateNewToBrandFTD(MathUtil.toBigDecimalStrWithScale(data.getSalesRateNewToBrandFTD(), 2) + "%");
        instance.setUnitsOrderedNewToBrandFTD(data.getUnitsOrderedNewToBrandFTD());
        instance.setUnitsOrderedRateNewToBrandFTD(MathUtil.toBigDecimalStrWithScale(data.getUnitsOrderedRateNewToBrandFTD(), 2) + "%");
    }

}
