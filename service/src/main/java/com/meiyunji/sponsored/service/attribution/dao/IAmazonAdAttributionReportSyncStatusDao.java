package com.meiyunji.sponsored.service.attribution.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionReportSyncStatus;

import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/21 9:54
 * @describe:
 */
public interface IAmazonAdAttributionReportSyncStatusDao extends IAdBaseDao<AmazonAdAttributionReportSyncStatus> {
    AmazonAdAttributionReportSyncStatus getByShopId(Integer puid, Integer shopId);
    Long insertStatus(AmazonAdAttributionReportSyncStatus status);
    List<AmazonAdAttributionReportSyncStatus> getNeedSyncAttributionReportShop(Integer puid, Integer shopId);
}
