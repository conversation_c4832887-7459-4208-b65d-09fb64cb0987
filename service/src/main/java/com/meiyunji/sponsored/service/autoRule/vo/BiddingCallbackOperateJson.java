package com.meiyunji.sponsored.service.autoRule.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告数据条件
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BiddingCallbackOperateJson implements Serializable {
    /**
     * 调整类型(百分比:percentage,国定值:fixed)
     */
    @JsonProperty("adjustType")
    private String adjustType;


    /**
     * 调整的具体数值
     */
    @JsonProperty("adJustValue")
    private String adJustValue;

    /**
     * 调整的具体数值
     */
    @JsonProperty("limitValue")
    private String limitValue;

    @JsonProperty("bid")
    private BidDataOperate bid;

    @JsonProperty("placementTopBidRatio")
    private BidDataOperate placementTopBidRatio;
}
