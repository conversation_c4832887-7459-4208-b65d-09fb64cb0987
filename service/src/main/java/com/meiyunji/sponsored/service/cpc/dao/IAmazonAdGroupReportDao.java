package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * AmazonAdGroupReport
 * <AUTHOR>
 */
public interface IAmazonAdGroupReportDao extends IBaseShardingSphereDao<AmazonAdGroupReport> {
    /**
     * 批量插入
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<AmazonAdGroupReport> list);

    /**
     * 发送Doris kafka
     */
    void insertDorisList(Integer puid, List<AmazonAdGroupReport> list);

    /**
     * 获取汇总数据用于列表页展示
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AmazonAdGroupReport> getSumByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate);

    /**
     * 获取广告组报告列表
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page getPageList(Integer puid, SearchVo search, Page page);

    /**
     * 获取详情页汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param adGroupId
     * @return
     */
    AmazonAdGroupReport getSumReportByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String adGroupId);

    /**
     * 获取图表数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param adGroupId
     * @return
     */
    List<AmazonAdGroupReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String adGroupId);

    /**
     * 详情页列表
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @param page
     * @return
     */
    Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param adGroupId
     * @return
     */
    AmazonAdGroupReport getDetailInfo(int puid, Integer shopId, String marketplaceId, String adGroupId);

    /**
     * 按广告组分组获取活动的汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param groupIds
     * @return
     */
    List<AmazonAdGroupReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> groupIds);

    List<AdHomePerformancedto> listSumReportByGroupIds(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param, List<String> groupIds);

    /**
     * 用于获取汇总数据
     * @param puid
     * @param shopId
     * @param startStr
     * @param endStr
     * @param param
     * @return
     */
    AdMetricDto getSumMetric(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param);

    /**
     * 获取指定组的汇总数据
     * @param puid
     * @param shopId
     * @param startDate
     * @param endDate
     * @param groupId
     * @return
     */
    List<AmazonAdGroupReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String groupId);

    List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId);

    List<AdHomePerformancedto> getSpReportByDate(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param);

    List<AdHomePerformancedto> getSpReportByGroupIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList);

    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getGroupListByUpdateTime(Integer puid, Integer shopId, Date date);

    List<AdGroupReportHourlyDTO> getGroupReportByGroupId(Integer puid, Integer shopId, String startDate, String endDate, List<String> adGroupIdList);

    List<AdReportData> getAllReportByGroupIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<GroupInfoPageVo> getReportByGroupIds(Integer puid, GroupPageParam param, List<String> groupIdList);

    List<String> getGroupIdListByParam(Integer puid, GroupPageParam param);

    AdMetricDto getGroupPageSumMetricDataByCampaignIdList(Integer puid, GroupPageParam param, List<String> groupIdList);

    List<AllGroupOrderBo> getAdGroupIdAndIndexList(Integer puid, GroupPageParam param);

    List<AdHomePerformancedto> getReportDataByGroupIdList(Integer puid, GroupPageParam param, List<String> groupIdList);

    List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopIdList, List<String> adGroupIdList, Set<String> fields, String startDate, String endState);

    /**
     * 查询某种类型广告在某个时间段内的广告组指标汇总
     *
     * @param shopList       shopList
     * @param adType         广告类型
     * @param startCountDate 开始时间
     * @param endCountDate   结束时间
     * @return 汇总指标
     */
    List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopList, AdTypeEnum adType, String startCountDate, String endCountDate);


}