package com.meiyunji.sponsored.service.multiPlatform.walmart.dao.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingItemDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingItemPageDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItem;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.WalmartAdDaoUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdItemsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动产品Dao
 */

@Repository
public class WalmartAdvertisingItemDaoImpl extends BaseShardingDaoImpl<WalmartAdvertisingItem> implements IWalmartAdvertisingItemDao {


    @Override
    public int update(Integer puid, WalmartAdvertisingItem item) {
        StringBuilder sql = new StringBuilder("update `t_walmart_advertising_item` set `bid` =?,");
        sql.append("`status`=?, `update_time`=now() where `puid` =? and `shop_id`=? and `ad_group_id` = ? and `item_id` = ?");
        List<Object> argsList = new ArrayList<>();
        argsList.add(item.getBid());
        argsList.add(item.getStatus());
        argsList.add(puid);
        argsList.add(item.getShopId());
        argsList.add(item.getAdGroupId());
        argsList.add(item.getItemId());
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public int delete(Integer puid, Long id) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        return jdbcTemplate.update("DELETE from t_walmart_advertising_item where puid = ? and id = ?", puid, id);
    }

    @Override
    public WalmartAdvertisingItem getById(Integer puid, Long id) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingItem> getByCampaignId(Integer puid, Long shopId, Long campaignId) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingItem> getByShopIdListAndCampaignIdList(Integer puid, List<Integer> shopIds, List<String> campaignIds) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sb = new StringBuilder("select ad_item_id, update_time from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        return getJdbcTemplate(puid).query(sb.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public List<WalmartAdvertisingItem> getByGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sb = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        if (StringUtils.isNotBlank(campaignId)) {
            sb.append(" and campaign_id = ? ");
            argsList.add(campaignId);
        }
        sb.append(" and ad_group_id = ? ");
        argsList.add(adGroupId);

        return getJdbcTemplate(puid).query(sb.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page<WalmartAdvertisingItemPageDTO> getPageListWithReport(int puid, WalmartAdItemsVo queryParams) {
        StringBuilder selectSql = new StringBuilder("SELECT t1.*," + WalmartAdDaoUtil.REPORT_FIELDS + " FROM `t_walmart_advertising_item` t1 ");
        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM `t_walmart_advertising_item` t1 ");
        // 条件搜索
        List<Object> argsList = Lists.newArrayList();
        // 联表查询，统计指定时间段内的报告数据
        StringBuilder reportSql = new StringBuilder("select `puid`,`shop_id`,`campaign_id`,`ad_group_id`,`item_id`," + WalmartAdDaoUtil.reportFieldsSum(queryParams.getAttributeDayType()));
        reportSql.append(" from `t_walmart_advertising_item_report`").append(" where puid = ? ");
        argsList.add(puid);
        this.setLeftJoinConditions(queryParams, reportSql, argsList);
        reportSql.append(" group by puid, shop_id, campaign_id, ad_group_id,item_id ");
        String leftJoinSql = String.format(" left join ( %s ) t2 " +
                " on t1.puid = t2.puid and t1.shop_id = t2.shop_id and t1.campaign_id = t2.campaign_id " +
                "and t1.ad_group_id = t2.ad_group_id and t1.item_id = t2.item_id", reportSql);
        StringBuilder whereSql = new StringBuilder(leftJoinSql + " where t1.puid = ? ");
        argsList.add(puid);

        this.combinationSqlString(queryParams, argsList, whereSql);
        String sortName = queryParams.getOrderField();
        Integer sortValue = "desc".equals(queryParams.getOrderType())? 2 : 1;
        selectSql.append(whereSql).append(WalmartAdDaoUtil.getOrderByState(sortName, sortValue));
        countSql.append(whereSql);

        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, queryParams.getPageNo(), queryParams.getPageSize(), countSql.toString(), args, selectSql.toString(), args, WalmartAdvertisingItemPageDTO.class);
    }

    @Override
    public WalmartAdvertisingItemPageDTO getAggregate(int puid, WalmartAdItemsVo queryParams) {
        StringBuilder selectSql = new StringBuilder("SELECT t1.puid, " + WalmartAdDaoUtil.REPORT_FIELDS_SUM + " FROM `t_walmart_advertising_item` t1 ");
        // 条件搜索
        List<Object> argsList = Lists.newArrayList();
        // 联表查询，统计指定时间段内的报告数据
        StringBuilder reportSql = new StringBuilder("select `puid`,`shop_id`,`campaign_id`, `ad_group_id`,`item_id`," + WalmartAdDaoUtil.reportFieldsSum(queryParams.getAttributeDayType()));
        reportSql.append(" from `t_walmart_advertising_item_report`").append(" where puid = ? ");
        argsList.add(puid);
        setLeftJoinConditions(queryParams, reportSql, argsList);
        reportSql.append(" group by puid, shop_id, campaign_id , ad_group_id,item_id ");
        String leftJoinSql = String.format(" left join ( %s ) t2 " +
                " on t1.puid = t2.puid and t1.shop_id = t2.shop_id and t1.campaign_id = t2.campaign_id and t1.ad_group_id = t2.ad_group_id and t1.item_id = t2.item_id", reportSql);
        StringBuilder whereSql = new StringBuilder(leftJoinSql + " where t1.puid = ? ");
        argsList.add(puid);

        combinationSqlString(queryParams, argsList, whereSql);
        selectSql.append(whereSql);
        selectSql.append(" group by t1.puid");
        Object[] args = argsList.toArray();
        List<WalmartAdvertisingItemPageDTO> result = getJdbcTemplate(puid).query(selectSql.toString() ,(r,i) -> {
            WalmartAdvertisingItemPageDTO dto = new WalmartAdvertisingItemPageDTO();
            dto.setPuid(r.getInt("puid"));
            dto.setAdCost(r.getDouble("adCost"));
            dto.setAdSale(r.getDouble("adSale"));
            dto.setImpressions(r.getInt("impressions"));
            dto.setClicks(r.getInt("clicks"));
            dto.setAdOrderNum(r.getInt("adOrderNum"));
            dto.setAdSaleNum(r.getInt("adSaleNum"));
            return dto;
        }, args);
        return result.isEmpty() ? new WalmartAdvertisingItemPageDTO() : result.get(0);
    }

    @Override
    public int deleteByCampaignId(Integer puid, Long shopId, Long campaignId) {
        return 0;
    }

    @Override
    public int getCountByAdGroupId(Integer puid, Long shopId, Long campaignId, Long adGroupId) {
        return 0;
    }

    @Override
    public WalmartAdvertisingItem getByItemId(Integer puid, Long shopId, Long itemId) {
        return null;
    }

    @Override
    public WalmartAdvertisingItem getByItemId(Integer puid, Long shopId, Long campaignId, Long adGroupId, Long itemId) {
        return null;
    }
    @Override
    public void add(WalmartAdvertisingItem item) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(item.getPuid());

        StringBuilder sb = new StringBuilder();
        sb.append("insert into t_walmart_advertising_item  (puid,shop_id,marketplace_code,campaign_id,ad_group_id,item_id,ad_item_id,bid,status,item_image_url,item_page_url,name,review_status,review_reason,create_time,update_time )");
        sb.append("  values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now())");
        List<Object> argsList = new ArrayList<>();

        argsList.add(item.getPuid());
        argsList.add(item.getShopId());
        argsList.add(item.getMarketplaceCode());
        argsList.add(item.getCampaignId());
        argsList.add(item.getAdGroupId());
        argsList.add(item.getItemId());
        argsList.add(item.getAdItemId());
        argsList.add(item.getBid());
        argsList.add(StringUtils.isBlank(item.getStatus()) ? "" : item.getStatus().toLowerCase());
        argsList.add(StringUtil.cutLimitedStr(item.getItemImageUrl(), 2000));
        argsList.add(StringUtil.cutLimitedStr(item.getItemPageUrl(), 2000));
        argsList.add(StringUtil.cutLimitedStr(item.getName(), 1000));
        argsList.add(item.getReviewStatus());
        argsList.add(StringUtil.cutLimitedStr(item.getReviewReason(), 2000));
        sb.append(" on duplicate key update `bid` = values(bid), `status` = values(status), `review_status` = values(review_status), `review_reason`=values(review_reason), `update_time` = now(3) ");
        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    @Override
    public void addBatch(Integer puid, List<WalmartAdvertisingItem> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        StringBuilder sb = new StringBuilder("insert into t_walmart_advertising_item  (puid, shop_id, campaign_id, ad_group_id, item_id, ad_item_id, bid,status, item_image_url, item_page_url,name, review_status, review_reason, create_time, update_time ) VALUES ");
        List<Object> argsList = new ArrayList<>();
        for (WalmartAdvertisingItem item : itemList) {
            sb.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(item.getPuid());
            argsList.add(item.getShopId());
            argsList.add(item.getCampaignId());
            argsList.add(item.getAdGroupId());
            argsList.add(item.getItemId());
            argsList.add(item.getAdItemId());
            argsList.add(item.getBid());
            argsList.add(StringUtils.isBlank(item.getStatus()) ? "" : item.getStatus().toLowerCase());
            argsList.add(StringUtil.cutLimitedStr(item.getItemImageUrl(), 2000));
            argsList.add(StringUtil.cutLimitedStr(item.getItemPageUrl(), 2000));
            argsList.add(StringUtil.cutLimitedStr(item.getName(), 1000));
            argsList.add(item.getReviewStatus());
            argsList.add(StringUtil.cutLimitedStr(item.getReviewReason(), 2000));
        }
        sb.deleteCharAt(sb.length() - 1);//去掉结尾逗号
        sb.append(" on duplicate key update `bid` = values(bid), `status` = values(status), `review_status` = values(review_status), `review_reason`=values(review_reason), `update_time` = now(3) ");

        getJdbcTemplate(puid).update(sb.toString(), argsList.toArray());
    }

    @Override
    public int deleteByCampaignId(Integer puid, Integer shopId, String campaignId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        return jdbcTemplate.update("DELETE from t_walmart_advertising_item where puid = ? and shop_id = ? and campaign_id = ?", puid, shopId, campaignId);
    }

    @Override
    public int deleteByCampaignIdAndGroupIdBatch(Integer puid, List<Integer> shopIdList,
                                                 List<String> campaignIdList, List<String> groupIdList,
                                                 List<String> adItemId) {
        if (CollectionUtils.isEmpty(groupIdList)) {
            return 0;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("DELETE from t_walmart_advertising_item where puid = ?");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(adItemId)) {
            sql.append(SqlStringUtil.dealInList("ad_item_id", adItemId, argsList));
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<WalmartAdvertisingItem> getByCampaignId(Integer puid, Integer shopId, String campaignId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        String sql = "select * from `t_walmart_advertising_item` where `puid`=? and shop_id = ? and campaign_id = ?";
        return jdbcTemplate.query(sql, new Object[]{puid, shopId, campaignId}, getRowMapper());
    }

    @Override
    public WalmartAdvertisingItem getByAdItemId(Integer puid, Integer shopId, String adGroupId, String adItemId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("ad_item_id", adItemId);
        return getByCondition(puid, builder.build());
    }

    private void setLeftJoinConditions(WalmartAdItemsVo queryParams, StringBuilder leftJoinSql, List<Object> argsList) {
        if (CollectionUtils.isNotEmpty(queryParams.getShopIdList())) {
            leftJoinSql.append(" and shop_id in (").append(Joiner.on(",").join(queryParams.getShopIdList())).append(") ");
        }
        if (CollectionUtils.isNotEmpty(queryParams.getCampaignIdList())) {
            leftJoinSql.append(" and campaign_id in (").append(Joiner.on(",").join(queryParams.getCampaignIdList())).append(") ");
        }
        if (CollectionUtils.isNotEmpty(queryParams.getGroupIdList())) {
            leftJoinSql.append(" and ad_group_id in (").append(Joiner.on(",").join(queryParams.getGroupIdList())).append(") ");
        }
        if (StringUtils.isNotEmpty(queryParams.getStartDate())) {
            leftJoinSql.append(" and report_date >= ? ");
            argsList.add(queryParams.getStartDate());
        }
        if (StringUtils.isNotEmpty(queryParams.getEndDate())) {
            leftJoinSql.append(" and report_date <= ? ");
            argsList.add(queryParams.getEndDate());
        }
    }

    private void combinationSqlString(WalmartAdItemsVo queryParams,
                                      List<Object> argsList, StringBuilder whereSql) {
        if (CollectionUtils.isNotEmpty(queryParams.getShopIdList())) {
            whereSql.append(" and t1.shop_id in (").append(Joiner.on(",").join(queryParams.getShopIdList())).append(") ");
        }
        if (CollectionUtils.isNotEmpty(queryParams.getCampaignIdList())) {
            whereSql.append(" and t1.campaign_id in (").append(Joiner.on(",").join(queryParams.getCampaignIdList())).append(") ");
        }
        if (CollectionUtils.isNotEmpty(queryParams.getGroupIdList())) {
            whereSql.append(" and t1.ad_group_id in (").append(Joiner.on(",").join(queryParams.getGroupIdList())).append(") ");
        }
        if ("productName".equals(queryParams.getSearchField()) && StringUtils.isNotEmpty(queryParams.getSearchValue())) {
            whereSql.append(" and lower(t1.name) like ?");
            argsList.add("%"+SqlStringUtil.dealLikeSql(queryParams.getSearchValue().trim().toLowerCase())+"%");
        }
        if ("itemId".equals(queryParams.getSearchField()) && StringUtils.isNotEmpty(queryParams.getSearchValue())) {
            whereSql.append(" and t1.item_id = ?");
            argsList.add(queryParams.getSearchValue().trim());
        }
        List<String> status = queryParams.getState();
        if (Objects.nonNull(status)) {
            status = status.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(status)) {
            whereSql.append(SqlStringUtil.dealInList("t1.status", status, argsList));
        }
        if (CollectionUtils.isNotEmpty(queryParams.getReviewStatus())) {
            whereSql.append(SqlStringUtil.dealInList("t1.review_status", queryParams.getReviewStatus(), argsList));

        }
    }
}
