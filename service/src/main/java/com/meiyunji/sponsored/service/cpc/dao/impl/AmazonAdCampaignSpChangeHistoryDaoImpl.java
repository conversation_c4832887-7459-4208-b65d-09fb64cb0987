package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignSpChangeHistoryDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignSpChangeHistory;
import com.meiyunji.sponsored.service.cpc.vo.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
@Repository
public class AmazonAdCampaignSpChangeHistoryDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignSpChangeHistory> implements IAmazonAdCampaignSpChangeHistoryDao {


    @Override
    public List<AmazonAdCampaignSpChangeHistory> getAllHistoryByCampaignId(AdCampaignChangeHistoryParam param) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .equalTo("shop_id", param.getShopId())
                .equalTo("change_type","IN_BUDGET")
                .between("change_time",param.getStartDate(),param.getEndDate())
                .equalTo("campaign_id",param.getCampaignId()).orderByAsc("change_time").build();

        return listByCondition(param.getPuid(),conditionBuilder);
    }


    @Override
    public List<AmazonAdCampaignSpChangeHistory> getAllHistoryByCampaignIdHour(AdCampaignChangeHistoryParam param) {
        StringBuilder sql = new StringBuilder("select change_hour as `changeHour` , count(1) as `count`, GROUP_CONCAT(distinct DATE(change_time)) as `dateCount` from t_amazon_ad_campaign_sp_change_history where puid = ? and shop_id = ? and change_type = 'IN_BUDGET' and new_value = 'false' ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        args.add(param.getShopId());

        StringBuilder whereSql = new StringBuilder();
        if(param.getStartDate() != null && param.getEndDate() != null ){
            whereSql.append(" and change_time between ? and ? ");
            args.add(param.getStartDate());
            args.add(param.getEndDate());
        }
        if(StringUtils.isNotBlank(param.getCampaignId())){
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());

        }

        sql.append(whereSql).append(" group by change_hour");
        return getJdbcTemplate(param.getPuid()).query(sql.toString(),
                args.toArray(),
                BeanPropertyRowMapper.newInstance(AmazonAdCampaignSpChangeHistory.class));

    }

    @Override
    public Page<AmazonAdCampaignSpChangeHistory> pageList(AdCampaignChangeHistoryParam param){
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .equalTo("shop_id", param.getShopId())
                .equalTo("change_type","IN_BUDGET")
                .between("change_time",param.getStartDate(),param.getEndDate());


        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getCampaignId())) {
            builder.equalTo("campaign_id",param.getCampaignId());
        }
        String orderBySql = " order by change_time desc";

        return page(param.getPuid(),param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdCampaignChangeHistoryVo> amazonAdCampaignList) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_campaign_sp_change_history` (`puid`,`shop_id`,`marketplace_id`,`change_timestamp`,")
                .append("`change_type`,`campaign_id`,`previous_value`,`new_value`,`daily_budget`,`change_time`,`change_hour`,");
        sql.append("`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for(AmazonAdCampaignChangeHistoryVo amazonAdCampaign : amazonAdCampaignList){
            sql.append("(?,?,?,?,?,?,?,?,?,?,?");
            sql.append(",now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdCampaign.getShopId());
            argsList.add(amazonAdCampaign.getMarketplaceId());
            argsList.add(amazonAdCampaign.getChangeTimestamp());
            argsList.add(amazonAdCampaign.getChangeType());
            argsList.add(amazonAdCampaign.getCampaignId());
            argsList.add(amazonAdCampaign.getPreviousValue());
            argsList.add(amazonAdCampaign.getNewValue());
            argsList.add(amazonAdCampaign.getDailyBudget());
            argsList.add(amazonAdCampaign.getChangeTime());
            argsList.add(amazonAdCampaign.getChangeHour());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `previous_value`=values(previous_value),`new_value`=values(new_value),`daily_budget`=values(daily_budget),")
                .append("`update_time`=now()");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public AmazonAdCampaignSpChangeHistory getAllHistoryByCampaignIdMaxTimestamp(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid",puid);
        builder.equalTo("shop_id",shopId);
        builder.equalTo("campaign_id",campaignId)
                .orderByDesc("change_timestamp").limit(1);
        return getByCondition(puid,builder.build());
    }


    @Override
    public List<AmazonAdCampaignSpChangeHistory> getMaxTimeOutOfBudgetByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("SELECT shop_id as shopId,campaign_id as campaignId,max(change_timestamp) as changeTimestamp FROM t_amazon_ad_campaign_sp_change_history where puid = ? and shop_id = ? and change_type = 'IN_BUDGET' ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id",campaignIds,args));
        sql.append(" and new_value = 'false' ");
        sql.append(" group by campaign_id,shop_id");
        return getJdbcTemplate(puid).query(sql.toString(),
                args.toArray(),
                BeanPropertyRowMapper.newInstance(AmazonAdCampaignSpChangeHistory.class));

    }


    @Override
    public AmazonAdCampaignSpChangeHistory getAllHistoryByCampaignIdAndBudgetAmount(Integer puid, Integer shopId, String campaignId,Long time) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid",puid);
        builder.equalTo("shop_id",shopId);
        builder.equalTo("campaign_id",campaignId)
                .equalTo("change_type","BUDGET_AMOUNT")
                .lessThan("change_timestamp",time)
                .orderByDesc("change_timestamp").limit(1);
        return getByCondition(puid,builder.build());
    }


}