package com.meiyunji.sponsored.service.syncAd.task.init.sp;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdProductApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.service.IAmazonAdShopDataInitTaskService;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sp + ShopDataSyncConstant.product)
public class SpProductTask extends AdShopDataSyncTask {

    @Autowired
    private CpcAdProductApiService cpcAdProductApiService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sp);
        setTaskType(ShopDataSyncTaskTypeEnum.PRODUCT);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步广告产品，查询所有状态
        cpcAdProductApiService.syncAds(shop, null, task.getAdGroupId(), null, null, true);
    }
}
