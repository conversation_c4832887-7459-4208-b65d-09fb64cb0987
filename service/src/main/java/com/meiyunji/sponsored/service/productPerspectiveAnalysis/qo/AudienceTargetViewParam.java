package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;

import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-01  16:27
 * 受众投放
 */

@Data
public class AudienceTargetViewParam extends ViewBaseParam {
    //广告活动ID
    private String campaignId;

    //广告组id
    private String groupId;

    //投放类型，下拉筛选的类型
    private String targetType;

    //投放二级类型，亚马逊消费对应type、再营销浏览定向和购买再营销对应targetText
    private String targetText;

    //有效状态
    private String status;

    //用于仅展示正在投放字段 勾选后传值 enabled
    private String servingStatus;

    //搜索值
    private String queryValue;

    private List<String> adGroupIdList;
    //用于小时分析
    private List<String> targetIdList;

    private String uuid;
}
