package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.sd.constant.TargetingTypeEnum;
import com.amazon.advertising.sd.mode.SDAudienceCategoryRecommendations;
import com.amazon.advertising.sd.mode.SDTargetingRecommendationsV35;
import com.amazon.advertising.spV3.targeting.AudienceV1;
import com.amazon.advertising.spV3.targeting.ListAudiencesTaxonomyResult;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.vo.AudiencesFilterListRpcVo;
import com.meiyunji.sponsored.rpc.vo.AudiencesListRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.SDTaticEnum;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdAudiencesService;
import com.meiyunji.sponsored.service.cpc.vo.SuggestAudienceInfo;
import com.meiyunji.sponsored.service.cpc.vo.SuggestAudiencesVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.Build;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/8/6 15:42
 * @describe:
 */
@Service
public class CpcSdAudiencesServiceImpl implements ICpcSdAudiencesService {

    @Autowired
    private CpcSdAudiencesApiService cpcSdAudiencesApiService;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public AudiencesListRpcVo getAudienceTaxonomyList(Integer puid, ShopAuth shop,
                                                      List<String> categoryPath) {
        if (Objects.isNull(shop)) {
            throw new ServiceException("没有CPC授权");
        }
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            throw new ServiceException("没有站点对应的配置信息");
        }

        ListAudiencesTaxonomyResult result = cpcSdAudiencesApiService.getTaxonomyList(shop, amazonAdProfile, categoryPath);

        AudiencesListRpcVo.Builder audiencesBuilder = AudiencesListRpcVo.newBuilder();
        if (Objects.nonNull(result)) {
            //拼装返回结构
            Optional.ofNullable(result.getCategoryPath()).ifPresent(audiencesBuilder::addAllCategoryPath);
            if (CollectionUtils.isNotEmpty(result.getCategories())) {
                audiencesBuilder.addAllCategories(result.getCategories().stream().map(category -> {
                    AudiencesListRpcVo.FetchTaxonomyNodeV1.Builder taxonomy = AudiencesListRpcVo.FetchTaxonomyNodeV1.newBuilder();
                    Optional.ofNullable(category.getCategory()).ifPresent(taxonomy::setCategory);
                    Optional.ofNullable(category.getAudienceCount()).ifPresent(taxonomy::setAudienceCount);
                    return taxonomy.build();
                }).collect(Collectors.toList()));
            }
        }

        return audiencesBuilder.build();
    }

    @Override
    public List<AudiencesFilterListRpcVo> getAudienceFilterList(Integer puid, ShopAuth shop, List<String> categoryPath, String audienceName) {
        if (Objects.isNull(shop)) {
            throw new ServiceException("没有CPC授权");
        }
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            throw new ServiceException("没有站点对应的配置信息");
        }
        List<AudienceV1> result = cpcSdAudiencesApiService.getAudiencesFilterList(shop, amazonAdProfile, categoryPath, audienceName);

        List<AudiencesFilterListRpcVo> audienceInfoList = new ArrayList<>();
        if (Objects.nonNull(result)) {
            for (AudienceV1 audience : result) {
                AudiencesFilterListRpcVo.Builder audiencesBuilder = AudiencesFilterListRpcVo.newBuilder();
                Optional.ofNullable(audience.getAudienceId()).ifPresent(audiencesBuilder::setAudienceId);
                Optional.ofNullable(audience.getAudienceName()).ifPresent(audiencesBuilder::setAudienceName);
                Optional.ofNullable(audience.getDescription()).ifPresent(audiencesBuilder::setDescription);
                Optional.ofNullable(audience.getCategory()).ifPresent(audiencesBuilder::setCategory);
                AudiencesFilterListRpcVo.ForecastVO.Builder forecast = AudiencesFilterListRpcVo.ForecastVO.newBuilder();
                if (Objects.nonNull(audience.getForecasts()) && Objects.nonNull(audience.getForecasts().getInventoryForecasts())
                        && Objects.nonNull(audience.getForecasts().getInventoryForecasts().getAll()) && Objects.nonNull(audience.getForecasts().getInventoryForecasts().getAll().getDailyReach())) {
                    Optional.ofNullable(audience.getForecasts().getInventoryForecasts().getAll().getDailyReach().getLowerBoundInclusive()).ifPresent(forecast::setLowerBoundInclusive);
                    Optional.ofNullable(audience.getForecasts().getInventoryForecasts().getAll().getDailyReach().getUpperBoundExclusive()).ifPresent(forecast::setUpperBoundExclusive);
                    audiencesBuilder.setForecasts(forecast.build());
                }
                audienceInfoList.add(audiencesBuilder.build());
            }
        }
        return audienceInfoList;
    }
}
