package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignPlacementReport;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;

import java.util.List;

public interface IAmazonAdCampaignPlacementReportDao extends IBaseShardingSphereDao<AmazonAdCampaignPlacementReport> {

    /**
     * 批量插入、更新
     *
     * @param puid 商户id
     * @param list 广告位报告
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdCampaignPlacementReport> list);

    /**
     * 报告下载中心 下载广告位数据
     *
     * @param puid   商户id
     * @param search 请求参数
     * @param page   分页参数
     * @return 响应参数
     */
    Page getPageSpaceList(Integer puid, SearchVo search, Page page);


    /**
     * 根据活动id获取广告位报告数据
     *
     * @param puid          商户id
     * @param shopId        店铺id
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param marketplaceId 站点id
     * @param campaignId    活动id
     * @param placement     广告位
     * @return 响应参数
     */
    List<AmazonAdCampaignAllReport> getReportByCampaignIdAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String campaignId, String placement);

    /**
     * 根据活动id集合获取广告位报告数据
     *
     * @param puid           商户id
     * @param shopId         店铺id
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param marketplaceId  站点id
     * @param campaignIdList 活动id集合
     * @param placement      广告位
     * @return 响应参数
     */
    List<AmazonAdCampaignAllReport> getReportByCampaignIdListAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList, String placement);

    /**
     * 获取广告位汇总报告
     *
     * @param puid  商户id
     * @param param 请求参数
     * @return 响应参数
     */
    List<AmazonAdCampaignAllReport> listSumPlacementReports(Integer puid, PlacementPageParam param);

    /**
     * 获取报告数据 根据活动id分组
     *
     * @param puid       商户id
     * @param shopId     店铺id
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param campaignId 活动id
     * @param predicate  广告位
     * @param param      请求参数
     * @return 响应参数
     */
    List<AdHomePerformancedto> listAllPlacementReportsByDate(Integer puid, Integer shopId, String startDate, String endDate, String campaignId, String predicate, PlacementPageParam param);

    /**
     * 获取报告数据 根据日期分组
     *
     * @param puid           商户id
     * @param shopId         店铺id
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @param predicate      广告位
     * @param campaignIdList 请求参数
     * @return 响应参数
     */
    List<AdHomePerformancedto> listAllPlacementReportsByCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, String predicate, List<String> campaignIdList);
}
