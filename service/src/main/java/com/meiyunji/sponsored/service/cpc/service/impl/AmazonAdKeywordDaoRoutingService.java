package com.meiyunji.sponsored.service.cpc.service.impl;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordCardAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdTargetBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNeKeywordsDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordShardingDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNeKeywords;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNetargetingSp;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeKeyword;
import com.meiyunji.sponsored.service.cpc.qo.KeywordSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewAggregateDto;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * keyword表路由层，这个service主要是用于上线，后续可以不用这个service
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Service
public class AmazonAdKeywordDaoRoutingService implements IAmazonAdKeywordDaoRoutingService {


    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNeKeywordDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;


    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdKeyword> amazonAdKeywords, String type) {

        insertSharding(puid, amazonAdKeywords, type);

    }

    private void insertSharding(Integer puid, List<AmazonAdKeyword> amazonAdKeywords, String type) {
        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdKeywords.stream().map(e -> {
                AmazonAdNeKeyword ne = converToNe(e);
                return ne;
            }).collect(Collectors.toList());
            amazonAdNeKeywordDao.insertOnDuplicateKeyUpdate(puid, amazonAdNeKeywords);
        } else {
            amazonAdKeywordShardingDao.insertOnDuplicateKeyUpdate(puid, amazonAdKeywords);
        }
    }

    @Override
    public Integer countByDxmAdGroupId(int puid, Integer shopId, String adGroupId) {
        return amazonAdKeywordShardingDao.countByDxmAdGroupId(puid, shopId, adGroupId);
    }

    @Override
    public List<String> getKeywordIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineKeywordId) {
        return amazonAdKeywordShardingDao.getKeywordIds(puid, shopId, marketPlaceId, onlineKeywordId);
    }

    @Override
    public List<AmazonAdKeyword> listKeyWordAndMatchTypeByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdNeKeywordDao.listKeyWordAndMatchTypeByGroupIdList(puid, shopId, groupIdList);
        List<AmazonAdKeyword> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
            result.addAll(amazonAdNeKeywords.stream().map(e -> {
                AmazonAdKeyword keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList()));
        }
        List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordShardingDao.listKeyWordAndMatchTypeByGroupIdList(puid, shopId, groupIdList);
        if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
            result.addAll(amazonAdKeywords);
        }
        return result;
    }

    @Override
    public List<AmazonAdKeyword> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {

        List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdNeKeywordDao.listByGroupIdList(puid, shopId, groupIdList);
        List<AmazonAdKeyword> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
            result.addAll(amazonAdNeKeywords.stream().map(e -> {
                AmazonAdKeyword keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList()));
        }
        List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordShardingDao.listByGroupIdList(puid, shopId, groupIdList);
        if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
            result.addAll(amazonAdKeywords);
        }
        return result;

    }


    @Override
    public List<AmazonAdKeyword> listByCampaignOrGroupOrName(AllUpdateAutoRuleParam allUpdateAutoRuleParam) {
        List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdNeKeywordDao.listByCampaignOrGroupOrName(allUpdateAutoRuleParam);
        List<AmazonAdKeyword> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
            result.addAll(amazonAdNeKeywords.stream().map(e -> {
                AmazonAdKeyword keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList()));
        }
        List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordShardingDao.listByCampaignOrGroupOrName(allUpdateAutoRuleParam);
        if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
            result.addAll(amazonAdKeywords);
        }
        return result;
    }

    @Override
    public AmazonAdKeyword getByKeywordId(int puid, Integer shopId, String marketplaceId, String keywordId) {

        return amazonAdKeywordShardingDao.getByKeywordId(puid, shopId, marketplaceId, keywordId);

    }


    @Override
    public AmazonAdKeyword getByKeywordId(int puid, Integer shopId, String keywordId) {
        return amazonAdKeywordShardingDao.getByKeywordId(puid, shopId, keywordId);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {
        return amazonAdKeywordShardingDao.pageListForTask(puid, dto, page);

    }

    @Override
    public List<AmazonAdKeyword> listKeywords(int puid, List<String> keywordIdList) {
        return amazonAdKeywordShardingDao.listKeywords(puid, keywordIdList);
    }

    /**
     * 只能查否定
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param campaignId
     * @param adGroupId
     * @param query
     * @param negative
     * @return
     */
    @Override
    public AmazonAdKeyword getByKeywordText(Integer puid, Integer shopId, String marketplaceId, String campaignId, String adGroupId, String query, String negative) {
        AmazonAdNeKeyword byKeywordText = amazonAdNeKeywordDao.getByKeywordText(puid, shopId, marketplaceId, campaignId, adGroupId, query, negative);
        if (byKeywordText == null) {
            return null;
        }
        AmazonAdKeyword amazonAdKeyword = converToPo(byKeywordText);
        return amazonAdKeyword;
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String adGroupId, String keywordText, String matchType, String type) {
        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            return amazonAdNeKeywordDao.exist(puid, shopId, adGroupId, keywordText, matchType);
        } else {
            return amazonAdKeywordShardingDao.exist(puid, shopId, adGroupId, keywordText, matchType);
        }
    }

    @Override
    public Page<AmazonAdKeyword> neKeywordsPageList(Integer puid, NeKeywordsPageParam param) {
        Page<AmazonAdNeKeyword> amazonAdNeKeywordPage = amazonAdNeKeywordDao.neKeywordsPageList(puid, param);
        Page<AmazonAdKeyword> page = new Page<>();
        page.setPageNo(amazonAdNeKeywordPage.getPageNo());
        page.setTotalPage(amazonAdNeKeywordPage.getTotalPage());
        page.setPageSize(amazonAdNeKeywordPage.getPageSize());
        List<AmazonAdKeyword> rows = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdNeKeywordPage.getRows())) {
            rows = amazonAdNeKeywordPage.getRows().stream().map(e -> {
                AmazonAdKeyword keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList());
        }
        page.setRows(rows);
        return page;
    }

    @Override
    public Page<AmazonAdKeyword> keywordsPageList(Integer puid, KeywordsPageParam param) {
        return amazonAdKeywordShardingDao.keywordsPageList(puid, param);
    }

    @Override
    public List<AmazonAdKeyword> listByCondition(Integer puid, KeywordsPageParam param) {

        return amazonAdKeywordShardingDao.listByCondition(puid, param);
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        return amazonAdKeywordShardingDao.statCountByAdGroup(puid, shopId, status, adGroupIds);
    }

    @Override
    public int countByGroup(Integer puid, Integer shopId, String adGroupId) {
        return amazonAdKeywordShardingDao.countByGroup(puid, shopId, adGroupId);
    }

    @Override
    public List getByKeywordIds(int puid, Integer shopId, List<String> keywordIds, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            List<AmazonAdNeKeyword> byKeywordIds = amazonAdNeKeywordDao.getByKeywordIds(puid, shopId, keywordIds);
            if (CollectionUtils.isNotEmpty(byKeywordIds)) {
                return byKeywordIds.stream().map(this::converToPo).collect(Collectors.toList());
            } else {
                return new ArrayList();
            }
        } else {
            return amazonAdKeywordShardingDao.getByKeywordIds(puid, shopId, keywordIds);
        }

    }

    @Override
    public List<AmazonAdKeyword> autoRuleKeyword(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds, List<String> seringStatus) {
        return amazonAdKeywordShardingDao.autoRuleKeyword(puid, shopId, campaignIds, adGroupIds, state, matchType, keywordText, keywordIds, seringStatus);
    }

    @Override
    public List<AmazonAdKeyword> getByKeywordSuggestBidBatchQo(int puid, List<KeywordSuggestBidBatchQo> keywordList) {
        return amazonAdKeywordShardingDao.getByKeywordSuggestBidBatchQo(puid, keywordList);
    }

    @Override
    public List<AmazonAdKeyword> getByKeywordIds(int puid, List<String> keywordIds) {
        return amazonAdKeywordShardingDao.getByKeywordIds(puid, keywordIds);
    }

    @Override
    public void batchUpdateSuggestValue(Integer puid, List<AmazonAdKeyword> amazonAdKeywords) {
        amazonAdKeywordShardingDao.batchUpdateSuggestValue(puid, amazonAdKeywords);
    }

    @Override
    public Page<NeKeywordsPageDto> getAllTypeNeKeyword(Integer puid, NeKeywordsPageParam param) {

        return amazonAdNeKeywordDao.getAllTypeNeKeyword(puid, param);

    }

    @Override
    public void updatePricing(Integer puid, Integer shopId, String keywordId, Integer isPricing, Integer pricingState, int updateId) {

        amazonAdKeywordShardingDao.updatePricing(puid, shopId, keywordId, isPricing, pricingState, updateId);

    }

    @Override
    public Page getPageList(Integer puid, KeywordsPageParam param, Page page) {

        return amazonAdKeywordShardingDao.getPageList(puid, param, page);

    }

    @Override
    public List<AmazonAdKeyword> getList(Integer puid, KeywordsPageParam param) {

        return amazonAdKeywordShardingDao.getList(puid, param);

    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String keywordId, LocalDate localDate) {

        amazonAdKeywordShardingDao.updateDataUpdateTime(puid, shopId, keywordId, localDate);

    }

    @Override
    public List<AmazonAdKeyword> getListByIds(Integer puid, Integer shopId, List<Long> ids) {

        return amazonAdKeywordShardingDao.getListByIds(puid, shopId, ids);

    }

    @Override
    public void updateList(Integer puid, List<AmazonAdKeyword> list, String type, String targetType) {

        if (Constants.NEGATIVE.equalsIgnoreCase(targetType)) {
            List<AmazonAdNeKeyword> amazonAdNeKeywords = list.stream().map(this::converToNe).collect(Collectors.toList());
            amazonAdNeKeywordDao.updateList(puid, amazonAdNeKeywords, type);
        } else {
            amazonAdKeywordShardingDao.updateList(puid, list, type);
        }

    }

    @Override
    public List<String> getKeywordByCampaignId(Integer puid, Integer shopId, String campaignId) {

        return amazonAdKeywordShardingDao.getKeywordByCampaignId(puid, shopId, campaignId);

    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {

        return amazonAdKeywordShardingDao.getArchivedItems(puid, shopId);

    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {

        return amazonAdKeywordShardingDao.getUpdateAfterReportSyncTimeItems(puid, shopId, syncAt);

    }

    @Override
    public List<AmazonAdKeyword> getListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {

        return amazonAdKeywordShardingDao.getListByGroupIds(puid, shopId, adGroupIds);

    }

    @Override
    public List<String> getKeywordByShopId(Integer puid, CpcQueryWordDto dto) {

        return amazonAdKeywordShardingDao.getKeywordByShopId(puid, dto);

    }

    @Override
    public List<String> getKeywordIdsByKeyword(Integer puid, KeywordsPageParam param) {

        return amazonAdKeywordShardingDao.getKeywordIdsByKeyword(puid, param);

    }

    @Override
    public Integer statSumCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {

        return amazonAdKeywordShardingDao.statSumCountByAdGroup(puid, shopId, status, adGroupIds);

    }

    @Override
    public Integer statSumCountByAdGroupPage(Integer puid, Integer shopId, List<String> status, GroupPageParam param) {

        return amazonAdKeywordShardingDao.statSumCountByAdGroupPage(puid, shopId, status, param);

    }

    @Override
    public Page<AmazonAdKeyword> querySpKeyword(AdTargetStrategyParam param) {

        return amazonAdKeywordShardingDao.querySpKeyword(param);

    }

    @Override
    public Page<AmazonAdKeyword> pageAutoRuleSpKeyword(AdKeywordTargetAutoRuleParam param, List<String> itemIds, List<String> similarRuleItemIdList) {

        return amazonAdKeywordShardingDao.pageAutoRuleSpKeyword(param, itemIds, similarRuleItemIdList);

    }

    @Override
    public Page<AmazonAdKeyword> pageKeywordCardSpKeyword(AdKeywordCardAutoRuleParam param, List<String> itemIds) {

        return amazonAdKeywordShardingDao.pageKeywordCardSpKeyword(param, itemIds);

    }

    @Override
    public List<String> queryAutoRuleSpKeyword(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String matchType) {

        return amazonAdKeywordShardingDao.queryAutoRuleSpKeyword(puid, shopId, campaignIds, groupIds, state, matchType);

    }

    @Override
    public List<String> querySpKeywordIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String targetType, String matchType, List<String> keywordIdList) {

        return amazonAdKeywordShardingDao.querySpKeywordIdList(puid, shopId, campaignIds, groupIds, state, targetType, matchType, keywordIdList);

    }

    @Override
    public List<KeywordLibsDetailVo> aggregateKeyword(Integer puid, List<String> keywordTextList) {

        return amazonAdKeywordShardingDao.aggregateKeyword(puid, keywordTextList);

    }

    @Override
    public void updateKeywordRank(Integer puid, Integer shopId, String keywordId, String advRank, Integer uid) {

        amazonAdKeywordShardingDao.updateKeywordRank(puid, shopId, keywordId, advRank, uid);

    }

    @Override
    public void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message) {

        amazonAdKeywordShardingDao.strategyUpdate(puid, shopId, message);

    }

    @Override
    public void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message) {

        amazonAdKeywordShardingDao.autoUpdate(puid, shopId, message);

    }

    @Override
    public List<AmazonAdKeyword> getKeywordsByGroupIdsAndKeywordTexts(Integer puid, List<String> groupIds, List<String> keywordTexts) {

        return amazonAdKeywordShardingDao.getKeywordsByGroupIdsAndKeywordTexts(puid, groupIds, keywordTexts);

    }

    @Override
    public List<AmazonAdKeyword> getListKeywordByKeywordIds(Integer puid, List<Integer> shopIds, List<String> keywords) {

        return amazonAdKeywordShardingDao.getListKeywordByKeywordIds(puid, shopIds, keywords);

    }

    @Override
    public List<AmazonAdKeyword> getKeywordViewList(Integer puid, KeywordViewParam param) {

        return amazonAdKeywordShardingDao.getKeywordViewList(puid, param);

    }

    @Override
    public Page<AmazonAdKeyword> getKeywordViewPage(Integer puid, KeywordViewParam param) {

        return amazonAdKeywordShardingDao.getKeywordViewPage(puid, param);

    }

    @Override
    public List<KeywordViewAggregateDto> getKeywordViewAggregateList(Integer puid, KeywordViewParam param) {

        return amazonAdKeywordShardingDao.getKeywordViewAggregateList(puid, param);

    }

    @Override
    public List<String> getKeywordIdListByKeywordViewParam(Integer puid, KeywordViewParam param) {

        return amazonAdKeywordShardingDao.getKeywordIdListByKeywordViewParam(puid, param);

    }

    @Override
    public List<String> getDiagnoseCountKeywordId(DiagnoseCountParam param) {

        return amazonAdKeywordShardingDao.getDiagnoseCountKeywordId(param);

    }

    @Override
    public void keywordCardUpdate(int puid, AdvertiseAutoRuleExecuteRecord message) {

        amazonAdKeywordShardingDao.keywordCardUpdate(puid, message);

    }

    @Override
    public List getListKeywordByQuery(Integer puid, Integer shopId, String adGroupId, String keywordText, String matchType, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            return amazonAdNeKeywordDao.getListKeywordByQuery(puid, shopId, adGroupId, keywordText, matchType, type);
        } else {
            return amazonAdKeywordShardingDao.getListKeywordByQuery(puid, shopId, adGroupId, keywordText, matchType, type);
        }

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        amazonAdNeKeywordDao.deleteByPuidAndShopId(puid, shopId, limit);
        return amazonAdKeywordShardingDao.deleteByPuidAndShopId(puid, shopId, limit);
    }

    @Override
    public Page<KeywordPageVo> getKeywordPage(Integer puid, KeywordsPageParam param) {

        return amazonAdKeywordShardingDao.getKeywordPage(puid, param);

    }

    @Override
    public List<KeywordPageVo> getKeywordPageVoListByKeywordIdList(Integer puid, KeywordsPageParam param, List<String> keywordIdList) {

        return amazonAdKeywordShardingDao.getKeywordPageVoListByKeywordIdList(puid, param, keywordIdList);

    }

    @Override
    public List<String> getKeywordIdListByParam(Integer puid, KeywordsPageParam param, List<String> keywordIdList) {

        return amazonAdKeywordShardingDao.getKeywordIdListByParam(puid, param, keywordIdList);

    }

    @Override
    public List<AdOrderBo> getKeywordIdAndOrderFieldList(Integer puid, KeywordsPageParam param, List<String> keywordIdList, String orderField) {

        return amazonAdKeywordShardingDao.getKeywordIdAndOrderFieldList(puid, param, keywordIdList, orderField);

    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdSet(Integer puid, Integer shopIs, Set<String> groupIdSet) {

        return amazonAdKeywordShardingDao.countByGroupIdSet(puid, shopIs, groupIdSet);

    }

    @Override
    public List<AmazonAdKeyword> listKeywordByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {

        return amazonAdKeywordShardingDao.listKeywordByGroupIdList(puid, shopId, groupIdList);

    }

    @Override
    public List<AmazonAdKeyword> listKeywordByGroupId(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdKeywordShardingDao.listKeywordByGroupId(puid, shopId, adGroupId);

    }

    @Override
    public List<AmazonAdKeyword> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList) {

        return amazonAdKeywordShardingDao.listByGroupIdAndItemIdList(puid, shopId, adGroupId, itemIdList);

    }


    @Override
    public AmazonAdKeyword getByPuidAndId(Integer puid, Long id, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            AmazonAdNeKeyword byPuidAndId = amazonAdNeKeywordDao.getByPuidAndId(puid, id);
            if (byPuidAndId != null) {
                AmazonAdKeyword keyword = converToPo(byPuidAndId);
                return keyword;
            }
        } else {
            return amazonAdKeywordShardingDao.getByPuidAndId(puid, id);
        }
        return null;

    }


    @Override
    public int updateById(Integer puid, AmazonAdKeyword amazonAdKeyword, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            AmazonAdNeKeyword amazonAdNeKeyword = converToNe(amazonAdKeyword);
            return amazonAdNeKeywordDao.updateById(puid, amazonAdNeKeyword);
        } else {
            return amazonAdKeywordShardingDao.updateById(puid, amazonAdKeyword);
        }

    }


    @Override
    public List<AmazonAdKeyword> getListByLongIdList(Integer puid, List<Long> idList, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            List<AmazonAdNeKeyword> byPuidAndId = amazonAdNeKeywordDao.getListByLongIdList(puid, idList);
            if (CollectionUtils.isNotEmpty(byPuidAndId)) {
                return byPuidAndId.stream().map(this::converToPo).collect(Collectors.toList());
            }
            return new ArrayList<>();
        } else {
            return amazonAdKeywordShardingDao.getListByLongIdList(puid, idList);
        }

    }

    @Override
    public List<AdTargetBo> getAdTargetBoList(Integer puid, KeywordsPageParam param, Integer limit) {

        return amazonAdKeywordShardingDao.getAdTargetBoList(puid, param, limit);

    }

    @Override
    public AmazonAdNeKeyword converToNe(AmazonAdKeyword amazonAdKeyword) {
        AmazonAdNeKeyword ne = new AmazonAdNeKeyword();
        ne.setId(amazonAdKeyword.getId());
        ne.setUniqueKey(amazonAdKeyword.getUniqueKey());
        ne.setPuid(amazonAdKeyword.getPuid());
        ne.setShopId(amazonAdKeyword.getShopId());
        ne.setMarketplaceId(amazonAdKeyword.getMarketplaceId());
        ne.setKeywordId(amazonAdKeyword.getKeywordId());
        ne.setAdGroupId(amazonAdKeyword.getAdGroupId());
        ne.setDxmGroupId(amazonAdKeyword.getDxmGroupId());
        ne.setCampaignId(amazonAdKeyword.getCampaignId());
        ne.setProfileId(amazonAdKeyword.getProfileId());
        ne.setKeywordText(amazonAdKeyword.getKeywordText());
        ne.setMatchType(amazonAdKeyword.getMatchType());
        ne.setBid(amazonAdKeyword.getBid());
        ne.setType(amazonAdKeyword.getType());
        ne.setState(amazonAdKeyword.getState());
        ne.setServingStatus(amazonAdKeyword.getServingStatus());
        ne.setRangeStart(amazonAdKeyword.getRangeStart());
        ne.setRangeEnd(amazonAdKeyword.getRangeEnd());
        ne.setSuggested(amazonAdKeyword.getSuggested());
        ne.setCreateId(amazonAdKeyword.getCreateId());
        ne.setUpdateId(amazonAdKeyword.getUpdateId());
        ne.setIsPricing(amazonAdKeyword.getIsPricing());
        ne.setPricingState(amazonAdKeyword.getPricingState());
        ne.setAdvRank(amazonAdKeyword.getAdvRank());
        ne.setDataUpdateTime(amazonAdKeyword.getDataUpdateTime());
        ne.setServingStatusName(amazonAdKeyword.getServingStatusName());
        ne.setServingStatusDec(amazonAdKeyword.getServingStatusDec());
        ne.setError(amazonAdKeyword.getError());
        ne.setIndex(amazonAdKeyword.getIndex());
        ne.setCreateTime(amazonAdKeyword.getCreateTime());
        ne.setUpdateTime(amazonAdKeyword.getUpdateTime());
        ne.setCreationDate(amazonAdKeyword.getCreationDate());
        return ne;
    }

    @Override
    public AmazonAdKeyword converToPo(AmazonAdNeKeyword amazonAdKeyword) {
        AmazonAdKeyword ne = new AmazonAdKeyword();
        ne.setId(amazonAdKeyword.getId());
        ne.setUniqueKey(amazonAdKeyword.getUniqueKey());
        ne.setPuid(amazonAdKeyword.getPuid());
        ne.setShopId(amazonAdKeyword.getShopId());
        ne.setMarketplaceId(amazonAdKeyword.getMarketplaceId());
        ne.setKeywordId(amazonAdKeyword.getKeywordId());
        ne.setAdGroupId(amazonAdKeyword.getAdGroupId());
        ne.setDxmGroupId(amazonAdKeyword.getDxmGroupId());
        ne.setCampaignId(amazonAdKeyword.getCampaignId());
        ne.setProfileId(amazonAdKeyword.getProfileId());
        ne.setKeywordText(amazonAdKeyword.getKeywordText());
        ne.setMatchType(amazonAdKeyword.getMatchType());
        ne.setBid(amazonAdKeyword.getBid());
        ne.setType(amazonAdKeyword.getType());
        ne.setState(amazonAdKeyword.getState());
        ne.setServingStatus(amazonAdKeyword.getServingStatus());
        ne.setRangeStart(amazonAdKeyword.getRangeStart());
        ne.setRangeEnd(amazonAdKeyword.getRangeEnd());
        ne.setSuggested(amazonAdKeyword.getSuggested());
        ne.setCreateId(amazonAdKeyword.getCreateId());
        ne.setUpdateId(amazonAdKeyword.getUpdateId());
        ne.setIsPricing(amazonAdKeyword.getIsPricing());
        ne.setPricingState(amazonAdKeyword.getPricingState());
        ne.setAdvRank(amazonAdKeyword.getAdvRank());
        ne.setDataUpdateTime(amazonAdKeyword.getDataUpdateTime());
        ne.setServingStatusName(amazonAdKeyword.getServingStatusName());
        ne.setServingStatusDec(amazonAdKeyword.getServingStatusDec());
        ne.setError(amazonAdKeyword.getError());
        ne.setIndex(amazonAdKeyword.getIndex());
        ne.setCreateTime(amazonAdKeyword.getCreateTime());
        ne.setUpdateTime(amazonAdKeyword.getUpdateTime());
        return ne;
    }

    @Override
    public void updateList(Integer puid, List<AmazonAdKeyword> list, String type) {

        if (Constants.NEGATIVE.equalsIgnoreCase(type)) {
            List<AmazonAdNeKeyword> amazonAdNeKeywords = list.stream().map(this::converToNe).collect(Collectors.toList());
            amazonAdNeKeywordDao.updateList(puid, amazonAdNeKeywords);
        } else {
            amazonAdKeywordShardingDao.updateList(puid, list);
        }


    }

    @Override
    public List<AmazonAdKeyword> getListByTargetTaskCondition(Integer puid, Integer shopId, Set<String> adGroupIds, Set<String> keywordTexts, Set<String> matchTypes) {
        return amazonAdKeywordShardingDao.getListByTargetTaskCondition(puid, shopId, adGroupIds, keywordTexts, matchTypes);
    }

    @Override
    public List<AmazonAdNeKeyword> getNeListByTargetTaskCondition(Integer puid, Integer shopId, Set<String> adGroupIds, Set<String> keywordTexts, Set<String> matchTypes) {
        return amazonAdNeKeywordDao.getListByTargetTaskCondition(puid, shopId, adGroupIds, keywordTexts, matchTypes);
    }

    @Override
    public List<AmazonAdCampaignNeKeywords> getCampaignNeListByTargetCondition(Integer puid, Integer shopId, Set<String> campaignIds, Set<String> keywordTexts, Set<String> matchTypes) {
        return amazonAdCampaignNeKeywordsDao.getCampaignNeKeywordsByCondition(puid, shopId, campaignIds, keywordTexts, matchTypes);
    }
}
