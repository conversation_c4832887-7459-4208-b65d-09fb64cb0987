package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/12/2 18:50
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AsinsLibMarkupAsinVo {
    private String asin;
    private String marketplaceId;
    private String marketplaceCN;
    private List<Long> ids;
}
