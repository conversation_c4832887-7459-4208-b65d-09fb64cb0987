package com.meiyunji.sponsored.service.common.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.common.dao.IWordTranslateDao;
import com.meiyunji.sponsored.service.common.po.WordTranslate;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-03-13  20:24
 */
@Repository
public class WordTranslateDaoImpl extends AdBaseDaoImpl<WordTranslate> implements IWordTranslateDao {

    private static final String tableName = "t_ad_word_translate_";

    @Override
    public List<WordTranslate> listByMarketplaceIdAndWord(List<WordTranslateQo> qo) {
        if (CollectionUtils.isEmpty(qo)) {
            return new ArrayList<>();
        }
        Map<String, List<String>> marketplaceIdWordsMap = qo.stream().collect(Collectors.groupingBy(WordTranslateQo::getMarketplaceId, Collectors.mapping(WordTranslateQo::getWord, Collectors.toList())));
        List<Object> argsList = new ArrayList<>();
        StringJoiner sj = new StringJoiner(" union all ");
        for (Map.Entry<String, List<String>> entry : marketplaceIdWordsMap.entrySet()) {
            StringBuilder sb = new StringBuilder("select marketplace_id marketplaceId, word, word_cn wordCn from ")
                    .append(this.getTableName(entry.getKey()));
            sb.append(" where marketplace_id = ? ");
            argsList.add(entry.getKey());
            sb.append(SqlStringUtil.dealInList("word", entry.getValue(), argsList));
            sj.add(sb.toString());
        }
        return getJdbcTemplate().query(sj.toString(), new BeanPropertyRowMapper<>(WordTranslate.class), argsList.toArray());
    }

    @Override
    public int batchInsert(String marketplaceId, List<WordTranslate> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO ").append(this.getTableName(marketplaceId)).append(" (`marketplace_id`,`word`,`word_cn`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (WordTranslate wordTranslate : list) {
            sql.append("(?,?,?,now(),now()),");
            argsList.add(wordTranslate.getMarketplaceId());
            argsList.add(wordTranslate.getWord());
            argsList.add(wordTranslate.getWordCn());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `word_cn`=values(word_cn),`update_time`=now() ");
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    private String getTableName(String marketplaceId) {
        return tableName + marketplaceId;
    }


}
