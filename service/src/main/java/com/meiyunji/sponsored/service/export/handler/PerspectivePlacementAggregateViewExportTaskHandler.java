package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.AggregateViewVo;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.PlacementViewAggregateRequest;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.PlacementViewAggregateResponse;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.KeywordViewAggregateExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.PlacementViewAggregateExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IViewManageService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_PLACEMENT_VIEW_AGGREGATE)
public class PerspectivePlacementAggregateViewExportTaskHandler implements AdManagePageExportTaskHandler {

    /**
     * 透视接口
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getPlacementViewAggregate(PlacementViewAggregateRequest, StreamObserver)
     * @see ViewManageServiceImpl#getPlacementViewAggregate(Integer, PlacementViewParam)
     * @param task
     */

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IViewManageService viewManageService;

    @Override
    public void export(AdManagePageExportTask task) {
        PlacementViewParam param = JSONUtil.jsonToObject(task.getParam(), PlacementViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 广告位视图列表页汇总 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();
        List<PlacementViewAggregateExcelVO> dataList = Lists.newArrayList();

        PlacementViewAggregateResponse.AggregateVo aggregateVo = viewManageService.getPlacementViewAggregate(param.getPuid(), param);
        if (Objects.isNull(aggregateVo)) {
            log.error(String.format("产品广告透视 广告位视图列表页汇总 export error, aggregateVo is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        if (aggregateVo.hasTopAggregateVo()) {
            dataList.add(new PlacementViewAggregateExcelVO(currency, "搜索结果顶部(首页)", aggregateVo.getTopAggregateVo()));
        }
        if (aggregateVo.hasOtherAggregateVo()) {
            dataList.add(new PlacementViewAggregateExcelVO(currency, "搜索结果顶部其余位置", aggregateVo.getOtherAggregateVo()));
        }
        if (aggregateVo.hasProductAggregateVo()) {
            dataList.add(new PlacementViewAggregateExcelVO(currency, "产品页面", aggregateVo.getProductAggregateVo()));
        }
        if (aggregateVo.hasOffAmazonAggregateVo()) {
            dataList.add(new PlacementViewAggregateExcelVO(currency, "Off Amazon", aggregateVo.getOffAmazonAggregateVo()));
        }
        if (CollectionUtils.isEmpty(dataList)) {
            log.error(String.format("产品广告透视 广告位视图列表页汇总 export error, dataList is empty, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), PlacementViewAggregateExcelVO.class,
                build.currencyNew(PlacementViewAggregateExcelVO.class), Collections.emptyList()));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(uuid, new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
}
