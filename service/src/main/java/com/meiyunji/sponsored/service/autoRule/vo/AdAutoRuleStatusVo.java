package com.meiyunji.sponsored.service.autoRule.vo;

import com.meiyunji.sponsored.service.enums.AutoRuleOperationTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 受控对象响应vo
 * @author: zzh
 * @date: 2024/12/16 11:35
 **/
@Data
public class AdAutoRuleStatusVo {

    @ApiModelProperty("受控对象主键id")
    private Long statusId;

    @ApiModelProperty("商户id")
    private Integer puid;

    @ApiModelProperty("店铺id")
    private Integer shopId;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("店铺类型")
    private String shopType;

    @ApiModelProperty("站点id")
    private String marketplaceId;

    @ApiModelProperty("站点名称")
    private String marketplaceName;

    @ApiModelProperty("活动类型")
    private String adType;

    @ApiModelProperty("受控对象id")
    private String itemId;

    @ApiModelProperty("受控对象类型")
    private String itemType;

    @ApiModelProperty("子受控对象类型")
    private String childrenItemType;

    @ApiModelProperty("规则类型")
    private String ruleType;

    @ApiModelProperty("规则模版id")
    private Long templateId;

    @ApiModelProperty("规则模版名称")
    private String templateName;

    @ApiModelProperty("已优化次数")
    private Integer optimizedTimes;

    @ApiModelProperty("最后修改时间")
    private String lastUpdateAt;

    @ApiModelProperty("创建人Id")
    private Integer createUid;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("最后修改人Id")
    private Integer updateUid;

    @ApiModelProperty("最后修改人")
    private String updateName;

    @ApiModelProperty("受控对象状态")
    private String status;

    @ApiModelProperty("模版更新状态")
    private String updateStatus;

    @ApiModelProperty("目标商品asin")
    private String asin;

    @ApiModelProperty("目标商品msku")
    private String sku;

    @ApiModelProperty("集合条件 全部/任一")
    private String setRelation;

    @ApiModelProperty("执行操作时间隔值")
    private String executeTimeSpaceValue;

    @ApiModelProperty("执行操作时间隔单位")
    private String executeTimeSpaceUnit;

    @ApiModelProperty("消息提醒类型")
    private String messageReminderType;

    @ApiModelProperty("还原状态")
    private String callbackState;

    @ApiModelProperty("时间选择类型")
    private String chooseTimeType;

    @ApiModelProperty("时间类型")
    private String timeType;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty("检查频率")
    private String checkFrequency;

    @ApiModelProperty("邮编设置")
    private String postalCodeSettings;

    @ApiModelProperty("消息推送的类型 不可修改从模板表获取")
    private List<String> pushMessageTypeStr;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("执行方式")
    private String executeType;

    /**
     * {@link AutoRuleOperationTypeEnum}
     */
    @ApiModelProperty("操作类型")
    private Integer operationType;

    @ApiModelProperty("自动化规则-规则列表json")
    private String rule;

    @ApiModelProperty("自动化规则执行操作json")
    private String performOperation;

    @ApiModelProperty("时间规则")
    private String timeRule;

    @ApiModelProperty("期望位置")
    private String desiredPosition;

    @ApiModelProperty("抢排名广告数据规则")
    private String adDataRule;

    @ApiModelProperty("抢排名广告数据执行操作")
    private String adDataOperate;

    @ApiModelProperty("抢排名自动降价规则")
    private String autoPriceRule;

    @ApiModelProperty("抢排名自动降价执行操作")
    private String autoPriceOperate;

    @ApiModelProperty("抢排名竞价回调")
    private String biddingCallbackOperate;

    @ApiModelProperty("自动化规则回调")
    private String callbackOperate;
}
