package com.meiyunji.sponsored.service.batchCreate.dao;


import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchSequence;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTaskSupport;

import java.time.LocalDateTime;
import java.util.List;

public interface IAmazonAdBatchTaskSupportDao extends IAdBaseDao<AmazonAdBatchTaskSupport> {

    void insertOrUpdate(int puid);

    List<Integer> getPuidsByUpdateTime(Integer puid, LocalDateTime date);
}