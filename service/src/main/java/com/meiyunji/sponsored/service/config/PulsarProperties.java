package com.meiyunji.sponsored.service.config;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PulsarProperties {
    private Boolean enabled = false;
    private String tenant;
    private String serviceUrl = "pulsar://localhost:6650";
    private String authenticationToken = null;
    private Integer listenerThreads = 1;
    private Integer ioThreads = 1;
    private Integer connectionsPerBroker = 1;
    private Integer connectionTimeoutSec = 1;
    private Boolean enableTcpNoDelay = true;
    private Integer keepAliveIntervalSec = 20;
    private Integer startingBackoffIntervalMs = 100;
    private Integer operationTimeoutSec = 30;
    private Integer maxNumberOfRejectedRequestPerConnection = 50;
    private Integer maxConcurrentLookupRequests = 5000;
    private Integer maxLookupRedirects = 20;
    private Integer maxLookupRequests = 50000;
    private Integer maxBackoffIntervalSec = 60;
    private Integer statsIntervalSec = 60;
}