package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleCampaignVo;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdTargetBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdTargetingTypeBo;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * AmazonAdTargeting
 * <AUTHOR>
 */
public interface IAmazonAdTargetingShardingDao extends IBaseShardingSphereDao<AmazonAdTargeting> {

    /**
     * 创建定位
     * @param puid
     * @param amazonAdTargetings
     */
    void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdTargeting> amazonAdTargetings);

    /**
     * 获取广告组定位个数
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    Integer countByAdGroupId(int puid, Integer shopId, String adGroupId);

    /**
     * 获取定位id
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param onlineTargetId
     * @return
     */
    List<String> getTargetIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineTargetId);

    /**
     * 批量更新
     * @param puid
     * @param updateList
     */
    void updateList(Integer puid, List<AmazonAdTargeting> updateList);

    /**
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    List<AmazonAdTargeting> listByAdGroupId(Integer puid, Integer shopId, String adGroupId);

    /**
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    List<AmazonAdTargeting> listAutoByAdGroupId(Integer puid, Integer shopId, String adGroupId);

    /**
     * 更新targetId
     * @param list
     */
    void updateTargetId(Integer puid,List<AmazonAdTargeting> list);

    /**
     * 删除广告组下的投放
     * @param adGroupId
     * @param puid
     * @return
     */
    int deleteByGroupId(int puid, Integer shopId, String adGroupId);

    /**
     * 删除创建失败的投放定位
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    int deleteFailByGroupId(Integer puid, Integer shopId, String adGroupId);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param targetId
     * @return
     */
    AmazonAdTargeting getByAdTargetId(int puid, Integer shopId, String marketplaceId, String targetId);

    /**
     * @param puid
     * @param shopId
     * @param targetId
     * @return
     */
    AmazonAdTargeting getByAdTargetId(int puid, Integer shopId, String targetId);

    /**
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page);

    /**
     * @param puid
     * @param targetIdList
     * @return
     */
    List<AmazonAdTargeting> listTargets(int puid, List<String> targetIdList);

    /**
     * 列表页
     * @param puid
     * @param param
     * @return
     */
    Page<AmazonAdTargeting> pageList(Integer puid, TargetingPageParam param);

    List<AmazonAdTargeting> listByCondition(Integer puid, TargetingPageParam param);

    /**
     * 否定投放列表页
     * @param puid
     * @param param
     * @return
     */
    Page<AmazonAdTargeting> neTargetingPageList(Integer puid, NeTargetingPageParam param);

    /**
     * 广告组下的产品投放数
     */
    Map<String,Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds);

    AmazonAdTargeting getByTargetId(int puid, Integer shopId, String marketplaceId, String targetId);

    List<AmazonAdTargeting> listNoAsinInfo(Integer puid, Integer shopId, Long offset, Integer limit);

    void batchSetAsinInfo(Integer puid,List<AmazonAdTargeting> needUpdateList);

    int countByGroup(Integer puid, Integer shopId, String adGroupId);

    List<AmazonAdTargeting> getByAdTargetIds(int puid, Integer shopId, List<String> targetIds);

    List<AmazonAdTargeting> getByTargetSuggestBidBatchQo(int puid, List<TargetSuggestBidBatchQo> targetList);

    List<AmazonAdTargeting> getByAdTargetIds(int puid, List<String> targetIds,String putType);

    void batchUpdateSuggestValue(Integer puid,Collection<AmazonAdTargeting> targetings);

    /**
     * 查询所有类型targeting数据
     * @param puid
     * @param param
     * @return
     */
    List<AmazonAdTargetingDto> listAllTargetingByType(int puid, TargetingPageParam param);

    /**
     * 分页查询所有类型否定投放
     * @param puid
     * @param param
     * @return
     */
    Page<AmazonAdNeTargetingDto> listAllNeTargetingByType(int puid, NeTargetingPageParam param);

    List<AmazonAdTargeting>  listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds);

    /**
     * 修改广告组分时调价信息
     * @param puid
     * @param shopId
     * @param targetId
     * @param isPricing
     * @param pricingState
     * @param updateId
     */
    void updatePricing(Integer puid, Integer shopId, String targetId, Integer isPricing, Integer pricingState, int updateId);


    Page getPageList(Integer puid, TargetingPageParam param, Page page);

    List<AmazonAdTargeting> getList(Integer puid, TargetingPageParam param);

    List<AdTargetBo> getAdTargetBoList(Integer puid, TargetingPageParam param, Integer limit);

    List<AmazonAdTargeting> getTargetViewList(Integer puid, TargetViewParam param, boolean isAuto);

    List<String> getTargetViewIdList(Integer puid, TargetViewParam param, boolean isAuto);

    List<String> getTargetIdListByTargetViewHourParam(Integer puid, TargetViewHourParam param, boolean isAuto);

    /**
     * 修改报告数据最新更新时间
     * @param puid
     * @param shopId
     * @param targetId
     * @param localDate
     */
    void updateDataUpdateTime(Integer puid, Integer shopId, String targetId, LocalDate localDate);

    void updateList(Integer puid, List<AmazonAdTargeting> list, String type);

    List<String> getAsinsByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<String> getArchivedItems(Integer puid, Integer shopId);

    List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt);

    List<AmazonAdTargeting> getListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    List<AmazonAdTargetingTypeBo> listTargetTypeBoByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    List<String> getTargetListsByKeyword(Integer puid,CpcQueryWordDto dto);

    List<String> getTargetListsByTarget(Integer puid,TargetingPageParam param);

    Integer statSumCountByAdGroupId(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds);

    Integer statSumCountByAdGroupPage(Integer puid, Integer shopId, List<String> status, GroupPageParam param);


    /**
     * 查询SP自动投放tatgetId
     *
     * @return
     */
    List<String> getByTargetIdList(Integer puid, Integer shopId,
                                              List<String> campaignIds,List<String> groupIds,String state,String targetType,List<String> targetIdList);

    /**
     * 查询SP自动投放
     *
     * @return
     */
    Page<AmazonAdTargeting> queryAdSpAutoTarget(AdTargetStrategyParam param);

    /**
     * 查询SP商品投放
     *
     * @param param
     * @return
     */
    Page<AmazonAdTargeting> queryAdSpCommodityTarget(AdTargetStrategyParam param);

    /**
     * 查询SP自动投放(自动化规则)
     *
     * @return
     */
    Page<AmazonAdTargeting> queryAutoRuleAdSpAutoTarget(AdKeywordTargetAutoRuleParam adKeywordTargetAutoRuleParam, List<String> itemIds, List<String> similarRuleItemIdList);

    /**
     * 查询SP商品投放(自动化规则)
     *
     * @param param
     * @return
     */
    Page<AmazonAdTargeting> queryAutoRuleAdSpCommodityTarget(AdKeywordTargetAutoRuleParam adKeywordTargetAutoRuleParam, List<String> itemIds, List<String> similarRuleItemIdList);

    /**
     * 查询SP单个自动投放
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param targetId
     * @return
     */
    AmazonAdTargeting getAutoByTargetId(int puid, Integer shopId, String marketplaceId, String targetId);

    void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message);

    void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message);

    List<AmazonAdTargeting> getAutoTargetsByGroupIds(Integer puid, List<String> adGroupIds);

    List<AmazonAdTargeting> listByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts);

    List<AmazonAdTargeting> listAllTargetByGroupIdsAndTargetText(Integer puid, List<String> adGroupIds, List<String> targetTexts);

    List<AmazonAdTargeting> autoRuleTarget(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds,List<String> servingStatus);

    List<String> listTargetNameByTargetIds(Integer puid,Integer shopId, List<String> targetIds);

    List<AmazonAdTargeting> getListTargetByTargetIds(Integer puid, List<Integer> shopIds, List<String> targetIds);

    List<String> getDiagnoseCountTargetId(DiagnoseCountParam diagnoseParam, boolean auto);

    List<AmazonAdTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String selectType, String type);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<AdGroupTargetCountDto> countByGroupIdSet(Integer puid, Integer shopId, Set<String> groupIdSet);

    Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param);

    List<TargetPageVo> getTargetPageVoListByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<AdOrderBo> getTargetIdAndOrderFieldList(Integer puid, TargetingPageParam param, List<String> TargetIdList, String orderField);

    List<AdGroupTargetCountDto> countByGroupIdSet4Auto(Integer puid, Integer shopId, Set<String> groupIdSet);

    List<AmazonAdTargeting>  listTargetByGroupIdList(Integer puid, Integer shopId, List<String> groupIds);

    List<AmazonAdTargeting>  listTargetByGroupId(Integer puid, Integer shopId, String adGroupId);

    List<AmazonAdTargeting> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList);

    List<String> getByTargetIdList(ProcessTaskParam param, String targetType);

    List<String> queryAutoRuleTargetIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param);

    List<AmazonAdTargeting> autoRuleTargetIdList(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds, List<String> servingStatus);

    List<SearchQueryTagParam> getSearchQueryTag(Integer puid, Integer shopId, List<String> matchTypeList, List<SearchQueryTagParam> queryTagParams);

    /**
     * 获取商品投放基础信息
     * @param puid
     * @param param
     * @param spTargetIds
     * @return
     */
    List<AsinLibsDetailVo> getSpAsinDetailDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> spTargetIds);

    List<AmazonAdTargeting> getArchivedTargetingByAdTargetIds(Integer puid, Integer id, List<String> targetItemIdList);

    List<DownloadCenterBaseDataBO> queryBaseData4DownloadByTargetIdList(Integer puid, Integer shopId, List<String> targetIdList);

    List<AutoRuleCampaignVo> getCountByCampaignId(int puid, int shopId, List<String> strings);

    List<AmazonAdTargeting> getByAdTargetIds(Integer puid, Set<Integer> shopIds, List<String> targetIds);

    List<String> listAsinByParam(Integer puid, Integer shopId, ListProductAsinParam listAsinParam);
}