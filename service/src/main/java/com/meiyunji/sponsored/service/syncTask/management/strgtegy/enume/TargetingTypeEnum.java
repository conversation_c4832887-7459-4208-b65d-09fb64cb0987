package com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/12/28 15:51
 */
public enum TargetingTypeEnum {

    //关键词
    KEYWORD("KeywordTarget"),

    //自动
    AUTO("AutoTarget"),

    //商品投放
    PRODUCT_TARGET("ProductCategoryTarget"),

    ;

    /**
     * stream 枚举
     */
    private final String targetType;

    TargetingTypeEnum(String targetType) {
        this.targetType = targetType;
    }

    public String getTargetType() {
        return targetType;
    }
}
