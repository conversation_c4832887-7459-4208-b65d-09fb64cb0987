package com.meiyunji.sponsored.service.reportImport.enums;


/**
 *
 * 领星和店小秘的matchType映射枚举类
 *@author: fang<PERSON><PERSON><PERSON>
 *@email: fangxia<PERSON><PERSON>@dianxiaomi.com
 *@date: 2023-07-07
 */
public enum LxMatchTypeEnums {

    BLOAD("广泛匹配","broad"),

    PHRASE("词组匹配","phrase"),

    EXACT("精确匹配","exact"),

    EXACT_COPY("精准匹配","exact");


    private String lxMatchType;

    private String dxmMatchType;

    LxMatchTypeEnums(String lxMatchType, String dxmMatchType) {
        this.lxMatchType = lxMatchType;
        this.dxmMatchType = dxmMatchType;
    }

    public static String getDxmMatchType(String lxMatchType) {
        for (LxMatchTypeEnums type : LxMatchTypeEnums.values()) {
            if (type.getLxMatchType().equals(lxMatchType)) {
                return type.getDxmMatchType();
            }
        }
        return "";
    }

    public String getLxMatchType() {
        return lxMatchType;
    }

    public String getDxmMatchType() {
        return dxmMatchType;
    }
}

