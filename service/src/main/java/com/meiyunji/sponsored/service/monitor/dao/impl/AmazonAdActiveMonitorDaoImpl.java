package com.meiyunji.sponsored.service.monitor.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdActiveMonitor;
import com.meiyunji.sponsored.service.monitor.dao.IAmazonAdActiveMonitorDao;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AmazonAdActiveMonitorDaoImpl extends AdBaseDaoImpl<AmazonAdActiveMonitor> implements IAmazonAdActiveMonitorDao {


    @Override
    public int insertAll(AmazonAdActiveMonitor adActiveMonitor) {
        StringBuilder sql = new StringBuilder("insert into " + getJdbcHelper().getTable() + " (puid, active_count, " +
                "  create_time, update_time,last_request_time) " +
                " values (?, ?, now(), now(),now()) ");
        List<Object> args = Lists.newArrayList(adActiveMonitor.getPuid(), adActiveMonitor.getActiveCount());
        sql.append(" on duplicate key update  active_count = values(active_count), last_request_time = values(last_request_time) ");
        return getJdbcTemplate().update(sql.toString(), args.toArray());
    }

    @Override
    public int updateTimeAndActiveCount(Long id, Long activeCount) {
        String sql = "update " + getJdbcHelper().getTable() + " set active_count = active_count + ?, last_request_time = now()  where id = ? ";
        List<Object> args = Lists.newArrayList(activeCount, id);
        return getJdbcTemplate().update(sql, args.toArray());
    }

    @Override
    public AmazonAdActiveMonitor findByPuid(Integer puid) {
        ConditionBuilder build = new ConditionBuilder.Builder()
                .equalTo("puid", puid).build();
        return getByCondition(build);
    }


}
