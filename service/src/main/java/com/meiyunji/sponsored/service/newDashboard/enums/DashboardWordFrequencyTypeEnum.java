package com.meiyunji.sponsored.service.newDashboard.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-11 16:52
 */
@Getter
public enum DashboardWordFrequencyTypeEnum {
    ALL(0, "全部"),
    SINGLE(1, "单词"),
    PHRASE(2, "词组"),
    ;

    private Integer code;

    private String desc;

    DashboardWordFrequencyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<Integer> frequencyTypeCodes = Arrays.stream(DashboardWordFrequencyTypeEnum.values())
            .map(DashboardWordFrequencyTypeEnum::getCode).collect(Collectors.toList());
}
