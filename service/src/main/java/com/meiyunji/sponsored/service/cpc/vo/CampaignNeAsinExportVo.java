package com.meiyunji.sponsored.service.cpc.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @author: wade
 * @date: 2021/9/9 9:49
 * @describe: 活动否定商品导出VO
 */
@Data
public class CampaignNeAsinExportVo {

    @ExcelProperty(value = "商品信息")
    private String asin;

    @ExcelProperty(value = "状态")
    private String state;

    @ExcelProperty(value = "所属广告活动")
    private String advertisingActivities;

    @ExcelProperty(value = "推广类型")
    private String type;

    @ExcelProperty(value = "投放类型")
    private String campaignTargetingType;

    @ExcelProperty(value = "创建时间")
    private String createTime;
}
