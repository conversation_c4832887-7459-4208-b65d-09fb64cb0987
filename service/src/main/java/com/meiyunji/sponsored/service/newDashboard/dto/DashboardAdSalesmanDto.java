package com.meiyunji.sponsored.service.newDashboard.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdYoyMomDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdYoyMomValueDataDto;
import lombok.Data;

import java.time.LocalDate;

/**
 * @author: ys
 * @date: 2024/4/9 16:39
 * @describe:
 */
@Data
public class DashboardAdSalesmanDto extends DashboardAdYoyMomDataDto {
    @ExcelProperty("业务员")
    private String name;
    private Integer devId;
    private LocalDate countDay;

}
