package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


@Data
@ApiModel
public class KeywordViewHourParam {
    @NotEmpty
    private Integer puid;
    private Integer uid;
    private String uuid;
    private String marketplaceId;
    @ApiModelProperty(value = "店铺")
    @NotEmpty
    private List<Integer> shopIdList;
    @ApiModelProperty(value ="开始时间",required = true)
    @NotBlank
    private String startDate;
    @ApiModelProperty(value ="结束时间",required = true)
    @NotBlank
    private String endDate;
    @ApiModelProperty("对比开始时间")
    private String startDateCompare;
    @ApiModelProperty("对比结束时间")
    private String endDateCompare;
    @ApiModelProperty("星期逗号分隔")
    private String weeks;
    @ApiModelProperty("是否对比")
    private Integer isCompare;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("排序类型")
    private String orderType;
    @ApiModelProperty(value = "模式(小时-HOURLY,日-DAILY,周-WEEKLY,月-MONTHLY) SB,SD不支持小时级数据",required = true)
    private DateModel dateModel;
    @ApiModelProperty(value = "广告类型(SP,SB,SD)",required = true)
    private String adType;
    @ApiModelProperty("活动ID")
    private String campaignId;
    @ApiModelProperty("广告组ID")
    private String groupId;
    @ApiModelProperty("匹配类型 EXACT->精确匹配  BROAD->广泛匹配  PHRASE->词组匹配")
    private String matchType;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;
    @ApiModelProperty("搜索字段")
    private String queryField;
    @ApiModelProperty("搜索值")
    private String queryValue;
    @ApiModelProperty("搜索类型，模糊、精确")
    private String queryType;
    private String pageSign;
    @ApiModelProperty("汇总类型（关键词）")
    private String aggregateType;

    private List<String> keywordIdList;
    private List<String> adIdList;

    public enum DateModel {
        HOURLY,
        DAILY,
        WEEKLY,
        MONTHLY
    }
}   
