package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.po.NaturalRport;
import com.meiyunji.sponsored.service.cpc.po.ReportBase;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.ReportExcelVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * @ClassName ReportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/17 21:25
 **/
public class ReportService<T extends ReportBase> {
    protected ReportVo getVo(T report,BigDecimal shopSales){
        ReportVo vo = new ReportVo();
        if(report instanceof NaturalRport){
            vo.setNaturalClicks(((NaturalRport) report).getNaturalClicks());
            Integer naturalOrderNum = ((NaturalRport) report).getNaturalOrderNum();
            vo.setNaturalOrderNum(naturalOrderNum != null && naturalOrderNum > 0 ? naturalOrderNum : 0);
            BigDecimal naturalSales = ((NaturalRport) report).getNaturalSales();
            vo.setNaturalSales(naturalSales!=null && naturalSales.compareTo(BigDecimal.ZERO) > 0 ? naturalSales.setScale(2,BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
            naturalSales =((NaturalRport)report).getNaturalSalesUsd();
            vo.setNaturalSalesUsd(naturalSales!=null? naturalSales.setScale(2,BigDecimal.ROUND_HALF_UP):null);
            naturalSales =((NaturalRport) report).getNaturalSalesRmb();
            vo.setNaturalSalesRmb(naturalSales!=null? naturalSales.setScale(2,BigDecimal.ROUND_HALF_UP):null);
            vo.setAdConversionRate(((NaturalRport) report).getAdConversionRate());
            vo.setAdClickRatio(((NaturalRport) report).getAdClickRatio());
        }
        String countDate = report.getCountDate();
        if(StringUtils.isNotEmpty(report.getCountDate())){
            countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
        }
        vo.setCountDate(countDate);
        vo.setCpc(Optional.ofNullable(report.getCpc()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        vo.setCost(Optional.ofNullable(report.getCost()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        vo.setSales(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        vo.setCpa((Optional.ofNullable(report.getCpa()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP)));
        vo.setAcos(Optional.ofNullable(report.getAcos()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        vo.setRoas(Optional.ofNullable(report.getRoas()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        //新加指标,需要获取广告
        BigDecimal shopTotalSales = Optional.ofNullable(shopSales).orElse(BigDecimal.ZERO);
        vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO)==0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(shopSales,2, RoundingMode.HALF_UP));
        vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO)==0 ? BigDecimal.ZERO : report.getTotalSales().multiply(BigDecimal.valueOf(100)).divide(shopSales,2, RoundingMode.HALF_UP));
        vo.setImpressions(Optional.ofNullable(report.getImpressions()).orElse(0));
        vo.setClicks(Optional.ofNullable(report.getClicks()).orElse(0));
        vo.setOrderNum(Optional.ofNullable(report.getSaleNum()).orElse(0));   //替换成了saleNum字段
        vo.setSaleNum(Optional.ofNullable(report.getOrderNum()).orElse(0));
        vo.setClickRate(Optional.ofNullable(report.getClickRate()).orElse(0.00));
        vo.setSalesConversionRate(Optional.ofNullable(report.getSalesConversionRate()).orElse(0.00));
        //新版报告下载中心
        vo.setAttributedConversions7d(Optional.ofNullable(report.getOrderNum()).orElse(0));
        vo.setAttributedConversions7dSameSKU(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
        if (report.getOrderNum() != null) {
            if (report.getAdOrderNum() != null) {
                vo.setAttributedConversions7dOtherSameSKU(report.getOrderNum() - report.getAdOrderNum());
            } else {
                vo.setAttributedConversions7dOtherSameSKU(report.getOrderNum());
            }
        }else {
            vo.setAttributedConversions7dOtherSameSKU(0);
        }
        vo.setAttributedSales7d(Optional.ofNullable(report.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        vo.setAttributedSales7dSameSKU(Optional.ofNullable(report.getAdSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        if (report.getTotalSales() != null) {
            if (report.getAdSales() != null) {
                vo.setAttributedSales7dOtherSameSKU(report.getTotalSales().subtract(report.getAdSales()));
            } else {
                vo.setAttributedSales7dOtherSameSKU(report.getTotalSales());
            }
        }else {
            vo.setAttributedSales7dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        vo.setAttributedUnitsOrdered7d(Optional.ofNullable(report.getSaleNum()).orElse(0));
        vo.setAttributedUnitsOrdered7dSameSKU(Optional.ofNullable(report.getAdSaleNum()).orElse(0));
        if (report.getSaleNum() != null) {
            if (report.getAdSaleNum() != null) {
                vo.setAttributedUnitsOrdered7dOtherSameSKU(report.getSaleNum() - report.getAdSaleNum());
            } else {
                vo.setAttributedUnitsOrdered7dOtherSameSKU(report.getSaleNum());
            }
        } else {
            vo.setAttributedUnitsOrdered7dOtherSameSKU(0);
        }

        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        vo.setAdSales(Optional.ofNullable(report.getAdSales()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        //本广告产品订单量
        vo.setAdSaleNum(Optional.ofNullable(report.getAdSaleNum()).orElse(0));
        //本广告产品销量
        vo.setAdSelfSaleNum(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
        //其他产品广告订单量
        vo.setAdOtherOrderNum(vo.getAttributedUnitsOrdered7dOtherSameSKU());
        //其他产品广告销售额
        vo.setAdOtherSales(vo.getAttributedSales7dOtherSameSKU());
        //其他产品广告销量
        vo.setAdOtherSaleNum(vo.getAttributedConversions7dOtherSameSKU());
        // 广告笔单价
        vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
        return vo;
    }

    protected ReportVo getVoWithDateType(T report,String dateType,BigDecimal shopSales) {
        //dateType:day,week,month
        String countDate = report.getCountDate();
        report.setCountDate(null);
        ReportVo vo = getVo(report,shopSales);
        // TODO 公共转换对于这个方法对外的接口是错的，这里调转一下
        vo.setSaleNum(report.getSaleNum());
        Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
        vo.setSalesConversionRate(salesConversionRate);
        //对日期格式进行处理
        if("day".equals(dateType)){
            if(StringUtils.isNotEmpty(countDate)){
                countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
            }
        }else if("month".equals(dateType)){
            if(StringUtils.isNotEmpty(countDate)){
                countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMM"),"yyyy-MM");
            }
        }
        vo.setCountDate(countDate);
        return vo;
    }

    /**
     * 获取汇总报告对比时间段
     * @param start
     * @param end
     * @param lastMonth
     * @return
     */
    protected Map<String,String> getLastTime(Date start,Date end,Integer lastMonth){
        Map<String,String> map = Maps.newHashMapWithExpectedSize(2);
        //获取上个时间段的数据
        if(lastMonth!=null&&lastMonth == 1){ //获取上一个月的同时间段的数据
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(start,-1),"yyyyMMdd"));
            map.put("endStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(end,-1),"yyyyMMdd"));
        }else {
            //获取时间差
            int days = DateUtil.getDayBetween(start,end);
            end = DateUtil.addDay(start,-1);
            map.put("endStr",DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addDay(end,-days),"yyyyMMdd"));
        }
        return map;
    }

    public static String getOrderField(String field,Boolean isSum) {
        return getOrderField(field, isSum, false);
    }

    public static String getOrderFieldIgnoreCountDate(String field,Boolean isSum, boolean isQueryJoinSearchTermsRank) {
        String orderField = getOrderField(field, isSum, isQueryJoinSearchTermsRank);
        return " count_date ".equals(orderField) ? null : orderField;
    }

    public static String getOrderField(String field,Boolean isSum, boolean isQueryJoinSearchTermsRank) {
        switch (field){
            //兼容前端传参，正常应该传cost，前端有时候会传adCost过来
            case "adCost" :
            case "adCostPercentage" :
            case "viewImpressions" :
                return isSum?" sum(cost) ":" cost ";
            case "cpc":
                return isSum?" ifnull(sum(cost)/sum(clicks),0) ":" ifnull(cost/clicks,0) ";
            case "cost" :
                return isSum?" sum(cost) ":" cost ";
            case "acots" :
                return isSum?" sum(cost) ":" cost ";
            case "sales" :
                return isSum?" sum(total_sales) ":" total_sales ";
            case "asots" :
                return isSum?" sum(total_sales) ":" total_sales ";
            case "allSales" :
                return isSum?" sum(IFNULL(natural_sales,0)) ":" IFNULL(natural_sales,0) ";
            case "acos" :
                return isSum?" ifnull(sum(cost)/sum(total_sales),0) ":" ifnull(cost/total_sales,0) ";
            case "roas" :
                return isSum?" ifnull(sum(total_sales)/sum(cost),0) ":" ifnull(total_sales/cost,0) ";
            case "impressions" :
                return isSum?" sum(impressions) ":" impressions ";
            case "clicks" :
                return isSum?" sum(clicks) ":" clicks ";
            //广告订单量
            case "orderNum" :
                return isSum?" sum(sale_num) ":" sale_num ";
            case "allOrderNum" :
                return isSum?" sum(IFNULL(natural_order_num,0)) ":" IFNULL(natural_order_num,0) ";
            case "clickRate" :
                return isSum?" ifnull(sum(clicks)/sum(impressions),0) ":" ifnull(clicks/impressions,0) ";
            case "salesConversionRate" :
                return isSum?" ifnull(sum(sale_num)/sum(clicks),0) ":" ifnull(sale_num/clicks,0) ";
            case "cpa" :
                return isSum?" ifnull(sum(cost)/sum(sale_num),0) ":" ifnull(cost/sale_num,0) ";
            case "countDate" :
                return isSum?" count_date ":" count_date ";
            //本广告产品订单量
            case "adSaleNum" :
                return isSum?" sum(ad_sale_num) ":" ad_sale_num ";
            //其他产品广告订单量
            case "adOtherOrderNum" :
                return isSum?" ifnull(sum(sale_num)-sum(ad_sale_num),0) ":" ifnull(sale_num-ad_sale_num,0) ";
            //本广告产品销售额
            case "adSales" :
                return isSum?" sum(ad_sales) ":" ad_sales ";
            //其他产品广告销售额
            case "adOtherSales" :
                return isSum?" ifnull(sum(total_sales)-sum(ad_sales),0) ":"ifnull(total_sales-ad_sales,0)";
            //广告销量
            case "saleNum" :
                return isSum?" sum(order_num) ":" order_num ";
            //本广告产品销量
            case "adSelfSaleNum" :
                return isSum?" sum(ad_order_num) ":" ad_order_num ";
            //其他产品广告销量
            case "adOtherSaleNum" :
                return isSum?" ifnull(sum(order_num)-sum(ad_order_num),0) ":" ifnull(order_num-ad_order_num,0) ";
            case "advertisingUnitPrice":
                return isSum ? " ifnull(sum(total_sales)/sum(sale_num), 0) " : " ifnull(total_sales/sale_num, 0) ";
            case "searchFrequencyRank":
                return isQueryJoinSearchTermsRank ? " search_frequency_rank " : null;
            case "weekRatio":
                return isQueryJoinSearchTermsRank ? " week_ratio " : null;
            default:
                return isSum?null:" count_date ";
        }
    }

    //获取 sd 报表排序字段
    public static String getSdReportField(String field, Boolean isSum) {
        switch (field){
            case "asots" :
                return isSum?" sum(sales14d) ":" sales14d ";
            case "sales" :          //广告销售额
                return isSum? " sum(sales14d) ":" sales14d ";
            case "acots" :
                return isSum?" sum(cost) ":" cost ";
            case "cost" :           //广告费
                return isSum? " sum(cost) ":" cost ";
            case "acos" :           //acos
                return isSum?" ifnull(sum(cost)/sum(sales14d),0) ":" ifnull(cost/sales14d,0) ";
            case "cpc":             //cpc
                return isSum?" ifnull(sum(cost)/sum(clicks),0) ":" ifnull(cost/clicks,0) ";
            case "impressions" :    //曝光量
                return isSum?" sum(impressions) ":" impressions ";
            case "clicks" :         //广告点击
                return isSum?" sum(clicks) ":" clicks ";
            case "clickRate" :      //广告点击率
                return isSum?" ifnull(sum(clicks)/sum(impressions),0) ":" ifnull(clicks/impressions,0) ";
            case "orderNum" :       //广告订单
                return isSum?" sum(conversions14d) ":" conversions14d ";
            case "salesConversionRate" :    //广告点击率
                return isSum?" ifnull(sum(conversions14d)/sum(clicks),0) ":" ifnull(conversions14d/clicks,0) ";
            case "cpa" :
                return isSum?" ifnull(sum(cost)/sum(conversions14d),0) ":" ifnull(cost/conversions14d,0) ";
            case "roas" :
                return isSum?" ifnull(sum(sales14d)/sum(cost),0) ":" ifnull(sales14d/cost,0) ";
            case "ordersNewToBrandPercentageFTD" :
                return isSum ? " ifnull(sum(orders_new_to_brand14d) / sum(clicks), 0) " : " ifnull(orders_new_to_brand14d / clicks, 0) ";
            case "ordersNewToBrandFTD" :
                return isSum ? " ifnull(sum(orders_new_to_brand14d), 0) " : " ifnull(orders_new_to_brand14d, 0) ";
            case "orderRateNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d) / sum(conversions14d) , 0) " : " ifnull(orders_new_to_brand14d / conversions14d, 0) ";
            case "salesNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d), 0) " : " ifnull(sales_new_to_brand14d, 0) ";
            case "salesRateNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d) / sum(sales14d), 0) " : " ifnull(sales_new_to_brand14d / sales14d, 0) ";
            case "impressionShare":
                return isSum ? " sum(impression_share) " : "  impression_share ";
            case "impressionRank":
                return isSum ? " sum(impression_rank) " : "  impression_rank ";
            case "newToBrandDetailPageViews":
                return isSum ? "ifnull(sum(new_to_brand_detail_page_views), 0)" : "ifnull(new_to_brand_detail_page_views, 0)";
            case "addToCart":
                return isSum ? "ifnull(sum(add_to_cart), 0)" : "ifnull(add_to_cart, 0)";
            case "addToCartRate":
                return isSum ? "ifnull(sum(add_to_cart)/sum(impressions), 0)" : "ifnull(add_to_car)/impressions, 0)";
            case "eCPAddToCart":
                return isSum ? "ifnull(sum(cost)/sum(add_to_cart), 0)" : "ifnull(cost/add_to_cart, 0)";
            case "video5SecondViews":
                return isSum ? "ifnull(sum(video5second_views), 0)" : "ifnull(video5second_views, 0)";
            case "video5SecondViewRate":
                return isSum ? "ifnull(sum(video5second_views)/sum(impressions), 0)" : "ifnull(video5second_views/impressions, 0)";
            case "videoFirstQuartileViews":
                return isSum ? "ifnull(sum(video_first_quartile_views), 0)" : "ifnull(video_first_quartile_views, 0)";
            case "videoMidpointViews":
                return isSum ? "ifnull(sum(video_Midpoint_Views), 0)" : "ifnull(video_Midpoint_Views, 0)";
            case "videoThirdQuartileViews":
                return isSum ? "ifnull(sum(video_third_quartile_views), 0)" : "ifnull(video_third_quartile_views, 0)";
            case "videoCompleteViews":
                return isSum ? "ifnull(sum(video_complete_views), 0)" : "ifnull(video_complete_views, 0)";
            case "videoUnmutes":
                return isSum ? "ifnull(sum(video_unmutes), 0)" : "ifnull(video_unmutes, 0)";
            case "viewImpressions":
                return isSum ? "ifnull(sum(view_impressions), 0)" : "ifnull(view_impressions, 0)";
            case "viewabilityRate":
                return isSum ? "ifnull(sum(view_impressions)/sum(impressions), 0)" : "ifnull(view_impressions/impressions, 0)";
            case "viewClickThroughRate":
                return isSum ? "ifnull(sum(clicks)/sum(view_impressions), 0)" : "ifnull(clicks/view_impressions, 0)";
            case "brandedSearches":
                return isSum ? "ifnull(sum(branded_searches14d), 0)" : "ifnull(branded_searches14d, 0)";
            case "advertisingUnitPrice":
                return isSum ? " ifnull(sum(sales14d)/sum(conversions14d), 0) " : " ifnull(sales14d/conversions14d, 0) ";
            default:
                return isSum?null:" count_date ";
        }
    }

    public static String getSbReportField(String field,Boolean isSum) {
        return getSbReportField(field, isSum, false);
    }

    //获取sb报表排序字段
    public static String getSbReportField(String field,Boolean isSum, boolean isQueryJoinSearchTermsRank) {
        switch (field){
            case "asots" :
                return isSum?" sum(sales14d) ":" sales14d ";
            case "sales" :          //广告销售额
                return isSum? " sum(sales14d) ":" sales14d ";
            case "acots" :
                return isSum?" sum(cost) ":" cost ";
            case "cost" :           //广告费
                return isSum? " sum(cost) ":" cost ";
            case "acos" :           //acos
                return isSum?" ifnull(sum(cost)/sum(sales14d),0) ":" ifnull(cost/sales14d,0) ";
            case "cpc":             //cpc
                return isSum?" ifnull(sum(cost)/sum(clicks),0) ":" ifnull(cost/clicks,0) ";
            case "impressions" :    //曝光量
                return isSum?" sum(impressions) ":" impressions ";
            case "clicks" :         //广告点击
                return isSum?" sum(clicks) ":" clicks ";
            case "clickRate" :      //广告点击率
                return isSum?" ifnull(sum(clicks)/sum(impressions),0) ":" ifnull(clicks/impressions,0) ";
            case "orderNum" :       //广告订单
                return isSum?" sum(conversions14d) ":" conversions14d ";
            case "salesConversionRate" :    //广告点击率
                return isSum?" ifnull(sum(conversions14d)/sum(clicks),0) ":" ifnull(conversions14d/clicks,0) ";
            case "cpa" :
                return isSum?" ifnull(sum(cost)/sum(conversions14d),0) ":" ifnull(cost/conversions14d,0) ";
            case "roas" :
                return isSum?" ifnull(sum(sales14d)/sum(cost),0) ":" ifnull(sales14d/cost,0) ";
            case "ordersNewToBrandPercentageFTD" :
                return isSum ? " ifnull(sum(orders_new_to_brand14d) / sum(clicks), 0) " : " ifnull(orders_new_to_brand14d / clicks, 0) ";
            case "ordersNewToBrandFTD" :
                return isSum ? " ifnull(sum(orders_new_to_brand14d), 0) " : " ifnull(orders_new_to_brand14d, 0) ";
            case "orderRateNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d) / sum(conversions14d) , 0) " : " ifnull(orders_new_to_brand14d / conversions14d, 0) ";
            case "salesNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d), 0) " : " ifnull(sales_new_to_brand14d, 0) ";
            case "salesRateNewToBrandFTD" :
                return isSum ? " ifnull(sum(sales_new_to_brand14d) / sum(sales14d), 0) " : " ifnull(sales_new_to_brand14d / sales14d, 0) ";
            case "impressionShare":
                return isSum ? " sum(impression_share) " : "  impression_share ";
            case "impressionRank":
                return isSum ? " sum(impression_rank) " : "  impression_rank ";
            case "newToBrandDetailPageViews":
                return isSum ? "ifnull(sum(new_to_brand_detail_page_views), 0)" : "ifnull(new_to_brand_detail_page_views, 0)";
            case "addToCart":
                return isSum ? "ifnull(sum(add_to_cart), 0)" : "ifnull(add_to_cart, 0)";
            case "addToCartRate":
                return isSum ? "ifnull(sum(add_to_cart)/sum(impressions), 0)" : "ifnull(add_to_car)/impressions, 0)";
            case "eCPAddToCart":
                return isSum ? "ifnull(sum(cost)/sum(add_to_cart), 0)" : "ifnull(cost/add_to_cart, 0)";
            case "video5SecondViews":
                return isSum ? "ifnull(sum(video5second_views), 0)" : "ifnull(video5second_views, 0)";
            case "video5SecondViewRate":
                return isSum ? "ifnull(sum(video5second_views)/sum(impressions), 0)" : "ifnull(video5second_views/impressions, 0)";
            case "videoFirstQuartileViews":
                return isSum ? "ifnull(sum(video_first_quartile_views), 0)" : "ifnull(video_first_quartile_views, 0)";
            case "videoMidpointViews":
                return isSum ? "ifnull(sum(video_Midpoint_Views), 0)" : "ifnull(video_Midpoint_Views, 0)";
            case "videoThirdQuartileViews":
                return isSum ? "ifnull(sum(video_third_quartile_views), 0)" : "ifnull(video_third_quartile_views, 0)";
            case "videoCompleteViews":
                return isSum ? "ifnull(sum(video_complete_views), 0)" : "ifnull(video_complete_views, 0)";
            case "videoUnmutes":
                return isSum ? "ifnull(sum(video_unmutes), 0)" : "ifnull(video_unmutes, 0)";
            case "viewImpressions":
                return isSum ? "ifnull(sum(viewable_impressions), 0)" : "ifnull(viewable_impressions, 0)";
            case "viewabilityRate":
                return isSum ? "ifnull(sum(viewable_impressions)/sum(impressions), 0)" : "ifnull(viewable_impressions/impressions, 0)";
            case "viewClickThroughRate":
                return isSum ? "ifnull(sum(clicks)/sum(viewable_impressions), 0)" : "ifnull(clicks/viewable_impressions, 0)";
            case "brandedSearches":
                return isSum ? "ifnull(sum(branded_searches14d), 0)" : "ifnull(branded_searches14d, 0)";
            case "advertisingUnitPrice":
                return isSum ? " ifnull(sum(sales14d)/sum(conversions14d), 0) " : " ifnull(sales14d/conversions14d, 0) ";
            case "searchFrequencyRank":
                return isQueryJoinSearchTermsRank ? " search_frequency_rank " : null;
            case "weekRatio":
                return isQueryJoinSearchTermsRank ? " week_ratio " : null;
            default:
                return isSum?null:" count_date ";
        }
    }

    /**
     * 处理excel导出vo
     * @param obj
     * @return
     */
    protected ReportExcelVo getExcelVo(ReportVo obj,String currency) {
        ReportExcelVo excelVo = new ReportExcelVo();
        excelVo.setCountDate(obj.getCountDate());
        excelVo.setQuery(obj.getQuery());
        excelVo.setKeywordText(obj.getKeywordText());
        if(StringUtils.isNotEmpty(obj.getMatchType())){
            if(Constants.PHRASE.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("词组匹配");
            }else if(Constants.EXACT.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("精确匹配");
            }else if(Constants.BROAD.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("广泛匹配");
            }
        }
        if(StringUtils.isNotEmpty(obj.getTargetingExpression())){
            if(Constants.TARGETING_EXPRESSION_SUBSTITUTES.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("同类商品");
            }else if(Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("紧密匹配");
            }else if(Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("宽泛匹配");
            }else if(Constants.TARGETING_EXPRESSION_COMPLEMENTS.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("关联商品");
            }else{
                excelVo.setTargetingExpression(obj.getTargetingExpression());
            }
        }
        excelVo.setAdGroupName(obj.getAdGroupName());
        excelVo.setCampaignName(obj.getCampaignName());
        if(StringUtils.isNotEmpty(obj.getTargetingType())){
            excelVo.setTargetingType(Constants.AUTO.equals(obj.getTargetingType()) ? "自动":"手动");
        }
        excelVo.setImpressions(obj.getImpressions());
        excelVo.setClicks(obj.getClicks());
        excelVo.setClickRate(obj.getClickRate() == null ? "-" : obj.getClickRate()+"%");
        excelVo.setCost(obj.getCost()!=null?currency+obj.getCost():"-");
        excelVo.setCpc(obj.getCpc()!=null?currency+obj.getCpc():"-");
        excelVo.setOrderNum(obj.getSaleNum());
        excelVo.setSalesConversionRate(obj.getSalesConversionRate() == null ? "-" : obj.getSalesConversionRate()+"%");
        excelVo.setCpa(obj.getCpa()!=null?currency+obj.getCpa():"-");
        excelVo.setSales(obj.getSales()!=null?currency+obj.getSales():"-");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos()+"%");
        return excelVo;
    }

}
