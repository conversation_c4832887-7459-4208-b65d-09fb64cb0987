package com.meiyunji.sponsored.service.account.service.impl;

import com.meiyunji.sponsored.common.security.UserAuthInfo;
import com.meiyunji.sponsored.service.account.service.IUserAuthService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 一个用于service的方法, 封装用户登录的信息, 省去每个人重复处理代码.
 * 此类只适用于单线程环境. 如果是多线程环境, 请修改service 的参数, 手动增加 UserAuthInfo
 *
 * chenyunshi 2020/12/30
 */
@Component
public class UserAuthServiceImpl implements IUserAuthService {

    private ThreadLocal<UserAuthInfo> threadLocal = new ThreadLocal<>();


    @Override
    public UserAuthInfo getUserInfo() {
        return threadLocal.get();
    }


    @Override
    public List<Integer> getShopIds() {
        UserAuthInfo info = this.getUserInfo();
        if (info == null)return new ArrayList<>();
        return info.getShopIds();
    }

}
