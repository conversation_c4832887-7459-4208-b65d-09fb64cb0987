package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2024/9/4 20:31
 * @describe:
 */
@Getter
public enum TargetableCampaignTypeEnum {
    SPONSORED_PRODUCT("sp", "SPONSORED_PRODUCT", "广告产品推广"),
    SPONSORED_BRANDS("sb", "SPONSORED_BRANDS", "品牌推广"),
    SPONSORED_DISPLAY("sd", "SPONSORED_DISPLAY", "展示推广")
    ;
    private String code;
    private String val;
    private String msg;

    TargetableCampaignTypeEnum(String code, String val, String msg) {
        this.code = code;
        this.val = val;
        this.msg = msg;
    }

    public static TargetableCampaignTypeEnum getTargetableCampaignTypeEnumByCode (String code) {
        for (TargetableCampaignTypeEnum en : TargetableCampaignTypeEnum.values()) {
            if (en.getCode().equals(code)) {
                return en;
            }
        }
        return null;
    }
}
