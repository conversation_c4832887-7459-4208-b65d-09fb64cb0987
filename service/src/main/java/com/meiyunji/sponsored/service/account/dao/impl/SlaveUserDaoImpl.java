package com.meiyunji.sponsored.service.account.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.SlaveBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.enums.UserStatusEnum;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-01-02  09:25
 */
@Repository
public class SlaveUserDaoImpl extends SlaveBaseDaoImpl<User> implements ISlaveUserDao {

    @Override
    public List<User> listNoDeleteUserByCreateTime(Date createTime) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .notEqualTo("status", UserStatusEnum.DELETE.getCode())
                .greaterThan("create_time", createTime)
                .orderBy("create_time")
                .build();
        return listByCondition(condition);
    }

    @Override
    public Integer getPlanTypeByPuid(Integer puid) {
        StringBuilder sql = new StringBuilder("select plan_type from t_user where id = ? and puid = ?");
        List<Object> argList = new ArrayList<>();
        argList.add(puid);
        argList.add(puid);
        return getJdbcTemplate().queryForObject(sql.toString(), argList.toArray(), Integer.class);
    }
}
