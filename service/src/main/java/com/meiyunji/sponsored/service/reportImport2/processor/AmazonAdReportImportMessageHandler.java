package com.meiyunji.sponsored.service.reportImport2.processor;

import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.processor.LxReportImportFromExcelHolder;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * 广告报告导入消息处理程序
 *
 * <AUTHOR>
 * @date 2023/05/25
 */
@Service
public class AmazonAdReportImportMessageHandler {

    private final AmazonAdLxReportImportFromInterfaceHolder amazonAdLxReportImportFromInterfaceHolder;

    /**
     * 广告报告导入消息处理程序
     */
    public AmazonAdReportImportMessageHandler(AmazonAdLxReportImportFromInterfaceHolder amazonAdLxReportImportFromInterfaceHolder) {
        this.amazonAdLxReportImportFromInterfaceHolder = amazonAdLxReportImportFromInterfaceHolder;
    }

    /**
     * 消息处理程序
     *
     * @param message 消息
     * @throws IOException ioexception
     */
    public void messageHandler(AmazonAdReportImportMessage message) throws IOException {
        amazonAdLxReportImportFromInterfaceHolder.executor(message);
    }
}
