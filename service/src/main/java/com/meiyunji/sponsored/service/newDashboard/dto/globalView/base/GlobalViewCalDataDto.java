package com.meiyunji.sponsored.service.newDashboard.dto.globalView.base;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 全局概览-计算指标
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Data
public class GlobalViewCalDataDto extends GlobalViewBaseDataDto {

    // ACoS
    private BigDecimal acos;

    // ROAS
    private BigDecimal roas;

    // 广告点击率
    private BigDecimal ctr;

    // 广告转化率
    private BigDecimal cvr;

    // CPC
    private BigDecimal adCostPerClick;

    // CPA
    private BigDecimal cpa;

    // ACoTS
    private BigDecimal acots;

    // ASoTS
    private BigDecimal asots;

    // 广告笔单价
    private BigDecimal advertisingUnitPrice;

    // SPC
    private BigDecimal spc;

    @ExcelProperty("ACoS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAcos;

    @ExcelProperty("ROAS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayRoas;

    @ExcelProperty("广告点击率")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayCtr;

    @ExcelProperty("广告转化率")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayCvr;

    @ExcelProperty("CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayAdCostPerClick;

    @ExcelProperty("CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayCpa;

    @ExcelProperty("ACoTS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAcots;

    @ExcelProperty("ASoTS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAsots;

    @ExcelProperty("广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayAdvertisingUnitPrice;

    @ExcelProperty("SPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displaySpc;
}
