package com.meiyunji.sponsored.service.adTagSystem.dao;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroupUser;
import com.meiyunji.sponsored.service.cpc.vo.TagGroupVo;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-22  22:44
 */
public interface IAdManageTagGroupUserDao extends IBaseShardingDao<AdManageTagGroupUser> {

    void batchInsert(int puid, List<AdManageTagGroupUser> list);

    void delByPuidGroupId(int puid, Long groupId);

    void delByPuidGroupIds(int puid, List<Long> groupIds);

    /**
     * 根据uid获取有权限的标签组id列表
     */
    List<Long> listGroupIdByUid(Integer puid, Integer type, Integer uid);

    /**
     * 根据puid获取所有标签组id，子管理员或超级管理员可用
     */
    List<Long> listGroupIdByPuid(Integer puid, Integer type);

    List<Long> listGroupIdByUid(Integer puid, Integer uid);

    List<AdManageTagGroupUser> listByGroupIds(Integer puid, List<Long> groupIds);

    List<Long> getTagGroup(int puid, Integer uid, Integer type);

    boolean hasGroupPermission(int puid, int uid, long groupId);

    List<Long> filterPermissionGroupIds(int puid, int uid, List<Long> groupIds);

    List<Integer> listUserIdByGroupId(Integer puid, Long groupId);

}
