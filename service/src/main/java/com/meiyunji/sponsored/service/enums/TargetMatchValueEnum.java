package com.meiyunji.sponsored.service.enums;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-23  16:26
 */
public enum TargetMatchValueEnum {

    /**
     * 投放匹配类型汇总
     * */
    exact("exact","精确匹配"),
    broad("broad","广泛匹配"),
    phrase("phrase","词组匹配"),
    complements("complements", "关联商品"),
    substitutes("substitutes", "同类商品"),
    loosematch("loose-match", "宽泛匹配"),
    closematch("close-match", "紧密匹配"),
    theme("theme","主题");

    TargetMatchValueEnum(String matchType, String matchValue){
        this.matchType=matchType;
        this.matchValue=matchValue;
    }

    private String matchType;
    private String matchValue;

    public static String getMatchValue(String matchType){
        TargetMatchValueEnum[] values = values();
        for (TargetMatchValueEnum value : values) {
            if (value.getMatchType().equalsIgnoreCase(matchType)){
                return value.getMatchValue();
            }
        }
        return "";
    }

    public static TargetMatchValueEnum getMatchValueEnumByMatchType(String matchType){
        TargetMatchValueEnum[] values = values();
        for (TargetMatchValueEnum value : values) {
            if (value.getMatchType().equalsIgnoreCase(matchType)){
                return value;
            }
        }
        return null;
    }

    public String getMatchType() {
        return matchType;
    }

    public String getMatchValue() {
        return matchValue;
    }
}
