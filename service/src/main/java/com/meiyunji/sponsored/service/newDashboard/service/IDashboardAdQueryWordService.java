package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdQueryWordResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.QueryListDataResponse;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardQueryWordReqVo;

import java.util.List;

public interface IDashboardAdQueryWordService {
    List<DashboardAdQueryWordResponseVo> queryAdQueryWordCharts(DashboardQueryWordReqVo reqVo);

    List<String> exportAdQueryWordCharts(DashboardQueryWordReqVo reqVo);

    QueryListDataResponse.KeywordListDataRpcVo.Page  queryAdQueryWordPageList(DashboardQueryWordReqVo reqVo);
}
