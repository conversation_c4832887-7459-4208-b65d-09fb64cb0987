package com.meiyunji.sponsored.service.newDashboard.dto.base;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-03-26  14:30
 */

@Data
public class DashboardAdBaseDataDto {

    //基本指标，从数据库查得
    private BigDecimal cost;

    @ExcelProperty("广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayCost;

    private BigDecimal totalSales;

    @ExcelProperty("广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayTotalSales;

    @ExcelProperty("广告曝光量")
    private Long impressions;

    @ExcelProperty("广告点击量")
    private Integer clicks;

    @ExcelProperty("广告订单量")
    private Integer orderNum;

    @ExcelProperty("广告销量")
    private Integer saleNum;

    private BigDecimal shopSales;

    @ExcelProperty("店铺销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayShopSales;

    @ExcelProperty("店铺销量")
    private Integer shopSaleNum;

}
