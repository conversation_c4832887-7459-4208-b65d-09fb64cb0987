package com.meiyunji.sponsored.service.budgetUsage.impl;

import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetUsageDayService;
import com.meiyunji.sponsored.service.budgetUsage.dao.IAmazonAdBudgetUsageDao;
import com.meiyunji.sponsored.service.budgetUsage.entity.AmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdOperationLogDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageOperationLogDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsageDay;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdBudgetUsageDayServiceImpl implements IAmazonAdBudgetUsageDayService {
    @Resource
    private IAmazonAdBudgetUsageDao amazonAdBudgetUsageDao;
    @Resource
    private IDorisService dorisService;
    @Resource
    private IAmazonAdOperationLogDao amazonAdOperationLogDao;
    @Resource
    private IOdsAdManageOperationLogDao odsAdManageOperationLogDao;

    @Override
    public void syncAmazonAdBudgetUsageToday(Integer puid, ShopAuth shopAuth) {
        // 计算站点今日的数据
        LocalDate siteDate = LocalDate.now(Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone().toZoneId());
        syncAmazonAdBudgetUsageByDate(puid, shopAuth, siteDate, true);
    }

    @Override
    public void syncAmazonAdBudgetUsageYesterday(Integer puid, ShopAuth shopAuth) {
        // 计算站点昨日的数据
        LocalDate siteDate = LocalDate.now(Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone().toZoneId()).minusDays(1L);
        syncAmazonAdBudgetUsageByDate(puid, shopAuth, siteDate, false);
    }

    @Override
    public void syncAmazonAdBudgetUsageByDay(Integer puid, ShopAuth shopAuth, LocalDate nowDate, long day) {
        for (int i = 0; i <= day; i++) {
            LocalDate syncDate = nowDate.minusDays(-i);
            syncAmazonAdBudgetUsageByDate(puid, shopAuth, syncDate, false);
        }
    }

    /**
     * 同步站点今日的数据
     */
    @Override
    public void syncAmazonAdBudgetUsageByDate(Integer puid, ShopAuth shopAuth, LocalDate siteDate, boolean isSiteToday) {
        Marketplace marketplace = Marketplace.fromId(shopAuth.getMarketplaceId());
        // 获取站点今日的日期
        List<AmazonAdBudgetUsage> amazonAdBudgetUsages = amazonAdBudgetUsageDao.listCampaignBySiteDate(puid, shopAuth.getId(), siteDate);
        if (CollectionUtils.isEmpty(amazonAdBudgetUsages)) {
            log.info(" syncAmazonAdBudgetUsageToday is null puid = {}, shopId = {}, siteDate = {}", puid, shopAuth.getId(), siteDate);
            return;
        }
        // 当前时间 或者 最后当天最后时刻
        LocalDateTime siteLastDateTime = isSiteToday ? LocalDateTime.now(marketplace.getTimeZone().toZoneId()) : siteDate.minusDays(-1).atStartOfDay();
        // 根据活动Id分组
        Map<String, List<AmazonAdBudgetUsage>> listMap = amazonAdBudgetUsages.stream().collect(Collectors.groupingBy(AmazonAdBudgetUsage::getBudgetScopeId));
        String nowDate = siteDate.toString();
        String lastNowDate = siteDate.minusDays(-1).toString();
        List<AmazonAdOperationLogBO> amazonAdOperationLogBOS = amazonAdOperationLogDao.listByOneDayAll(puid, shopAuth.getId(), AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), nowDate + " 00:00:00", lastNowDate + " 00:00:00");
        Map<String, List<AdManageOperationLog>> dorisLogMap = listSellfoxAndAutoLog(puid, shopAuth, nowDate).stream().collect(Collectors.groupingBy(AdManageOperationLog::getCampaignId));
        Map<String, List<AmazonAdOperationLogBO>> map = amazonAdOperationLogBOS.stream().collect(Collectors.groupingBy(AmazonAdOperationLogBO::getIdentifyId));
        List<OdsAmazonAdBudgetUsageDay> dorisMessage = new ArrayList<>(listMap.keySet().size());
        LocalDateTime siteLastEndTime = siteDate.atStartOfDay();
        listMap.forEach((key, value) -> dealAmazonAdBudgetUsageDay(value, dorisMessage, siteLastEndTime, siteLastDateTime, map, dorisLogMap, isSiteToday));
        if (CollectionUtils.isNotEmpty(dorisMessage)) {
            // 发送doris消息
            dorisService.saveDorisByRoutineLoad("doris_ods_t_amazon_ad_budget_usage_day", dorisMessage);
        }
    }

    /**
     * 查找doris请求日志
     */
    private List<AdManageOperationLog> listSellfoxAndAutoLog(Integer puid, ShopAuth shopAuth, String siteDate) {
        List<AdManageOperationLog> list = new ArrayList<>();
        List<AdManageOperationLog> adManageOperationLogs = odsAdManageOperationLogDao.listBudgetByDate(puid, shopAuth.getId(), OperationLogFromEnum.SELLFOX.getOperationType(), siteDate);
        if (CollectionUtils.isNotEmpty(adManageOperationLogs)) {
            list.addAll(adManageOperationLogs);
        }
        List<AdManageOperationLog> adManageOperationLogList = odsAdManageOperationLogDao.listBudgetByDate(puid, shopAuth.getId(), OperationLogFromEnum.AUTO.getOperationType(), siteDate);
        if (CollectionUtils.isNotEmpty(adManageOperationLogList)) {
            list.addAll(adManageOperationLogList);
        }
        return list;
    }

    private void dealAmazonAdBudgetUsageDay(List<AmazonAdBudgetUsage> amazonAdBudgetUsages, List<OdsAmazonAdBudgetUsageDay> dorisMessage, LocalDateTime siteStarDateTime, LocalDateTime siteEndDateTime, Map<String, List<AmazonAdOperationLogBO>> map, Map<String, List<AdManageOperationLog>> dorisLogMap, boolean isSiteToday) {
        amazonAdBudgetUsages.sort(Comparator.comparing(AmazonAdBudgetUsage::getUsageUpdatedSiteTime));
        // 获取最大的的记录
        OdsAmazonAdBudgetUsageDay amazonAdBudgetUsageDay = build(amazonAdBudgetUsages.get(amazonAdBudgetUsages.size() - 1), Collections.max(amazonAdBudgetUsages.stream().map(AmazonAdBudgetUsage::getBudget).collect(Collectors.toList())));
        // 活跃的分钟
        long budgetTime = 0;
        long overBudget = 0;
        // 调整次数
        int budgetAdjustmentNum = 0;
        // 是否超预算
        boolean isBudgetOver = false;
        StringBuilder adjustmentTime = null;
        //开始时间
        LocalDateTime beginDateTime = siteStarDateTime;
        int length = amazonAdBudgetUsages.size();
        BigDecimal budget = amazonAdBudgetUsages.get(0).getBudget();
        for (int i = 0; i < length; i++) {
            AmazonAdBudgetUsage amazonAdBudgetUsage = amazonAdBudgetUsages.get(i);
            // 判断是否调整预算
            if (amazonAdBudgetUsage.getBudget().compareTo(budget) != 0) {
                budgetAdjustmentNum++;
                budget = amazonAdBudgetUsage.getBudget();
                if (adjustmentTime == null) {
                    adjustmentTime = new StringBuilder(amazonAdBudgetUsage.getUsageUpdatedSiteTime().format(DateTimeFormatter.ofPattern("HH:mm")));
                } else {
                    adjustmentTime.append(",").append(amazonAdBudgetUsage.getUsageUpdatedSiteTime().format(DateTimeFormatter.ofPattern("HH:mm")));
                }
            }
            LocalDateTime siteTime = amazonAdBudgetUsage.getUsageUpdatedSiteTime();
            long time = Math.abs(ChronoUnit.MINUTES.between(beginDateTime, siteTime));
            if (isBudgetOver) {
                // 代表这段时间 是超预算的
                overBudget += time;
            } else {
                // 代表没有超预算
                budgetTime += time;
            }
            beginDateTime = siteTime;
            // 代表超预算了
            isBudgetOver = amazonAdBudgetUsage.getBudgetUsagePercentage().compareTo(BigDecimal.valueOf(100L)) >= 0;
            // 代表最后结束了
            if (i == (length - 1)) {
                long endTime = Math.abs(ChronoUnit.MINUTES.between(siteTime, siteEndDateTime));
                if (isBudgetOver) {
                    // 代表这段时间 是超预算的
                    overBudget += endTime;
                } else {
                    // 代表没有超预算
                    budgetTime += endTime;
                }
            }
        }
        // 查询亚马逊日志的操作以及调整时间
        if (map.containsKey(amazonAdBudgetUsageDay.getBudgetScopeId())) {
            List<AmazonAdOperationLogBO> bos = map.get(amazonAdBudgetUsageDay.getBudgetScopeId());
            bos.sort(Comparator.comparing(AmazonAdOperationLogBO::getSiteOperationTime));
            budgetAdjustmentNum = bos.size();
            adjustmentTime = new StringBuilder(bos.stream().map(k -> k.getSiteOperationTime().substring(11, 16)).collect(Collectors.joining(",")));
        }
        // 不存在既查doris sellfox 和 auto 日志
        if (!map.containsKey(amazonAdBudgetUsageDay.getBudgetScopeId())) {
            if (dorisLogMap.containsKey(amazonAdBudgetUsageDay.getBudgetScopeId())) {
                List<AdManageOperationLog> bos = dorisLogMap.get(amazonAdBudgetUsageDay.getBudgetScopeId());
                bos.sort(Comparator.comparing(AdManageOperationLog::getSiteOperationTime));
                budgetAdjustmentNum = bos.size();
                adjustmentTime = new StringBuilder(bos.stream().map(k -> k.getSiteOperationTime().substring(11, 16)).collect(Collectors.joining(",")));
            }
        }
        amazonAdBudgetUsageDay.setBudgetAdjustmentNum(budgetAdjustmentNum);
        amazonAdBudgetUsageDay.setBudgetAdjustmentTime(adjustmentTime == null ? "" : adjustmentTime.toString());
        amazonAdBudgetUsageDay.setOverBudgetTime(BigDecimal.valueOf(overBudget).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP));
        amazonAdBudgetUsageDay.setBudgetTime(isSiteToday ? BigDecimal.valueOf(budgetTime).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP) : BigDecimal.valueOf(24L).subtract(amazonAdBudgetUsageDay.getOverBudgetTime()).setScale(2, RoundingMode.HALF_UP));
        amazonAdBudgetUsageDay.setTotalBudgetTime(isSiteToday ? BigDecimal.valueOf(budgetTime + overBudget).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP) : BigDecimal.valueOf(24L));
        dorisMessage.add(amazonAdBudgetUsageDay);
    }

    /**
     * 构造基本信息
     */
    private OdsAmazonAdBudgetUsageDay build(AmazonAdBudgetUsage amazonAdBudgetUsage, BigDecimal budget) {
        OdsAmazonAdBudgetUsageDay amazonAdBudgetUsageDay = new OdsAmazonAdBudgetUsageDay();
        amazonAdBudgetUsageDay.setPuid(amazonAdBudgetUsage.getPuid());
        amazonAdBudgetUsageDay.setShopId(amazonAdBudgetUsage.getShopId());
        amazonAdBudgetUsageDay.setSellerId(amazonAdBudgetUsage.getSellerId());
        amazonAdBudgetUsageDay.setMarketplaceId(amazonAdBudgetUsage.getMarketplaceId());
        amazonAdBudgetUsageDay.setBudgetScopeId(amazonAdBudgetUsage.getBudgetScopeId());
        amazonAdBudgetUsageDay.setBudgetScopeType(amazonAdBudgetUsage.getBudgetScopeType());
        amazonAdBudgetUsageDay.setAdvertisingProductType(amazonAdBudgetUsage.getAdvertisingProductType());
        amazonAdBudgetUsageDay.setBudget(budget);
        amazonAdBudgetUsageDay.setBudgetUsagePercentage(amazonAdBudgetUsage.getBudgetUsagePercentage());
        // 预算使用
        amazonAdBudgetUsageDay.setBudgetUsage(amazonAdBudgetUsage.getBudget().multiply(amazonAdBudgetUsage.getBudgetUsagePercentage()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        // 剩余预算
        BigDecimal remainingBudget = amazonAdBudgetUsage.getBudget().multiply(BigDecimal.valueOf(100L).subtract(amazonAdBudgetUsage.getBudgetUsagePercentage())).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        amazonAdBudgetUsageDay.setBudgetRemaining(remainingBudget);
        // 日期参数
        amazonAdBudgetUsageDay.setUsageUpdatedSiteDate(amazonAdBudgetUsage.getUsageUpdatedSiteDate());
        amazonAdBudgetUsageDay.setUsageUpdatedTimestamp(LocalDateTime.now());
        amazonAdBudgetUsageDay.setBudgetAdjustmentNum(0);
        amazonAdBudgetUsageDay.setSiteMonth(Integer.parseInt(amazonAdBudgetUsage.getUsageUpdatedSiteDate().format(DateTimeFormatter.ofPattern(LocalDateTimeUtil.YM_DATE_FORMATE))));
        Marketplace marketplace = Marketplace.fromId(amazonAdBudgetUsage.getMarketplaceId());
        amazonAdBudgetUsageDay.setCurrency(marketplace.getCurrencyCode().name());
        return amazonAdBudgetUsageDay;
    }

}
