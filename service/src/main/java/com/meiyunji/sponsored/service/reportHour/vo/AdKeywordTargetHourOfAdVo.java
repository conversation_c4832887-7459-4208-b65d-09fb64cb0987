package com.meiyunji.sponsored.service.reportHour.vo;

import com.meiyunji.sponsored.common.util.MathUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/12/1 20:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdKeywordTargetHourOfAdVo extends AdKeywordTargetHourBaseVo {
    private String asin;

    public void dataScaleFormat() {
        this.setAdCost(MathUtil.safeDecimal(this.getAdCost(), 2));
        this.setRoas(MathUtil.safeDecimal(this.getRoas(), 2));
        this.setCpa(MathUtil.safeDecimal(this.getCpa(), 2));
        this.setAdCostPerClick(MathUtil.safeDecimal(this.getAdCostPerClick(), 2));
        this.setAcos(MathUtil.safeDecimal(this.getAcos(), 2));
        this.setCtr(MathUtil.safeDecimal(this.getCtr(), 2));
        this.setCvr(MathUtil.safeDecimal(this.getCvr(), 2));
        this.setAdSale(MathUtil.safeDecimal(this.getAdSale(), 2));
        this.setAdSelfSale(MathUtil.safeDecimal(this.getAdSelfSale(), 2));
        this.setAdOtherSale(MathUtil.safeDecimal(this.getAdOtherSale(), 2));
        if (CollectionUtils.isNotEmpty(this.getDetails())) {
            this.getDetails().forEach(d -> {
                d.setAdCost(MathUtil.safeDecimal(d.getAdCost(), 2));
                d.setRoas(MathUtil.safeDecimal(d.getRoas(), 2));
                d.setCpa(MathUtil.safeDecimal(d.getCpa(), 2));
                d.setAdCostPerClick(MathUtil.safeDecimal(d.getAdCostPerClick(), 2));
                d.setAcos(MathUtil.safeDecimal(d.getAcos(), 2));
                d.setCtr(MathUtil.safeDecimal(d.getCtr(), 2));
                d.setCvr(MathUtil.safeDecimal(d.getCvr(), 2));
                d.setAdSale(MathUtil.safeDecimal(d.getAdSale(), 2));
                d.setAdSelfSale(MathUtil.safeDecimal(d.getAdSelfSale(), 2));
                d.setAdOtherSale(MathUtil.safeDecimal(d.getAdOtherSale(), 2));
            });
        }
        this.setDetail(this.getDetails());
    }
}
