package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: wade
 * @date: 2021/8/20 10:38
 * @describe: sp广告活动-否定商品投放-创建参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CampaignNeTargetingSpAddParam {

    private Integer puid;
    private Integer uid;
    private String loginIp;
    private Integer shopId;

    /**
     * 活动id
     */
    private String campaignId;


    /**
     * vos
     */
    private List<NeTargetingVo> asinList;

}
