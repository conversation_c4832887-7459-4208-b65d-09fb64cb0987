package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmcQueryTargetingReport;
import com.meiyunji.sponsored.service.amc.dto.QueryWordAdSpaceFilterDto;
import com.meiyunji.sponsored.service.amc.dto.QueryWordAdSpaceReportDto;
import com.meiyunji.sponsored.service.amc.resp.ListHighValueWordsResp;

import java.util.List;
import java.util.Map;

/**
 * @Author: hejh
 * @Date: 2025/2/12 13:52
 */
public interface IOdsAmcQueryTargetingReportDao extends IDorisBaseDao<OdsAmcQueryTargetingReport> {

    List<String> getQueryIdsByFilter(QueryWordAdSpaceFilterDto filterDto);

    Page<QueryWordAdSpaceReportDto> pageByFilter(QueryWordAdSpaceFilterDto filterDto, boolean containAsin, List<String>queryId, boolean joinSearchTermsRank , Map<String, String> marketplaceIdLatestDateMap, int pageNo, int pageSize);


    List<ListHighValueWordsResp.HighValueWord> groupQueryWordAndCount(QueryWordAdSpaceFilterDto filterDto, boolean containAsin, List<String> queryId);


}
