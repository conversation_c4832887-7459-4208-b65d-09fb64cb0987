package com.meiyunji.sponsored.service.log.enums;

public enum OperationLogResultEnum {

    /**
     * 操作类型
     */
    SUCCESS("success", 0),
    FAIL("fail", 1)
    ;

    private String result;

    private Integer resultValue;

    OperationLogResultEnum(String result, Integer resultValue) {
        this.result = result;
        this.resultValue = resultValue;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getResultValue() {
        return resultValue;
    }

    public void setResultValue(Integer resultValue) {
        this.resultValue = resultValue;
    }
}
