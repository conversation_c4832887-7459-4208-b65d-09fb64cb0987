package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sellfox.ams.api.entry.ProductPerspectiveAnalysisAdvancedFilterDataPb;
import com.meiyunji.sellfox.ams.api.service.AmsApiGrpc;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.AmazonSdAdCampaignCostTypeBo;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.ProductPerspectiveAnalysisFilterDto;
import com.meiyunji.sponsored.service.cpc.dto.ProductPerspectiveDiagnoseSelectDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.dataWarehouse.service.SearchAnalysisStatsV2Client;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.bo.AdTargetOrderBo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregateIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.TargetFirstPlaceIsDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.handler.ViewManageHandler;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.helper.ViewServiceHelper;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AudienceTargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IAudienceTargetViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-14  15:35
 */
@Service
@Slf4j
public class AudienceTargetViewServiceImpl implements IAudienceTargetViewService {
    @Qualifier("adFeedBlockingStub")
    @Autowired
    private AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdOperationLogService amazonAdOperationLogService;
    @Autowired
    SearchAnalysisStatsV2Client searchAnalysisStatsV2Client;
    @Autowired
    ISyncAsinImageService syncAsinImageService;
    @Autowired
    private ICpcTargetingReportDao cpcTargetingReportDao;
    @Autowired
    private IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private ViewManageHandler viewManageHandler;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;

    /**
     * 通过Feed表分页
     */
    @Override
    public Page<AudienceTargetViewVo> getAllAudienceTargetViewPageVoList(Integer puid, AudienceTargetViewParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析 {} --受众投放视图列表页接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        Page<AudienceTargetViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        //根据所选的ASIN查询广告产品表查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = viewManageHandler.queryProductBoLists(puid, param);
        if (CollectionUtils.isEmpty(productBoList)) {
            return voPage;
        }
        //填充店铺数据与销售额，过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        viewManageHandler.fillShopSellerId(puid, param);
        viewManageHandler.fillShopSale(puid, param);
        //与筛选的广告组对比，若无交集则直接返回空
        List<String> adGroupIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getAdGroupId).distinct().collect(Collectors.toList());
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return voPage;
            }
        }
        //获取受众
        param.setAdGroupIdList(adGroupIdList);
        //广告活动筛选
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> adGroupIds = amazonSdAdGroupDao.getIdListByCampaignIds(puid, param.getShopIdList(), StringUtil.splitStr(param.getCampaignId()));
            if (CollectionUtils.isEmpty(adGroupIds)) {
                return voPage;
            }
            if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
                adGroupIds.retainAll(param.getAdGroupIdList());
                if (CollectionUtils.isEmpty(adGroupIds)) {
                    return voPage;
                }
            }
            param.setAdGroupIdList(adGroupIds);
        }
        //若有诊断优化定位和数据指标排序，在查Feed时分页，否则在查广告活动时分页
        List<AmazonSdAdTargeting> sdTargetList = amazonSdAdTargetingDao.getAudienceTargetViewList(puid, param, false);
        log.info("产品透视分析{}--受众投放视图列表页接口调用-获取投放数据- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(sdTargetList)) {
            return voPage;
        }
        //填充投放id和广告产品id准备查feed
        this.fillProductBoTargetIdAndAdId(param, productBoList, sdTargetList);
        //分页参数
        ProductPerspectiveAnalysisFilterDto advance = null;
        if (param.getUseAdvanced()) {
            ProductPerspectiveAnalysisAdvancedFilterDataPb.ProductPerspectiveAnalysisAdvancedFilterData advancedFilter =
                    ViewServiceHelper.campaignViewParamToAdvancedFilter(param);
            advance = new ProductPerspectiveAnalysisFilterDto(advancedFilter);
        }
        param.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(param.getSellerIdList(), param.getMarketplaceId(), param.getStartDate()));
        BigDecimal shopSales = Optional.ofNullable(param.getShopSales()).orElse(BigDecimal.ZERO);
        //是否填充过cpc和vcpm类型标识
        boolean fillFeedSdTypeFlag = false;
        //竞价排序或者sd本产品广告订单量和其他产品广告订单量筛选时内存分页，否则查feed时sql分页
        Page<AmazonMarketingStreamData> targetViewResponse;
        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && (KeywordViewParam.OrderFieldEnum.BID.getField().equals(param.getOrderField())
                || KeywordViewParam.OrderFieldEnum.VCPM.getField().equals(param.getOrderField()) || KeywordViewParam.OrderFieldEnum.AD_OTHER_ORDER_NUM.getField().equals(param.getOrderField())))
                || (param.getUseAdvanced() && advance != null && (advance.getAdSaleNumMin() != null || advance.getAdSaleNumMax() != null || advance.getAdOtherOrderNumMin() != null|| advance.getAdOtherOrderNumMax() != null))
                || (param.getUseAdvanced() && (param.getVcpmMax() != null || param.getVcpmMin() != null))) {
            List<AmazonMarketingStreamData> targetViewList = amazonMarketingStreamDataDao.productPerspectiveAnalysisAllTargetViewList(param, shopSales, advance);
            //本产品广告订单量和其他产品广告订单量筛选内存过滤
            if (param.getUseAdvanced() && advance != null && (advance.getAdSaleNumMin() != null || advance.getAdSaleNumMax() != null || advance.getAdOtherOrderNumMin() != null|| advance.getAdOtherOrderNumMax() != null)) {
                //填充cpc和vcpm
                Map<String, String> targetGroupIdMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId));
                viewManageHandler.fillFeedSdType(puid, param.getShopIdList(), targetGroupIdMap, targetViewList);
                fillFeedSdTypeFlag = true;
                viewManageHandler.sdFilterItemList(advance, targetViewList);
            }
            // vcpm高级筛选内存过滤
            filterVcpmAdvance(puid, param, targetViewList, sdTargetList);
            targetViewResponse = this.getTargetViewPageByInternalMemory(param, targetViewList, sdTargetList);
        } else {
            if ((!StringUtils.isAnyBlank(param.getOrderField(), param.getOrderType())) && OrderTypeEnum.typeSet().contains(param.getOrderType())) {
                param.setOrderField(AudienceTargetViewVo.orderFieldTransferMap.getOrDefault(param.getOrderField(), param.getOrderField()));
                param.setOrderType(param.getOrderType());
            }
            targetViewResponse = amazonMarketingStreamDataDao.productPerspectiveAnalysisAllTargetViewPage(param, shopSales, advance);
        }
        List<AmazonMarketingStreamData> itemList = targetViewResponse.getRows();
        voPage.setTotalPage(targetViewResponse.getTotalPage());
        voPage.setTotalSize(targetViewResponse.getTotalSize());
        if (CollectionUtils.isEmpty(itemList)) {
            return voPage;
        }
        log.info("产品透视分析{}--受众投放视图列表页接口调用-获取Feed数据- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        //填充sd广告类型，cpc或vcpm
        if (!fillFeedSdTypeFlag) {
            Map<String, String> targetGroupIdMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId));
            viewManageHandler.fillFeedSdType(puid, param.getShopIdList(), targetGroupIdMap, itemList);
        }
        //批量获取is指标
        List<String> sbTargetIdList = itemList.stream().map(AmazonMarketingStreamData::getKeywordId).collect(Collectors.toList());
        List<TargetFirstPlaceIsDto> targetFirstPlaceIsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sbTargetIdList)) {
            targetFirstPlaceIsList.addAll(amazonAdSbTargetingReportDao.getFirstPlaceIsDtoByKeywordId(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), sbTargetIdList));
        }
        Map<String, TargetFirstPlaceIsDto> targetFirstPlaceIsMap = targetFirstPlaceIsList.stream().collect(Collectors.toMap(TargetFirstPlaceIsDto::getTargetId, Function.identity()));
        Map<String, AmazonMarketingStreamData> dataMap = itemList.stream()
                .collect(Collectors.toMap(AmazonMarketingStreamData::getKeywordId, Function.identity(), (c1, c2) -> c2, LinkedHashMap::new));
        //获取占比指标汇总数据
        List<AmazonMarketingStreamData> sumDataList = amazonMarketingStreamDataDao.productPerspectiveAnalysisAllTargetViewSumAdMetric(param, shopSales, advance);
        AdMetricDto adMetricDto = this.getSumAdMetricDto(sumDataList);
        //构建受众投放列表页
        voPage.setRows(this.buildAudienceTargetViewVo(puid, param, sdTargetList, dataMap, adMetricDto, targetFirstPlaceIsMap));
        return voPage;
    }

    @Override
    public AudienceTargetViewAggregatePageVo getAllAudienceTargetViewAggregatePageVo(Integer puid, AudienceTargetViewParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析 {}--受众投放视图汇总接口调用-参数 {}", uuid, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        AudienceTargetViewAggregatePageVo aggregatePageVo = new AudienceTargetViewAggregatePageVo();
        Page<AudienceTargetViewAggregateVo> voPage = new Page<>();
        List<AudienceTargetViewAggregateVo> voList = new ArrayList<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        voPage.setRows(voList);
        aggregatePageVo.setPage(voPage);
        //根据所选的ASIN查询广告产品表查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = viewManageHandler.queryProductBoLists(puid, param);
        if (CollectionUtils.isEmpty(productBoList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool().execute(() -> {
                cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(new AggregateIdsTemporary(), param.getPageSign(), "");
            });
            return aggregatePageVo;
        }
        //填充店铺数据与销售额，过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        viewManageHandler.fillShopSellerId(puid, param);
        viewManageHandler.fillShopSale(puid, param);
        //与筛选的广告组对比，若无交集则直接返回空
        List<String> adGroupIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getAdGroupId).distinct().collect(Collectors.toList());
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                //存储列表页的所有id到数据库中，用于展示汇总趋势图
                ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool().execute(() -> {
                    cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(new AggregateIdsTemporary(), param.getPageSign(), "");
                });
                return aggregatePageVo;
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        //广告活动筛选
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> adGroupIds = amazonSdAdGroupDao.getIdListByCampaignIds(puid, param.getShopIdList(), StringUtil.splitStr(param.getCampaignId()));
            if (CollectionUtils.isEmpty(adGroupIds)) {
                return aggregatePageVo;
            }
            if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
                adGroupIds.retainAll(param.getAdGroupIdList());
                if (CollectionUtils.isEmpty(adGroupIds)) {
                    return aggregatePageVo;
                }
            }
            param.setAdGroupIdList(adGroupIds);
        }
        //若有诊断优化定位和数据指标排序，在查Feed时分页，否则在查广告活动时分页
        List<AmazonSdAdTargeting> sdTargetList = amazonSdAdTargetingDao.getAudienceTargetViewList(puid, param, true);
        log.info("产品透视分析{}--受众投放视图汇总接口调用-获取投放数据- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(sdTargetList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool().execute(() -> {
                cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(new AggregateIdsTemporary(), param.getPageSign(), "");
            });
            return aggregatePageVo;
        }
        //填充投放id和广告产品id准备查feed
        this.fillProductBoTargetIdAndAdId(param, productBoList, sdTargetList);
        //分页参数
        ProductPerspectiveAnalysisFilterDto advance = null;
        if (param.getUseAdvanced()) {
            ProductPerspectiveAnalysisAdvancedFilterDataPb.ProductPerspectiveAnalysisAdvancedFilterData advancedFilter =
                    ViewServiceHelper.campaignViewParamToAdvancedFilter(param);
            advance = new ProductPerspectiveAnalysisFilterDto(advancedFilter);
        }
        param.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(param.getSellerIdList(), param.getMarketplaceId(), param.getStartDate()));
        BigDecimal shopSales = Optional.ofNullable(param.getShopSales()).orElse(BigDecimal.ZERO);
        List<AmazonMarketingStreamData> itemList = amazonMarketingStreamDataDao.productPerspectiveAnalysisAllTargetViewList(param, shopSales, advance);
        log.info("产品透视分析 {}--受众投放视图汇总接口调用-获取Feed数据-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        // vcpm高级筛选过滤
        itemList = filterVcpmAdvance(puid, param, itemList, sdTargetList);
        if (CollectionUtils.isEmpty(itemList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool().execute(() -> {
                cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(new AggregateIdsTemporary(), param.getPageSign(), "");
            });
            return aggregatePageVo;
        }
        //填充sd广告类型，cpc或vcpm
        Map<String, String> targetGroupIdMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId));
        viewManageHandler.fillFeedSdType(puid, param.getShopIdList(), targetGroupIdMap, itemList);
        if (param.getUseAdvanced() && advance != null) {
            viewManageHandler.sdFilterItemList(advance, itemList);
        }
        //存储列表页的所有id到数据库中，用于展示汇总趋势图
        List<AmazonMarketingStreamData> finalItemList = itemList;
        ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool().execute(() -> {
            List<String> allAdIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(param.getSpAdIdList())) {
                allAdIdList.addAll(param.getSpAdIdList());
            }
            if (CollectionUtils.isNotEmpty(param.getSbAdIdList())) {
                allAdIdList.addAll(param.getSbAdIdList());
            }
            if (CollectionUtils.isNotEmpty(param.getSdAdIdList())) {
                allAdIdList.addAll(param.getSdAdIdList());
            }
            AggregateIdsTemporary aggregateIdsTemporary = new AggregateIdsTemporary(allAdIdList,
                    finalItemList.stream().map(AmazonMarketingStreamData::getKeywordId).collect(Collectors.toList()));
            cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(aggregateIdsTemporary, param.getPageSign(), "");
        });

        //总汇总数据
        AmazonMarketingStreamData marketingStreamData = ViewServiceHelper.summaryStreamData(itemList);
        AudienceTargetViewAggregateVo vo = new AudienceTargetViewAggregateVo();
        ViewServiceHelper.fillStreamAggregateDataIntoViewVo(vo, marketingStreamData, param.getShopSales());
        vo.setAdCostPercentage(marketingStreamData.getCost() > 0 ? "100" : "0");
        vo.setAdSalePercentage(marketingStreamData.getAttributedSales7d().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        vo.setAdOrderNumPercentage(marketingStreamData.getAttributedConversions7d() > 0 ? "100" : "0");
        vo.setOrderNumPercentage(marketingStreamData.getAttributedUnitsOrdered7d() > 0 ? "100" : "0");
        aggregatePageVo.setAggregateVo(vo);
        //比例指标
        AdMetricDto adMetricDto = new AdMetricDto();
        adMetricDto.setSumCost(new BigDecimal(vo.getAdCost()));
        adMetricDto.setSumAdSale(new BigDecimal(vo.getAdSale()));
        adMetricDto.setSumAdOrderNum(new BigDecimal(vo.getAdOrderNum()));
        adMetricDto.setSumOrderNum(new BigDecimal(vo.getOrderNum()));

        Map<String, AmazonSdAdTargeting> sdTargetMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity(), (v1, v2) -> v2));
        //根据投放值聚合
        Map<String, List<AmazonMarketingStreamData>> targetTextDataMap = itemList.stream()
                .filter(e -> sdTargetMap.containsKey(e.getKeywordId()))
                .collect(Collectors.groupingBy(e -> {
                    AmazonSdAdTargeting target = sdTargetMap.get(e.getKeywordId());
                    String targetType = target.getTargetType();
                    if (SBTargetingAudienceTypeEnum.AUDIENCE.getType().equalsIgnoreCase(targetType)) {
                        return target.getTargetType() + "#" + AudienceCategoryTypeEnum.fromValue(target.getType()).getDesc();
                    } else {
                        return target.getTargetType() + "#" + target.getTargetText();
                    }
                }, LinkedHashMap::new, Collectors.toList()));
        //将Feed数据转换为列表页汇总指标
        for (Map.Entry<String, List<AmazonMarketingStreamData>> entry : targetTextDataMap.entrySet()) {
            vo = new AudienceTargetViewAggregateVo();
            String[] keyArray = entry.getKey().split("#");
            vo.setTargetType(keyArray[0]);
            vo.setTargetText(keyArray[1]);
            vo.setTitle(SBTargetingAudienceTypeEnum.fromValue(vo.getTargetType()).getDesc());
            marketingStreamData = ViewServiceHelper.summaryStreamData(entry.getValue());
            ViewServiceHelper.fillStreamAggregateDataIntoViewVo(vo, marketingStreamData, param.getShopSales());
            ViewServiceHelper.filterAdMetricData(vo, adMetricDto);
            voList.add(vo);
        }
        log.info("产品透视分析 {} --受众投放视图汇总接口调用-填充指标-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);

        //根据选择做排序（内存）
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            String orderField = AudienceTargetViewAggregateVo.orderFieldTransferMap.getOrDefault(param.getOrderField(), param.getOrderField());
            boolean isSorted = StringUtils.isNotBlank(orderField) && Constants.isADOrderField(orderField, AudienceTargetViewAggregateVo.class);
            if (isSorted) {
                voList = PageUtil.sort(voList, orderField, param.getOrderType());
            }
        }
        PageUtil.getPage(voPage, voList);
        log.info("产品透视分析 {} --受众投放视图汇总接口调用-排序分页-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        return aggregatePageVo;
    }

    /**
     * vcpm高级筛选内存过滤
     */
    private List<AmazonMarketingStreamData> filterVcpmAdvance(Integer puid, AudienceTargetViewParam param, List<AmazonMarketingStreamData> targetViewList, List<AmazonSdAdTargeting> sdTargetList) {
        if(CollectionUtils.isEmpty(targetViewList)){
            return targetViewList;
        }
        if((param.getUseAdvanced() && (param.getVcpmMax() != null || param.getVcpmMin() != null))){
            Map<String,String> targetIdGroupIdMap= StreamUtil.toMap(sdTargetList, AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId);
            List<AmazonSdAdGroup> groupIds = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(param.getPuid(), param.getShopIdList(), new ArrayList<>(targetIdGroupIdMap.values()));
            Map<String, String> groupIdCampainIdMap = StreamUtil.toMap(groupIds, AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getCampaignId);
            List<AmazonSdAdCampaignCostTypeBo> campaignBoList = amazonAdCampaignAllDao.listSdCostTypeBoByCampaignIds(param.getPuid(), param.getShopIdList(), new ArrayList<>(groupIdCampainIdMap.values()));
            Map<String, AmazonSdAdCampaignCostTypeBo> campaignMap = StreamUtil.toMap(campaignBoList, AmazonSdAdCampaignCostTypeBo::getCampaignId);
            targetViewList = targetViewList.stream().filter(it ->{
                String groupId = targetIdGroupIdMap.get(it.getKeywordId());
                String campaignId = groupIdCampainIdMap.get(groupId);
                AmazonSdAdCampaignCostTypeBo campaignAll = campaignMap.get(campaignId);
                return SBCampaignCostTypeEnum.VCPM.getCode().equals(campaignAll.getCostType());
            }).collect(Collectors.toList());
        }
        return targetViewList;
    }

    private void fillProductBoTargetIdAndAdId(AudienceTargetViewParam param, List<AmazonAdProductPerspectiveBO> productBoList, List<AmazonSdAdTargeting> sdTargetList) {
        Map<String, List<String>> groupTargetIdsMap = sdTargetList.stream().collect(Collectors.groupingBy(AmazonSdAdTargeting::getAdGroupId, Collectors.mapping(AmazonSdAdTargeting::getTargetId, Collectors.toList())));
        Set<String> sdVcpmAdTargetIdList = new HashSet<>();
        Set<String> sdVcpmAdIdList = new HashSet<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            if (groupTargetIdsMap.containsKey(bo.getAdGroupId())) {
                List<String> targetIds = groupTargetIdsMap.get(bo.getAdGroupId());
                sdVcpmAdTargetIdList.addAll(targetIds);
                sdVcpmAdIdList.add(bo.getAdId());
            }
        }
        param.setSdAdTargetIdList(new ArrayList<>(sdVcpmAdTargetIdList));
        param.setSdAdIdList(new ArrayList<>(sdVcpmAdIdList));
    }

    private ProductPerspectiveDiagnoseSelectDto buildBaseRequest(AudienceTargetViewParam param, List<String> targetIds) {
        //填充高级筛选数据
        ProductPerspectiveAnalysisFilterDto advance = null;
        if (param.getUseAdvanced()) {
            ProductPerspectiveAnalysisAdvancedFilterDataPb.ProductPerspectiveAnalysisAdvancedFilterData advancedFilter =
                    ViewServiceHelper.campaignViewParamToAdvancedFilter(param);
            advance = new ProductPerspectiveAnalysisFilterDto(advancedFilter);
        }
        return ProductPerspectiveDiagnoseSelectDto.builder()
                .sellerIds(param.getSellerIdList())
                .marketplaceId(param.getMarketplaceId())
                .adIds(param.getAdIdList())
                .startDate(amazonAdFeedReportService.getSellerIdsDataStartTime(param.getSellerIdList(), param.getMarketplaceId(), param.getStartDate()))
                .endDate(param.getEndDate())
                .shopSales(Optional.ofNullable(param.getShopSales()).orElse(BigDecimal.ZERO))
                .advance(advance)
                .keywordIds(targetIds)
                .build();
    }

    /**
     * 构建列表页vo
     */
    private List<AudienceTargetViewVo> buildAudienceTargetViewVo(Integer puid, AudienceTargetViewParam param, List<AmazonSdAdTargeting> sdTargetList,
                                                                 Map<String, AmazonMarketingStreamData> dataMap, AdMetricDto adMetricDto, Map<String, TargetFirstPlaceIsDto> targetFirstPlaceIsMap) {
        if (MapUtils.isEmpty(dataMap)) {
            return new ArrayList<>();
        }
        String uuid = UUID.randomUUID().toString();
        long t = Instant.now().toEpochMilli();
        //批量获取标签
        List<String> targetPageIdList = new ArrayList<>(dataMap.keySet());
        //批量获取标签
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVosByShopIdList(puid, param.getShopIdList(), AdTagTypeEnum.TARGET.getType(), StringUtil.splitStr(param.getType()), AdMarkupTargetTypeEnum.TARGET.getType(), null, targetPageIdList);
        List<Long> tagIdList = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Long, AdTag> adTagMap = new HashMap<>();
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, tagIdList);
            adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
            adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        }
        log.info("产品透视分析{}--受众投放视图列表页接口调用-批量获取标签- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        Map<String, AmazonSdAdTargeting> sdTargetMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity(), (c1, c2) -> c2, LinkedHashMap::new));
        //批量获取广告组、广告活动、广告组合信息
        List<String> sdGroupIdList = new ArrayList<>();
        for (String keywordId : targetPageIdList) {
            AmazonSdAdTargeting amazonAdTargeting = sdTargetMap.get(keywordId);
            sdGroupIdList.add(amazonAdTargeting.getAdGroupId());
        }
        //获取广告组信息
        Map<String, AmazonSdAdGroup> sdGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sdGroupIdList)) {
            sdGroupMap = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(puid, param.getShopIdList(), sdGroupIdList).stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity()));
        }

        //获取广告活动信息
        List<String> campaignIdList = sdGroupMap.values().stream().map(AmazonSdAdGroup::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.getByCampaignIdsAndShopIdList(puid, param.getShopIdList(), param.getMarketplaceId(), campaignIdList, StringUtil.splitStr(param.getType()));
        Map<String, AmazonAdCampaignAll> adCampaignAllMap = amazonAdCampaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
        //获取广告组合信息
        List<String> portfolioIdList = amazonAdCampaignAllList.stream().filter(e -> StringUtils.isNotBlank(e.getPortfolioId())).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> adPortfolioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(portfolioIdList)) {
            List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.listByShopId(puid, param.getShopIdList(), portfolioIdList);
            adPortfolioMap = amazonAdPortfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
        }
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIdsShopIdListEffectiveStatus(puid, param.getShopIdList(), "TARGET", targetPageIdList);
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1, e2) -> e1));
        }
        log.info("产品透视分析{}--受众投放视图列表页接口调用-批量获取广告组、广告活动、广告组合信息- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        //获取竞价日志
        Date lastDate = DateUtil.addDay(new Date(), -1);
        Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyIdAndShopIdList(puid, param.getShopIdList(), param.getType(),
                AmazonAdOperationLogEntityTypeEnum.PRODUCT_TARGETING.getCode(), AmazonAdOperationLogChangeTypeEnum.PRODUCT_BID_AMOUNT.getCode(), targetPageIdList, lastDate);
        log.info("产品透视分析{}--受众投放视图列表页接口调用-获取竞价日志- 花费时间 {}", uuid, Instant.now().toEpochMilli() - t);

        List<AudienceTargetViewVo> targetViewVoList = new ArrayList<>();
        AudienceTargetViewVo targetViewVo;
        for (String targetId : targetPageIdList) {
            if (sdTargetMap.containsKey(targetId) && dataMap.containsKey(targetId)) {
                targetViewVo = new AudienceTargetViewVo();
                if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(targetId)) {
                    AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(targetId);
                    targetViewVo.setIsPricing(1);
                    if ("ENABLED".equals(advertiseStrategyStatus.getStatus())) {
                        targetViewVo.setPricingState(1);
                    } else {
                        targetViewVo.setPricingState(0);
                    }
                } else {
                    targetViewVo.setIsPricing(0);
                    targetViewVo.setPricingState(0);
                }
                AmazonMarketingStreamData data = dataMap.get(targetId);
                //填充投放和广告组信息
                this.fillSdAudienceTargetVo(targetViewVo, sdTargetMap.get(targetId), sdGroupMap);
                //竞价日志
                this.setCategoryTargetViewOperationLog(targetViewVo, amazonAdOperationLogMap);
                //标签
                if (adMarkupTagVoMap.containsKey(targetViewVo.getTargetId())) {
                    AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(targetViewVo.getTargetId());
                    if (CollectionUtils.isNotEmpty(adMarkupTagVo.getTagIds())) {
                        List<AdTag> adTags = new ArrayList<>();
                        for (Long tagId : adMarkupTagVo.getTagIds()) {
                            if (adTagMap.containsKey(tagId)) {
                                adTags.add(adTagMap.get(tagId));
                            }
                        }
                        targetViewVo.setAdTags(adTags);
                    }
                }
                if (adCampaignAllMap.containsKey(targetViewVo.getCampaignId())) {
                    AmazonAdCampaignAll campaign = adCampaignAllMap.get(targetViewVo.getCampaignId());
                    targetViewVo.setCampaignName(campaign.getName());
                    targetViewVo.setType(campaign.getType());
                    targetViewVo.setCampaignTargetingType(campaign.getTactic());
                    targetViewVo.setCostType(campaign.getCostType());
                    if (campaign.getBudget() != null) {
                        targetViewVo.setDailyBudget(campaign.getBudget().toString());
                    }
                    if (StringUtils.isNotBlank(campaign.getPortfolioId()) && adPortfolioMap.containsKey(campaign.getPortfolioId())) {
                        AmazonAdPortfolio amazonAdPortfolio = adPortfolioMap.get(campaign.getPortfolioId());
                        targetViewVo.setPortfolioId(campaign.getPortfolioId());
                        targetViewVo.setPortfolioName(amazonAdPortfolio.getName());
                        targetViewVo.setIsHidden(amazonAdPortfolio.getIsHidden());
                    }
                }
                //is数据
                if (MapUtils.isNotEmpty(targetFirstPlaceIsMap) && targetFirstPlaceIsMap.containsKey(targetId)) {
                    TargetFirstPlaceIsDto targetFirstPlaceIsDto = targetFirstPlaceIsMap.get(targetId);
                    targetViewVo.setTopImpressionShare(ReportParamUtil.getTopOfSearchImpressionShare(targetFirstPlaceIsDto.
                            getMaxTopIs(), targetFirstPlaceIsDto.getMinTopIs()));
                }
                //填充数据指标
                ViewServiceHelper.fillStreamDataIntoViewVo(targetViewVo, data, param.getShopSales());
                //填充占比数据
                ViewServiceHelper.filterAdMetricData(targetViewVo, adMetricDto);
                targetViewVoList.add(targetViewVo);
            }
        }
        return targetViewVoList;
    }

    private void fillSdAudienceTargetVo(AudienceTargetViewVo targetViewVo, AmazonSdAdTargeting amazonAdTargeting, Map<String, AmazonSdAdGroup> sdGroupMap) {
        targetViewVo.setId(amazonAdTargeting.getId());
        targetViewVo.setShopId(amazonAdTargeting.getShopId());
        targetViewVo.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
        targetViewVo.setState(amazonAdTargeting.getState());
        targetViewVo.setTargetType(amazonAdTargeting.getType());
        targetViewVo.setTargetText(amazonAdTargeting.getTargetText());
        SBTargetingAudienceTypeEnum targetType = SBTargetingAudienceTypeEnum.fromValue(amazonAdTargeting.getTargetType());
        targetViewVo.setTitle(targetType != null ? targetType.getDesc() : null);
        if (TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
            targetViewVo.setTitle(amazonAdTargeting.getTitle());
        }
        if (StringUtils.isNotBlank(amazonAdTargeting.getResolvedExpression())
                && (TargetTypeEnum.category.name().equalsIgnoreCase(amazonAdTargeting.getType())
                || SBTargetingAudienceTypeEnum.fromValue(amazonAdTargeting.getTargetType()) != null)) {
            JSONArray jsonArray = JSONArray.parseArray(amazonAdTargeting.getResolvedExpression());
            if (jsonArray != null && !jsonArray.isEmpty()) {
                this.fillBrandMessage(targetViewVo, jsonArray);
            }
        }
        //如果为数字ID,表明类目或品牌已经被amazon删除
        if (StringUtils.isNumeric(amazonAdTargeting.getTargetText())) {
            targetViewVo.setTargetText("此类目亚马逊已删除");
        }
        targetViewVo.setTargetId(amazonAdTargeting.getTargetId());
        targetViewVo.setServingStatus(amazonAdTargeting.getServingStatus());
        if (StringUtils.isNotBlank(amazonAdTargeting.getServingStatus())) {
            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(amazonAdTargeting.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
            if (byCode != null) {
                targetViewVo.setServingStatusName(byCode.getName());
                targetViewVo.setServingStatusDec(byCode.getDescription());
            } else {
                targetViewVo.setServingStatusName(amazonAdTargeting.getServingStatus());
                targetViewVo.setServingStatusDec(amazonAdTargeting.getServingStatus());
            }
        }
        targetViewVo.setAdGroupId(amazonAdTargeting.getAdGroupId());
        if (sdGroupMap.containsKey(targetViewVo.getAdGroupId())) {
            AmazonSdAdGroup amazonAdGroup = sdGroupMap.get(targetViewVo.getAdGroupId());
            targetViewVo.setAdGroupName(amazonAdGroup.getName());
            targetViewVo.setIsStateBidding(amazonAdGroup.getIsStateBidding());
            targetViewVo.setPricingStateBidding(amazonAdGroup.getPricingStateBidding());
            if (amazonAdTargeting.getBid() == null) {
                targetViewVo.setBid(String.valueOf(amazonAdGroup.getDefaultBid()));
            }
            targetViewVo.setCampaignId(amazonAdGroup.getCampaignId());
        }
        if (amazonAdTargeting.getBid() != null) {
            targetViewVo.setBid(String.valueOf(amazonAdTargeting.getBid()));
        }
        if (amazonAdTargeting.getSuggested() != null) {
            targetViewVo.setSuggestBid(DataFormatUtil.scale(amazonAdTargeting.getSuggested(), 2));
        }
        if (amazonAdTargeting.getRangeStart() != null) {
            targetViewVo.setRangeStart(DataFormatUtil.scale(amazonAdTargeting.getRangeStart(), 2));
        }
        if (amazonAdTargeting.getRangeEnd() != null) {
            targetViewVo.setRangeEnd(DataFormatUtil.scale(amazonAdTargeting.getRangeEnd(), 2));
        }
    }

    /**
     * 提取JSONArray值的方法，增加异常处理。
     *
     * @param jsonObject JSON对象
     * @return JSONArray对象，如果发生异常则返回null
     */
    private JSONArray getJsonValueAsArray(JSONObject jsonObject) {
        JSONArray result = new JSONArray();
        try {
            // 尝试获取"value"键对应的JSONArray
            result = jsonObject.getJSONArray("value");
        } catch (JSONException e) {
            //处理旧数据导致的解析异常的问题
            // 先尝试获取"value"键对应的原始字符串
            String valueStr = jsonObject.getString("value");

            // 解析字符串内容
            if (valueStr == null || valueStr.trim().isEmpty()) {
                return result;
            }
            String[] pairs = valueStr.split(" ");
            for (String pair : pairs) {
                String[] keyValue = pair.split("[=><]");
                if (keyValue.length != 2) {
                    continue;
                }
                String key = keyValue[0];
                String val = keyValue[1].replaceAll("^\"|\"$", ""); // 移除值两边的双引号
                JSONObject item = new JSONObject();
                // 对price和rating两种特殊情况分类处理
                // 以及key为category转化为asinCategorySameAs key为brand转化为asinBrandSameAs key为prime-shipping-eligible转化为asinIsPrimeShippingEligible
                if ("rating".equals(key)) {
                    if (val.contains("-")) {
                        item.put("value", val);
                        item.put("type", "asinReviewRatingBetween");
                    } else if (pair.contains(">")) {
                        item.put("type", "asinReviewRatingGreaterThan");
                        item.put("value", val);
                    } else if (pair.contains("<")) {
                        item.put("type", "asinReviewRatingLessThan");
                        item.put("value", val);
                    }
                } else if ("price".equals(key)) {
                    if (val.contains("-")) {
                        item.put("type", "asinPriceBetween");
                        item.put("value", val);
                    } else if (pair.contains(">")) {
                        item.put("type", "asinPriceGreaterThan");
                        item.put("value", val);
                    } else if (pair.contains("<")) {
                        item.put("type", "asinPriceLessThan");
                        item.put("value", val);
                    }
                } else if ("category".equals(key)) {
                    item.put("type", "asinCategorySameAs");
                    item.put("value", val);
                } else if ("brand".equals(key)) {
                    item.put("type", "asinBrandSameAs");
                    item.put("value", val);
                } else if ("prime-shipping-eligible".equals(key)) {
                    item.put("type", "asinIsPrimeShippingEligible");
                    item.put("value", val);
                } else {
                    item.put("type", key);
                    item.put("value", val);
                }
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 填充回溯期
     */
    private void fillBrandMessage(AudienceTargetViewVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = this.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookback(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    /**
     * 商品列表页竞价日志
     *
     * @param vo
     * @param amazonAdOperationLogMap
     */
    private void setCategoryTargetViewOperationLog(AudienceTargetViewVo vo, Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap) {
        //竞价日志
        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PRODUCT_BID_AMOUNT.getCode(), vo.getTargetId());
        if (amazonAdOperationLogMap.containsKey(logKey)) {
            AmazonAdOperationLogBO logBO = amazonAdOperationLogMap.get(logKey);
            if (StringUtils.isNotBlank(logBO.getContent())) {
                com.meiyunji.sponsored.service.log.po.OperationContent operationContent = JSONUtil.jsonToObject(logBO.getContent(), com.meiyunji.sponsored.service.log.po.OperationContent.class);
                if (operationContent != null) {
                    DataLogVo bidLog = new DataLogVo();
                    bidLog.setCount(logBO.getCount());
                    bidLog.setPreviousValue(StringUtils.isNotBlank(operationContent.getPreviousValue()) ? operationContent.getPreviousValue() : "null");
                    bidLog.setNewValue(StringUtils.isNotBlank(operationContent.getNewValue()) ? operationContent.getNewValue() : "null");
                    bidLog.setSiteOperationTime(logBO.getSiteOperationTime());
                    vo.setBidLog(bidLog);
                }
            }
            vo.setIsUpdateBid(true);
        } else {
            vo.setIsUpdateBid(stringRedisService.get(logKey) != null);
        }
    }


    /**
     * 获取占比指标汇总数据
     */
    private AdMetricDto getSumAdMetricDto(List<AmazonMarketingStreamData> dataList) {
        AdMetricDto adMetricDto = new AdMetricDto();
        if (CollectionUtils.isNotEmpty(dataList)) {
            adMetricDto.setSumCost(BigDecimal.valueOf(dataList.stream().mapToDouble(AmazonMarketingStreamData::getCost).sum()));
            adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(dataList.stream().mapToInt(AmazonMarketingStreamData::getAttributedConversions7d).sum()));
            adMetricDto.setSumAdSale(BigDecimal.valueOf(dataList.stream().mapToDouble(e -> e.getAttributedSales7d().doubleValue()).sum()));
            adMetricDto.setSumOrderNum(BigDecimal.valueOf(dataList.stream().mapToInt(AmazonMarketingStreamData::getAttributedUnitsOrdered7d).sum()));
        }
        return adMetricDto;
    }

    /**
     * 根据feed数据列表的keywordId做竞价排序并分页
     */
    private Page<AmazonMarketingStreamData> getTargetViewPageByInternalMemory(AudienceTargetViewParam param, List<AmazonMarketingStreamData> keywordViewList,
                                                                              List<AmazonSdAdTargeting> sdTargetList) {
        Page<AmazonMarketingStreamData> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0);
        if (CollectionUtils.isEmpty(keywordViewList)) {
            return page;
        }
        List<AdTargetOrderBo> targetVoList = new ArrayList<>();
        //填充竞价，根据竞价分页
        if (KeywordViewParam.OrderFieldEnum.BID.getField().equals(param.getOrderField())) {
            // 竞价排序
            bidSort(param, keywordViewList, sdTargetList, targetVoList);
        } else if(KeywordViewParam.OrderFieldEnum.AD_OTHER_ORDER_NUM.getField().equals(param.getOrderField())){
            //其他广告产品订单量排序分页
            adOrderNumSort(param, keywordViewList, sdTargetList, targetVoList);
        }else if(KeywordViewParam.OrderFieldEnum.VCPM.getField().equals(param.getOrderField())){
            // vcpm排序
            vcpmSort(param, keywordViewList, sdTargetList, targetVoList);
        } else{
            // 默认排序
            defaultSort(keywordViewList, targetVoList);
        }
        if (OrderTypeEnum.desc.getType().equals(param.getOrderType())) {
            targetVoList.sort(Comparator.comparing(AdTargetOrderBo::getOrderValue).reversed());
        } else {
            targetVoList.sort(Comparator.comparing(AdTargetOrderBo::getOrderValue));
        }
        Page<AdTargetOrderBo> targetVoPage = PageUtil.getPage(new Page<>(param.getPageNo(), param.getPageSize()), targetVoList);
        page.setTotalPage(targetVoPage.getTotalPage());
        page.setTotalSize(targetVoPage.getTotalSize());
        if (CollectionUtils.isNotEmpty(targetVoPage.getRows())) {
            Map<String, AmazonMarketingStreamData> dataMap = keywordViewList.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getKeywordId, Function.identity()));
            page.setRows(targetVoPage.getRows().stream().map(e -> dataMap.get(e.getTargetId())).collect(Collectors.toList()));
        }
        return page;
    }

    /**
     * 默认排序
     */
    private static void defaultSort(List<AmazonMarketingStreamData> keywordViewList, List<AdTargetOrderBo> targetVoList) {
        for (AmazonMarketingStreamData data : keywordViewList) {
            AdTargetOrderBo bo = new AdTargetOrderBo();
            bo.setTargetId(data.getKeywordId());
            bo.setOrderValue(BigDecimal.ZERO);
            targetVoList.add(bo);
        }
    }

    /**
     * 其他广告产品订单量排序分页
     */
    private void adOrderNumSort(AudienceTargetViewParam param, List<AmazonMarketingStreamData> keywordViewList, List<AmazonSdAdTargeting> sdTargetList, List<AdTargetOrderBo> targetVoList) {
        Map<String, String> targetGroupIdMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId));
        viewManageHandler.fillFeedSdType(param.getPuid(), param.getShopIdList(), targetGroupIdMap, keywordViewList);
        for (AmazonMarketingStreamData data : keywordViewList) {
            AdTargetOrderBo bo = new AdTargetOrderBo();
            bo.setTargetId(data.getKeywordId());
            //sd的vcpm类型没有本产品广告订单量和其他广告产品订单量
            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(data.getSdType())) {
                bo.setOrderValue(BigDecimal.ZERO);
            } else {
                bo.setOrderValue(BigDecimal.valueOf(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku())));
            }
            targetVoList.add(bo);
        }
    }

    /**
     * 竞价排序
     */
    private void bidSort(AudienceTargetViewParam param, List<AmazonMarketingStreamData> keywordViewList, List<AmazonSdAdTargeting> sdTargetList, List<AdTargetOrderBo> targetVoList) {
        //获取广告组默认竞价
        Map<String, BigDecimal> sdGroupBidMap = new HashMap<>();
        List<String> sdGroupIdList = sdTargetList.stream().filter(e -> e.getBid() == null).map(AmazonSdAdTargeting::getAdGroupId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(sdGroupIdList)) {
            sdGroupBidMap = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(param.getPuid(), param.getShopIdList(), sdGroupIdList).stream()
                    .collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getDefaultBid));
        }

        Map<String, AmazonSdAdTargeting> sdTargetIdBidMap = sdTargetList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity()));
        for (AmazonMarketingStreamData data : keywordViewList) {
            AdTargetOrderBo bo = new AdTargetOrderBo();
            bo.setTargetId(data.getKeywordId());
            if (sdTargetIdBidMap.containsKey(data.getKeywordId())) {
                AmazonSdAdTargeting target = sdTargetIdBidMap.get(data.getKeywordId());
                if (target.getBid() != null) {
                    bo.setOrderValue(target.getBid());
                } else {
                    bo.setOrderValue(sdGroupBidMap.get(target.getAdGroupId()));
                }
            }
            targetVoList.add(bo);
        }
    }

    /**
     * vcpm排序
     */
    private void vcpmSort(AudienceTargetViewParam param, List<AmazonMarketingStreamData> keywordViewList, List<AmazonSdAdTargeting> sdTargetList, List<AdTargetOrderBo> targetVoList) {
        Map<String,String> targetIdGroupIdMap= StreamUtil.toMap(sdTargetList, AmazonSdAdTargeting::getTargetId, AmazonSdAdTargeting::getAdGroupId);
        List<AmazonSdAdGroup> groupIds = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(param.getPuid(), param.getShopIdList(), new ArrayList<>(targetIdGroupIdMap.values()));
        Map<String, String> groupIdCampainIdMap = StreamUtil.toMap(groupIds, AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getCampaignId);
        List<AmazonSdAdCampaignCostTypeBo> campaignBoList = amazonAdCampaignAllDao.listSdCostTypeBoByCampaignIds(param.getPuid(), param.getShopIdList(), new ArrayList<>(groupIdCampainIdMap.values()));
        Map<String, AmazonSdAdCampaignCostTypeBo> campaignMap = StreamUtil.toMap(campaignBoList, AmazonSdAdCampaignCostTypeBo::getCampaignId);
        for (AmazonMarketingStreamData data : keywordViewList) {
            String groupId = targetIdGroupIdMap.get(data.getKeywordId());
            String campaignId = groupIdCampainIdMap.get(groupId);
            AmazonSdAdCampaignCostTypeBo costTypeBo = campaignMap.get(campaignId);
            AdTargetOrderBo bo = new AdTargetOrderBo();
            bo.setTargetId(data.getKeywordId());
            int value = OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? Integer.MIN_VALUE : Integer.MAX_VALUE;
            if(Constants.SD_REPORT_VCPM.equalsIgnoreCase(costTypeBo.getCostType())){
                if(data.getViewImpressions() > 0){
                    String multiply = MathUtil.multiply(MathUtil.divide(String.valueOf(data.getCost()), String.valueOf(data.getViewImpressions()), MathUtil.DEF_DIV_DIFF), "1000");
                    bo.setOrderValue(new BigDecimal(multiply));
                }else{
                    bo.setOrderValue(BigDecimal.ZERO);
                }
            }else{
                bo.setOrderValue(new BigDecimal(value));
            }
            targetVoList.add(bo);
        }
    }

}
