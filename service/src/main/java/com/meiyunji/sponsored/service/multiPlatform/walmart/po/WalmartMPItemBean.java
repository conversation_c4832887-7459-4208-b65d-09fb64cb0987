package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class WalmartMPItemBean {

    @JsonProperty("MPItemFeedHeader")
    private WalmartMPItemFeedHeader header;

    @JsonProperty("MPItem")
    private List<WalmartMPItem> mpItem;

    public WalmartMPItemFeedHeader getHeader() {
        return header;
    }

    public void setHeader(WalmartMPItemFeedHeader header) {
        this.header = header;
    }

    public List<WalmartMPItem> getMpItem() {
        return mpItem;
    }

    public void setMpItem(List<WalmartMPItem> mpItem) {
        this.mpItem = mpItem;
    }
}
