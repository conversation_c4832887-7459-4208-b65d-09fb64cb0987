package com.meiyunji.sponsored.service.cpc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: ys
 * @date: 2023/11/2 13:54
 * @describe:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CampaignAndReportSearchDTO {
    @NotNull(message = "shopId is null")
    @ApiModelProperty(value = "shopId", required = true)
    //商店id
    private Integer shopId;
    //多店铺场景
    private List<Integer> shopIds;
    private String marketplaceId;
    //多店铺场景
    private List<String> marketplaceIds;
    //广告活动Id
    private String campaignId;
    //搜索字段名称，对应的查询字段为name
    private String searchField;
    //搜索内容--对应字段，对应的查询字段为name
    private String searchValue;
    //状态
    private String status;
    //用于仅展示正在投放字段 勾选后传值 enabled
    private String servingStatus;
    //广告组合id
    private String portfolioId;
    private String productType;
    private String productValue;
    private String state;
    //竞价策略类型 legacyForSales,autoForSales,manual->sp广告  none->sd sb广告
    private String strategyType;
    //广告类型 sp, sd, sb
    private String type;
    //预算状态 under->未超 exceeded->已超
    private String budgetState;
    //"付费方式
    private String costType;
    //"是否开启高级搜索
    private Boolean useAdvanced;
    //"高级搜索投放类型
    private String filterTargetType;
    //"高级搜索每日预算最小
    private BigDecimal dailyBudgetMin;
    //"高级搜索每日预算最大
    private BigDecimal dailyBudgetMax;
    //"高级搜索开始日期
    private String filterStartDate;
    //"高级搜索结束日期
    private String filterEndDate;

    @NotNull(message = "startDate is null")
    @ApiModelProperty(value = "startDate", required = true)
    //开始时间
    private String startDate;
    @NotNull(message = "endDate is null")
    //结束时间
    private String endDate;
    //广告组合下的活动id
    private List<String> campaignIdList;

    //高级搜索值
    //高级搜索展示量最小值
    private Integer impressionsMin;  //展示量
    //高级搜索展示量最大值
    private Integer impressionsMax;
    //高级搜索点击量小值
    private Integer clicksMin;  //点击量
    //高级搜索点击量大值
    private Integer clicksMax;
    //高级搜索点击率小值
    private BigDecimal clickRateMin;  //点击率
    //高级搜索点击率大值
    private BigDecimal clickRateMax;
    //高级搜索花费小值
    private BigDecimal costMin; //花费
    //高级搜索花费大值
    private BigDecimal costMax;
    //高级搜索cpc小值
    private BigDecimal cpcMin;  //cpc
    //高级搜索cpc大值
    private BigDecimal cpcMax;
    //高级搜索广告订单量小值
    private Integer orderNumMin;  //广告订单量
    //高级搜索广告订单量大值
    private Integer orderNumMax;
    //高级搜索sales小值
    private BigDecimal salesMin;  //sales
    //高级搜索sales大值
    private BigDecimal salesMax;
    //高级搜索acos小值
    private BigDecimal acosMin;  //acos
    //高级搜索acos大值
    private BigDecimal acosMax;
    //高级搜索roas小值
    private BigDecimal roasMin;   //roas
    //高级搜索roas大值
    private BigDecimal roasMax;
    //高级搜索广告订单转化率小值
    private BigDecimal salesConversionRateMin;  //订单转化率
    //高级搜索广告订单转化率大值
    private BigDecimal salesConversionRateMax;
    //高级搜索acots小值
    private BigDecimal acotsMin;  //acots
    //高级搜索acots大值
    private BigDecimal acotsMax;
    //高级搜索asots小值
    private BigDecimal asotsMin;  //acos
    //高级搜索asots大值
    private BigDecimal asotsMax;
    //高级搜索广告位顶部最小
    private BigDecimal placementTopMin;
    //高级搜索广告位顶部最大
    private BigDecimal placementTopMax;
    //高级搜索广告位产品页面最小
    private BigDecimal placementProductMin;
    //高级搜索广告位产品页面最大
    private BigDecimal placementProductMax;

    //可见展示次数最小值
    private Integer viewImpressionsMin;
    //可见展示次数最大值
    private Integer viewImpressionsMax;


    //cpa最小值
    private BigDecimal cpaMin;
    //cpa最大值
    private BigDecimal cpaMax;


    //vcpm最小值
    private BigDecimal vcpmMin;
    //vcpm最大值
    private BigDecimal vcpmMax;


    //本广告产品订单量最小值
    private Integer adSaleNumMin;
    //本广告产品订单量最大值
    private Integer adSaleNumMax;


    //其他产品广告订单量最小值
    private Integer adOtherOrderNumMin;
    //其他产品广告订单量最大值
    private Integer adOtherOrderNumMax;


    //本广告产品销售额最小值
    private BigDecimal adSalesMin;
    //本广告产品销售额最大值
    private BigDecimal adSalesMax;


    //其他产品广告销售额最小值
    private BigDecimal adOtherSalesMin;
    //其他产品广告销售额最大值
    private BigDecimal adOtherSalesMax;


    //本廣告产品销量最小值
    private Integer adSelfSaleNumMin;
    //本廣告产品销量最大值
    private Integer adSelfSaleNumMax;


    //其他产品广告销量最小值
    private Integer adOtherSaleNumMin;
    //其他产品广告销量最大值
    private Integer adOtherSaleNumMax;


    //“品牌新买家”订单量最小值
    private Integer ordersNewToBrandFTDMin;
    //“品牌新买家”订单量最大值
    private Integer ordersNewToBrandFTDMax;


    //“品牌新买家”订单百分比最小值
    private BigDecimal orderRateNewToBrandFTDMin;
    //“品牌新买家”订单百分比最大值
    private BigDecimal orderRateNewToBrandFTDMax;


    //“品牌新买家”销售额最小值
    private BigDecimal salesNewToBrandFTDMin;
    //“品牌新买家”销售额最大值
    private BigDecimal salesNewToBrandFTDMax;


    //“品牌新买家”销售额百分比最小值
    private BigDecimal salesRateNewToBrandFTDMin;
    //“品牌新买家”销售额百分比最大值
    private BigDecimal salesRateNewToBrandFTDMax;


    //“品牌新买家”销量最小值
    private Integer unitsOrderedNewToBrandFTDMin;
    //“品牌新买家”销量最大值
    private Integer unitsOrderedNewToBrandFTDMax;


    //“品牌新买家”销量百分比最小值
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    //“品牌新买家”销量百分比最大值
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    //广告销量最小值
    private Integer adSalesTotalMin;
    //广告销量最大值
    private Integer adSalesTotalMax;

    //高级搜索店铺销售额
    private BigDecimal shopSales;
    //高级搜索广告花费占比最小
    private BigDecimal adCostPercentageMin;
    //高级搜索广告花费占比最大
    private BigDecimal adCostPercentageMax;
    //高级搜索广告销售额占比最小
    private BigDecimal adSalePercentageMin;
    //高级搜索广告销售额占比最大
    private BigDecimal adSalePercentageMax;
    //高级搜索广告订单量占比最小
    private BigDecimal adOrderNumPercentageMin;
    //高级搜索广告订单量占比最大
    private BigDecimal adOrderNumPercentageMax;
    //高级搜索广告销量占比最小
    private BigDecimal orderNumPercentageMin;
    //高级搜索广告销量占比最大
    private BigDecimal orderNumPercentageMax;
}
