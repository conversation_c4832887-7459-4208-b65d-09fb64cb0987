package com.meiyunji.sponsored.service.wordFrequency.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.wordFrequency.AsinListResponseVo;
import com.meiyunji.sponsored.rpc.wordFrequency.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.wordFrequency.GetChartAggregateDataVo;
import com.meiyunji.sponsored.rpc.wordFrequency.GetChartDataResponse;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.helper.CpcPageWordRootHelper;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonWordRootQueryDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.ItemTypeEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.util.GrayUtil;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.qo.*;
import com.meiyunji.sponsored.service.wordFrequency.service.IWordRootSbService;
import com.meiyunji.sponsored.service.wordFrequency.service.IWordRootSpService;
import com.meiyunji.sponsored.service.wordFrequency.service.IWordRootService;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-08  09:26
 */
@Service
@Slf4j
public class WordRootServiceImpl implements IWordRootService {
    @Autowired
    private IWordRootSpService wordRootSpService;

    @Autowired
    private IWordRootSbService wordRootSbService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcShopDataService cpCShopDataService;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private RedisService redisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;

    @Autowired
    private IOdsAmazonWordRootQueryDao odsAmazonWordRootQueryDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;

    @Autowired
    private StringRedisService stringRedisService;

    @Override
    public Result<List<WordRootTopVo>> getQueryWordTopList(QueryWordTopQo qo) {
        //检查店铺是否存在
        this.checkShopId(qo.getShopId());
        // 参数赋值
        if(setParam(qo)){
            return ResultUtil.success();
        }

        //获取店铺销售额
        qo.setShopSales(getShopSales(qo.getShopId(), qo.getStart(), qo.getEnd()));

        Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
        boolean supportAbaRankOrder = GrayUtil.isHit(qo.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        if (dynamicRefreshConfiguration.verifyDorisPageByPuid(qo.getPuid(), whitePuidSet)) {
            //日期转换格式
            qo.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getStart(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
            qo.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getEnd(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
            //aba参数数据前置查询
            qo.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(qo, true));
            if (qo.isQueryJoinSearchTermsRank()) {
                String date = weekSearchTermsAnalysisService.getLatestDate(qo.getMarketplaceId());
                qo.setLastWeekSearchTermsRankDate(date);
            }
            if (Constants.SP.equals(qo.getType())) {
                return wordRootSpService.getDorisTopList(qo);
            } else {
                return wordRootSbService.getDorisTopList(qo);
            }
        } else {
            CpcPageWordRootHelper.clearUnsupportedOrderField(qo);
            if (Constants.SP.equals(qo.getType())) {
                return wordRootSpService.getQueryWordTopList(qo);
            } else {
                return wordRootSbService.getQueryWordTopList(qo);
            }
        }
    }

    private Boolean setParam(QueryWordTopQo qo) {
        //获取广告组合筛选条件
        if (StringUtils.isNotBlank(qo.getPortfolioId()) || StringUtils.isNotBlank(qo.getState()) || StringUtils.isNotBlank(qo.getServingStatus())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(qo.getPuid(), qo.getShopId(), qo.getPortfolioId(), qo.getState(), qo.getServingStatus(), qo.getType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                qo.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        //若只选择了标签asin，则直接返回空列表
        if (CollectionUtils.isNotEmpty(qo.getQueryWordTagTypeList())) {
            Set<String> targetTagTypeSet = new HashSet<>(Lists.newArrayList(CpcQueryWordDto.QueryWordTagTypeEnum.TARGET_ASIN.getValue(), CpcQueryWordDto.QueryWordTagTypeEnum.NE_TARGET_ASIN.getValue()));
            if (targetTagTypeSet.containsAll(qo.getQueryWordTagTypeList())) {
                return true;
            }
        }
        // 自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdQueryStrategyTypeEnum.operationTypeList(qo.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            String queryType = AmazonAd.AdQueryTypeEnum.SP_QUERY.getSearchField();
            if (Constants.SB.equals(qo.getType())) {
                queryType = AmazonAd.AdQueryTypeEnum.SB_QUERY.getSearchField();
            }
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(qo.getPuid(), CollectionUtil.newArrayList(qo.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, qo.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString(), null, queryType);
            qo.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(qo.getPuid(), CollectionUtil.newArrayList(qo.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, qo.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString(), null, null);
            qo.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !qo.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        return false;
    }

    @Override
    public Result<List<WordRootTopVo>> getAllQueryWordTopList(QueryWordTopQo qo) {
        Integer puid = qo.getPuid();
        if (CollectionUtils.isNotEmpty(qo.getPortfolioIds()) || CollectionUtils.isNotEmpty(qo.getCampaignIds())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdByPortfolioIdCampaignId(puid, qo.getMarketplaceId(), qo.getShopIdList(), qo.getPortfolioIds(), qo.getCampaignIds());
            if (CollectionUtils.isEmpty(campaignIds)) {
                return ResultUtil.success(Lists.newArrayList());
            }
            List<AmazonAdGroup> spGroups = amazonAdGroupDao.getGroupsByCampaignIdGroupId(puid, qo.getMarketplaceId(), qo.getShopIdList(), campaignIds, qo.getGroupIdList());
            List<AmazonSbAdGroup> sbGroups = amazonSbAdGroupDao.getGroupsByCampaignIdGroupId(puid, qo.getMarketplaceId(), qo.getShopIdList(), campaignIds, qo.getGroupIdList());
            List<String> groupIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(spGroups)) {
                groupIds.addAll(spGroups.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(sbGroups)) {
                groupIds.addAll(sbGroups.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(groupIds)) {
                return ResultUtil.success(Lists.newArrayList());
            }
            log.info("出单搜索词 获取词频 检验之后的组id: {}", groupIds);
            qo.setGroupIdList(groupIds.stream().distinct().collect(Collectors.toList()));
        }

        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        //aba参数数据前置查询
        qo.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(qo, true));
        if (qo.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(qo.getMarketplaceId());
            qo.setLastWeekSearchTermsRankDate(date);
        }
        return ResultUtil.success(odsAmazonWordRootQueryDao.getAllQueryWordTopList(qo));
    }

    @Override
    public Result<List<WordRootTopVo>> getKeywordTopList(KeywordTopQo qo) {
        // 参数赋值
        if (setParam(qo)) return ResultUtil.success();

        //日期转换格式
        qo.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        qo.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        boolean supportAbaRankOrder = GrayUtil.isHit(qo.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        qo.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(qo, true));
        if (qo.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(qo.getMarketplaceId());
            qo.setLastWeekSearchTermsRankDate(date);
        }
        if (Constants.SP.equals(qo.getType())) {
            return wordRootSpService.getDorisKeywordTopList(qo);
        } else {
            return wordRootSbService.getDorisKeywordTopList(qo);
        }
    }

    private Boolean setParam(KeywordTopQo qo) {
        //检查店铺是否存在
        ShopAuth shopAuth = this.checkShopId(qo.getShopId());
        qo.setMarketplaceId(shopAuth.getMarketplaceId());

        // 标签筛选
        if (CollectionUtils.isNotEmpty(qo.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(qo.getPuid(), qo.getShopId(), AdTagTypeEnum.TARGET.getType(), qo.getAdTagIdList(), qo.getType(), AdMarkupTargetTypeEnum.KEYWORD.getType());
            if (CollectionUtils.isNotEmpty(relationIds)) {
                qo.setKeywordIds(relationIds);
            } else {
                return true;
            }
        }

        // 自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdTargetStrategyTypeEnum.operationTypeList(qo.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            String targetType = AutoRuleTargetTypeEnum.keywordTarget.getTargetType();
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(qo.getPuid(), CollectionUtil.newArrayList(qo.getShopId()), AutoRuleItemTypeEnum.TARGET.getName(),
                    operationTypeList, qo.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET.toString(), targetType, null);
            qo.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(qo.getPuid(), CollectionUtil.newArrayList(qo.getShopId()), AutoRuleItemTypeEnum.TARGET.getName(),
                    operationTypeList, qo.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_TARGET_GROUP.toString(), null, null);
            qo.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !qo.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode()) &&
                    !qo.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.BID_PRICING.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        // 分时策略筛选关键词id集合
        if(CollectionUtil.isNotEmpty(qo.getAdStrategyTypeList()) &&(qo.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.BID_PRICING.getCode()) ||
                qo.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())) &&
                qo.getAdStrategyTypeList().size() != AdTargetStrategyTypeEnum.values().length){
            List<String> itemIdList = advertiseStrategyStatusDao.listAllItemId(qo.getPuid(), CollectionUtil.newArrayList(qo.getShopId()), ItemTypeEnum.TARGET.getItemType(),
                    AutoRuleTargetTypeEnum.keywordTarget.getTargetType(), qo.getType().toUpperCase());
            if(CollectionUtil.isNotEmpty(qo.getAutoRuleIds())){
                itemIdList.addAll(qo.getAutoRuleIds());
                itemIdList = itemIdList.stream().distinct().collect(Collectors.toList());
            }
            if(CollectionUtil.isNotEmpty(itemIdList)){
                qo.setAutoRuleIds(itemIdList);
            } else if (!qo.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Result<Page<GetWordRootDataVo>> getData(GetWordRootDataQo qo) {
        //检查店铺是否存在
        this.checkShopId(qo.getShopId());
        if (Constants.SP.equals(qo.getAdType())) {
            //获取asin和广告产品状态筛选的广告组id
            if (!this.getWordRootQoGroupIdList(qo)) {
                return ResultUtil.success(new Page<>(qo.getPageNo(), qo.getPageSize()));
            }
        }

        //获取广告组合筛选条件
        if (StringUtils.isNotBlank(qo.getStatus()) || StringUtils.isNotBlank(qo.getServingStatus()) || StringUtils.isNotBlank(qo.getCampaignIds()) || StringUtils.isNotBlank(qo.getPortfolioIds())) {
            List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(qo.getPuid(), qo.getShopId(), qo.getPortfolioIds(), qo.getCampaignIds(),
                    qo.getStatus(), qo.getServingStatus(), qo.getAdType());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return ResultUtil.success(new Page<>(qo.getPageNo(), qo.getPageSize()));
            } else {
                qo.setCampaignIdList(campaignIdList);
            }
        }

        //获取店铺销售额
        qo.setShopSales(getShopSales(qo.getShopId(), qo.getStartDate(), qo.getEndDate()));

        if (WordRoot.WordRootType.KEYWORD.getType().equals(qo.getWordRootType())) {
            this.transferDateFormat(qo);
            if (!this.verifyKeywordWordRootMatchTypes(qo.getMatchType())) {
                return ResultUtil.success(new Page<>(qo.getPageNo(), qo.getPageSize()));
            }
            if (Constants.SP.equals(qo.getAdType())) {
                return wordRootSpService.getDorisKeywordData(qo, false);
            } else {
                return wordRootSbService.getDorisKeywordData(qo, false);
            }
        } else {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(qo.getPuid(), whitePuidSet)) {
                //日期转换格式
                this.transferDateFormat(qo);
                if (Constants.SP.equals(qo.getAdType())) {
                    return wordRootSpService.getDorisData(qo, false);
                } else {
                    return wordRootSbService.getDorisData(qo, false);
                }
            } else {
                if (Constants.SP.equals(qo.getAdType())) {
                    return wordRootSpService.getData(qo);
                } else {
                    return wordRootSbService.getData(qo);
                }
            }
        }
    }

    @Override
    public Result<GetWordRootAggregateDataVo> getAggregateData(GetWordRootAggregateDataQo qo) {
        //检查店铺是否存在
        this.checkShopId(qo.getShopId());

        if (Constants.SP.equals(qo.getAdType())) {
            //获取asin和广告产品状态筛选的广告组id
            if (!this.getWordRootQoGroupIdList(qo)) {
                return ResultUtil.success(this.initAggregateDataVo());
            }
        }
        //获取广告组合筛选条件
        if (StringUtils.isNotBlank(qo.getStatus()) || StringUtils.isNotBlank(qo.getServingStatus()) || StringUtils.isNotBlank(qo.getCampaignIds()) || StringUtils.isNotBlank(qo.getPortfolioIds())) {
            List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(qo.getPuid(), qo.getShopId(), qo.getPortfolioIds(), qo.getCampaignIds(),
                    qo.getStatus(), qo.getServingStatus(), qo.getAdType());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return ResultUtil.success(this.initAggregateDataVo());
            } else {
                qo.setCampaignIdList(campaignIdList);
            }
        }

        //获取店铺销售额
        qo.setShopSales(getShopSales(qo.getShopId(), qo.getStartDate(), qo.getEndDate()));

        if (WordRoot.WordRootType.KEYWORD.getType().equals(qo.getWordRootType())) {
            this.transferDateFormat(qo);
            if (!this.verifyKeywordWordRootMatchTypes(qo.getMatchType())) {
                return ResultUtil.success(this.initAggregateDataVo());
            }
            if (Constants.SP.equals(qo.getAdType())) {
                return wordRootSpService.getDorisKeywordAggregateData(qo);
            } else {
                return wordRootSbService.getDorisKeywordAggregateData(qo);
            }
        } else {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(qo.getPuid(), whitePuidSet)) {
                //日期转换格式
                this.transferDateFormat(qo);
                if (Constants.SP.equals(qo.getAdType())) {
                    return wordRootSpService.getDorisAggregateData(qo);
                } else {
                    return wordRootSbService.getDorisAggregateData(qo);
                }
            } else {
                if (Constants.SP.equals(qo.getAdType())) {
                    return wordRootSpService.getAggregateData(qo);
                } else {
                    return wordRootSbService.getAggregateData(qo);
                }
            }
        }
    }

    @Override
    public Result<GetChartDataResponse.CharData> getChartData(int top, GetWordRootDataQo qo) {
        //检查店铺是否存在
        this.checkShopId(qo.getShopId());
        //获取币种
        String currency = AmznEndpoint.getByMarketplaceId(qo.getMarketplaceId()).getCurrencyCode().value();

        if (Constants.SP.equals(qo.getAdType())) {
            //获取asin和广告产品状态筛选的广告组id
            if (!this.getWordRootQoGroupIdList(qo)) {
                //返回空图表
                return ResultUtil.success(this.getAdHomeChartRpcVoList(BigDecimal.ZERO, currency, new ArrayList<>()));
            }
        }
        //获取广告组合筛选条件
        if (StringUtils.isNotBlank(qo.getStatus()) || StringUtils.isNotBlank(qo.getServingStatus()) || StringUtils.isNotBlank(qo.getCampaignIds()) || StringUtils.isNotBlank(qo.getPortfolioIds())) {
            List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(qo.getPuid(), qo.getShopId(), qo.getPortfolioIds(), qo.getCampaignIds(),
                    qo.getStatus(), qo.getServingStatus(), qo.getAdType());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                //返回空图表
                return ResultUtil.success(this.getAdHomeChartRpcVoList(BigDecimal.ZERO, currency, new ArrayList<>()));
            } else {
                qo.setCampaignIdList(campaignIdList);
            }
        }

        //获取店铺销售额
        qo.setShopSales(getShopSales(qo.getShopId(), qo.getStartDate(), qo.getEndDate()));

        //获取词根列表页数据
        qo.setPageNo(1);
        qo.setPageSize(top);

        List<GetWordRootDataVo> dataVoList;
        if (WordRoot.WordRootType.KEYWORD.getType().equals(qo.getWordRootType())) {
            this.transferDateFormat(qo);
            if (!this.verifyKeywordWordRootMatchTypes(qo.getMatchType())) {
                //返回空图表
                return ResultUtil.success(this.getAdHomeChartRpcVoList(BigDecimal.ZERO, currency, new ArrayList<>()));
            }
            if (Constants.SP.equals(qo.getAdType())) {
                dataVoList = wordRootSpService.getDorisKeywordChartData(qo);
            } else {
                dataVoList = wordRootSbService.getDorisKeywordChartData(qo);
            }
        } else {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(qo.getPuid(), whitePuidSet)) {
                //日期转换格式
                this.transferDateFormat(qo);
                if (Constants.SP.equals(qo.getAdType())) {
                    dataVoList = wordRootSpService.getDorisChartData(qo);
                } else {
                    dataVoList = wordRootSbService.getDorisChartData(qo);
                }
            } else {
                if (Constants.SP.equals(qo.getAdType())) {
                    dataVoList = wordRootSpService.getChartData(qo);
                } else {
                    dataVoList = wordRootSbService.getChartData(qo);
                }
            }
        }

        //列表页转换为图标数据结构
        GetChartDataResponse.CharData charData = this.getAdHomeChartRpcVoList(qo.getShopSales(), currency, dataVoList);

        return ResultUtil.success(charData);
    }

    @Async
    @Override
    public void getExportData(String uuid, GetWordRootDataQo qo) {
        //检查店铺是否存在
        this.checkShopId(qo.getShopId());

        if (Constants.SP.equals(qo.getAdType())) {
            //获取asin和广告产品状态筛选的广告组id
            if (!this.getWordRootQoGroupIdList(qo)) {
                stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
                return;
            }
        }
        //获取广告组合筛选条件
        if (StringUtils.isNotBlank(qo.getStatus()) || StringUtils.isNotBlank(qo.getServingStatus()) || StringUtils.isNotBlank(qo.getCampaignIds()) || StringUtils.isNotBlank(qo.getPortfolioIds())) {
            List<String> campaignIdList = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(qo.getPuid(), qo.getShopId(), qo.getPortfolioIds(), qo.getCampaignIds(),
                    qo.getStatus(), qo.getServingStatus(), qo.getAdType());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
                return;
            } else {
                qo.setCampaignIdList(campaignIdList);
            }
        }

        //获取店铺销售额
        qo.setShopSales(getShopSales(qo.getShopId(), qo.getStartDate(), qo.getEndDate()));

        if (WordRoot.WordRootType.KEYWORD.getType().equals(qo.getWordRootType())) {
            this.transferDateFormat(qo);
            if (!this.verifyKeywordWordRootMatchTypes(qo.getMatchType())) {
                stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
                return;
            }
            if (Constants.SP.equals(qo.getAdType())) {
                wordRootSpService.getExportDorisKeywordData(uuid, qo);
            } else {
                wordRootSbService.getExportDorisKeywordData(uuid, qo);
            }
        } else {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(qo.getPuid(), whitePuidSet)) {
                //日期转换格式
                this.transferDateFormat(qo);
                if (Constants.SP.equals(qo.getAdType())) {
                    wordRootSpService.getExportDorisData(uuid, qo);
                } else {
                    wordRootSbService.getExportDorisData(uuid, qo);
                }
            } else {
                if (Constants.SP.equals(qo.getAdType())) {
                    wordRootSpService.getExportData(uuid, qo);
                } else {
                    wordRootSbService.getExportData(uuid, qo);
                }
            }
        }
    }

    @Override
    public AsinListResponseVo getAsinList(GetAsinListQo qo) {
        //查询广告组合
        if (StringUtils.isNotBlank(qo.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(qo.getPuid(), qo.getShopId(), qo.getPortfolioId(), Constants.SP, qo.getStatus(), qo.getServingStatus());
            if (CollectionUtils.isEmpty(campaignIds)) {
                return this.buildAsinListEmptyResponseVo(qo);
            }
            if (StringUtils.isNotBlank(qo.getCampaignId())) {
                campaignIds.retainAll(StringUtil.splitStr(qo.getCampaignId()));
                if (CollectionUtils.isEmpty(campaignIds)) {
                    return this.buildAsinListEmptyResponseVo(qo);
                }
            }
            qo.setCampaignId(StringUtil.joinString(campaignIds));
        }
        //查询广告产品运行状态下的所有adid
        if (StringUtils.isNotBlank(qo.getProductState())) {
            List<String> adIds = odsAmazonAdProductDao.getAdIdByState(qo.getPuid(), qo.getShopId(), qo.getCampaignId(), qo.getGroupId(), qo.getProductState());
            if (CollectionUtils.isEmpty(adIds)) {
                //page对象
                return this.buildAsinListEmptyResponseVo(qo);
            }
            qo.setAdIdList(adIds);
        }
        //查询asin列表
        Page<AsinListDto> page = odsAmazonAdProductDao.getAsinPage(qo.getPuid(), qo);
        //封装数据
        //获取店铺名称
        List<AsinListDto> rows = page.getRows();
        Map<Integer, String> shopIdNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rows)) {
            shopIdNameMap = shopAuthDao.getShopAuthBoByIds(qo.getPuid(), rows.stream().map(AsinListDto::getShopId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(ShopAuthBo::getId, ShopAuthBo::getName));
        }

        //asin集合
        List<AsinListResponseVo.AsinListVo> asinList = new ArrayList<>();
        AsinListResponseVo.AsinListVo.Builder asinBuilder = null;
        for (AsinListDto asinListDto : page.getRows()) {
            asinBuilder = AsinListResponseVo.AsinListVo.newBuilder();
            asinBuilder.setAsin(asinListDto.getAsin());
            asinBuilder.setParentAsin(Optional.ofNullable(asinListDto.getParentAsin()).orElse(""));
            asinBuilder.setMsku(asinListDto.getMsku());
            asinBuilder.setShopId(asinListDto.getShopId());
            asinBuilder.setShopName(shopIdNameMap.getOrDefault(asinListDto.getShopId(), ""));
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(asinListDto.getMarketplaceId());
            asinBuilder.setMarketplaceCN(Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""));
            asinList.add(asinBuilder.build());
        }
        //page对象
        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(asinList);
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        return builder.build();
    }

    private AsinListResponseVo buildAsinListEmptyResponseVo(GetAsinListQo qo) {
        //page对象
        AsinListResponseVo.Builder builder = AsinListResponseVo.newBuilder();
        builder.addAllRows(new ArrayList<>());
        builder.setPageNo(qo.getPageNo());
        builder.setPageSize(qo.getPageSize());
        builder.setTotalPage(0);
        builder.setTotalSize(0);
        return builder.build();
    }

    /**
     * 获取asin和广告产品状态筛选的广告组id
     * @param qo
     * @return
     */
    private boolean getWordRootQoGroupIdList(GetWordRootAggregateDataQo qo) {
        //广告产品服务状态和asin
        if (StringUtils.isNotBlank(qo.getProductState()) || (StringUtils.isNotBlank(qo.getQueryType()) && StringUtils.isNotBlank(qo.getQueryValue()))) {
            // 先修改为空
            String asin = null;
            String sku = null;
            if (StringUtils.isNotBlank(qo.getQueryType()) && StringUtils.isNotBlank(qo.getQueryValue())) {
                if (AsinListReqVo.SearchTypeEnum.PARENT_ASIN.getType().equalsIgnoreCase(qo.getQueryType())) {
                    List<String> parentAsin = odsProductDao.listByParentAsin(qo.getPuid(), qo.getShopId(), StringUtil.splitStr(qo.getQueryValue(), StringUtil.SPECIAL_COMMA));
                    if (CollectionUtils.isEmpty(parentAsin)) {
                        return false;
                    }
                    asin = String.join(StringUtil.SPECIAL_COMMA, parentAsin);
                }
                if (AsinListReqVo.SearchTypeEnum.ASIN.getType().equalsIgnoreCase(qo.getQueryType())) {
                    asin = qo.getQueryValue();
                }
                if (AsinListReqVo.SearchTypeEnum.MSKU.getType().equalsIgnoreCase(qo.getQueryType())) {
                    sku = qo.getQueryValue();
                }
            }
            List<String> groupIds = odsAmazonAdProductDao.getGroupIdByState(qo.getPuid(), qo.getShopId(), qo.getCampaignIds(), qo.getGroupIds(), qo.getProductState(), asin, sku);
            if (CollectionUtils.isEmpty(groupIds)) {
                return false;
            }
            qo.setGroupIdList(groupIds);
        }
        return true;
    }

    /**
     * 获取店铺销售额
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    private BigDecimal getShopSales(Integer shopId, String startDate, String endDate) {
        BigDecimal shopSales = cpCShopDataService.getShopSalesByDate(shopId, startDate, endDate);
        return shopSales != null ? shopSales : BigDecimal.ZERO;
    }

    /**
     * 检测shopid是否存在
     * @param shopId
     */
    private ShopAuth checkShopId(Integer shopId) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        return shopAuth;
    }

    /**
     * 列表页转换为图标数据结构
     * @param currency
     * @param rows
     * @return
     */
    private GetChartDataResponse.CharData getAdHomeChartRpcVoList(BigDecimal shopSales, String currency, List<GetWordRootDataVo> rows) {
        GetChartDataResponse.CharData.Builder builder = GetChartDataResponse.CharData.newBuilder();
        List<AdHomeChartRpcVo> adHomeChartVos = new ArrayList<>();
        int size = CollectionUtils.isEmpty(rows) ? 0 : rows.size();

        //封装数据
        List<AdHomeChartRpcVo.ChartRpcRecord> frequencyList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> ctrList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> clicksList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> impressionsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cvrList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSaleList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adCostPerClickList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adCostList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOrderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acosList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> roasList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> acotsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> asotsList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> cpaList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSaleNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherOrderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSalesList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherSalesList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> orderNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adSelfSaleNumList = Lists.newCopyOnWriteArrayList();
        List<AdHomeChartRpcVo.ChartRpcRecord> adOtherSaleNumList = Lists.newCopyOnWriteArrayList();

        Integer frequencySum = 0;
        Integer clicksSum = 0;
        Long impressionSum = 0L;
        Integer adOrderNumSum = 0;
        Integer adSaleNumSum = 0;
        Integer adOtherOrderNumSum = 0;
        Integer orderNumSum = 0;
        Integer adSelfSaleNumSum = 0;
        Integer adOtherSaleNumSum = 0;
        BigDecimal adCostSum = BigDecimal.ZERO;
        BigDecimal adSaleSum = BigDecimal.ZERO;
        BigDecimal adSalesSum = BigDecimal.ZERO;

        for (GetWordRootDataVo vo : rows) {
            frequencySum = vo.getFrequency() + frequencySum;
            clicksSum = vo.getClicks() + clicksSum;
            impressionSum = vo.getImpressions() + impressionSum;
            adOrderNumSum = vo.getAdOrderNum() + adOrderNumSum;
            adSaleNumSum = vo.getAdSaleNum() + adSaleNumSum;
            adOtherOrderNumSum = vo.getAdOtherOrderNum() + adOtherOrderNumSum;
            orderNumSum = vo.getOrderNum() + orderNumSum;
            adSelfSaleNumSum = vo.getAdSelfSaleNum() + adSelfSaleNumSum;
            adOtherSaleNumSum = vo.getAdOtherSaleNum() + adOtherSaleNumSum;
            adCostSum = adCostSum.add(new BigDecimal(vo.getAdCost()));
            adSaleSum = adSaleSum.add(new BigDecimal(vo.getAdSale()));
            adSalesSum = adSalesSum.add(new BigDecimal(vo.getAdSales()));

            AdHomeChartRpcVo.ChartRpcRecord frequency = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getFrequency())).build();
            frequencyList.add(frequency);
            AdHomeChartRpcVo.ChartRpcRecord ctr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getCtr())).build();
            ctrList.add(ctr);
            AdHomeChartRpcVo.ChartRpcRecord clicks = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getClicks())).build();
            clicksList.add(clicks);
            AdHomeChartRpcVo.ChartRpcRecord impression = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getImpressions())).build();
            impressionsList.add(impression);
            AdHomeChartRpcVo.ChartRpcRecord cvr = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getCvr())).build();
            cvrList.add(cvr);
            AdHomeChartRpcVo.ChartRpcRecord adSale = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdSale())).build();
            adSaleList.add(adSale);
            AdHomeChartRpcVo.ChartRpcRecord adCostPerClick = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdCostPerClick())).build();
            adCostPerClickList.add(adCostPerClick);
            AdHomeChartRpcVo.ChartRpcRecord adCost = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdCost())).build();
            adCostList.add(adCost);
            AdHomeChartRpcVo.ChartRpcRecord acos = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAcos())).build();
            acosList.add(acos);
            AdHomeChartRpcVo.ChartRpcRecord roas = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getRoas())).build();
            roasList.add(roas);
            AdHomeChartRpcVo.ChartRpcRecord acots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAcots())).build();
            acotsList.add(acots);
            AdHomeChartRpcVo.ChartRpcRecord asots = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAsots())).build();
            asotsList.add(asots);
            AdHomeChartRpcVo.ChartRpcRecord adOrderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdOrderNum())).build();
            adOrderNumList.add(adOrderNum);
            AdHomeChartRpcVo.ChartRpcRecord cpa = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getCpa())).build();
            cpaList.add(cpa);
            AdHomeChartRpcVo.ChartRpcRecord adSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdSaleNum())).build();
            adSaleNumList.add(adSaleNum);
            AdHomeChartRpcVo.ChartRpcRecord adOtherOrderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdOtherOrderNum())).build();
            adOtherOrderNumList.add(adOtherOrderNum);
            AdHomeChartRpcVo.ChartRpcRecord adSales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdSales())).build();
            adSalesList.add(adSales);
            AdHomeChartRpcVo.ChartRpcRecord adOtherSales = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdOtherSales())).build();
            adOtherSalesList.add(adOtherSales);
            AdHomeChartRpcVo.ChartRpcRecord orderNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getOrderNum())).build();
            orderNumList.add(orderNum);
            AdHomeChartRpcVo.ChartRpcRecord adSelfSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdSelfSaleNum())).build();
            adSelfSaleNumList.add(adSelfSaleNum);
            AdHomeChartRpcVo.ChartRpcRecord adOtherSaleNum = AdHomeChartRpcVo.ChartRpcRecord.newBuilder().setWordRoot(vo.getWordRoot()).setValue(String.valueOf(vo.getAdOtherSaleNum())).build();
            adOtherSaleNumList.add(adOtherSaleNum);
        }

        GetChartAggregateDataVo.Builder aggregateDataVo = GetChartAggregateDataVo.newBuilder();
        aggregateDataVo.setFrequency(frequencySum);
        aggregateDataVo.setAdCost(adCostSum.setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setClicks(clicksSum);
        aggregateDataVo.setImpressions(impressionSum);
        aggregateDataVo.setCpa((BigDecimal.valueOf(adOrderNumSum).compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.divide(adCostSum, new BigDecimal(adOrderNumSum)).setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAdCostPerClick((BigDecimal.valueOf(clicksSum).compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.divide(adCostSum, BigDecimal.valueOf(clicksSum)).setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setCtr((impressionSum == 0) ? "0.00" : DoubleUtil.divide(Double.valueOf(clicksSum) * 100, Double.valueOf(impressionSum), 2).toString());
        aggregateDataVo.setCvr((clicksSum == 0) ? "0.00" : DoubleUtil.divide(Double.valueOf(adOrderNumSum) * 100, Double.valueOf(clicksSum), 2).toString());
        aggregateDataVo.setAcos((adSaleSum.compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.multiply(MathUtil.divide(adCostSum, adSaleSum), BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setRoas(adCostSum.compareTo(BigDecimal.ZERO) == 0 ? "0" : adSaleSum.divide(adCostSum, 2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAcots((shopSales.compareTo(BigDecimal.ZERO) == 0) ? "0" : adCostSum.multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAsots((shopSales.compareTo(BigDecimal.ZERO) == 0) ? "0" : adSaleSum.multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAdSale(adSaleSum.setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAdSales(adSalesSum.setScale(2, RoundingMode.HALF_UP).toString());
        aggregateDataVo.setAdOtherSales(MathUtil.subtract(adSaleSum, adSalesSum).toString());
        aggregateDataVo.setAdOrderNum(adOrderNumSum);
        aggregateDataVo.setAdSaleNum(adSaleNumSum);
        aggregateDataVo.setAdOtherOrderNum(adOtherOrderNumSum);
        aggregateDataVo.setOrderNum(orderNumSum);
        aggregateDataVo.setAdSelfSaleNum(adSelfSaleNumSum);
        aggregateDataVo.setAdOtherSaleNum(adOtherSaleNumSum);

        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("词频").setName("frequency").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(frequencyList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告点击率").setName("ctr").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(ctrList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告点击量").setName("clicks").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(clicksList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告曝光量").setName("impressions").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(impressionsList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告转化率").setName("cvr").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(cvrList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告销售额").setName("adSale").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adSaleList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("CPC").setName("adCostPerClick").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adCostPerClickList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告花费").setName("adCost").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adCostList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告订单量").setName("adOrderNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adOrderNumList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("ACoS").setName("acos").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(acosList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("ROAS").setName("roas").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(roasList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("ACoTS").setName("acots").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(acotsList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("ASoTS").setName("asots").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(asotsList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("CPA").setName("cpa").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(cpaList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("本广告产品订单量").setName("adSaleNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adSaleNumList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("其他产品广告订单量").setName("adOtherOrderNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adOtherOrderNumList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("本广告产品销售额").setName("adSales").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adSalesList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("其他产品广告销售额").setName("adOtherSales").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adOtherSalesList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("广告销量").setName("orderNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(orderNumList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("本广告产品销量").setName("adSelfSaleNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adSelfSaleNumList).build());
        adHomeChartVos.add(AdHomeChartRpcVo.newBuilder().setDescription("其他产品广告销量").setName("adOtherSaleNum").setTotal(Int32Value.of(size)).setCurrency(currency).addAllRecords(adOtherSaleNumList).build());

        builder.addAllChartVo(adHomeChartVos);
        builder.setAggregateDataVo(aggregateDataVo);
        return builder.build();
    }

    /**
     * 转换成doris的日期格式
     * @param qo
     * @return
     */
    private void transferDateFormat(GetWordRootAggregateDataQo qo) {
        qo.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        qo.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(qo.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private GetWordRootAggregateDataVo initAggregateDataVo() {
        GetWordRootAggregateDataVo vo = new GetWordRootAggregateDataVo();
        vo.setFrequency(0);
        vo.setFrequencyPercentage("0");
        vo.setTargetingNum(0);
        vo.setNeTargetingNum(0);
        vo.setAdCost("0");
        vo.setAdCostPercentage("0");
        vo.setImpressions(0L);
        vo.setClicks(0);
        vo.setCpa("0");
        vo.setAdCostPerClick("0");
        vo.setCtr("0");
        vo.setCvr("0");
        vo.setAcos("0");
        vo.setRoas("0");
        vo.setAcots("0");
        vo.setAsots("0");
        vo.setAdOrderNum(0);
        vo.setAdOrderNumPercentage("0");
        vo.setAdSaleNum(0);
        vo.setAdOtherOrderNum(0);
        vo.setAdSale("0");
        vo.setAdSalePercentage("0");
        vo.setAdSales("0");
        vo.setAdOtherSales("0");
        vo.setOrderNum(0);
        vo.setOrderNumPercentage("0");
        vo.setAdSelfSaleNum(0);
        vo.setAdOtherSaleNum(0);
        return vo;
    }

    private boolean verifyKeywordWordRootMatchTypes(String matchTypes) {
        if (StringUtils.isBlank(matchTypes)) {
            return true;
        }
        List<String> matchTypeList = StringUtil.stringToList(matchTypes, StringUtil.SPLIT_COMMA);
        if (!matchTypeList.contains(TargetMatchValueEnum.exact.getMatchType())
                && !matchTypeList.contains(TargetMatchValueEnum.broad.getMatchType())
                && !matchTypeList.contains(TargetMatchValueEnum.phrase.getMatchType())) {
            return false;
        }
        return true;
    }
}
