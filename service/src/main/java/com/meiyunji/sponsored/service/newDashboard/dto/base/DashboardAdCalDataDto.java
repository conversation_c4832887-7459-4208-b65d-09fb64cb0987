package com.meiyunji.sponsored.service.newDashboard.dto.base;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-03-26  14:30
 */

@Data
public class DashboardAdCalDataDto extends DashboardAdBaseDataDto {

    //计算指标，以下数据没有 * 100
    private BigDecimal acos;

    @ExcelProperty("ACoS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAcos;

    @ExcelProperty("ROAS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayRoas;

    @ExcelProperty("ROAS")
    private BigDecimal roas;

    private BigDecimal clickRate;

    @ExcelProperty("广告点击率")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayClickRate;

    private BigDecimal conversionRate;

    @ExcelProperty("广告转化率")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayConversionRate;

    private BigDecimal cpc;

    @ExcelProperty("CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayCpc;

    private BigDecimal cpa;

    @ExcelProperty("CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayCpa;

    private BigDecimal acots;

    @ExcelProperty("ACoTS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAcots;

    private BigDecimal asots;

    @ExcelProperty("ASoTS")
    @ExportFormat(value = ExportFormatType.RATE)
    private String displayAsots;

    private BigDecimal advertisingUnitPrice;

    @ExcelProperty("广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displayAdvertisingUnitPrice;

    private BigDecimal spc;

    @ExcelProperty("SPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String displaySpc;

}
