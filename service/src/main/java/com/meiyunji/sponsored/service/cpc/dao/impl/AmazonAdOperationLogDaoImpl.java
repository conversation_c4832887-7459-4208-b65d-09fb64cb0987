package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdOperationLogDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdOperationLog;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-07-07  10:39
 */
@Repository
public class AmazonAdOperationLogDaoImpl extends BaseShardingDaoImpl<AmazonAdOperationLog> implements IAmazonAdOperationLogDao {

    @Override
    public Integer batchInsert(Integer puid, List<AmazonAdOperationLog> amazonAdOperationLogList) {
        StringBuilder sql = new StringBuilder("INSERT INTO " + getJdbcHelper().getTable() +
                "(`puid`, `shop_id`, `type`, `entity_type`, `change_type`, `identify_id`, `marketplace_id`, `content`, `platform_create_time`, `site_operation_time`, `create_time`, `update_time`) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdOperationLog amazonAdOperationLog : amazonAdOperationLogList) {
            sql.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now()),");
            argsList.add(amazonAdOperationLog.getPuid());
            argsList.add(amazonAdOperationLog.getShopId());
            argsList.add(amazonAdOperationLog.getType());
            argsList.add(amazonAdOperationLog.getEntityType());
            argsList.add(amazonAdOperationLog.getChangeType());
            argsList.add(amazonAdOperationLog.getIdentifyId());
            argsList.add(amazonAdOperationLog.getMarketplaceId());
            argsList.add(amazonAdOperationLog.getContent());
            argsList.add(amazonAdOperationLog.getPlatformCreateTime());
            argsList.add(amazonAdOperationLog.getSiteOperationTime());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE `puid` = values(puid), `shop_id` = values(shop_id), `type` = values(`type`)," +
                " `entity_type` = values(entity_type), `change_type` = values(change_type), `identify_id` = values(identify_id)," +
                " `marketplace_id` = values(marketplace_id), `content` = values(content), `platform_create_time` = values(platform_create_time)," +
                " `site_operation_time` = values(site_operation_time), `update_time` = values(update_time) ");
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdOperationLogBO> listByOneDayAll(Integer puid, Integer shopId, Integer entityType, Integer changeType, String beginDate, String endDate) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", Constants.SP)
                .equalTo("entity_type", entityType)
                .equalTo("change_type", changeType)
                .greaterThanOrEqualTo("site_operation_time", beginDate)
                .lessThan("site_operation_time", endDate)
                .build();
        String sql = "select `type`, `entity_type`, `change_type`, `identify_id`, `content`, `platform_create_time`, `site_operation_time`" +
                " from " + getJdbcHelper().getTable() +
                " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdOperationLogBO.class));
    }

    @Override
    public List<AmazonAdOperationLogBO> listByTypeAndIdentifyIds(Integer puid, Integer shopId, String type, Integer entityType,  List<Integer> changeType, List<String> identifyIds, Date startDate) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", type)
                .equalTo("entity_type", entityType)
                .inIntList("change_type", changeType.toArray(new Integer[0]))
                .in("identify_id", identifyIds.toArray())
                .greaterThan("platform_create_time", startDate)
                .orderBy("platform_create_time")
                .build();
        String sql = "select `type`, `entity_type`, `change_type`, `identify_id`, `content`, `platform_create_time`, `site_operation_time`" +
                " from " + getJdbcHelper().getTable() +
                " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdOperationLogBO.class));
    }

    @Override
    public List<AmazonAdOperationLogBO> listByTypeAndIdentifyIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String type, Integer entityType, List<Integer> changeType, List<String> identifyIds, Date startDate) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .equalTo("type", type)
                .equalTo("entity_type", entityType)
                .inIntList("change_type", changeType.toArray(new Integer[0]))
                .in("identify_id", identifyIds.toArray())
                .greaterThan("platform_create_time", startDate)
                .orderBy("platform_create_time")
                .build();
        String sql = "select `type`, `entity_type`, `change_type`, `identify_id`, `content`, `platform_create_time`, `site_operation_time`" +
                " from " + getJdbcHelper().getTable() +
                " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdOperationLogBO.class));
    }

    @Override
    public List<AmazonAdOperationLogBO> listByTypeAndIdentifyIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> typeList, Integer entityType, Integer changeType, List<String> identifyIds, Date startDate) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .in("type", typeList.toArray())
                .equalTo("entity_type", entityType)
                .equalTo("change_type", changeType)
                .in("identify_id", identifyIds.toArray())
                .greaterThan("platform_create_time", startDate)
                .orderBy("platform_create_time")
                .build();
        String sql = "select `type`, `entity_type`, `change_type`, `identify_id`, `content`, `platform_create_time`, `site_operation_time`" +
                " from " + getJdbcHelper().getTable() +
                " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdOperationLogBO.class));
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}
