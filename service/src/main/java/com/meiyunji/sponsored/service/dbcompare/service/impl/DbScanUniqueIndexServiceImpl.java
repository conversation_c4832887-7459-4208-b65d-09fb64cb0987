package com.meiyunji.sponsored.service.dbcompare.service.impl;

import com.meiyunji.sponsored.service.dbcompare.dto.IndexDto;
import com.meiyunji.sponsored.service.dbcompare.dto.ScanUniqueIndexDto;
import com.meiyunji.sponsored.service.dbcompare.service.IDbScanUniqueIndexService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-10-27  10:17
 */

@Service
@Slf4j
public class DbScanUniqueIndexServiceImpl implements IDbScanUniqueIndexService {

    @Value(value = "${spring.profiles.active}")
    private String active;

    @Override
    public List<String> scanUniqueIndex(List<ScanUniqueIndexDto> indexList, List<DataSource> dsList) {
        List<String> outputStrList = new ArrayList<>();
        for (ScanUniqueIndexDto indexDto : indexList) {
            outputStrList.add(String.format("表%s的索引[%s]扫描结果: ", indexDto.getTableName(), indexDto.getIndexColumnName()));
            for (DataSource ds : dsList) {
                try {
                    outputStrList.add(String.format("    数据源%s: ", getDbName(ds)));
                } catch (Exception e) {
                    log.error("获取数据源名称失败", e);
                }
                outputStrList.addAll(scanUniqueIndex4Table(ds, indexDto));
            }
        }
        return outputStrList;
    }

    /**
     * 从datasource中获取数据库实例名称如：ysql-dev.meiyunji.net:3306/myj_amzup_sponsored
     *
     * @param ds
     * @return
     */
    private String getDbName(DataSource ds) throws SQLException {
        String[] split = ds.getConnection().getMetaData().getURL().split("/");
        return split[2] + "/" + ds.getConnection().getCatalog();
    }


    /**
     * 扫描某一个库中的某个表(或多个表)是否存在某个索引
     * @param ds
     * @param indexDto
     * @return
     */
    private List<String> scanUniqueIndex4Table(DataSource ds, ScanUniqueIndexDto indexDto) {
        List<String> result = new ArrayList<>();
        if (1 == indexDto.getShard()) {
            //解析分表
            List<String> shardList = readShardTableSuffix();
            if (!CollectionUtils.isEmpty(shardList)) {
                shardList.forEach(x -> {
                    String info = scanUniqueIndex4SingleTable(ds, indexDto.getTableName() + x, indexDto.getIndexColumnName());
                    result.add(info);
                });
            }
        } else {
            //直接处理单表
            String info = scanUniqueIndex4SingleTable(ds, indexDto.getTableName(), indexDto.getIndexColumnName());
            result.add(info);
        }

        return result;
    }

    private List<String> readShardTableSuffix() {
        List<String> shardList = new ArrayList<>();
        //从resources下读取yml配置
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("table-sharding-" + active + ".yml");
        // 创建一个用于读取文本的BufferedReader对象
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        // 逐行读取配置
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                if (StringUtils.isNotBlank(line)) {
                    shardList.add(line.split(":")[0]);
                }
            }
        } catch (Exception e) {
            log.error("读取分表配置文件异常", e);
        } finally {
            // 关闭IO流
            try {
                reader.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return shardList;
    }

    /**
     * 扫描某一个库中的指定表是否存在某个索引
     * @param ds  数据源
     * @param tableName  表名
     * @param indexColumnName 索引列，逗号分隔
     * @return
     */
    private String scanUniqueIndex4SingleTable(DataSource ds, String tableName, String indexColumnName) {
        try {
            Connection connection = ds.getConnection();
            DatabaseMetaData databaseMetaData = connection.getMetaData();
            //数据库名称
            String catalog = connection.getCatalog();
            //元数据表集合
            ResultSet tableRS = databaseMetaData.getTables(catalog, null, tableName, null);
            List<IndexDto> indexDtoList = null;
            while (tableRS.next()) {
                //查询所有表索引，此处查询元数据需要注意大小写
                indexDtoList = getIndexDtoList(databaseMetaData, catalog, tableName);
                break;
            }

            if (CollectionUtils.isEmpty(indexDtoList)) {
                return String.format("        表%s查询索引失败, 请检查表名是否正确", tableName);
            }

            //遍历结果
            List<IndexDto> existList = indexDtoList.stream().filter(x -> indexColumnName.equals(x.getIndexFields())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(existList)) {
                return String.format("        表%s不存在[%s]的索引", tableName, indexColumnName);
            } else {
                StringBuilder builder = new StringBuilder(String.format("        表%s存在%s个[%s]的索引, ", tableName, existList.size(), indexColumnName));
                existList.forEach(x -> builder.append(String.format("索引[%s], 是否唯一索引: %s", x.getIndexName(), x.isUniqueIndex())));
                return builder.toString();
            }
        } catch (Exception e) {
            log.error(String.format("表%s查询索引异常", tableName), e);
            return String.format("        表%s查询索引失败, 请检查表名是否正确", tableName);
        }
    }


    /**
     * 查询指定的表索引信息
     * @param databaseMetaData
     * @param catalog
     * @param tableName
     * @return
     * @throws SQLException
     */
    private List<IndexDto> getIndexDtoList(DatabaseMetaData databaseMetaData, String catalog, String tableName) throws SQLException {
        //获取数据库下的指定表的索引元数据
        ResultSet indexRS = databaseMetaData.getIndexInfo(catalog, databaseMetaData.getUserName(), tableName, false, false);
        List<IndexDto> indexDtoList = new ArrayList<>();
        Map<String, List<String>> indexMap = new HashMap<>(16);
        Map<String, IndexDto> map = new HashMap<>(16);
        //解析封装
        while (indexRS.next()) {
            String indexName = indexRS.getString("INDEX_NAME");
            String columnName = indexRS.getString("COLUMN_NAME");
            boolean unique = !indexRS.getBoolean("NON_UNIQUE");
            short ordinalPosition = indexRS.getShort("ORDINAL_POSITION");
            if (!map.containsKey(indexName)) {
                IndexDto indexDto = new IndexDto();
                indexDto.setTableName(tableName);
                indexDto.setIndexName(indexName);
                indexDto.setUniqueIndex(unique);
                map.put(indexName, indexDto);
            }
            //暂存索引列
            List<String> indexList = indexMap.computeIfAbsent(indexName, k -> new ArrayList<>());
            indexList.add(ordinalPosition - 1, columnName);
        }

        //拼接索引列字符串用于比较
        map.forEach((indexName, index) -> {
            List<String> indexList = indexMap.get(indexName);
            String join = StringUtils.join(indexList, ",");
            index.setIndexFields(join.toLowerCase(Locale.ROOT));
            indexDtoList.add(index);
        });
        return indexDtoList;
    }
}
