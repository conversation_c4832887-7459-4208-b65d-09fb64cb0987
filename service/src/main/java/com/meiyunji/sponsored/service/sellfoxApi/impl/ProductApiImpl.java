package com.meiyunji.sponsored.service.sellfoxApi.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.cpc.qo.AmazonAdGroupPageQo;
import com.meiyunji.sponsored.service.enums.SellfoxApiEnum;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.util.OkHttpClientUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import com.meiyunji.sponsored.service.vo.PurchasedProductInfoVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.TreeMap;

/**
 * Product
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductApiImpl implements IProductApi {


    @Value("${services.amzup.prefix}")
    private String amzupPrefix;

    /**
     * 根据sku获取产品
     *
     * @param puid
     * @param shopId
     * @param sku
     * @return
     */
    @Override
    public ProductAdReportVo getProductBySku(int puid, Integer shopId, String sku) {

        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("sku",sku);
        log.info("======================开始请求根据sku获取产品接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getProductBySku,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<ProductAdReportVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<ProductAdReportVo>>() {
            });
            if (result!=null && result.getCode()==0) {
                return  result.getData();
            }

        } catch (IOException e) {
            log.info("======================请求根据sku获取产品接口出现异常======================", e);

        }
        log.info("======================结束请求根据sku获取产品接口结束======================");
        return null;
    }

    /**
     * 批量查询产品接口
     * @param puid
     * @param shopId
     * @param skus 逗号拼接
     * @return
     */
    @Override
    public List<ProductAdReportVo> getListProductBySku(int puid, Integer shopId, String skus) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("skus",skus);
        log.info("======================开始请求getListProductBySku接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getListProductBySku,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });
            if (result!=null && result.getCode()==0) {
                return  result.getData();
            }

        } catch (IOException e) {
            log.info("======================请求getListProductBySku接口出现异常======================", e);

        }
        log.info("======================结束请求getListProductBySku接口结束======================");
        return null;
    }

    @Override
    public List<ProductAdReportVo> getListProductBySkuList(Integer puid, Integer shopId, String skuList) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("skuList",skuList);
        log.info("======================开始请求getListProductBySkuList接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getListProductBySkuList,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });
            if (result!=null && result.getCode()==0) {
                return  result.getData();
            }

        } catch (IOException e) {
            log.info("======================请求getListProductBySkuList接口出现异常======================", e);

        }
        log.info("======================结束请求getListProductBySkuList接口结束======================");
        return null;
    }

    /**
     * 根据asin 和sku 获取商品
     *
     * @param puid
     * @param shopId
     * @param asin
     * @param sku
     * @return
     */
    @Override
    public ProductAdReportVo getProductByAsinAndSku(int puid, Integer shopId, String asin, String sku) {

        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("asin",asin);
        map.put("sku",sku);
        log.info("======================开始请求根据asin 和sku 获取商品接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getProductByAsinAndSku,map));
        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            //http状态码不为2xx直接返回null
            if (!response.isSuccessful()) {
                return null;
            }
            Result<ProductAdReportVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<ProductAdReportVo>>() {
            });
            //业务状态码不为0
            if (result == null || result.error()) {
                return null;
            }
            return result.getData();
        } catch (IOException e) {
            log.info("======================请求根据asin 和sku 获取商品接口出现异常======================", e);
        }
        log.info("======================结束请求根据asin 和sku 获取商品接口======================");
        return null;
    }

    @Override
    public List<ProductAdReportVo> getListProductByAsinAndSku(int puid, Integer shopId, String asin, String sku) {
        return null;
    }


    @Override
    public List<ProductAdReportVo> getListByParentId(int puid, Integer shopId, String marketplaceId, List<Integer> idList) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("ids", StringUtils.join(idList,","));
        log.info("======================开始请求根据getListByParentId接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getListByParentId,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });

            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求根据getListByParentId接口出现异常======================", e);

        }
        log.info("======================结束请求根据getListByParentId接口======================");
        return null;
    }



    @Override
    public String getMainImageBySku(int puid, Integer shopId, String marketplaceId, String sku) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("sku",sku);
        log.info("======================开始请求根据sku获取图片接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getMainImageBySku,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<String> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, Result.class);
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求根据sku获取图片接口出现异常======================", e);

        }
        log.info("======================结束请求根据sku获取图片接口======================");
        return null;
    }



    @Override
    public List<ProductAdReportVo> getAsinBySkus(int puid, Integer shopId, String marketplaceId, List<String> skus) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("skus",StringUtils.join(skus,","));
        log.info("======================开始请求getAsinBySkus接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getAsinBySkus,map));
        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            //http状态码不为2xx直接返回null
            if (!response.isSuccessful()) {
                return null;
            }
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {});
            //业务状态码不为0
            if (result == null || result.error()) {
                return null;
            }

            return result.getData();
        } catch (IOException e) {
            log.info("======================请求getAsinBySkus接口出现异常======================", e);
        }
        log.info("======================结束请求getAsinBySkus接口======================");
        return null;
    }


    @Override
    public Page<ProductAdReportVo> getProductPageList(int puid, AmazonAdGroupPageQo qo, Page page) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",qo.getShopId());
        map.put("marketplaceId",qo.getMarketplaceId());
        map.put("type",qo.getType());
        map.put("searchField",qo.getSearchField());
        map.put("searchValue",qo.getSearchValue());
        map.put("pageNo",page.getPageNo());
        map.put("pageSize",page.getPageSize());
        log.info("======================开始请求接口sp产品投放接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getProductPageList,map));
        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<Page<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<Page<ProductAdReportVo>>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求接口sp产品投放接口出现异常======================", e);

        }
        log.info("======================结束请求接口sp产品投放接口======================");
        return null;
    }


    @Override
    public ProductAdReportVo getByAsinAndSku(int puid, Integer shopId, String asin, String msku) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("asin",asin);
        map.put("msku",msku);
        log.info("======================开始请求通过asin和sku查询product接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getByAsinAndSku,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<ProductAdReportVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<ProductAdReportVo>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求通过asin和sku查询product接口出现异常======================", e);

        }
        log.info("======================结束请求通过asin和sku查询product接口======================");
        return null;
    }

    @Override
    public ProductAdReportVo getChildProduct(int puid, Integer shopId, String asin, String msku) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("asin",asin);
        map.put("msku",msku);
        log.info("======================开始请求子商品接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getChildProduct,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<ProductAdReportVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<ProductAdReportVo>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求子商品接口异常======================", e);

        }
        log.info("======================结束请求子商品接口======================");
        return null;
    }

    @Override
    public List<ProductAdReportVo> getListByParentAsin(int puid, Integer shopId, String marketPlaceId, String parentAsin) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketPlaceId",marketPlaceId);
        map.put("parentAsin",parentAsin);
        log.info("======================开始请求根据父Asin查询商品列表 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getListByParentAsin,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求根据父Asin查询商品列表出现异常======================", e);

        }
        log.info("======================结束请求根据父Asin查询商品列表======================");
        return null;
    }


    @Override
    public List<ProductAdReportVo> getChildAsinInfo(Integer puid, Integer shopId, Integer parentId) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("parentId",parentId);
        log.info("======================开始请求子asin信息 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getChildAsinInfo,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求子asin信息出现异常======================", e);

        }
        log.info("======================结束请求子asin信息======================");
        return null;
    }


    @Override
    public List<ProductAdReportVo> getAsinMainImageAndTitle(Integer puid, String marketPlaceId, List<String> asins) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("marketplaceId",marketPlaceId);
        map.put("asins",StringUtil.joinString(asins,","));
        log.info("======================开始请求asin主图和标题信息 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getAsinMainImageAndTitle,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            if (!response.isSuccessful()) {
                return null;
            }
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });

            //业务状态码不为0
            if (result == null || result.error()) {
                return null;
            }

            return result.getData();
        } catch (IOException e) {
            log.info("======================请求asin主图和标题信息出现异常======================", e);

        }
        log.info("======================结束请求asin主图和标题信息======================");
        return null;
    }

    @Override
    public List<ProductAdReportVo> getParentAsinsByChildAsins(Integer puid, Integer shopId, List<String> asins) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map =new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("asins",StringUtil.joinString(asins,","));
        log.info("======================开始请求asin对应父asin的字段 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getParentAsinsByChildAsins,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<ProductAdReportVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<ProductAdReportVo>>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求asin对应父asin值出现异常======================", e);

        }
        log.info("======================结束请求asin对应父asin值信息======================");
        return null;
    }

    /**
     * 根据asin 和sku 获取商品
     *
     * @param puid
     * @param shopId
     * @param asin
     * @return
     */
    @Override
    public ProductAdReportVo getProductByParentAsin(int puid, Integer shopId, String asin) {

        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("asin",asin);
        log.info("======================开始请求根据父ASIN获取标题以及图片接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getProductByParentAsin,map));
        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<ProductAdReportVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<ProductAdReportVo>>() {
            });
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求根据父ASIN获取标题以及图片接口出现异常======================", e);

        }
        log.info("======================结束请求根据父ASIN获取标题以及图片接口======================");
        return null;
    }

    @Override
    public PurchasedProductInfoVO getProductLabelDevs(int puid, int uid, String marketPlaceId, String asinType, List<Integer> shopIdList, List<String> asinList, List<String> skuList) {
//        log.info(" PurchasedProductInfoVO getProductLabelDevs amzupPrefix:{}", amzupPrefix);
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid", puid);
        map.put("uid", uid);
        map.put("marketPlaceId", marketPlaceId);
        map.put("asinType", asinType);
        map.put("shopIdList", shopIdList);
        map.put("asinList", asinList);
        map.put("skuList", skuList);
        log.info("======================开始请求根据ASIN获取业务员以及标签接口 :params: {}======================", map);
        Request request = OkHttpClientUtil.postRequest(amzupPrefix, SellfoxApiEnum.getProductLabelDevs, map);
//        log.info(" PurchasedProductInfoVO getProductLabelDevs url: {}", request.url());
//        log.info(" PurchasedProductInfoVO getProductLabelDevs headers: {}", request.headers());
//        log.info(" PurchasedProductInfoVO getProductLabelDevs body: {}", request.body());
//        log.info(" PurchasedProductInfoVO getProductLabelDevs method: {}", request.method());
        Call call = okHttpClient.newCall(request);
//        Call call = okHttpClient.newCall(OkHttpClientUtil.postRequest("http://127.0.0.1:8080/api/sponsored/v1/", SellfoxApiEnum.getProductLabelDevs, map));
        try {
            Response response = call.execute();
            String body = response.body().string();
//            log.info("response: " + body);
            Result<PurchasedProductInfoVO> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<PurchasedProductInfoVO>>() {
            });
            return result == null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求根据ASIN获取业务员以及标签接口出现异常======================", e);

        }
        log.info("======================结束请求根据ASIN获取业务员以及标签接口======================");
        return null;
    }
}