package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.excel.excelTools.converter.StringConverter;
import com.meiyunji.sponsored.service.util.ReflectionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-07-19  14:27
 */
@Data
@SuperBuilder
@AllArgsConstructor
public class AdPlacementHourVo extends AdAnalysisAndCompareVo{

    private String time;

    private String date;

    @ExcelProperty(value = "小时")
    private String label;

    /**
     * 广告花费/广告订单量
     */
    @ExcelProperty(value = "每笔订单花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal cpa;

    @ExcelProperty(value = "每笔订单花费(对比)")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal cpaCompare;

    @ExcelProperty(value = "每笔订单花费(比率)", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal cpaCompareRate;

    @ExcelProperty(value = "CPC", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adCostPerClick;

    @ExcelProperty(value = "CPC(对比)")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adCostPerClickCompare;

    @ExcelProperty(value = "CPC(比率)", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adCostPerClickCompareRate;

    @ExcelProperty(value = "广告点击率", converter = StringConverter.class)
    private BigDecimal ctr;

    @ExcelProperty(value = "广告点击率(对比)", converter = StringConverter.class)
    private BigDecimal ctrCompare;

    @ExcelProperty(value = "广告点击率(比率)", converter = StringConverter.class)
    private BigDecimal ctrCompareRate;

    @ExcelProperty(value = "广告转化率", converter = StringConverter.class)
    private BigDecimal cvr;

    @ExcelProperty(value = "广告转化率(对比)", converter = StringConverter.class)
    private BigDecimal cvrCompare;

    @ExcelProperty(value = "广告转化率(比率)", converter = StringConverter.class)
    private BigDecimal cvrCompareRate;

    @ExcelProperty(value = "ACoS", converter = StringConverter.class)
    private BigDecimal acos;

    @ExcelProperty(value = "ACoS(对比)", converter = StringConverter.class)
    private BigDecimal acosCompare;

    @ExcelProperty(value = "ACoS(比率)", converter = StringConverter.class)
    private BigDecimal acosCompareRate;
    @ExcelProperty(value = "ROAS")
    private BigDecimal roas;

    @ExcelProperty(value = "ROAS(对比)")
    private BigDecimal roasCompare;

    @ExcelProperty(value = "ROAS(比率)", converter = StringConverter.class)
    private BigDecimal roasCompareRate;
    @ExcelProperty(value = "ACOTS", converter = StringConverter.class)
    private BigDecimal acots;

    @ExcelProperty(value = "ACOTS(对比)", converter = StringConverter.class)
    private BigDecimal acotsCompare;

    @ExcelProperty(value = "ACOTS(比率)", converter = StringConverter.class)
    private BigDecimal acotsCompareRate;

    @ExcelProperty(value = "ASOTS", converter = StringConverter.class)
    private BigDecimal asots;

    @ExcelProperty(value = "ASOTS(对比)", converter = StringConverter.class)
    private BigDecimal asotsCompare;

    @ExcelProperty(value = "ASOTS(比率)", converter = StringConverter.class)
    private BigDecimal asotsCompareRate;

    @ExcelProperty(value = "本广告产品订单量")
    private Integer selfAdOrderNum;

    @ExcelProperty(value = "本广告产品订单量(对比)")
    private Integer selfAdOrderNumCompare;

    @ExcelProperty(value = "本广告产品订单量(比率)", converter = StringConverter.class)
    private BigDecimal selfAdOrderNumCompareRate;

    @ExcelProperty(value = "其他产品广告订单量")
    private Integer otherAdOrderNum;

    @ExcelProperty(value = "其他产品广告订单量(对比)")
    private Integer otherAdOrderNumCompare;

    @ExcelProperty(value = "其他产品广告订单量(比率)", converter = StringConverter.class)
    private BigDecimal otherAdOrderNumCompareRate;

    @ExcelProperty(value = "星期")
    private Integer weekDay;

    @ExcelProperty(value = "本广告产品销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adSelfSale;

    @ExcelProperty(value = "本广告产品销售额(对比)", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adSelfSaleCompare;

    @ExcelProperty(value = "本广告产品销售额(比率)", converter = StringConverter.class)
    private BigDecimal adSelfSaleCompareRate;

    @ExcelProperty(value = "其他产品广告销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adOtherSale;

    @ExcelProperty(value = "其他产品广告销售额(对比)", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adOtherSaleCompare;

    @ExcelProperty(value = "其他产品广告销售额(比率)", converter = StringConverter.class)
    private BigDecimal adOtherSaleCompareRate;

    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;

    @ExcelProperty(value = "本广告产品销量（对比）")
    private Integer adSelfSaleNumCompare;

    @ExcelProperty(value = "本广告产品销量（比率）")
    private BigDecimal adSelfSaleNumCompareRate;

    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;

    @ExcelProperty(value = "其他产品广告销量（对比）")
    private Integer adOtherSaleNumCompare;

    @ExcelProperty(value = "其他产品广告销量（比率）")
    private BigDecimal adOtherSaleNumCompareRate;

    @ExcelProperty(value = "广告花费占比")
    private BigDecimal adCostPercentage;

    @ExcelProperty(value = "广告订单量占比")
    private BigDecimal adSalePercentage;

    @ExcelProperty(value = "广告销售额占比")
    private BigDecimal adOrderNumPercentage;

    @ExcelProperty(value = "广告销量占比")
    private BigDecimal orderNumPercentage;

    //用户小时字段排序
    private Integer hour;

    public AdPlacementHourVo(){
        this.clicks = 0L ;
        this.acos = BigDecimal.ZERO;
        this.cpa = BigDecimal.ZERO;
        this.adCostPerClick = BigDecimal.ZERO;
        this.asots = BigDecimal.ZERO;
        this.adCost = BigDecimal.ZERO;
        this.adCostCompare = BigDecimal.ZERO;
        this.adCostCompareRate = BigDecimal.ZERO;
        this.impressions = 0L;
        this.impressionsCompare = 0L;
        this.impressionsCompareRate = BigDecimal.ZERO;
        this.clicksCompare = 0L;
        this.clicksCompareRate = BigDecimal.ZERO;
        this.ctr = BigDecimal.ZERO;
        this.cvr = BigDecimal.ZERO;
        this.roas = BigDecimal.ZERO;
        this.acots = BigDecimal.ZERO;
        this.adOrderNum = 0;
        this.adOrderNumCompare = 0;
        this.adOrderNumCompareRate = BigDecimal.ZERO;
        this.selfAdOrderNum = 0;
        this.otherAdOrderNum = 0;
        this.adSale = BigDecimal.ZERO;
        this.adSaleCompare = BigDecimal.ZERO;
        this.adSaleCompareRate = BigDecimal.ZERO;
        this.adSelfSale = BigDecimal.ZERO;
        this.adOtherSale = BigDecimal.ZERO;
        this.adSaleNum = 0;
        this.adSaleNumCompare = 0;
        this.adSaleNumCompareRate = BigDecimal.ZERO;
        this.adSelfSaleNum = 0;
        this.adOtherSaleNum = 0;
    }

    @Override
    public void afterPropertiesSet() {
        //invoke parent's afterPropertiesSet()
        super.afterPropertiesSet();
        //set ad placement compare rete properties
        this.adCostCompareRate = calculateCompareRete(this.adCost, this.adCostCompare);
        this.cpaCompareRate = calculateCompareRete(this.cpa, this.cpaCompare);
        this.adCostPerClickCompareRate = calculateCompareRete(this.adCostPerClick, this.adCostPerClickCompare);
        this.ctrCompareRate = calculateCompareRete(this.ctr, this.ctrCompare);
        this.cvrCompareRate = calculateCompareRete(this.cvr, this.cvrCompare);
        this.acosCompareRate = calculateCompareRete(this.acos, this.acosCompare);
        this.roasCompareRate = calculateCompareRete(this.roas, this.roasCompare);
        this.selfAdOrderNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.selfAdOrderNum), BigDecimal.valueOf(this.selfAdOrderNumCompare));
        this.otherAdOrderNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.otherAdOrderNum), BigDecimal.valueOf(this.otherAdOrderNumCompare));
        this.adSelfSaleCompareRate = calculateCompareRete(this.adSelfSale, this.adSelfSaleCompare);
        this.adOtherSaleCompareRate = calculateCompareRete(this.adOtherSale, this.adOtherSaleCompare);
        this.adSelfSaleNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.adSelfSaleNum), BigDecimal.valueOf(this.adSelfSaleNumCompare));
        this.adOtherSaleNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.adOtherSaleNum), BigDecimal.valueOf(this.adOtherSaleNumCompare));
        this.asotsCompareRate = calculateCompareRete(this.asots, this.asotsCompare);
    }

    public void compareDataSet(AdPlacementHourVo compare) {
        if (Objects.isNull(compare)) {
            ReflectionUtil.setCompareNull(this);
            return;
        }
        super.compareDataSet(compare);
        this.adCostPerClickCompare = compare.getAdCostPerClick();
        this.cpaCompare = compare.getCpa();
        this.ctrCompare = compare.getCtr();
        this.cvrCompare = compare.getCvr();
        this.acosCompare = compare.getAcos();
        this.roasCompare = compare.getRoas();
        this.selfAdOrderNumCompare = compare.getSelfAdOrderNum();
        this.otherAdOrderNumCompare = compare.getOtherAdOrderNum();
        this.adSelfSaleCompare = compare.getAdSelfSale();
        this.adOtherSaleCompare = compare.getAdOtherSale();
        this.adSelfSaleNumCompare = compare.getAdSelfSaleNum();
        this.adOtherSaleNumCompare = compare.getAdOtherSaleNum();
        this.asotsCompare = compare.getAsots();
        afterPropertiesSet();
    }

}
