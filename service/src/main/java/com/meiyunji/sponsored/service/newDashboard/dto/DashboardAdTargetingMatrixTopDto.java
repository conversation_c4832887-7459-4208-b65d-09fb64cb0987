package com.meiyunji.sponsored.service.newDashboard.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: ys
 * @date: 2024/4/23 14:46
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardAdTargetingMatrixTopDto extends DashboardAdCalDataDto {
    private String campaignId;
    private Integer shopId;
    private String adGroupId;
    private String marketplaceId;
    private String keywordId;
    private String targetingId;
    private String keywordText;
    private String matchType;
    private String type;
    private String adGroupName;
    private String campaignName;
}
