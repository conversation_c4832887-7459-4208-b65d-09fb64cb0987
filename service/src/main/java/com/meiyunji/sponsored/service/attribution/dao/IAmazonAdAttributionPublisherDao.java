package com.meiyunji.sponsored.service.attribution.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionPublisher;

import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/12 11:53
 * @describe:
 */
public interface IAmazonAdAttributionPublisherDao extends IAdBaseDao<AmazonAdAttributionPublisher> {
    Integer insertOrUpdate(List<AmazonAdAttributionPublisher> publishers);

    List<AmazonAdAttributionPublisher> list(String publishName, Boolean macroEnable);

    AmazonAdAttributionPublisher getPublisherById(String publisherId);

    AmazonAdAttributionPublisher getPublisherByName(String name);
}
