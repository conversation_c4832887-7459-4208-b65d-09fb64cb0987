package com.meiyunji.sponsored.service.log.enums;




public enum OperationLogChangeTypeEnum {


    /**
     * 亚马逊的更新内容
     */

    CREATED("CREATED","新建"),
    STATUS("STATUS","状态"),

    //	IN_BUDGET	true or false
    IN_BUDGET("IN_BUDGET","超预算"),
    //portfolioId
    PORTFOLIO_ID("PORTFOLIO_ID","广告组合"),

    //amount in marketplace currency
    BUDGET_AMOUNT("BUDGET_AMOUNT","预算费用"),
    NAME("NAME","名称"),

    START_DATE("START_DATE","开始时间"),
    END_DATE("END_DATE","结束时间"),
    //OPTIMIZE_FOR_SALES, LEGACY, MANUAL
    SMART_BIDDING_STRATEGY("SMART_BIDDING_STRATEGY","广告活动的竞价策略"),
    //number as percent
    PLACEMENT_GROUP("PLACEMENT_GROUP","广告位竞价"),
    //CREATED, ENABLED, PAUSED, ARCHIVED
    DEFAULT_BID_AMOUNT("DEFAULT_BID_AMOUNT","默认竞价"),
    BID("BID","竞价"),
    bid("bid","竞价"),
    TOP_OF_SEARCH("TOP_OF_SEARCH","搜索结果顶部（首页）"),
    //亚马逊日志接口metadata.placementGroupPosition值实际返回TOP而不是TOP_OF_SEARCH
    TOP("TOP", "搜索结果顶部（首页）"),
    DETAIL_PAGE("DETAIL_PAGE","产品页面"),
    REST_OF_SEARCH("REST_OF_SEARCH","搜索结果其余位置"),
    SITE_AMAZON_BUSINESS("SITE_AMAZON_BUSINESS","企业购"),
    BID_AMOUNT("BID_AMOUNT", "竞价"),
    /**
     * 赛狐本地枚举
     * 针对本地操作日志多个属性一起编辑
     */
    startDate("startDate","开始时间"),
    endDate("endDate","开始时间"),
    ALL_EDIT("ALL_EDIT","编辑"),
    PORTFOLIO("portfolioId","广告组合"),
    strategy("strategy","竞价策略"),
    ADJUSTMENTS("adjustments","广告位竞价"),
    ADPLACE("adPlace","广告位展示调整"),
    PLACEMENTTOP("placementTop", "搜索结果顶部（首页）"),
    PLACEMENTPRODUCTPAGE("placementProductPage", "产品页面"),
    TOP_BUDGET("topBudget","SP顶顶级预算"),
    PORTFOLIO_BUDGET("portfolioBudget","调广告组合"),
    PLACEMENTRESTOFSEARCH("placementRestOfSearch", "搜索结果的其余位置"),
    SITEAMAZONBUSINESS("siteAmazonBusiness", "亚马逊企业购"),

    PORTFOLIO_AMOUNT("amount", "广告组合每月预算"),
    PORTFOLIO_BUDGET_START_DATE("budgetStartDate", "广告组合预算开始日期"),
    PORTFOLIO_BUDGET_END_DATE("budgetEndDate", "广告组合预算结束日期"),
    SD_CREATIVE_PROPERTIES("creativeProperties", "SD创意属性"),
    BID_MULTIPLIER("bidMultiplier", "自动竞价"),
    ;

    private String changeType;

    private String changeValue;

    OperationLogChangeTypeEnum(String changeType, String changeValue) {
        this.changeType = changeType;
        this.changeValue = changeValue;
    }

    public String getChangeTypeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getChangeValue() {
        return changeValue;
    }

    public void setChangeValue(String changeValue) {
        this.changeValue = changeValue;
    }

    public static String getOperationLogChangeValue(String changeType){
        OperationLogChangeTypeEnum[] values = values();
        for (OperationLogChangeTypeEnum value : values) {
            if(changeType.equalsIgnoreCase(value.getChangeTypeType())){
                return value.getChangeValue();
            }
        }
        return null;
    }

    public static OperationLogChangeTypeEnum getOperationLogChange(String changeType){
        OperationLogChangeTypeEnum[] values = values();
        for (OperationLogChangeTypeEnum value : values) {
            if(changeType.equalsIgnoreCase(value.getChangeTypeType())){
                return value;
            }
        }
        return null;
    }

}
