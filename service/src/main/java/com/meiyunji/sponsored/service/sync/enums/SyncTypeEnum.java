package com.meiyunji.sponsored.service.sync.enums;

/**
 * @author: li<PERSON><PERSON>
 * @email: liwei<PERSON>@dianxiaomi.com
 * @date: 2023-08-11  14:42
 */
public enum SyncTypeEnum {
    CAMPAIGN(1, "广告活动" ),
    GROUP(2, "广告组"),
    AD(3, "广告产品"),
    TARGET(4, "商品投放"),
    KEYWORD(5, "关键词投放");

    private Integer code;
    private String desc;

    SyncTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
