package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardFlowConversionReqVo;

/**
 * <AUTHOR>
 * created time:2024-06-25 15:42:45
 */
public interface IDashboardAdFlowConversionService {
    /**
     * @param reqVo
     * @return DashboardAdFlowConversionResponseVo
     */
    DashboardAdFlowConversionResponseVo queryFlowConversion(DashboardFlowConversionReqVo reqVo);
}
