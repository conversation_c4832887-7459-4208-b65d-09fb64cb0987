package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

public enum SheinMarketplace {
    WWW("shein-www", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "SHEIN国际站"),
    FR("shein-fr", MultiPlatformMarketplaceEnum.FR, "EUR", "SHEIN法国站"),
    ES("shein-es", MultiPlatformMarketplaceEnum.ES, "EUR", "SHEIN西班牙站"),
    DE("shein-de", MultiPlatformMarketplaceEnum.DE, "EUR", "SHEIN德国站"),
    RU("shein-ru", MultiPlatformMarketplaceEnum.INTERNATIONAL, "RUB", "SHEIN俄罗斯站"),
    IT("shein-it", MultiPlatformMarketplaceEnum.IT, "EUR", "SHEIN意大利站"),
    AR("shein-ar", MultiPlatformMarketplaceEnum.INTERNATIONAL, "SAR", "SHEIN阿拉伯站"),
    TW("shein-tw", MultiPlatformMarketplaceEnum.INTERNATIONAL, "TWD", "SHEIN台湾站"),
    US("shein-us", MultiPlatformMarketplaceEnum.US, "USD", "SHEIN美国站"),
    UK("shein-uk", MultiPlatformMarketplaceEnum.UK, "GBP", "SHEIN英国站"),
    AU("shein-au", MultiPlatformMarketplaceEnum.AU, "AUD", "SHEIN澳大利亚站"),
    IN("shein-in", MultiPlatformMarketplaceEnum.IN, "INR", "SHEIN印度站"),
    SUS("shein-sus", MultiPlatformMarketplaceEnum.US, "USD", "shein美国清仓站"),
    MX("shein-mx", MultiPlatformMarketplaceEnum.MX, "MXN", "SHEIN墨西哥站"),
    SA("shein-sa", MultiPlatformMarketplaceEnum.SA, "SAR", "SHEIN沙特阿拉伯站"),
    KW("shein-kw", MultiPlatformMarketplaceEnum.INTERNATIONAL, "KWD", "SHEIN科威特站"),
    AE("shein-ae", MultiPlatformMarketplaceEnum.AE, "AED", "SHEIN阿联酋站"),
    QA("shein-qa", MultiPlatformMarketplaceEnum.INTERNATIONAL, "QAR", "SHEIN卡塔尔站"),
    OM("shein-om", MultiPlatformMarketplaceEnum.INTERNATIONAL, "OMR", "SHEIN阿曼站"),
    BH("shein-bh", MultiPlatformMarketplaceEnum.INTERNATIONAL, "BHD", "SHEIN巴林站"),
    EG("shein-eg", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "SHEIN埃及站"),
    IL("shein-il", MultiPlatformMarketplaceEnum.INTERNATIONAL, "ILS", "SHEIN以色列站"),
    TH("shein-th", MultiPlatformMarketplaceEnum.TH, "THB", "SHEIN泰国站"),
    ID("shein-id", MultiPlatformMarketplaceEnum.ID, "IDR", "SHEIN印尼站"),
    NL("shein-nl", MultiPlatformMarketplaceEnum.NL, "EUR", "SHEIN荷兰站"),
    HK("shein-hk", MultiPlatformMarketplaceEnum.INTERNATIONAL, "HKD", "SHEIN香港站"),
    TR("shein-tr", MultiPlatformMarketplaceEnum.TR, "TRY", "SHEIN土耳其站"),
    VN("shein-vn", MultiPlatformMarketplaceEnum.VN, "VND", "SHEIN越南站"),
    BR("shein-br", MultiPlatformMarketplaceEnum.BR, "BRL", "SHEIN巴西站"),
    CA("shein-ca", MultiPlatformMarketplaceEnum.CA, "CAD", "SHEIN加拿大站"),
    SE("shein-se", MultiPlatformMarketplaceEnum.SE, "SEK", "SHEIN瑞典站"),
    SNS("shein-sns", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "SHEIN南沙清仓站"),
    SEU("shein-seu", MultiPlatformMarketplaceEnum.INTERNATIONAL, "EUR", "SHEIN欧洲清仓站"),
    EUR("shein-eur", MultiPlatformMarketplaceEnum.INTERNATIONAL, "EUR", "SHEIN欧洲站"),
    ARG("shein-arg", MultiPlatformMarketplaceEnum.INTERNATIONAL, "ARS", "SHEIN阿根廷站"),
    CL("shein-cl", MultiPlatformMarketplaceEnum.INTERNATIONAL, "CLP", "SHEIN智利站"),
    FUNMART("shein-funmart", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "FUNMART"),
    MA("shein-ma", MultiPlatformMarketplaceEnum.INTERNATIONAL, "COP", "SHEIN摩洛哥站"),
    SG("shein-sg", MultiPlatformMarketplaceEnum.SG, "SGD", "SHEIN新加坡站"),
    ZA("shein-za", MultiPlatformMarketplaceEnum.INTERNATIONAL, "KRW", "SHEIN南非站"),
    NZ("shein-nz", MultiPlatformMarketplaceEnum.NZ, "NZD", "SHEIN新西兰站"),
    PL("shein-pl", MultiPlatformMarketplaceEnum.PL, "PLN", "SHEIN波兰站"),
    ASIA("shein-asia", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "shein亚洲站"),
    USLITE("shein-uslite", MultiPlatformMarketplaceEnum.US, "USD", "SHEIN轻量化美国站"),
    PH("shein-ph", MultiPlatformMarketplaceEnum.PH, "PHP", "SHEIN菲律宾站"),
    PT("shein-pt", MultiPlatformMarketplaceEnum.INTERNATIONAL, "EUR", "SHEIN葡萄牙站"),
    JP("shein-jp", MultiPlatformMarketplaceEnum.JP, "JPY", "SHEIN日本站"),
    MY("shein-my", MultiPlatformMarketplaceEnum.MY, "RON", "SHEIN马来西亚站"),
    CH("shein-ch", MultiPlatformMarketplaceEnum.INTERNATIONAL, "USD", "SHEIN瑞士站"),
    ROE("shein-roe", MultiPlatformMarketplaceEnum.INTERNATIONAL, "EUR", "SHEIN欧洲ROE站"),
    EUQS("shein-euqs", MultiPlatformMarketplaceEnum.INTERNATIONAL, "EUR", "SHEIN欧洲QS站"),
    ZXX("shein-zxx", MultiPlatformMarketplaceEnum.INTERNATIONAL, "CAD", "zx站"),
    CO("shein-co", MultiPlatformMarketplaceEnum.INTERNATIONAL, "COP", "SHEIN哥伦比亚站"),
    ;


    private String code;
    private MultiPlatformMarketplaceEnum multiPlatformMarketplace;
    private String currency;
    private String desc;

    public static SheinMarketplace getByCode(String code) {
        for (SheinMarketplace value : SheinMarketplace.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
    public static List<SheinMarketplace> getByMultiPlatformMarketplace(MultiPlatformMarketplaceEnum multiPlatformMarketplace) {
        List<SheinMarketplace> result = Lists.newArrayList();
        for (SheinMarketplace value : SheinMarketplace.values()) {
            if (value.multiPlatformMarketplace == multiPlatformMarketplace) {
                result.add(value);
            }
        }
        return result;
    }

    SheinMarketplace(String code, MultiPlatformMarketplaceEnum multiPlatformMarketplaceEnum, String currency, String desc) {
        this.code = code;
        this.multiPlatformMarketplace = multiPlatformMarketplaceEnum;
        this.currency = currency;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public MultiPlatformMarketplaceEnum getMultiPlatformMarketplace() {
        return multiPlatformMarketplace;
    }

    public String getCurrency() {
        return currency;
    }

    public String getDesc() {
        return desc;
    }

    public static MultiPlatformMarketplaceEnum getMultiPlatformMarketplace(String salesSite) {
        for (SheinMarketplace value : SheinMarketplace.values()) {
            if (value.code.equalsIgnoreCase(salesSite)) {
                return value.multiPlatformMarketplace;
            }
        }
        return MultiPlatformMarketplaceEnum.INTERNATIONAL;
    }


    public static List<SheinMarketplace> getByCodes(List<String> marketplaceCodes) {
        List<SheinMarketplace> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(marketplaceCodes)) {
            return result;
        }
        for (String code : marketplaceCodes) {
            SheinMarketplace sheinMarketplace = SheinMarketplace.getByCode(code);
            if (Objects.isNull(sheinMarketplace)) {
                continue;
            }
            result.add(sheinMarketplace);
        }
        return result;
    }
}
