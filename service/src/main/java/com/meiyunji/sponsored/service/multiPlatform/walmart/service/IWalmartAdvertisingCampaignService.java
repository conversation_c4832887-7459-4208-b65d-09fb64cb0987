package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdCampaignPageDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingSyncDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdCampaignCreateTogetherResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdCampaignCreateTogetherVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdCampaignCreateVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdCampaignVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartCampaignGetAllReq;


import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingCampaignService {

    void update(int puid, WalmartAdvertisingCampaign campaign);

    /**
     * 新增
     */
    int add(WalmartAdvertisingCampaign campaign);

    /**
     * 删除
     */
    int deleteByCampaignId(Integer puid, Integer shopId, String campaignId);

    /**
     * 更新
     */
    int update(WalmartAdvertisingCampaign campaign);

    /**
     * 根据主键 id 查询
     */
    WalmartAdvertisingCampaign getById(Integer puid, Long id);

    /**
     * 统一流程创建SP广告活动
     * @param req
     * @return
     * @throws ServiceException
     */
    WalmartAdCampaignCreateTogetherResp createCampaign(WalmartAdCampaignCreateTogetherVo req) throws ServiceException;

    /**
     * @author: pxq
     * @date: 2025/02/24
     * @description: 更新广告活动
     */
    WalmartAdCampaignCreateVo editCampaign(Integer puid, Integer operatorId, WalmartAdCampaignCreateVo campaign) throws ServiceException;

    Boolean updateStatusOrBidOrDate(Integer puid, WalmartAdCampaignCreateVo campaign) throws ServiceException;

    /**
     * @author: pxq
     * @date: 2025/02/24
     * @description: 删除广告活动
     */
    Boolean deleteCampaign(Integer puid, Integer shopId,
                           String campaignId, Integer operatorId) throws ServiceException;

    /**
     * 获取广告活动列表
     * @param reqVo
     * @return
     */
    Page<WalmartAdCampaignPageDTO> getPageList(WalmartAdCampaignVo reqVo);

    /**
     * 获取广告活动列表汇总接口
     * @param reqVo
     * @return
     */
    WalmartAdCampaignPageDTO getAggregateData(WalmartAdCampaignVo reqVo);

    WalmartAdCampaignCreateVo getByCampaignId(Integer puid, Integer shopId, String campaignId);

    Page<WalmartAdCampaignCreateVo> getAllListByShopId(WalmartCampaignGetAllReq req);

    void syncCampaign(int puid, WalmartAdvertisingSyncDTO dto, String uuid);

    void syncCampaign(MultiPlatformShopAuth shop, String campaigns, String syncTime, Boolean isAllSync);
    void syncCampaignAllBySnapshot(int puid, WalmartAdvertisingSyncDTO dto, String uuid);

    void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus);

    /**
     * 根据id获取活动名称，方便用于列表页反查
     * @param puid
     * @param shopIds
     * @return
     */
    Map<String, String> getCampaignNameMap(Integer puid, List<Integer> shopIds, List<String> campaignIds);

}
