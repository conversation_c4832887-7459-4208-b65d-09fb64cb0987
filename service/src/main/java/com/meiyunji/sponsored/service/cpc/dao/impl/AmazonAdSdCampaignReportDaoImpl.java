package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdCampaignReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdCampaignReport;
import com.meiyunji.sponsored.service.cpc.vo.CampaignReportDetails;
import com.meiyunji.sponsored.service.cpc.vo.CampaignReportSearchVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
public class AmazonAdSdCampaignReportDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignAllReport> implements IAmazonAdSdCampaignReportDao {

    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSdCampaignReport> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_sd_campaign_report` (`puid`,`shop_id`,`marketplace_id`,`count_date`,`tactic_type`,`campaign_name`,`campaign_id`,")
                .append("`impressions`,`clicks`,`cost`,`currency`,`conversions1d`,`conversions7d`,`conversions14d`,`conversions30d`,`conversions1d_same_sku`,`conversions7d_same_sku`,")
                .append("`conversions14d_same_sku`,`conversions30d_same_sku`,`units_ordered1d`,`units_ordered7d`,`units_ordered14d`,`units_ordered30d`,`sales1d`,`sales7d`,`sales14d`,`sales30d`,")
                .append("`sales1d_same_sku`,`sales7d_same_sku`,`sales14d_same_sku`,`sales30d_same_sku`,`orders_new_to_brand14d`,`sales_new_to_brand14d`,`units_ordered_new_to_brand14d`,`detail_page_view14d`,`view_impressions`,`cost_type`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSdCampaignReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTacticType());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCost());
            argsList.add(report.getCurrency());
            argsList.add(report.getConversions1d());
            argsList.add(report.getConversions7d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions30d());
            argsList.add(report.getConversions1dSameSKU());
            argsList.add(report.getConversions7dSameSKU());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getConversions30dSameSKU());
            argsList.add(report.getUnitsOrdered1d());
            argsList.add(report.getUnitsOrdered7d());
            argsList.add(report.getUnitsOrdered14d());
            argsList.add(report.getUnitsOrdered30d());
            argsList.add(report.getSales1d());
            argsList.add(report.getSales7d());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales30d());
            argsList.add(report.getSales1dSameSKU());
            argsList.add(report.getSales7dSameSKU());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getSales30dSameSKU());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getDetailPageView14d());
            argsList.add(report.getViewImpressions());
            argsList.add(report.getCostType());
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`impressions`=values(impressions),`clicks`=values(clicks),`cost`=values(cost),`currency`=values(currency),");
        sql.append("`conversions1d`=values(conversions1d),`conversions7d`=values(conversions7d),`conversions14d`=values(conversions14d),`conversions30d`=values(conversions30d),`conversions1d_same_sku`=values(conversions1d_same_sku),`conversions7d_same_sku`=values(conversions7d_same_sku),");
        sql.append("`conversions14d_same_sku`=values(conversions14d_same_sku),`conversions30d_same_sku`=values(conversions30d_same_sku),`units_ordered1d`=values(units_ordered1d),`units_ordered7d`=values(units_ordered7d),`units_ordered14d`=values(units_ordered14d),`units_ordered30d`=values(units_ordered30d),");
        sql.append("`sales1d`=values(sales1d),`sales7d`=values(sales7d),`sales14d`=values(sales14d),`sales30d`=values(sales30d),`sales1d_same_sku`=values(sales1d_same_sku),`sales7d_same_sku`=values(sales7d_same_sku),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`sales30d_same_sku`=values(sales30d_same_sku),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`sales_new_to_brand14d`=values(sales_new_to_brand14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`detail_page_view14d`=values(detail_page_view14d),`view_impressions`=values(view_impressions),`cost_type`=values(cost_type)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Page getPageList(int puid, SearchVo search, Page page) {
        return amazonAdCampaignAllReportDao.getSdPageList(puid, search, page);
    }

    @Override
    public Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
       return amazonAdCampaignAllReportDao.sdDetailPageList(puid, shopId, marketplaceId, param, page);
    }

    @Override
    public AmazonAdCampaignAllReport getSumReportByCampaignId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
        return amazonAdCampaignAllReportDao.getSdSumReportByCampaignId(puid, shopId, marketplaceId, startStr, endStr, campaignId);
    }

    @Override
    public List<AmazonAdCampaignAllReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId) {
       return amazonAdCampaignAllReportDao.getSdChartList(puid, shopId, marketplaceId, startStr, endStr, campaignId);
    }

    @Override
    public AmazonAdCampaignAllReport getSumReport(Integer puid, Integer shopId, String marketplaceId, String reportDate, String tacticType) {
       return amazonAdCampaignAllReportDao.getSdSumReport(puid, shopId, marketplaceId, reportDate, tacticType);
    }

    @Override
    public List<AmazonAdCampaignAllReport> listSumReports(int puid, Integer shopId, String startDate, String endDate, List<String> campaignIds) {
        return amazonAdCampaignAllReportDao.sdListSumReports(puid, shopId, startDate, endDate, campaignIds);
    }

    @Override
    public List<AmazonAdCampaignAllReport> listReports(int puid, Integer shopId, String startDate, String endDate, String campaignId) {
       return amazonAdCampaignAllReportDao.sdListReports(puid, shopId, startDate, endDate, campaignId);
    }

    @Override
    public AmazonAdCampaignAllReport statByDateRange(int puid, Integer shopId, String start, String end) {
       return amazonAdCampaignAllReportDao.sdStatByDateRange(puid,shopId,start,end);
    }

    @Override
    public List<String> getCampaignListByUpdateTime(Integer puid, Integer shopId, Date date) {
        return amazonAdCampaignAllReportDao.getSdCampaignListByUpdateTime(puid, shopId, date);
    }

    @Override
    public AmazonAdCampaignAllReport getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo) {
       return amazonAdCampaignAllReportDao.getSdReportVoByCampaignId(puid,campaignId,searchVo);
    }

    @Override
    public AmazonAdCampaignAllReport getDetailsSumVo(Integer puid, CampaignReportDetails detailsVo) {
        return amazonAdCampaignAllReportDao.getSdDetailsSumVo(puid, detailsVo);
    }

    @Override
    public List<AmazonAdCampaignAllReport> getListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo) {
       return amazonAdCampaignAllReportDao.getSdListCampaignDetailsDay(puid, detailsVo);
    }
}
