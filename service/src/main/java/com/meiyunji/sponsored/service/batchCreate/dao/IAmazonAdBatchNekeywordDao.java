package com.meiyunji.sponsored.service.batchCreate.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.TaskStatusSetDto;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchGroup;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNekeyword;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-16  09:30
 */
public interface IAmazonAdBatchNekeywordDao extends IBaseShardingDao<AmazonAdBatchNekeyword> {

    void insertList(Integer puid, List<AmazonAdBatchNekeyword> nekeywordList);

    void updateStatusByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> idList, Byte status);

    /**
     * 批量修改为失败状态
     */
    void updateErrTaskStatusByIdList(Integer puid, Map<Long, String> idErrMsgMap, boolean updateExecuteCount);

    /**
     * 批量修改为重试状态
     */
    void updateRetryTaskStatusByIdList(Integer puid, List<Long> idList, Date nextRetryTime);

    /**
     * 批量修改为成功状态
     */
    void updateSuccTaskStatusByIdList(Integer puid, Map<Long, String> idAdIdMap);

    /**
     * 根据广告组id列表获取id列表
     * @param puid
     * @param taskId
     * @param groupIdList
     * @return
     */
    List<Long> listIdByGroupIdList(Integer puid, Long taskId, List<Long> groupIdList);
    Map<Long, String> listIdByGroupIdListAndStatus(Integer puid, Long taskId, List<Long> groupIdList, List<Integer> status);

    /**
     * 根据任务id获取需要创建的广告产品
     * @param puid
     * @param shopId
     * @param taskId
     * @param groupIdList
     * @param taskStatus
     * @return
     */
    List<AmazonAdBatchNekeyword> listByGroupIdList(Integer puid, Integer shopId, Long taskId, List<Long> groupIdList, List<Byte> taskStatus);

    /**
     * 根据任务id获取需要创建的广告产品
     * @param puid
     * @param shopId
     * @param idList
     * @param taskStatus
     * @return
     */
    List<AmazonAdBatchNekeyword> listByIdList(Integer puid, Integer shopId, List<Long> idList, List<Byte> taskStatus);

    void batchUpdateCampaignId(List<AmazonAdBatchCampaign> batchCampaignList);

    void batchUpdateAdGroupId(List<AmazonAdBatchGroup> batchGroupList);

    int terminateByTaskId(Integer puid, Integer shopId, Long taskId, Byte status,
                          List<Byte> includeStatus, String errMsg);

    List<TaskStatusSetDto> distinctStatusByTaskId(Integer puid, Integer shopId, Long taskId);


    List<AmazonAdBatchNekeyword> selectNeedRetryAmazon(Integer puid, Integer shopId, Long taskId, List<Long> groupIds);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
