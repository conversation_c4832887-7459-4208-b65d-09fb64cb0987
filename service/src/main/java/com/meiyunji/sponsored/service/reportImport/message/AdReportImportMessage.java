package com.meiyunji.sponsored.service.reportImport.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdReportImportMessage {
    private Integer puid;
    private Long taskId;
    private Long scheduleId;
    private String reportType;
    private String fileId;
    private Integer shopId;
    private String countDate;
    private String adType;

}
