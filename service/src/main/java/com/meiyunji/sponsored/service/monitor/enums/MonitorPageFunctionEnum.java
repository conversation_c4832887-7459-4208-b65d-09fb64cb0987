package com.meiyunji.sponsored.service.monitor.enums;


/**
 * 监控枚举类型
 *
 * <AUTHOR>
 * @date 2023/07/20
 */
public enum MonitorPageFunctionEnum {


    /**
     * 广告活动
     */
    CAMPAIGN("CAMPAIGN", "广告活动", "campaignListMonitorAnalysisProcess"),

    /**
     * 广告组
     */
    GROUP("GROUP", "广告组", "groupListMonitorAnalysisProcess"),

    /**
     * 数据聚合
     */
    PRODUCT_AGGREGATION_TARGET("PRODUCT_AGGREGATION_TARGET", "数据聚合投放", "productAggregationTargetListMonitorAnalysisProcess"),

    /**
     * 搜索词关键词产生
     */
    QUERY_KEYWORD("QUERY_KEYWORD", "搜索词关键词产生", "queryKeywordListMonitorAnalysisProcess"),

    /**
     * 搜索词投放产生
     */
    QUERY_TARGET("QUERY_TARGET", "搜索词投放产生", "queryTargetListMonitorAnalysisProcess"),

    /**
     * 商品投放
     */
    TARGET("TARGET", "商品|自动投放", "targetListMonitorAnalysisProcess"),

    /**
     * 广告产品
     */
    AD_PRODUCT("AD_PRODUCT", "广告产品", "adProductListMonitorAnalysisProcess"),

    /**
     * 关键词投放
     */
    KEYWORD("KEYWORD", "关键词投放", "keywordListMonitorAnalysisProcess"),

    /**
     * 广告位
     */
    PLACEMENT("PLACEMENT", "广告位", "placementListMonitorAnalysisProcess"),

    /**
     * 广告组合
     */
    PORTFOLIO("PORTFOLIO", "广告组合", "portfolioListMonitorAnalysisProcess"),
    ;

    private String type;
    private String description;
    private String processBeanName;


    MonitorPageFunctionEnum(String type, String description, String processBeanName) {
        this.type = type;
        this.description = description;
        this.processBeanName = processBeanName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProcessBeanName() {
        return processBeanName;
    }

    public void setProcessBeanName(String processBeanName) {
        this.processBeanName = processBeanName;
    }
}

