package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.strategy.enums.WeekEnum;
import lombok.Data;

import java.math.RoundingMode;

@Data
public class AdCampaignWeekExportVo extends AdCampaignHourBaseExportVo {
    @ExcelProperty(value = "星期")
    private String weekDay;


    public AdCampaignWeekExportVo(String currency, AdCampaignHourVo vo) {
        super(currency, vo);
        this.weekDay = WeekEnum.getWeekByDay(vo.getWeekDay());
        setViewableImpressions(vo.getViewableImpressions());
        setVctr(vo.getVCtr() == null ? "0.00%" : vo.getVCtr().setScale(2, RoundingMode.HALF_UP) + "%");
        setVrt(vo.getVrt() == null ? "0.00%" : vo.getVrt().setScale(2, RoundingMode.HALF_UP) + "%");
        setVcpm(vo.getVcpm() == null ? currency + 0 : currency + vo.getVcpm().setScale(2, RoundingMode.HALF_UP));
        setAdvertisingUnitPrice(vo.getAdvertisingUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP));
        setSalesNewToBrand(vo.getSalesNewToBrand() == null ? currency + 0 : currency + vo.getSalesNewToBrand().setScale(2, RoundingMode.HALF_UP));
        setUnitsOrderedNewToBrand(vo.getUnitsOrderedNewToBrand());
        setOrdersNewToBrand(vo.getOrdersNewToBrand());

        setSalesNewToBrandPercentage(vo.getSalesNewToBrandPercentage() == null ? null : vo.getSalesNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setUnitsOrderedNewToBrandPercentage(vo.getUnitsOrderedNewToBrandPercentage() == null ? null : vo.getUnitsOrderedNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setOrdersNewToBrandPercentage(vo.getOrdersNewToBrandPercentage() == null ? null : vo.getOrdersNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setAdvertisingOtherProductUnitPrice(vo.getAdvertisingOtherProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingOtherProductUnitPrice().setScale(2, RoundingMode.HALF_UP));
        setAdvertisingProductUnitPrice(vo.getAdvertisingProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingProductUnitPrice().setScale(2, RoundingMode.HALF_UP));

    }

    public AdCampaignWeekExportVo(String currency, AdReportHourlyVO vo) {
        this.weekDay = WeekEnum.getWeekByDay(vo.getWeekDay());
        setLabel(vo.getLabel());
        setCost(vo.getAdCost() == null ? currency + 0 : currency + vo.getAdCost().setScale(2, RoundingMode.HALF_UP));
        setCostCompare(vo.getAdCostCompare() == null ? currency + 0 : currency + vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP));
        setCostCompareRate(vo.getAdCostCompareRate() == null ? null : vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setImpressions(vo.getImpressions());
        setImpressionsCompare(vo.getImpressionsCompare());
        setImpressionsCompareRate(vo.getImpressionsCompareRate() == null ? null : vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setClicks(vo.getClicks());
        setClicksCompare(vo.getClicksCompare());
        setClicksCompareRate(vo.getClicksCompareRate() == null ? null : vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setCpa(vo.getCpa() == null ? currency + 0 : currency + vo.getCpa().setScale(2, RoundingMode.HALF_UP));
        setCpc(vo.getAdCostPerClick() == null ? null : vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP));
        setCpcCompare(vo.getAdCostPerClickCompare() == null ? null : vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP));
        setCpcCompareRate(vo.getAdCostPerClickCompareRate() == null ? null : vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setClickRate(vo.getCtr() == null ? null : vo.getCtr().setScale(2, RoundingMode.HALF_UP) + "%");
        setClickRateCompare(vo.getCtrCompare() == null ? null : vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP) + "%");
        setClickRateCompareRate(vo.getCtrCompareRate() == null ? null : vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setSalesConversionRate(vo.getCvr() == null ? null : vo.getCvr().setScale(2, RoundingMode.HALF_UP) + "%");
        setSalesConversionRateCompare(vo.getCvrCompare() == null ? null : vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP) + "%");
        setSalesConversionRateCompareRate(vo.getCvrCompareRate() == null ? null : vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setAcos(vo.getAcos() == null ? null : vo.getAcos().setScale(2, RoundingMode.HALF_UP) + "%");
        setAcosCompare(vo.getAcosCompare() == null ? null : vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP) + "%");
        setAcosCompareRate(vo.getAcosCompareRate() == null ? null : vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setRoas(vo.getRoas() == null ? null : vo.getRoas().setScale(2, RoundingMode.HALF_UP));
        setRoasCompare(vo.getRoasCompare() == null ? null : vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP));
        setRoasCompareRate(vo.getRoasCompareRate() == null ? null : vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setAdOrderNum(vo.getAdOrderNum());
        setAdOrderNumCompare(vo.getAdOrderNumCompare());
        setAdOrderNumCompareRate(vo.getAdOrderNumCompareRate() == null ? null : vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setSelfAdOrderNum(vo.getSelfAdOrderNum());
        setOtherAdOrderNum(vo.getOtherAdOrderNum());
        setAdSales(vo.getAdSale() == null ? currency + 0 : currency + vo.getAdSale().setScale(2, RoundingMode.HALF_UP));
        setAdSalesCompare(vo.getAdSaleCompare() == null ? currency + 0 : currency + vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP));
        setAdSalesCompareRate(vo.getAdSaleCompareRate() == null ? null : vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setAdSelfSales(vo.getAdSelfSale() == null ? currency + 0 : currency + vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP));
        setAdOtherSales(vo.getAdOtherSale() == null ? currency + 0 : currency + vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP));
        setAdSaleNum(vo.getAdSaleNum());
        setAdSaleNumCompare(vo.getAdSaleNumCompare());
        setAdSaleNumCompareRate(vo.getAdSaleNumCompareRate() == null ? null : vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP) + "%");
        setAdSelfSaleNum(vo.getAdSelfSaleNum());
        setAdOtherSaleNum(vo.getAdOtherSaleNum());
        // 花费占比
        setAdCostPercentage(vo.getAdCostPercentage() != null ? vo.getAdCostPercentage().toString() + "%" : "0.00%");
        // 销售额占比
        setAdSalePercentage(vo.getAdSalePercentage() != null ? vo.getAdSalePercentage().toString() + "%" : "0.00%");
        // 订单量占比
        setAdOrderNumPercentage(vo.getAdOrderNumPercentage() != null ? vo.getAdOrderNumPercentage().toString() + "%" : "0.00%");
        // 销量占比
        setOrderNumPercentage(vo.getOrderNumPercentage() != null ? vo.getOrderNumPercentage().toString() + "%" : "0.00%");

        setAcots(vo.getAcots() != null ? vo.getAcots().toString() + "%" : "0.00%");
        setAsots(vo.getAsots() != null ? vo.getAsots().toString() + "%" : "0.00%");

        setViewableImpressions(vo.getViewableImpressions());
        setVctr(vo.getVCtr() == null ? "0.00%" : vo.getVCtr().setScale(2, RoundingMode.HALF_UP) + "%");
        setVrt(vo.getVrt() == null ? "0.00%" : vo.getVrt().setScale(2, RoundingMode.HALF_UP) + "%");
        setVcpm(vo.getVcpm() == null ? null : vo.getVcpm().setScale(2, RoundingMode.HALF_UP).toString());
        setAdvertisingUnitPrice(vo.getAdvertisingUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP));
        setSalesNewToBrand(vo.getSalesNewToBrand() == null ? currency + 0 : currency + vo.getSalesNewToBrand().setScale(2, RoundingMode.HALF_UP));
        setUnitsOrderedNewToBrand(vo.getUnitsOrderedNewToBrand());
        setOrdersNewToBrand(vo.getOrdersNewToBrand());
        setSalesNewToBrandPercentage(vo.getSalesNewToBrandPercentage() == null ? null : vo.getSalesNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setUnitsOrderedNewToBrandPercentage(vo.getUnitsOrderedNewToBrandPercentage() == null ? null : vo.getUnitsOrderedNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setOrdersNewToBrandPercentage(vo.getOrdersNewToBrandPercentage() == null ? null : vo.getOrdersNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%");
        setAdvertisingOtherProductUnitPrice(vo.getAdvertisingOtherProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingOtherProductUnitPrice().setScale(2, RoundingMode.HALF_UP));
        setAdvertisingProductUnitPrice(vo.getAdvertisingProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingProductUnitPrice().setScale(2, RoundingMode.HALF_UP));
    }
}
