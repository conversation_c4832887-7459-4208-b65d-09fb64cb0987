package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdCampaignStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageParam;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.multiple.campagin.service.IMultipleCampaignService;
import com.meiyunji.sponsored.service.multiple.campagin.vo.MultipleCampaignPageVo;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import com.meiyunji.sponsored.service.vo.AdvertisingCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多店铺广告活动导出handler
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Service(AdManagePageExportTaskConstant.CAMPAIGN_MULTIPLE)
@Slf4j
public class CampaignMultiplePageExportTaskHandler implements AdManagePageExportTaskHandler {

    @Resource
    private IMultipleCampaignService multipleCampaignService;

    @Resource
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;

    @Resource
    private StringRedisService stringRedisService;

    @Resource
    private IExcelService excelService;

    @Override
    public void export(AdManagePageExportTask task) {
        long t = Instant.now().toEpochMilli();
        CampaignPageParam param = JSONUtil.jsonToObject(task.getParam(), CampaignPageParam.class);
        log.info("export campaign page taskId:{} params: {}", task.getId(), param);
        try {
            param.setPageNo(1);
            param.setPageSize(Constants.EXPORT_MAX_SIZE);
            Page<MultipleCampaignPageVo> page = multipleCampaignService.getAllCampaignData(param, true);
            if (CollectionUtils.isEmpty(page.getRows())) {
                // 导出数据为空
                adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
                // 修改状态，前端收到后转圈效果停止
                stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            } else {
                // 获取导出url
                List<String> urlList = getExportUrl(task, param, page);
                // 修改任务状态
                adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
                stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
                log.info("export campaign page finish taskId:{} urlList: {}", task.getId(), urlList);
            }
        } catch (Exception e) {
            log.error("campaign export error, task id : {}", task.getId(), e);
            String msg = "系统异常";
            if (e instanceof SponsoredBizException) {
                msg = ((SponsoredBizException) e).getMsg();
            }
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            // 修改状态，前端收到后转圈效果停止
            if (param != null) {
                stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, msg));
            }
        }
        log.info("export campaign page finish taskId:{} time: {}", task.getId(), (Instant.now().toEpochMilli() - t));
    }

    /**
     * 获取导出url
     */
    private List<String> getExportUrl(AdManagePageExportTask task, CampaignPageParam param, Page<MultipleCampaignPageVo> page) throws Exception {
        List<String> urlList = new ArrayList<>();
        // 文件名称
        String fileName = MultipleUtils.getExportShopName(param.getShopAuthList()) + "_广告活动" + "_" + param.getStartDate() + "_" + param.getEndDate();
        List<String> excludeFileds = Collections.emptyList();
        if (Constants.SD.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("topOfSearchImpressionShare");
        }
        List<AdvertisingCampaignVo> adverList = new LinkedList<>();
        for (MultipleCampaignPageVo vo : page.getRows()) {
            adverList.add(this.buildExportVo(vo));
        }
        if (StringUtils.isNotBlank(param.getExportSortField())) {
            // 自定义导出
            customColumnExport(task, param, adverList, urlList, fileName, excludeFileds);
        } else {
            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdvertisingCampaignVo.class);
            urlList.add(excelService.easyExcelHandlerExport(task.getPuid(), adverList, fileName, AdvertisingCampaignVo.class, build, excludeFileds));
        }
        return urlList;
    }

    /**
     * 自定义导出
     */
    private void customColumnExport(AdManagePageExportTask task, CampaignPageParam param, List<AdvertisingCampaignVo> adverList, List<String> urlList, String fileName, List<String> excludeFileds) throws IllegalAccessException, NoSuchFieldException {
        // Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        if (param.getFreezeNum() != null) {
            // 冻结前n列前1行
            build.freezeRowAndCol(param.getFreezeNum(), 1);
        }
        List<String> sortFields = new ArrayList<>(Arrays.asList(param.getExportSortField().split(",")));
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");
        List<String> headNames = new ArrayList<>(sortFields.size());
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingCampaignExportFieldEnum fieldEnum = AdvertisingCampaignExportFieldEnum.fromPoParamKey(sortFields.get(i));
            if (fieldEnum != null && fieldEnum.isCurrencyStyle()) {
                currencyIndex.add(i);
            }
            if (fieldEnum != null) {
                headNames.add(fieldEnum.getTableColName());
            }
        }
        // 非对象导出货币样式
        build.noModleHandler(currencyIndex);
        List<List<Object>> rows = new ArrayList<>(adverList.size());
        Class<AdvertisingCampaignVo> aClass = AdvertisingCampaignVo.class;
        for (AdvertisingCampaignVo vo : adverList) {
            List<Object> cols = new ArrayList<>(AdvertisingCampaignExportFieldEnum.values().length);
            for (String sortField : sortFields) {
                AdvertisingCampaignExportFieldEnum fieldEnum = AdvertisingCampaignExportFieldEnum.fromPoParamKey(sortField);
                if (fieldEnum == null) {
                    throw new SponsoredBizException("自定义导出包含非法字段！");
                }
                // 通过反射获取对应字段值
                Field field = aClass.getDeclaredField(fieldEnum.getVoName());
                field.setAccessible(Boolean.TRUE);
                cols.add(field.get(vo));
            }
            rows.add(cols);
        }
        urlList.add(excelService.exportByCustomColSort(task.getPuid(), headNames, rows, fileName, build));
    }

    /**
     * 列表vo转导出vo
     */
    private AdvertisingCampaignVo buildExportVo(MultipleCampaignPageVo vo) {
        //处理数据
        AdvertisingCampaignVo adverVo = new AdvertisingCampaignVo();
        adverVo.setShopName(vo.getShopName());
        adverVo.setServingStatusName(vo.getServingStatusName());
        adverVo.setAdOrderNum(Math.toIntExact(vo.getAdOrderNum()));
        adverVo.setImpressions(Math.toIntExact(vo.getImpressions()));
        adverVo.setTopOfSearchImpressionShare("-".equals(vo.getTopImpressionShare()) ? "-" : ReportParamUtil.getExportTopIS(vo.getTopImpressionShare()));
        adverVo.setClicks(Math.toIntExact(vo.getClicks()));
        adverVo.setName(vo.getName());
        adverVo.setPortfolioName(vo.getPortfolioName());
        //状态
        adverVo.setState(StateEnum.getStateValue(vo.getState()));
        // 获取站点币种
        String currency = AmznEndpoint.getByMarketplaceId(vo.getMarketplaceId()).getCurrencyCode().value();
        //每日预算
        adverVo.setDailyBudget(currency + vo.getDailyBudget());
        //点击率
        adverVo.setCtr(ExportStringUtil.modifyFormat(vo.getCtr()));
        //订单转换率
        adverVo.setCvr(ExportStringUtil.modifyFormat(vo.getCvr()));
        //ACoS
        adverVo.setAcos(ExportStringUtil.modifyFormat(vo.getAcos()));
        //ACoTS
        adverVo.setAcots(ExportStringUtil.modifyFormat(vo.getAcots()));
        adverVo.setRoas(vo.getRoas());
        //ASoTS
        adverVo.setAsots(ExportStringUtil.modifyFormat(vo.getAsots()));
        //广告花费（数据为空时展示为0.00%）
        adverVo.setAdCost(currency + ExportStringUtil.formatToNumber(vo.getAdCost()));
        //平均点击费用（数据为空时展示为0.00%）
        adverVo.setAdCostPerClick(currency + ExportStringUtil.getAdCostPerClick(vo.getAdCostPerClick()));
        //广告销售额（数据为空时展示为0.00%）
        adverVo.setAdSale(currency + ExportStringUtil.formatToNumber(vo.getAdSale()));
        // 花费占比
        adverVo.setAdCostPercentage("-".equals(vo.getAdCostPercentage()) ? "-" : ExportStringUtil.modifyFormat(vo.getAdCostPercentage()));
        // 销售额占比
        adverVo.setAdSalePercentage("-".equals(vo.getAdSalePercentage()) ? "-" : ExportStringUtil.modifyFormat(vo.getAdSalePercentage()));
        // 订单量占比
        adverVo.setAdOrderNumPercentage(ExportStringUtil.modifyFormat(vo.getAdOrderNumPercentage()));
        // 销量占比
        adverVo.setOrderNumPercentage(ExportStringUtil.modifyFormat(vo.getOrderNumPercentage()));
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(vo.getType())) {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.sponsoredBrands.getCampaignValue());
            //投放类型
            adverVo.setTargetingType(TargetingEnum.manual.getTargetingValue());
        } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(vo.getType())) {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.sponsoredDisplay.getCampaignValue());
            //投放类型
            adverVo.setTargetingType(TargetingEnum.manual.getTargetingValue());
        } else {
            //推广类型
            adverVo.setCampaignType(CampaignEnum.getCampaignValue(vo.getCampaignType()));
            //投放类型
            adverVo.setTargetingType(TargetingEnum.getTargetingValue(vo.getTargetingType()));
        }
        //广告活动竞价策略
        adverVo.setStrategy(StrategyEnum.getStrategyValue(vo.getStrategy()));
        //搜索结果顶部(首页)广告位
        adverVo.setPlacementTop(ExportStringUtil.getPlacementProductPage(vo.getPlacementTop()));
        //产品页面广告位
        adverVo.setPlacementProductPage(ExportStringUtil.getPlacementProductPage(vo.getPlacementProductPage()));
        //亚马逊企业购广告位
        adverVo.setPlacementSiteAmazonBusiness(ExportStringUtil.getPlacementProductPage(vo.getPlacementSiteAmazonBusiness()));
        //其他搜索位置
        adverVo.setPlacementRestOfSearch(ExportStringUtil.getPlacementRestOfSearchPage(vo.getPlacementRestOfSearch()));
        adverVo.setStartDate(vo.getStartDate());
        adverVo.setEndDate(ExportStringUtil.getDateState(vo.getEndDate()));
        adverVo.setCreator(vo.getCreator());
        adverVo.setViewImpressions(Math.toIntExact(vo.getViewImpressions()));
        adverVo.setCpa(currency + ExportStringUtil.formatToNumber(vo.getCpa()));
        adverVo.setVcpm("-".equals(vo.getVcpm()) ? "-" : currency + ExportStringUtil.formatToNumber(vo.getVcpm()));
        //本广告产品订单量
        adverVo.setAdSaleNum(Math.toIntExact(vo.getAdSaleNum()));
        //其他广告产品订单量
        adverVo.setAdOtherOrderNum(Math.toIntExact(vo.getAdOtherOrderNum()));
        //本广告产品销售额
        adverVo.setAdSales(currency + ExportStringUtil.formatToNumber(vo.getAdSales()));
        //其他广告产品销售额
        adverVo.setAdOtherSales(currency + ExportStringUtil.formatToNumber(vo.getAdOtherSales()));
        //广告销量
        adverVo.setOrderNum(Math.toIntExact(vo.getOrderNum()));
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(vo.getType())) {
            //本广告产品销量
            adverVo.setAdSelfSaleNum("0");
            //其他广告产品销量
            adverVo.setAdOtherSaleNum("0");
        } else if (CampaignTypeEnum.sd.getCampaignType().equalsIgnoreCase(vo.getType())) {
            //本广告产品销量
            adverVo.setAdSelfSaleNum("-");
            //其他广告产品销量
            adverVo.setAdOtherSaleNum("-");
        } else {
            //本广告产品销量
            adverVo.setAdSelfSaleNum(String.valueOf(vo.getAdSelfSaleNum()));
            //其他广告产品销量
            adverVo.setAdOtherSaleNum(String.valueOf(vo.getAdOtherSaleNum()));
        }
        adverVo.setOrdersNewToBrand14d(Math.toIntExact(vo.getOrdersNewToBrandFTD()));
        adverVo.setOrderRateNewToBrand14d(vo.getOrderRateNewToBrandFTD());
        adverVo.setSalesNewToBrand14d(currency + ExportStringUtil.formatToNumber(vo.getSalesNewToBrandFTD()));
        adverVo.setSalesRateNewToBrand14d(vo.getSalesRateNewToBrandFTD());
        adverVo.setUnitsOrderedNewToBrand14d(Math.toIntExact(vo.getUnitsOrderedNewToBrandFTD()));
        adverVo.setUnitsOrderedRateNewToBrand14d(vo.getUnitsOrderedRateNewToBrandFTD());
        adverVo.setNewToBrandDetailPageViews(vo.getNewToBrandDetailPageViews());
        adverVo.setAddToCart(vo.getAddToCart());
        adverVo.setAddToCartRate(ExportStringUtil.modifyFormat(vo.getAddToCartRate()));
        adverVo.setEcpAddToCart(currency + ExportStringUtil.formatToNumber(vo.getECPAddToCart()));
        adverVo.setVideo5SecondViews(vo.getVideo5SecondViews());
        adverVo.setVideo5SecondViewRate(ExportStringUtil.modifyFormat(vo.getVideo5SecondViewRate()));
        adverVo.setVideoFirstQuartileViews(vo.getVideoFirstQuartileViews());
        adverVo.setVideoMidpointViews(vo.getVideoMidpointViews());
        adverVo.setVideoThirdQuartileViews(vo.getVideoThirdQuartileViews());
        adverVo.setVideoCompleteViews(vo.getVideoCompleteViews());
        adverVo.setVideoUnmutes(vo.getVideoUnmutes());
        adverVo.setViewabilityRate(ExportStringUtil.modifyFormat(vo.getViewabilityRate()));
        adverVo.setViewClickThroughRate(ExportStringUtil.modifyFormat(vo.getViewClickThroughRate()));
        adverVo.setBrandedSearches(vo.getBrandedSearches());
        adverVo.setDetailPageViews(vo.getDetailPageViews());
        adverVo.setCumulativeReach(vo.getCumulativeReach());
        adverVo.setImpressionsFrequencyAverage(ExportStringUtil.formatToNumber(vo.getImpressionsFrequencyAverage()));
        adverVo.setAdvertisingUnitPrice(currency + ExportStringUtil.formatToNumber(vo.getAdvertisingUnitPrice()));
        if (CollectionUtils.isNotEmpty(vo.getAdTags())) {
            List<AdTag> adTagList = vo.getAdTags();
            List<String> nameList = adTagList.stream().map(AdTag::getName).collect(Collectors.toList());
            adverVo.setAdTag(String.join(",", nameList));
        } else {
            adverVo.setAdTag("");
        }
        if (CollectionUtils.isNotEmpty(vo.getStrategyList())) {
            List<AdStrategyVo> adStrategyList = vo.getStrategyList();
            List<String> nameList = adStrategyList.stream().map(it-> AdCampaignStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
            adverVo.setAdStrategyTag(String.join(",", nameList));
        } else {
            adverVo.setAdStrategyTag("");
        }
        adverVo.setCostType(vo.getCostType());
        return adverVo;
    }
}
