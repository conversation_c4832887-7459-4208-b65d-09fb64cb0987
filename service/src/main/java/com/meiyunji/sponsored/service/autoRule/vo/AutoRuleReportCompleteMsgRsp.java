package com.meiyunji.sponsored.service.autoRule.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/2/6 9:33
 * @describe:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AutoRuleReportCompleteMsgRsp {
    private Integer puid;
    private List<CompleteMsgUserVO> userVOList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CompleteMsgUserVO {
        private Integer shopId;
        private Integer uid;;
    }
}
