package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@DbTable("ods_t_amazon_ad_budget_usage_day")
public class OdsAmazonAdBudgetUsageDay implements Serializable {

    @DbColumn("puid")
    private Integer puid;
    @DbColumn("shop_id")
    private Integer shopId;

    @DbColumn("seller_id")
    private String sellerId;

    @DbColumn("marketplace_id")
    private String marketplaceId;
    /**
     * 活动Id
     */
    @DbColumn("budget_scope_id")
    private String budgetScopeId;
    /**
     * 预算类型 只有活动
     */
    @DbColumn("budget_scope_type")
    private String budgetScopeType;
    /**
     * sp,sb,sd 类型
     */
    @DbColumn("advertising_product_type")
    private String advertisingProductType;
    /**
     * 预算
     */
    @DbColumn("budget")
    private BigDecimal budget;
    /**
     * 预算使用的百分比
     */
    @DbColumn("budget_usage_percentage")
    private BigDecimal budgetUsagePercentage;
    /**
     * 预算花费
     */
    @DbColumn("budget_usage")
    private BigDecimal budgetUsage;
    /**
     * 剩余预算
     */
    @DbColumn("budget_remaining")
    private BigDecimal budgetRemaining;
    /**
     * 预算使用时间
     */
    @DbColumn("budget_time")
    private BigDecimal budgetTime;
    /**
     * 超预算使用时间
     */
    @DbColumn("over_budget_time")
    private BigDecimal overBudgetTime;
    /**
     * 总共预算时间
     */
    @DbColumn("total_budget_time")
    private BigDecimal totalBudgetTime;
    /**
     * 更新日期(站点)
     */
    @DbColumn("usage_updated_site_date")
    private LocalDate usageUpdatedSiteDate;
    /**
     * 更新时间(北京)
     */
    @DbColumn("usage_updated_timestamp")
    private LocalDateTime usageUpdatedTimestamp;
    /**
     * 调整次数
     */
    @DbColumn("budget_adjustment_num")
    private Integer budgetAdjustmentNum;
    /**
     * 预算调整时间
     */
    @DbColumn("budget_adjustment_time")
    private String budgetAdjustmentTime;
    /**
     * 币种
     */
    @DbColumn("currency")
    private String currency;
    /**
     * 月份 yyyyMM 数字
     */
    @DbColumn("site_month")
    private Integer siteMonth;

}
