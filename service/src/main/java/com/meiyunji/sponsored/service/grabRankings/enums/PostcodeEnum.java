package com.meiyunji.sponsored.service.grabRankings.enums;




import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/08/19
 */
public enum PostcodeEnum {

    US_Alabama("US", "Alabama", "Montgomery", "AL", "12345"),

    US_Alaska("US", "Alaska", "Juneau", "AK", "36101"),

    US_Arizona("US", "Arizona", "Phoenix", "AZ", "99801"),

    US_Arkansas("US", "Arkansas", "Little Rock", "AR", "72223"),

    US_California("US", "California", "Sacramento", "CA", "94203"),

    US_Colorado("US", "Colorado", "Denver", "CO", "80201"),

    US_Connecticut("US", "Connecticut", "Hartford", "CT", "06101"),

    US_Delaware("US", "Delaware", "Dover", "DE", "19901"),

    US_Florida("US", "Florida", "Tallahassee", "FL", "32301"),

    US_Georgia("US", "Georgia", "Atlanta", "GA", "30301"),

    US_Hawaii("US", "Hawaii", "Honolulu", "HI", "96801"),

    US_Idaho("US", "Idaho", "Boise", "ID", "83701"),

    US_Illinois("US", "Illinois", "Springfield", "ID", "62701"),

    US_Indiana("US", "Indiana", "Indianapolis", "IN", "46201"),

    US_Iowa("US", "Iowa", "Des Moines", "IA", "50226"),

    US_Kansas("US", "Kansas", "Topeka", "KS", "66601"),

    US_Kentucky("US", "Kentucky", "Frankfort", "KY", "40601"),

    US_Louisiana("US", "Louisiana", "Baton Rouge", "LA", "70801"),

    US_Maine("US", "Maine", "Augusta", "ME", "04330"),

    US_Maryland("US", "Maryland", "Annapolis", "MD", "21401"),

    US_Massachusetts("US", "Massachusetts", "Boston", "MA", "02108"),

    US_Michigan("US", "Michigan", "Boston", "MI", "48901"),

    US_Minnesota("US", "Minnesota", "Saint Paul", "MN", "55101"),

    US_Mississippi("US", "Mississippi", "Jackson", "MS", "39201"),

    US_Missouri("US", "Missouri", "Jefferson City", "MO", "65101"),


    US_Montana("US", "Montana", "Helena", "MT", "59601"),

    US_Nebraska("US", "Nebraska", "Lincoln", "Nebraska", "68501"),

    US_Nevada("US", "Nevada", "Carson City", "NV", "89701"),

    US_NewHampshire("US", "New Hampshire", "Merrimack County", "NH", "03301"),

    US_NewJersey("US", "New Jersey", "Mercer County", "NJ", "08601"),

    US_NewMexico("US", "NewMexico", "Santa Fe", "NM", "87501"),


    US_NewYork("US", "New York", "Albany", "NY", "12201"),

    US_NorthCarolina("US", "North Carolina", "Raleigh", "NC", "27601"),

    US_NorthDakota("US", "North Dakota", "Bismarck", "ND", "58501"),

    US_Ohio("US", "Ohio", "Columbus", "OH", "43201"),


    US_Oklahoma("US", "Oklahoma", "Oklahoma City", "OK", "73101"),

    US_Oregon("US", "Oregon", "Salem", "OR", "97301"),

    US_Pennsylvania("US", "Pennsylvania", "Harrisburg", "PA", "17101"),

    US_RhodeIsland("US", "Rhode Island", "Providence", "RI", "02901"),

    US_SouthCarolina("US", "South Carolina", "Richland County", "SC", "29202"),

    US_SouthDakota("US", "South Dakota", "Pierre", "SD", "57501"),

    US_Tennessee("US", "Tennessee", "Nashville", "TN", "37201"),

    US_Texas("US", "Texas", "Austin", "TX", "37201"),

    US_Utah("US", "Utah", "Salt Lake City", "UT", "84101"),

    US_Vermont("US", "Vermont", "Montpelier", "VT", "05601"),

    US_Virginia("US", "Virginia", "Richmond", "VA", "23218"),

    US_Washington("US", "Washington", "Olympia", "WA", "98501"),

    US_WestVirginia("US", "West Virginia", "Charleston", "WA", "25301"),

    US_Wisconsin("US", "Wisconsin", "Madison", "WI", "53701"),

    US_Wyoming("US", "Wyoming", "Cheyenne", "WY", "82001"),

    US_DistrictofColumbia("US", "DistrictofColumbia", "District of Columbia", "DC", "04623"),

    US_PuertoRico("US", "PuertoRico", "Puerto Rico", "PuertoRico", "00985"),


    CA_Ontario("CA", "Ontario", "Toronto", "Ontario", "M3C 0C1"),

    CA_BritishColumbia("CA", "British Columbia", "Vancouver", "British Columbia", "V5K 0A1"),

    CA_Alberta("CA", "Alberta", "Edmonton", "Alberta", "T2G 1S6"),

    CA_Québec("CA", "Québec", "Montréal", "Québec", "H1A 0A1"),

    CA_NovaScotia("CA", "Nova Scotia", "Halifax", "NovaScotia", "B3H 0A1"),

    CA_Manitoba("CA", "Manitoba", "Winnipeg", "Manitoba", "R2W 1T9"),

    CA_Saskatchewan("CA", "Saskatchewan", "Saskatoon", "Saskatchewan", "S7H 0A1"),

    CA_NewBrunswick("CA", "NewBrunswick", "Saint John", "NewBrunswick", "E2E 0S9"),

    CA_OTHER("CA", "OTHER", "Toronto", "OTHER", "M3C 0C1"),

    CA_PrinceEdwardIsland("CA", "Prince Edward Island", "Toronto", "PrinceEdwardIsland", "A1A 1A1"),

    CA_Nunavut("CA", "Nunavut", "Nunavut Territory", "Nunavut", "X0B 2A0"),

    CA_NorthwestTerritories("CA", "Northwest Territories", "Aklavik", "NorthwestTerritories", "X0E 0A0"),

    CA_YukonTerritory("CA", "Yukon Territory", "Whitehorse", "YukonTerritory", "Y1A 3N5"),


    MX_OTHER("MX", "Aguascalientes", "Aguascalientes", "Aguascalientes", "20234"),

    MX_BajaCalifornia("MX", "BajaCalifornia", "Mexicali", "BajaCalifornia", "21396"),

    MX_BajaCaliforniaSur("MX", "BajaCaliforniaSur", "La Paz", "BajaCaliforniaSur", "23077"),

    MX_Campeche("MX", "Campeche", "Campeche", "Campeche", "24095"),

    MX_Chiapas("MX", "Chiapas", "Tuxtla Gutiérrez", "Chiapas", "29149"),

    MX_Chihuahua("MX", "Chihuahua", "Chihuahua", "Chihuahua", "32380"),

    MX_Coahuila("MX", "Coahuila", "Saltillo", "Coahuila", "25014"),

    MX_Colima("MX", "Colima", "Colima", "Colima", "28466"),

    MX_Durango("MX", "Durango", "Durango", "Durango", "34986"),

    MX_Guanajuato("MX", "Guanajuato", "Guanajuato", "Guanajuato", "38010"),

    MX_Guerrero("MX", "Guerrero", "Chilpancingo", "Guerrero", "39014"),

    MX_Hidalgo("MX", "Hidalgo", "Pachuca", "Hidalgo", "42039"),

    MX_Jalisco("MX", "Jalisco", "Guadalajara", "Jalisco", "44970"),

    MX_México("MX", "México", "Toluca", "México", "50071"),

    MX_Michoacán("MX", "Michoacán", "Morelia", "Michoacán", "58096"),

    MX_Morelos("MX", "Morelos", "Cuernavaca", "Morelos", "62507"),

    MX_Nayarit("MX", "Nayarit", "Tepic", "Nayarit", "63170"),

    MX_NuevoLeón("MX", "NuevoLeón", "Monterrey", "NuevoLeón", "64220"),

    MX_Oaxaca("MX", "Oaxaca", "Oaxaca", "Oaxaca", "68403"),

    MX_Puebla("MX", "Puebla", "Puebla", "Puebla", "72270"),

    MX_Querétaro("MX", "Querétaro", "Querétaro", "Querétaro", "76118"),

    MX_QuintanaRoo("MX", "QuintanaRoo", "Chetumal", "QuintanaRoo", "77048"),

    MX_SanLuisPotosí("MX", "SanLuisPotosí", "San Luis Potosí", "SanLuisPotosí", "78389"),

    MX_Sinaloa("MX", "Sinaloa", "Culiacán", "Sinaloa", "80139"),

    MX_Sonora("MX", "Sonora", "Hermosillo", "Sonora", "83334"),

    MX_Tabasco("MX", "Tabasco", "Villahermosa", "Tabasco", "86000"),

    MX_Tamaulipas("MX", "Tamaulipas", "Victoria", "Tamaulipas", "87090"),

    MX_Tlaxcala("MX", "Tlaxcala", "Tlaxcala", "Tlaxcala", "90115"),

    MX_Veracruz("MX", "Veracruz", "Jalapa", "Veracruz", "91000"),

    MX_Yucatán("MX", "Yucatán", "Mérida", "Yucatán", "97203"),

    MX_Zacatecas("MX", "Zacatecas", "Zacatecas", "Zacatecas", "98000"),

    MX_DistritoFederal("MX", "DistritoFederal", "Mexicali", "DistritoFederal", "21830"),


    FR_Zacatecas("FR", "Auvergne-Rhône-Alpes", "Lyon", "Auvergne-Rhône-Alpes", "69000"),

    FR_BourgogneFrancheComté("FR", "Bourgogne-Franche-Comté", "Dijon", "Bourgogne-Franche-Comté", "21000"),

    FR_Bretagne("FR", "Bretagne", "Rennes", "Bretagne", "35000"),

    FR_CentreValdeLoire("FR", "Centre-ValdeLoire", "Tours", "Centre-ValdeLoire", "37000"),

    FR_Corse("FR", "Corse", "Corse-du-Sud", "Corse", "20167"),

    FR_GrandEst("FR", "Grand Est", "Mulhouse", "Grand Est", "68050"),

    FR_HautsdeFrance("FR", "Hauts-de-France", "Lille", "Hauts-de-France", "59000"),

    FR_ÎledeFrance("FR", "Île-de-France", "Paris", "Île-de-France", "75000"),

    FR_Normandie("FR", "Normandiee", "Calvados", "Normandie", "14600"),

    FR_NouvelleAquitaine("FR", "Nouvelle-Aquitaine", "Bordeaux", "Nouvelle-Aquitaine", "33000"),

    FR_Occitanie("FR", "Occitanie", "Albi", "Occitanie", "81000"),


    FR_PaysdelaLoire("FR", "PaysdelaLoire", "Nantes", "PaysdelaLoire", "44000"),

    FR_ProvenceAlpesCôtedAzur("FR", "Provence-Alpes-Côted'Azur", "Marseille", "Provence-Alpes-Côted'Azur", "13000"),

    DE_BadenWürttemberg("DE", "Baden-Württemberg", "Stuttgart", "Baden-Württemberg", "70173"),

    DE_Bayern("DE", "Bayern", "München", "Bayern", "80331"),

    DE_Berlin("DE", "Berlin", "Berlin", "Berlin", "10115"),

    DE_Brandenburg("DE", "Brandenburg", "Potsdam", "Brandenburg", "14467"),

    DE_Bremen("DE", "Bremen", "Bremen", "Bremen", "28195"),

    DE_Hamburg("DE", "Hamburg", "Hamburg", "Hamburg", "20095"),

    DE_Hessen("DE", "Hessen", "Wiesbaden", "Hessen", "65183"),

    DE_MecklenburgVorpommern("DE", "Mecklenburg-Vorpommern", "Schwerin", "Mecklenburg-Vorpommern", "19053"),

    DE_Niedersachsen("DE", "Niedersachsen", "Hannover", "Niedersachsen", "30159"),

    DE_NordrheinWestfalen("DE", "Nordrhein-Westfalen", "Düsseldorf", "Nordrhein-Westfalen", "40210"),

    DE_RheinlandPfalz("DE", "Rheinland-Pfalz", "Mainz", "Rheinland-Pfalz", "55116"),

    DE_Saarland("DE", "Saarland", "Saarbrücken", "Saarland", "66111"),

    DE_Sachsen("DE", "Sachsen", "Dresden", "Sachsen", "01067"),

    DE_SachsenAnhalt("DE", "Sachsen-Anhalt", "Magdeburg", "Sachsen-Anhalt", "39106"),

    DE_SchleswigHolstein("DE", "Schleswig-Holstein", "Kiel", "Schleswig-Holstein", "24103"),

    DE_Thüringen("DE", "Thüringen", "Erfurt", "Thüringen", "99084"),

    IT_Abruzzo("IT", "Abruzzo", "L'Aquila", "Abruzzo", "67100"),

    IT_Apulia("IT", "Apulia", "Bari", "Apulia", "70100"),

    IT_Basilicata("IT", "Basilicata", "Potenza", "Basilicata", "85100"),

    IT_Calabria("IT", "Calabria", "Catanzaro", "Calabria", "88100"),

    IT_Campania("IT", "Campania", "Napoli", "Campania", "80100"),

    IT_EmiliaRomagna("IT", "Emilia-Romagna", "Bologna", "Emilia-Romagna", "40121"),

    IT_FriuliVeneziaGiulia("IT", "Friuli-VeneziaGiulia", "Trieste", "Friuli-VeneziaGiulia", "34014"),

    IT_Lazio("IT", "Lazio", "Roma", "Lazio", "00118"),

    IT_Liguria("IT", "Liguria", "Genova", "Liguria", "16121"),

    IT_Lombardia("IT", "Lombardia", "Milano", "Lombardia", "20121"),

    IT_Marche("IT", "Marche", "Ancona", "Marche", "60121"),

    IT_Molise("IT", "Molise", "Campobasso", "Molise", "86100"),

    IT_Piemonte("IT", "Piemonte", "Torino", "Piemonte", "10121"),

    IT_Sardegna("IT", "Sardegna", "Cagliari", "Sardegna", "09121"),

    IT_Sicilia("IT", "Sicilia", "Palermo", "Sicilia", "90121"),

    IT_Toscana("IT", "Toscana", "Firenze", "Toscana", "50121"),

    IT_TrentinoAltoAdige("IT", "Trentino-AltoAdige", "Trento", "Trentino-AltoAdige", "38121"),

    IT_Umbria("IT", "Umbria", "Perugia", "Umbria", "06121"),

    IT_ValledAosta("IT", "Valled'Aosta", "Aosta", "Valled'Aosta", "11100"),

    IT_Veneto("IT", "Veneto", "Venezia", "Veneto", "30121"),


    ES_Andalucía("ES", "Andalucía", "Sevilla", "Andalucía", "41001"),

    ES_Aragón("ES", "Aragón", "Zaragoza", "Aragón", "50001"),

    ES_Cantabria("ES", "Cantabria", "Santander", "Cantabria", "39001"),

    ES_CastillaLaMancha("ES", "Castilla-LaMancha", "Toledo", "Castilla-LaMancha", "45001"),

    ES_CastillayLeón("ES", "CastillayLeón", "Valladolid", "CastillayLeón", "47001"),

    ES_Cataluña("ES", "Cataluña", "Barcelona", "Cataluña", "08001"),

    ES_CeutayMelilla("ES", "CeutayMelilla", "Melilla", "CeutayMelilla", "52001"),

    ES_ComunidaddeMadrid("ES", "ComunidaddeMadrid", "Madrid", "ComunidaddeMadrid", "28001"),

    ES_ComunidadForaldeNavarrad("ES", "ComunidadForaldeNavarra", "Pamplona", "ComunidadForaldeNavarra", "31001"),

    ES_ComunidadValenciana("ES", "Comunidad Valenciana", "Valencia", "Comunidad Valenciana", "46001"),

    ES_Extremadura("ES", "Extremadura", "Merida", "Extremadura", "06800"),

    ES_Galicia("ES", "Galicia", "Santiago de Compostela", "Galicia", "15701"),

    ES_IslasBaleares("ES", "Islas Baleares", "Palma De Mallorca", "Islas Baleares", "07001"),

    ES_IslasCanarias("ES", "Islas Canarias", "Lomo Blanco (Las Palmas)", "Islas Canarias", "35017"),

    ES_RLaRiojaI("ES", "RLaRiojaI", "Logroño", "RLaRiojaI", "26001"),

    ES_PaísVasco("ES", "PaísVasco", "Vitoria-Gasteiz", "PaísVasco", "01001"),

    ES_PrincipadodeAsturias("ES", "PrincipadodeAsturias", "Abedul (Oviedo)", "PrincipadodeAsturias", "33919"),

    ES_RegióndeMurcia("ES", "RegióndeMurcia", "Murcia", "RegióndeMurcia", "30001"),

    ;

    private String marketplace;

    private String state;

    private String city;

    private String sort;

    private String postcode;

    private static final Map<String, List<PostcodeEnum>> postcodeByMarketplace = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.groupingBy(PostcodeEnum::getMarketplace)));

    public String getMarketplace() {
        return marketplace;
    }

    public String getState() {
        return state;
    }

    public String getCity() {
        return city;
    }

    public String getSort() {
        return sort;
    }

    public String getPostcode() {
        return postcode;
    }

    PostcodeEnum(String marketplace, String state, String city, String sort, String postcode) {
        this.marketplace = marketplace;
        this.state = state;
        this.city = city;
        this.sort = sort;
        this.postcode = postcode;
    }

    public static List<PostcodeEnum> getPostcodeByMarketplace(String marketplace) {
        return postcodeByMarketplace.get(marketplace);
    }

}
