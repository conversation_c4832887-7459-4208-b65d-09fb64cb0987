package com.meiyunji.sponsored.service.cpc.vo;

import com.alibaba.fastjson.JSONObject;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.enums.SDCreateErrorEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: ys
 * @date: 2024/9/12 16:23
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SDCommonErrorVo {
    private String field;
    private String msg;

    public static String getErrorListByRawWithField(String field, String errorInfoRaw) {
        //先从枚举中获取对应的信息
        //再讲异常信息放入list中进行返回
        SDCreateErrorEnum en;
        if (Objects.nonNull(en= SDCreateErrorEnum.getSDCreateErrorEnumByMsg(errorInfoRaw))) {
            SDCommonErrorVo vo = SDCommonErrorVo.builder().field(en.getField()).msg(en.getTranslatedCn()).build();
            return JSONObject.toJSONString(Collections.singletonList(vo));
        }
        return JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.builder()
                .field(Optional.ofNullable(field).orElse("-")).msg(errorInfoRaw).build()));
    }

    public static String getErrorListByRaw(String errorInfoRaw) {
        //先从枚举中获取对应的信息
        //再讲异常信息放入list中进行返回
        SDCreateErrorEnum en;
        if (Objects.nonNull(en= SDCreateErrorEnum.getSDCreateErrorEnumByMsg(errorInfoRaw))) {
            SDCommonErrorVo vo = SDCommonErrorVo.builder().field(en.getField()).msg(en.getTranslatedCn()).build();
            return JSONObject.toJSONString(Collections.singletonList(vo));
        }
        return JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.builder().field("-").msg(errorInfoRaw).build()));
    }

    public static SDCommonErrorVo getErrorVo(String errorInfoRaw) {
        SDCreateErrorEnum en;
        if (Objects.nonNull(en= SDCreateErrorEnum.getSDCreateErrorEnumByMsg(errorInfoRaw))) {
            return SDCommonErrorVo.builder().field(en.getField()).msg(en.getTranslatedCn()).build();
        }
        return SDCommonErrorVo.builder().field("-").msg(errorInfoRaw).build();
    }

    public static SDCommonErrorVo getErrorVo(String field, String errorInfoRaw) {
        SDCreateErrorEnum en;
        if (StringUtils.isEmpty(errorInfoRaw)) {
            return SDCommonErrorVo.builder().field(Optional.ofNullable(field).orElse("-")).msg(SBCreateErrorEnum.UNKNOWN_ERROR.getMsg()).build();
        }
        SDCreateErrorEnum errorEn = SDCreateErrorEnum.getEnByMsgNumberContains(errorInfoRaw);
        if (Objects.nonNull(errorEn)) {
            //开始获取异常信息中的对应数字
            //获取浮点型
            String pattern = "[0-9]*(\\.[0-9]{1,2})|[0-9]*";
            Pattern p = Pattern.compile(pattern);
            Matcher m = p.matcher(errorInfoRaw);
            List<Float> replaceStr = new ArrayList<>(3);
            while(m.find()) {
                if (StringUtils.isNotEmpty(m.group())) {
                    BigDecimal match = new BigDecimal(m.group());
                    replaceStr.add(match.floatValue());
                }
            }
            if (CollectionUtils.isNotEmpty(replaceStr)) {
                return SDCommonErrorVo.builder().field(Optional.ofNullable(field).orElse("-"))
                        .msg(MessageFormat.format(errorEn.getTranslatedCn(), replaceStr.toArray())).build();
            }
        }
        if (Objects.nonNull(en= SDCreateErrorEnum.getSDCreateErrorEnumByMsg(errorInfoRaw))) {
            return SDCommonErrorVo.builder().field(Optional.ofNullable(field).orElse("-")).msg(en.getTranslatedCn()).build();
        }
        return SDCommonErrorVo.builder().field(Optional.ofNullable(field).orElse("-")).msg(errorInfoRaw).build();
    }

    public static SDCommonErrorVo getAssetErrorVo(String errorInfoRaw, List<String> args) {
        if (StringUtils.isEmpty(errorInfoRaw)) {
            return SDCommonErrorVo.builder().field("-").msg(SDCreateErrorEnum.UNKNOWN_ERROR.getMsg()).build();
        }
        SDCreateErrorEnum errorEn = SDCreateErrorEnum.getEnByMsgContains(errorInfoRaw);
        if (Objects.nonNull(errorEn)) {
            SDCommonErrorVo vo = SDCommonErrorVo.builder().field(Optional.ofNullable(errorEn.getField()).orElse("-")).build();
            if (!args.isEmpty()) {
                vo.setMsg(MessageFormat.format(errorEn.getTranslatedCn(), args.toArray()));
            } else {
                vo.setMsg(errorEn.getTranslatedCn());
            }
            return vo;
        }
        return SDCommonErrorVo.builder().field("-").msg(errorInfoRaw).build();
    }
}
