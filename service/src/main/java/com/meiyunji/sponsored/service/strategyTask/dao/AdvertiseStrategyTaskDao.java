package com.meiyunji.sponsored.service.strategyTask.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.vo.QueryTaskHourglassParam;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:35
 */
public interface AdvertiseStrategyTaskDao extends IBaseShardingDao<AdvertiseStrategyTask> {

    List<AdvertiseStrategyTask> queryListByTemplateId(QueryTaskHourglassParam param);

    List<AdvertiseStrategyTask> queryListByTemplateIdAsc(Integer puid, Integer shopId, Long templateId);

    Integer queryCountByTemplateId(int puid, int shopId, Long templateId, Integer taskAction);

    Integer queryCountByTemplateId(int puid, Long templateId);

    int updateStateByPrimaryKey(int puid, AdvertiseStrategyTask record);

    int batchInsert(int puid, List<AdvertiseStrategyTask> list);

    List<AdvertiseStrategyTask> queryListByTemplateIdList(Integer puid, List<Long> templateIdList);

    List<AdvertiseStrategyTask> queryListByTemplateIdList(Integer puid, List<Integer> shopIdList, List<Long> templateIdList);

    List<AdvertiseStrategyTask> queryListByTemplateIdList(Integer puid, Integer shopId, Long templateId, Long taskId);

    void deleteByShop(Integer puid, Integer shopId);

    AdvertiseStrategyTask queryTaskById(Integer puid, Long id);

    Integer queryNeedRunTaskCount(Integer puid, Integer shopId, Long templateId, Long taskId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
