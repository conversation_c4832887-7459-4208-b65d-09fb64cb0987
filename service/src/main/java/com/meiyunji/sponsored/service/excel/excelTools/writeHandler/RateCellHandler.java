package com.meiyunji.sponsored.service.excel.excelTools.writeHandler;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.meiyunji.sponsored.common.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/4 15:13
 */
public class RateCellHandler implements CellWriteHandler {

    /**
     * 复用单元格样式，workBook创建单元格样式超过64000 会报错
     */
    private CellStyle rateStyle = null;
    /**
     * 转换百分比单元格样式后，原百分比数需除以100
     */
    private static final BigDecimal DENOMINATOR = new BigDecimal("100");

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer integer, Integer integer1, Boolean isHead) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer integer, Boolean isHead) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer integer, Boolean isHead) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> list, Cell cell, Head head, Integer integer, Boolean isHead) {
        //对带百分数的单元格进行样式写入
        if (!isHead && CellType.STRING.equals(cell.getCellTypeEnum())) {
            String val = cell.getStringCellValue();
            if (null != val) {
                String trim = val.trim();
                if (trim.endsWith("%")) {
                    String valStr = trim.substring(0,trim.length() -1).trim();
                    if (StringUtils.isNotBlank(valStr) && StringUtil.isNumber(valStr)) {
                        if (rateStyle == null) {
                            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                            DataFormat format = workbook.createDataFormat();
                            rateStyle = workbook.createCellStyle();
                            rateStyle.setDataFormat(format.getFormat("0.00%"));
                            rateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                        }
                        cell.setCellStyle(rateStyle);
                        Double value = new BigDecimal(valStr).divide(DENOMINATOR,BigDecimal.ROUND_HALF_UP, RoundingMode.HALF_UP).doubleValue();
                        cell.setCellValue(value);
                    }
                }
            }
        }
    }
}
