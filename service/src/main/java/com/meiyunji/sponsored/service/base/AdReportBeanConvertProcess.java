package com.meiyunji.sponsored.service.base;

import com.google.protobuf.BoolValue;
import com.meiyunji.sponsored.rpc.vo.ReportDataRpcVo;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.Optional;

public class AdReportBeanConvertProcess {
    public static ReportVo convertRpcVoToReportVo(ReportRpcVo rpcVo) {
        ReportVo vo = new ReportVo();
        vo.setCampaignStartDate(rpcVo.hasCampaignStartDate() ? rpcVo.getCampaignStartDate() : null);
        vo.setCampaignEndDate(rpcVo.hasCampaignEndDate() ? rpcVo.getCampaignEndDate() : null);
        vo.setState(rpcVo.hasState() ? rpcVo.getState() : null);
        vo.setShopId(rpcVo.hasShopId() ? rpcVo.getShopId().getValue() : null);
        vo.setCountDate(rpcVo.getCountDate());
        vo.setCpc(BigDecimal.valueOf(rpcVo.getCpc().getValue()));
        vo.setCost(BigDecimal.valueOf(rpcVo.getCost().getValue()));
        vo.setSales(BigDecimal.valueOf(rpcVo.getSales().getValue()));
        vo.setAcos(BigDecimal.valueOf(rpcVo.getAcos().getValue()));
        vo.setRoas(BigDecimal.valueOf(rpcVo.getRoas().getValue()));
        vo.setAsots(BigDecimal.valueOf(rpcVo.getAsots().getValue()));
        vo.setAcots(BigDecimal.valueOf(rpcVo.getAcots().getValue()));
        vo.setImpressions(rpcVo.getImpressions().getValue());
        vo.setClicks(rpcVo.getClicks().getValue());
        vo.setOrderNum(rpcVo.getOrderNum().getValue());
        vo.setSaleNum(rpcVo.getSaleNum().getValue());
        vo.setClickRate(rpcVo.getClickRate().getValue());
        vo.setSalesConversionRate(rpcVo.getSalesConversionRate().getValue());
        vo.setCpa(BigDecimal.valueOf(rpcVo.getCpa().getValue()));
        vo.setNaturalSales(BigDecimal.valueOf(rpcVo.getNaturalSales().getValue()));
        vo.setNaturalClicks(StringUtils.isNotBlank(rpcVo.getNaturalClicks()) ? Integer.parseInt(rpcVo.getNaturalClicks()) : 0);
        vo.setNaturalOrderNum(rpcVo.getNaturalOrderNum().getValue());
        vo.setAdClickRatio(rpcVo.getAdClickRatio().getValue());
        vo.setAdConversionRate(rpcVo.getAdConversionRate().getValue());
        vo.setCampaignId(rpcVo.getCampaignId());
        vo.setCampaignName(rpcVo.getCampaignName());
        vo.setAdGroupId(rpcVo.getAdGroupId());
        vo.setAdGroupName(rpcVo.getAdGroupName());
        vo.setKeywordText(rpcVo.getKeywordText());
        vo.setMatchType(rpcVo.getMatchType());
        vo.setKeywordId(rpcVo.getKeywordId());
        vo.setSku(rpcVo.getSku());
        vo.setAsin(rpcVo.getAsin());
        vo.setParentAsin(rpcVo.getParentAsin());
        vo.setTitle(rpcVo.getTitle());
        if (StringUtils.isNotBlank(rpcVo.getPrice())) {
            vo.setPrice(Double.parseDouble(rpcVo.getPrice()));
        }
        if (StringUtils.isNotBlank(rpcVo.getRating())) {
            vo.setRating(rpcVo.getRating());
        }
        if (StringUtils.isNotBlank(rpcVo.getRatingCount())) {
            vo.setRatingCount(Integer.parseInt(rpcVo.getRatingCount()));
        }
        vo.setMainImage(rpcVo.getMainImage());
        vo.setQuery(rpcVo.getQuery());
        vo.setQueryCn(rpcVo.getQueryCn());
        vo.setNegaType(rpcVo.getNegaType());
        vo.setTargetingType(rpcVo.getTargetingType());
        vo.setTargetingExpression(rpcVo.getTargetingExpression());
        vo.setTargetId(rpcVo.getTargetId());
        vo.setAdId(rpcVo.getAdId());
        vo.setTargetingText(rpcVo.getTargetingText());
        vo.setSpCampaignType(rpcVo.getSpCampaignType());
        vo.setSpGroupType(rpcVo.getSpGroupType());
        vo.setPortfolioName(rpcVo.getPortfolioName());
        vo.setSpTargetType(rpcVo.getSpTargetType());
        vo.setCampaignBudgetCurrencyCode(rpcVo.getCampaignBudgetCurrencyCode());
        //标签填充
        if (rpcVo.getIsBroad().getValue()) {
            vo.setIsBroad(true);
        }
        if (rpcVo.getIsPhrase().getValue()) {
            vo.setIsPhrase(true);
        }
        if (rpcVo.getIsExact().getValue()) {
            vo.setIsExact(true);
        }
        if (rpcVo.getIsNegativeExact().getValue()) {
            vo.setIsNegativeExact(true);
        }
        if (rpcVo.getIsNegativePhrase().getValue()) {
            vo.setIsNegativePhrase(true);
        }
        if (rpcVo.getIsNeTargetAsin().getValue()) {
            vo.setIsNeTargetAsin(true);
        }
        if (rpcVo.getIsTargetAsin().getValue()) {
            vo.setIsTargetAsin(true);
        }

        //新版广告报告下载中心
        vo.setCampaignType(rpcVo.getCampaignType());
        vo.setAttributedConversions7d(rpcVo.getAttributedConversions7D().getValue());
        vo.setAttributedConversions7dSameSKU(rpcVo.getAttributedConversions7DSameSKU().getValue());
        vo.setAttributedConversions7dOtherSameSKU(rpcVo.getAttributedConversions7DOtherSameSKU().getValue());
        vo.setAttributedSales7d(BigDecimal.valueOf(rpcVo.getAttributedSales7D().getValue()));
        vo.setAttributedSales7dSameSKU((BigDecimal.valueOf(rpcVo.getAttributedSales7DSameSKU().getValue())));
        vo.setAttributedSales7dOtherSameSKU((BigDecimal.valueOf(rpcVo.getAttributedSales7DOtherSameSKU().getValue())));
        vo.setAttributedUnitsOrdered7d(rpcVo.getAttributedUnitsOrdered7D().getValue());
        vo.setAttributedUnitsOrdered7dSameSKU(rpcVo.getAttributedUnitsOrdered7DSameSKU().getValue());
        vo.setAttributedUnitsOrdered7dOtherSameSKU(rpcVo.getAttributedUnitsOrdered7DOtherSameSKU().getValue());
        vo.setAttributedSales7dOtherSKU(BigDecimal.valueOf(rpcVo.getAttributedSales7DOtherSKU().getValue()));
        vo.setAttributedUnitsOrdered7dOtherSKU(rpcVo.getAttributedUnitsOrdered7DOtherSKU().getValue());
        vo.setOtherAsin(rpcVo.getOtherAsin());
        vo.setCountDate(rpcVo.getCountDate());
        // 广告花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(rpcVo.getAdCostPercentage()) ? rpcVo.getAdCostPercentage() : "0.00");
        // 广告销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(rpcVo.getAdSalePercentage()) ? rpcVo.getAdSalePercentage() : "0.00");
        // 广告订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(rpcVo.getAdOrderNumPercentage()) ? rpcVo.getAdOrderNumPercentage() : "0.00");
        // 广告销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(rpcVo.getOrderNumPercentage()) ? rpcVo.getOrderNumPercentage() : "0.00");
        vo.setSearchFrequencyRank(rpcVo.getSearchFrequencyRank());
        vo.setWeekRatio(StringUtils.isNotBlank(rpcVo.getWeekRatio()) ? new BigDecimal(rpcVo.getWeekRatio()): BigDecimal.ZERO);

        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        if (rpcVo.getAdSales() != null) {
            vo.setAdSales(BigDecimal.valueOf(rpcVo.getAdSales().getValue()));
        }
        //本广告产品订单量
        if (rpcVo.getAdSaleNum() != null) {
            vo.setAdSaleNum(rpcVo.getAdSaleNum().getValue());
        }
        //本广告产品销量
        if (rpcVo.getAdSelfSaleNum() != null) {
            vo.setAdSelfSaleNum(rpcVo.getAdSelfSaleNum().getValue());
        }
        //其他产品广告订单量
        if (rpcVo.getAdOtherOrderNum() != null) {
            vo.setAdOtherOrderNum(rpcVo.getAdOtherOrderNum().getValue());
        }
        //其他产品广告销售额
        if (rpcVo.getAdOtherSales() != null) {
            vo.setAdOtherSales(BigDecimal.valueOf(rpcVo.getAdOtherSales().getValue()));
        }
        //其他产品广告销量
        if (rpcVo.getAdOtherSaleNum() != null) {
            vo.setAdOtherSaleNum(rpcVo.getAdOtherSaleNum().getValue());
        }
        //“品牌新买家”订单量
        if (rpcVo.getOrdersNewToBrandFTD() != null) {
            vo.setOrdersNewToBrand14d(rpcVo.getOrdersNewToBrandFTD().getValue());
        }
        //“品牌新买家”销售额
        if (rpcVo.getSalesNewToBrandFTD() != null) {
            vo.setSalesNewToBrand14d(BigDecimal.valueOf(rpcVo.getSalesNewToBrandFTD().getValue()));
        }
        //搜索词展示量排名
        if (rpcVo.getImpressionRank() != null) {
            vo.setImpressionRank(rpcVo.getImpressionRank().getValue());
        }
        //搜索词展示份额
        if (rpcVo.getImpressionShare() != null) {
            vo.setImpressionShare(rpcVo.getImpressionShare().getValue());
        }
        //“品牌新买家”订单百分比
        if (rpcVo.getOrderRateNewToBrandFTD() != null) {
            vo.setOrderRateNewToBrand14d(rpcVo.getOrderRateNewToBrandFTD().getValue());
        }
        //“品牌新买家”销售额百分比
        if (rpcVo.getSalesRateNewToBrandFTD() != null) {
            vo.setSalesRateNewToBrand14d(rpcVo.getSalesRateNewToBrandFTD().getValue());
        }
        //“品牌新买家”订单转化率
        if (rpcVo.getOrdersNewToBrandPercentageFTD() != null) {
            vo.setOrdersNewToBrandPercentage14d(rpcVo.getOrdersNewToBrandPercentageFTD().getValue());
        }
        if(rpcVo.getCampaignStatus() != null) {
            vo.setCampaignStatus(rpcVo.getCampaignStatus());
        }

        vo.setVideo5SecondViews(Optional.ofNullable(rpcVo.getVideo5SecondViews()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setVideo5SecondViewRate(Optional.ofNullable(rpcVo.getVideo5SecondViewRate()).filter(StringUtils::isNotBlank).map(Double::parseDouble).orElse(0.00));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(rpcVo.getVideoFirstQuartileViews()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setVideoMidpointViews(Optional.ofNullable(rpcVo.getVideoMidpointViews()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(rpcVo.getVideoThirdQuartileViews()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setVideoCompleteViews(Optional.ofNullable(rpcVo.getVideoCompleteViews()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setVideoUnmutes(Optional.ofNullable(rpcVo.getVideoUnmutes()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setViewImpressions(Optional.ofNullable(rpcVo.getViewImpressions()).filter(StringUtils::isNotBlank).map(Integer::parseInt).orElse(0));
        vo.setViewabilityRate(Optional.ofNullable(rpcVo.getViewabilityRate()).filter(StringUtils::isNotBlank).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        vo.setViewClickThroughRate(Optional.ofNullable(rpcVo.getViewClickThroughRate()).filter(StringUtils::isNotBlank).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(rpcVo.getAdvertisingUnitPrice()).filter(StringUtils::isNotBlank).map(BigDecimal::new).orElse(BigDecimal.ZERO));
        //流量报告表字段
        vo.setGrossClickThroughs(Optional.of(rpcVo.getGrossClickThroughs()).orElse(0L));
        vo.setGrossImpressions(Optional.of(rpcVo.getGrossImpressions()).orElse(0L));
        vo.setInvalidClickThroughRate(BigDecimal.valueOf(rpcVo.getInvalidClickThroughRate().getValue()));
        vo.setInvalidClickThroughs(Optional.of(rpcVo.getInvalidClickThroughs()).orElse(0L));
        vo.setInvalidImpressionRate(BigDecimal.valueOf(rpcVo.getInvalidImpressionRate().getValue()));
        vo.setInvalidImpressions(Optional.of(rpcVo.getInvalidImpressions()).orElse(0L));


        return vo;
    }

    public static ReportDataVo convertRpcVoToReportDataVo(ReportDataRpcVo rpcVo) {
        ReportDataVo vo = new ReportDataVo();
        vo.setShopId(rpcVo.hasShopId() ? rpcVo.getShopId().getValue() : null);
        vo.setCampaignStartDate(rpcVo.hasCampaignStartDate() ? rpcVo.getCampaignStartDate() : null);
        vo.setCampaignEndDate(rpcVo.hasCampaignEndDate() ? rpcVo.getCampaignEndDate() : null);
        vo.setState(rpcVo.hasState() ? rpcVo.getState() : null);
        vo.setCountDate(rpcVo.getCountDate());
        vo.setCampaignId(rpcVo.getCampaignId());
        vo.setCampaignName(rpcVo.getCampaignName());
        vo.setAdGroupId(rpcVo.getAdGroupId());
        vo.setAdGroupName(rpcVo.getAdGroupName());
        vo.setSku(rpcVo.getSku());
        vo.setAsin(rpcVo.getAsin());
        vo.setParentAsin(rpcVo.getParentAsin());
        vo.setTitle(rpcVo.getTitle());
        vo.setMainImage(rpcVo.getMainImage());
        vo.setTargetingExpression(rpcVo.getTargetingExpression());
        vo.setTargetingText(rpcVo.getTargetingText());
        vo.setKeywordText(rpcVo.getKeywordText());
        vo.setKeywordId(rpcVo.getKeywordId());
        vo.setTargetId(rpcVo.getTargetId());
        vo.setAdId(rpcVo.getAdId());
        vo.setCpc(BigDecimal.valueOf(rpcVo.getCpc().getValue()));
        vo.setCost(BigDecimal.valueOf(rpcVo.getCost().getValue()));
        vo.setSales(BigDecimal.valueOf(rpcVo.getSales().getValue()));
        vo.setAcos(BigDecimal.valueOf(rpcVo.getAcos().getValue()));
        vo.setRoas(BigDecimal.valueOf(rpcVo.getRoas().getValue()));
        vo.setImpressions(rpcVo.getImpressions().getValue());
        vo.setClicks(rpcVo.getClicks().getValue());
        vo.setClickRate(rpcVo.getClickRate().getValue());
        vo.setOrderNum(rpcVo.getOrderNum().getValue());
        vo.setSalesConversionRate(rpcVo.getSalesConversionRate().getValue());
        vo.setAdFormat(rpcVo.getAdFormat());
        vo.setPortfolioName(rpcVo.getPortfolioName());
        vo.setCampaignBudgetCurrencyCode(rpcVo.getCampaignBudgetCurrencyCode());

        //新版广告报告下载中心
        vo.setMatchType(rpcVo.getMatchType());
        vo.setQuery(rpcVo.getQuery());
        vo.setAttributedDetailPageView14d(rpcVo.getAttributedDetailPageView14D().getValue());
        vo.setVcpm(BigDecimal.valueOf(rpcVo.getVcpm().getValue()));
        vo.setSpCampaignType(rpcVo.getSpCampaignType());
        vo.setAttributedConversions14d(rpcVo.getAttributedConversions14D().getValue());
        vo.setAttributedConversions14dSameSKU(rpcVo.getAttributedConversions14DSameSKU().getValue());
        vo.setAttributedConversions14dOtherSameSKU(rpcVo.getAttributedConversions14DOtherSameSKU().getValue());
        vo.setAttributedSales14d(BigDecimal.valueOf(rpcVo.getAttributedSales14D().getValue()));
        vo.setAttributedSales14dSameSKU((BigDecimal.valueOf(rpcVo.getAttributedSales14DSameSKU().getValue())));
        vo.setAttributedSales14dOtherSameSKU((BigDecimal.valueOf(rpcVo.getAttributedSales14DOtherSameSKU().getValue())));
        vo.setAttributedUnitsOrdered14d(rpcVo.getAttributedUnitsOrdered14D().getValue());
        vo.setOtherAsin(rpcVo.getOtherAsin());
        vo.setAttributedOrderRateNewToBrand14d(rpcVo.getAttributedOrderRateNewToBrand14D().getValue());
        vo.setAttributedOrdersNewToBrand14d(rpcVo.getAttributedOrdersNewToBrand14D().getValue());
        vo.setAttributedOrdersNewToBrandPercentage14d(rpcVo.getAttributedOrdersNewToBrandPercentage14D().getValue());
        vo.setAttributedSalesNewToBrand14d(BigDecimal.valueOf(rpcVo.getAttributedSalesNewToBrand14D().getValue()));
        vo.setAttributedSalesNewToBrandPercentage14d(rpcVo.getAttributedSalesNewToBrandPercentage14D().getValue());
        vo.setAttributedUnitsOrderedNewToBrand14d(rpcVo.getAttributedUnitsOrderedNewToBrand14D().getValue());
        vo.setAttributedUnitsOrderedNewToBrandPercentage14d(rpcVo.getAttributedUnitsOrderedNewToBrandPercentage14D().getValue());
        vo.setVctr(rpcVo.getVctr().getValue());
        vo.setVideo5SecondViewRate(rpcVo.getVideo5SecondViewRate().getValue());
        vo.setVideo5SecondViews(rpcVo.getVideo5SecondViews().getValue());
        vo.setVideoFirstQuartileViews(rpcVo.getVideoFirstQuartileViews().getValue());
        vo.setVideoMidpointViews(rpcVo.getVideoMidpointViews().getValue());
        vo.setVideoThirdQuartileViews(rpcVo.getVideoThirdQuartileViews().getValue());
        vo.setVideoUnmutes(rpcVo.getVideoUnmutes().getValue());
        vo.setViewableImpressions(rpcVo.getViewableImpressions().getValue());
        vo.setVideoCompleteViews(rpcVo.getVideoCompleteViews().getValue());
        vo.setVtr(rpcVo.getVtr().getValue());
        vo.setSearchTermImpressionRank(rpcVo.getSearchTermImpressionRank().getValue());
        vo.setSearchTermImpressionShare(rpcVo.getSearchTermImpressionShare().getValue());
        vo.setSalesConversionRate(rpcVo.getSalesConversionRate().getValue());
        vo.setCostType(rpcVo.getCostType());
        vo.setBidOptimization(rpcVo.getBidOptimization());
        vo.setViewImpressions(rpcVo.getViewImpressions().getValue());
        vo.setAttributedBrandedSearches14d(rpcVo.getAttributedBrandedSearches14D().getValue());
        vo.setPlacement(rpcVo.getPlacement());
        return vo;
    }
}
