package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsDetailParam;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsDetailVo;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsVo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSd;

import java.util.List;

/**
 * amazon SD广告投放定位表(OdsAmazonAdTargetingSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
public interface IOdsAmazonAdTargetingSdDao extends IDorisBaseDao<OdsAmazonAdTargetingSd> {
    List<OdsAmazonAdTargetingSd> getByTargetingIds(int puid, List<Integer> shopIds, List<String> targetingIds);

    List<OdsAmazonAdTargetingSd> getByTargetingIds(int puid, Integer shopId, List<String> targetIds);

    List<String> getTargetIdsByAdGroupId(int puid, List<Integer> shopIdList, List<String> spAdGroupIdList);

    List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList);

}

