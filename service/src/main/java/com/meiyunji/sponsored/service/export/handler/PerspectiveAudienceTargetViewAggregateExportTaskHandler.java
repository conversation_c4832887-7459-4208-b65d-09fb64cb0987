package com.meiyunji.sponsored.service.export.handler;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.AudienceTargetViewAggregateRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.AudienceTargetViewAggregateExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.AutoTargetViewExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.AudienceTargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IAudienceTargetViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IViewManageService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.AudienceTargetViewServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AudienceTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AudienceTargetViewAggregateVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_AUDIENCE_TARGET_VIEW_AGGREGATE)
public class PerspectiveAudienceTargetViewAggregateExportTaskHandler implements AdManagePageExportTaskHandler {

    /**
     * 透视接口
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getAudienceTargetViewAggregate(AudienceTargetViewAggregateRequest, StreamObserver)
     * @see ViewManageServiceImpl#getAllAudienceTargetViewAggregate(Integer, AudienceTargetViewParam)
     * @see AudienceTargetViewServiceImpl#getAllAudienceTargetViewAggregatePageVo(Integer, AudienceTargetViewParam)
     * @param task
     */

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IViewManageService viewManageService;
    @Autowired
    private IAudienceTargetViewService audienceTargetViewService;


    @Override
    public void export(AdManagePageExportTask task) {
        AudienceTargetViewParam param = JSONUtil.jsonToObject(task.getParam(), AudienceTargetViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 受众投放视图列表页汇总 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        AudienceTargetViewAggregatePageVo aggregatePageVo = audienceTargetViewService.getAllAudienceTargetViewAggregatePageVo(puid, param);
        if (Objects.isNull(aggregatePageVo) || Objects.isNull(aggregatePageVo.getPage())) {
            log.error(String.format("产品广告透视 受众投放视图列表页汇总 export error, aggregatePageVo is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        if (CollectionUtils.isEmpty(aggregatePageVo.getPage().getRows())) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();
        List<AudienceTargetViewAggregateExcelVO> dataList = aggregatePageVo.getPage().getRows().stream()
                .map(i -> new AudienceTargetViewAggregateExcelVO(currency, i)).collect(Collectors.toList());

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), AudienceTargetViewAggregateExcelVO.class,
                build.currencyNew(AudienceTargetViewAggregateExcelVO.class), Collections.emptyList()));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));

    }
}
