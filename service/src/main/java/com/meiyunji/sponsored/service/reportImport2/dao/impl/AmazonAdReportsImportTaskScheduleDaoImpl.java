package com.meiyunji.sponsored.service.reportImport2.dao.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import com.meiyunji.sponsored.service.reportImport2.dao.IAmazonAdReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport2.entity.AmazonAdReportsImportTaskSchedule;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class AmazonAdReportsImportTaskScheduleDaoImpl extends AdBaseDaoImpl<AmazonAdReportsImportTaskSchedule> implements IAmazonAdReportsImportTaskScheduleDao {

    @Override
    public int updateStatusById(Integer puid, Long id, ReportImportStatus newStatus, String errInfo, ReportImportStatus originStatus) {
        String sql = "update t_amazon_ad_reports_import_task_schedule set status = ?, err_info = ? where puid = ? and id = ? and status = ? ";
        return getJdbcTemplate().update(sql, newStatus.name(), errInfo, puid, id, originStatus.name());
    }

    @Override
    public int batchInsert(int puid, List<AmazonAdReportsImportTaskSchedule> list) {
        StringBuilder sql = new StringBuilder("insert into t_amazon_ad_reports_import_task_schedule (puid, shop_id, count_date, task_id, data_source, ad_type, report_type, status, file_name, file_path, file_id) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdReportsImportTaskSchedule report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?),");
            argsList.add(report.getPuid());
            argsList.add(report.getShopId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTaskId());
            argsList.add(report.getDataSource());
            argsList.add(report.getAdType());
            argsList.add(report.getReportType());
            argsList.add(report.getStatus());
            argsList.add(report.getFileName());
            argsList.add(report.getFilePath());
            argsList.add(report.getFileId());
        }

        sql.deleteCharAt(sql.length() - 1);

        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public AmazonAdReportsImportTaskSchedule getById(Integer puid, Long scheduleId) {
        return getByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("id", scheduleId)
                .build());
    }


    @Override
    public List<AmazonAdReportsImportTaskSchedule> listByTaskIds(int puid, List<Long> taskIds) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("task_id", taskIds.toArray(new Long[0]))
                .build());
    }


    @Override
    public List<AmazonAdReportsImportTaskSchedule> listByTaskIdsAndCountDate(int puid, List<Long> taskIds, List<String> countDates) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("task_id", taskIds.toArray(new Long[0]))
                .in("count_date", countDates.toArray(new String[0]))
                .build());
    }

    @Override
    public List<AmazonAdReportsImportTaskSchedule> listWaitTimeOutReportTask() {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("status", "WAITING")
                .greaterThanOrEqualTo("last_update_at", LocalDateTime.now().minusDays(10))
                .build());
    }
}