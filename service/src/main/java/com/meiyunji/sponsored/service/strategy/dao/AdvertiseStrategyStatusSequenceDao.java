package com.meiyunji.sponsored.service.strategy.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatusSequence;

import java.util.List;

public interface AdvertiseStrategyStatusSequenceDao extends IAdBaseDao<AdvertiseStrategyStatusSequence> {

    Long genId();

    List<Long> batchGenId(Integer size);

}