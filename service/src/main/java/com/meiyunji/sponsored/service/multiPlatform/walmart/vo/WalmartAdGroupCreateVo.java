package com.meiyunji.sponsored.service.multiPlatform.walmart.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @author: ys
 * @date: 2025/2/25 13:44
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalmartAdGroupCreateVo {
    private String groupId;
    @NotBlank(message = "广告组名称不能为空")
    private String name;
}
