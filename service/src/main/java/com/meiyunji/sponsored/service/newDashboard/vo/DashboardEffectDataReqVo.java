package com.meiyunji.sponsored.service.newDashboard.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-01  15:10
 */

@Data
@ApiModel
public class DashboardEffectDataReqVo extends DashboardBaseReqVo {
    @ApiModelProperty("是否小时级数据")
    private Boolean hourData;

    @ApiModelProperty("页面类型，广告效果：AD_EFFECT，广告转化：AD_CONVERSION")
    private String modelType;
    /**
     * 时间对比
     */
    private Boolean timeDate;
}
