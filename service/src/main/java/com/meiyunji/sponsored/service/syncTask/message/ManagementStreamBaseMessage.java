package com.meiyunji.sponsored.service.syncTask.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.pulsar.shade.com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-01-11  19:48
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManagementStreamBaseMessage {

    @JsonProperty("audit")
    private Audit audit;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Audit {
        /**
         * datetime	Creation time stamp in ISO8601 format.
         */
        @JsonProperty("creationDateTime")
        private LocalDateTime creationDateTime;
        /**
         * datetime	Last time record was updated timestamp in ISO8601 format.
         */
        @JsonProperty("lastUpdatedDateTime")
        private LocalDateTime lastUpdatedDateTime;
    }

}
