package com.meiyunji.sponsored.service.reportDiffMonitor.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 各类型报告拉取时间层级，单位：分钟
 * @Author: hejh
 * @Date: 2024/6/5 13:42
 */
public class TimeLevel {
    /**
     * sp广告组合时间层级
     */
    public static final Map<String, Integer> SP_CAMPAIGN_TIME_LEVEL;
    /**
     * sp广告组时间层级
     */
    public static final Map<String, Integer> SP_GROUP_TIME_LEVEL;
    /**
     * sp广告投放时间层级
     */
    public static final Map<String, Integer> SP_TARGET_TIME_LEVEL;
    /**
     * sp广告产品时间层级
     */
    public static final Map<String, Integer> SP_PRODUCT_TIME_LEVEL;

    /**
     * sd广告组合时间层级
     */
    public static final Map<String, Integer> SD_CAMPAIGN_TIME_LEVEL;
    /**
     * sd广告组时间层级
     */
    public static final Map<String, Integer> SD_GROUP_TIME_LEVEL;
    /**
     * sd广告投放时间层级
     */
    public static final Map<String, Integer> SD_TARGET_TIME_LEVEL;
    /**
     * sd广告产品时间层级
     */
    public static final Map<String, Integer> SD_PRODUCT_TIME_LEVEL;

    static {
        SP_CAMPAIGN_TIME_LEVEL = new HashMap<>();
        SP_CAMPAIGN_TIME_LEVEL.put("0-3", 10);
        SP_CAMPAIGN_TIME_LEVEL.put("4-7", 6 * 60);
        SP_CAMPAIGN_TIME_LEVEL.put("8-15", 24 * 60);
        SP_CAMPAIGN_TIME_LEVEL.put("16-30", 48 * 60);
        SP_CAMPAIGN_TIME_LEVEL.put("31-45", 48 * 60);
        SP_CAMPAIGN_TIME_LEVEL.put("46-62", 72 * 60);
        SP_GROUP_TIME_LEVEL = new HashMap<>();
        SP_GROUP_TIME_LEVEL.put("0-3", 2 * 60);
        SP_GROUP_TIME_LEVEL.put("4-7", 12 * 60);
        SP_GROUP_TIME_LEVEL.put("8-15", 24 * 60);
        SP_GROUP_TIME_LEVEL.put("16-30", 48 * 60);
        SP_GROUP_TIME_LEVEL.put("31-45", 48 * 60);
        SP_GROUP_TIME_LEVEL.put("46-62", 72 * 60);
        SP_TARGET_TIME_LEVEL = new HashMap<>();
        SP_TARGET_TIME_LEVEL.put("0-7", 90);
        SP_TARGET_TIME_LEVEL.put("8-11", 24 * 60);
        SP_TARGET_TIME_LEVEL.put("12-15", 24 * 60);
        SP_TARGET_TIME_LEVEL.put("16-19", 48 * 60);
        SP_TARGET_TIME_LEVEL.put("20-23", 48 * 60);
        SP_TARGET_TIME_LEVEL.put("24-27", 48 * 60);
        SP_TARGET_TIME_LEVEL.put("28-30", 48 * 60);
        SP_TARGET_TIME_LEVEL.put("46-49", 72 * 60);
        SP_TARGET_TIME_LEVEL.put("50-53", 72 * 60);
        SP_TARGET_TIME_LEVEL.put("54-57", 72 * 60);
        SP_TARGET_TIME_LEVEL.put("58-62", 72 * 60);
        SP_PRODUCT_TIME_LEVEL = new HashMap<>();
        SP_PRODUCT_TIME_LEVEL.put("0-3",  60);
        SP_PRODUCT_TIME_LEVEL.put("4-7", 12 * 60);
        SP_PRODUCT_TIME_LEVEL.put("8-11", 24 * 60);
        SP_PRODUCT_TIME_LEVEL.put("12-15", 24 * 60);
        SP_PRODUCT_TIME_LEVEL.put("16-19", 48 * 60);
        SP_PRODUCT_TIME_LEVEL.put("20-23", 48 * 60);
        SP_PRODUCT_TIME_LEVEL.put("24-27", 48 * 60);
        SP_PRODUCT_TIME_LEVEL.put("28-30", 48 * 60);
        SP_PRODUCT_TIME_LEVEL.put("46-49", 72 * 60);
        SP_PRODUCT_TIME_LEVEL.put("50-53", 72 * 60);
        SP_PRODUCT_TIME_LEVEL.put("54-57", 72 * 60);
        SP_PRODUCT_TIME_LEVEL.put("58-62", 72 * 60);
        SD_CAMPAIGN_TIME_LEVEL = new HashMap<>();
        SD_CAMPAIGN_TIME_LEVEL.put("0-3", 20);
        SD_CAMPAIGN_TIME_LEVEL.put("4-7", 12 * 60);
        SD_CAMPAIGN_TIME_LEVEL.put("8-15", 24 * 60);
        SD_CAMPAIGN_TIME_LEVEL.put("16-30", 48 * 60);
        SD_CAMPAIGN_TIME_LEVEL.put("31-45", 48 * 60);
        SD_CAMPAIGN_TIME_LEVEL.put("46-62", 72 * 60);
        SD_GROUP_TIME_LEVEL = new HashMap<>();
        SD_GROUP_TIME_LEVEL.put("0-3", 2 * 60);
        SD_GROUP_TIME_LEVEL.put("4-7", 12 * 60);
        SD_GROUP_TIME_LEVEL.put("8-15", 24 * 60);
        SD_GROUP_TIME_LEVEL.put("16-30", 48 * 60);
        SD_GROUP_TIME_LEVEL.put("31-45", 48 * 60);
        SD_GROUP_TIME_LEVEL.put("46-62", 72 * 60);
        SD_TARGET_TIME_LEVEL = new HashMap<>();
        SD_TARGET_TIME_LEVEL.put("0-3", 2 * 60);
        SD_TARGET_TIME_LEVEL.put("4-7", 12 * 60);
        SD_TARGET_TIME_LEVEL.put("8-15", 24 * 60);
        SD_TARGET_TIME_LEVEL.put("16-30", 48 * 60);
        SD_TARGET_TIME_LEVEL.put("31-45", 48 * 60);
        SD_TARGET_TIME_LEVEL.put("46-62", 72 * 60);
        SD_PRODUCT_TIME_LEVEL = new HashMap<>();
        SD_PRODUCT_TIME_LEVEL.put("0-3", 2 * 60);
        SD_PRODUCT_TIME_LEVEL.put("4-7", 12 * 60);
        SD_PRODUCT_TIME_LEVEL.put("8-15", 24 * 60);
        SD_PRODUCT_TIME_LEVEL.put("16-30", 48 * 60);
        SD_PRODUCT_TIME_LEVEL.put("31-45", 48 * 60);
        SD_PRODUCT_TIME_LEVEL.put("46-62", 72 * 60);
    }

    /**
     * 将"0-3"变成[0, 3]
     * @param str
     * @return
     */
    public static List<Integer> splitTimeGap(String str) {
        return Arrays.stream(str.split("-"))
            .mapToInt(Integer::parseInt)
            .boxed()
            .collect(Collectors.toList());
    }
    
}
