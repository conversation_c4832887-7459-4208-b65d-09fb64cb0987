package com.meiyunji.sponsored.service.dashboard.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 广告贡献vo
 */
@Data
public class AdContributesDataDto {
    private BigDecimal shopSale = BigDecimal.ZERO;
    private BigDecimal sales = BigDecimal.ZERO;
    private BigDecimal cost = BigDecimal.ZERO;
    private BigDecimal aCoTS = BigDecimal.ZERO;
    private BigDecimal aSoTS = BigDecimal.ZERO;
    private BigDecimal perShopSale = BigDecimal.ZERO;
    private BigDecimal perSales = BigDecimal.ZERO;
    private BigDecimal perCost = BigDecimal.ZERO;
    private BigDecimal perACoTS = BigDecimal.ZERO;
    private BigDecimal perASoTS = BigDecimal.ZERO;
    private BigDecimal shopSaleGrowRate = BigDecimal.ZERO;
    private BigDecimal aSoTSGrowRate = BigDecimal.ZERO;
    private BigDecimal aCoTSGrowRate = BigDecimal.ZERO;

    private List<AdContributesDto> list;
}
