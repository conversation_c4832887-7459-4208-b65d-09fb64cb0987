package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InvoiceProductDto {
    private String asin;
    private String sku;
    private String campaignId;
    private BigDecimal cost;
    private Date updateTime;
}
