package com.meiyunji.sponsored.service.newDashboard.util;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;

/**
 * 指标计算工具类
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-03-27  11:20
 */
public class CalculateUtil {

    public static final String BLANK = "--";
    private static final String RATE_ZERO = "0.00%";
    private static final String RATE_HUNDRED_POSITIVE = "100.00%";
    private static final String RATE_HUNDRED_NEGATIVE = "-100.00%";
    private static final String RATE_ZERO_NO_PERCENT = "0.00";
    private static final String RATE_HUNDRED_POSITIVE_NO_PERCENT = "100.00";
    private static final String RATE_HUNDRED_NEGATIVE_NO_PERCENT = "-100.00";

    //转成百分比并保留2位小数
    private static DecimalFormat percentFormat = new DecimalFormat("0.00%");

    //保留2位小数
    private static DecimalFormat decimalFormat = new DecimalFormat("0.00");
    //保留5位小数
    private static DecimalFormat decimal5Format = new DecimalFormat("0.00000");


    /**
     * 两个int值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4Int(Integer numerator, Integer denominator) {
        if (numerator == null || denominator == null) {
            return BigDecimal.ZERO;
        }
        if (numerator == 0 || denominator == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 5, RoundingMode.HALF_UP);
    }
    /**
     * 计算VCPM,并返回string类型
     * @param numerator 分子，这里为广告花费
     * @param denominator 这里为可见广告次数
     * @return String
     */
    public static String calVcpm2String(BigDecimal numerator, Integer denominator){
        if(denominator == null || denominator == 0 || numerator == null || BigDecimal.ZERO.compareTo(numerator) == 0){
            return "0";
        }
        BigDecimal divByKilo = new BigDecimal(denominator).divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP);
        return decimalFormat.format(calPercent4Decimal(numerator,divByKilo));
    }
    /**
     * 计算VCPM,并返回bigdecimal类型
     * @param numerator 分子，这里为广告花费
     * @param denominator 这里为可见广告次数
     * @return 并返回bigdecimal类型
     */
    public static BigDecimal calVcpm(BigDecimal numerator, int denominator){
        if(denominator == 0 || numerator == null || BigDecimal.ZERO.compareTo(numerator) == 0){
            return BigDecimal.ZERO;
        }
        BigDecimal divByKilo = new BigDecimal(denominator).divide(new BigDecimal(1000), 5, RoundingMode.HALF_UP);
        return calPercent4Decimal(numerator,divByKilo);
    }

    /**
     * 两个int值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4Int(Integer numerator, Long denominator) {
        if (numerator == null || denominator == null) {
            return BigDecimal.ZERO;
        }
        if (numerator == 0 || denominator == 0L) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 5, RoundingMode.HALF_UP);
    }

    /**
     * 两个int值计算增长量，返回数值
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static BigDecimal calValue4Int(int numerator, int denominator) {
        return new BigDecimal(numerator).subtract(new BigDecimal(denominator));
    }

    /**
     * 两个int值计算占比，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4Int(int numerator, int denominator) {
        return percentFormat.format(calPercent4Int(numerator, denominator));
    }

    /**
     * 两个int值计算增长值，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr4Int(int numerator, int denominator) {
        return decimalFormat.format(calValue4Int(numerator, denominator));
    }


    /**
     * 两个int值计算增长值，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr5Int(int numerator, int denominator) {
        return decimal5Format.format(calValue4Int(numerator, denominator));
    }


    /**
     * 两个long值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4Long(long numerator, long denominator) {
        if (numerator == 0 || denominator == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 5, RoundingMode.HALF_UP);
    }

    /**
     * 两个int值计算增长值，返回数值
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static BigDecimal calValue4Long(long numerator, long denominator) {
        return new BigDecimal(numerator).subtract(new BigDecimal(denominator));
    }

    /**
     * 两个long值计算占比，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4Long(long numerator, long denominator) {
        return percentFormat.format(calPercent4Long(numerator, denominator));
    }

    /**
     * 两个Long值计算增长值，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr4Long(long numerator, long denominator) {
        return decimalFormat.format(calValue4Long(numerator, denominator));
    }


    /**
     * 两个Long值计算增长值，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr5Long(long numerator, long denominator) {
        return decimalFormat.format(calValue4Long(numerator, denominator));
    }


    /**
     * bigdecimal除以int值计算占比，保留两位小数，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4DecimalAndInt(BigDecimal numerator, Integer denominator) {
        if (denominator == null) {
            return BigDecimal.ZERO;
        }
        return calPercent4Decimal(numerator, new BigDecimal(denominator));
    }

    /**
     * bigdecimal除以int值计算占比，保留两位小数，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4DecimalAndInt(BigDecimal numerator, int denominator) {
        return percentFormat.format(calPercent4DecimalAndInt(numerator, denominator));
    }


    /**
     * 两个bigdecimal值计算占比，保留两位小数，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4Decimal(BigDecimal numerator, BigDecimal denominator) {
        if (Objects.isNull(numerator) || Objects.isNull(denominator)) {
            return BigDecimal.ZERO;
        }

        if (numerator.compareTo(BigDecimal.ZERO) == 0 || denominator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        //保留5位小数避免丢失进度
        return numerator.divide(denominator, 5, RoundingMode.HALF_UP);
    }

    /**
     * 两个bigdecimal值计算增长量，保留两位小数，返回数值
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static BigDecimal calValue4Decimal(BigDecimal numerator, BigDecimal denominator) {
        if (numerator == null) {
            numerator = BigDecimal.ZERO;
        }
        if (denominator == null) {
            denominator = BigDecimal.ZERO;
        }
        return numerator.subtract(denominator);
    }

    /**
     * 两个bigdecimal值计算占比，保留两位小数，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4Decimal(BigDecimal numerator, BigDecimal denominator) {
        return percentFormat.format(calPercent4Decimal(numerator, denominator));
    }

    /**
     * 两个bigdecimal值计算增长值，保留两位小数，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr4Decimal(BigDecimal numerator, BigDecimal denominator) {
        return decimalFormat.format(calValue4Decimal(numerator, denominator));
    }

    public static String calValuePreStr4Decimal(BigDecimal numerator, BigDecimal denominator) {
        percentFormat.setRoundingMode(RoundingMode.HALF_UP);
        return percentFormat.format(calValue4Decimal(numerator, denominator));
    }


    /**
     * 两个bigdecimal值计算增长值，保留两位小数，返回字符串
     * @param numerator 增长前
     * @param denominator 增长后
     * @return
     */
    public static String calValueStr5Decimal(BigDecimal numerator, BigDecimal denominator) {
        return decimal5Format.format(calValue4Decimal(numerator, denominator));
    }

    /**
     * 格式化数值为百分比字符串，2位小数，例如传入0.5354234，返回53.54%
     * @param value
     * @return
     */
    public static String formatPercent(BigDecimal value) {
        return percentFormat.format(value);
    }

    /**
     * 格式化数值，保留2位小数
     * @param value
     * @return
     */
    public static String formatDecimal(BigDecimal value) {
        return decimalFormat.format(value);
    }


    /**
     * 两个int值计算同环比增长率，保留两位小数，返回百分比字符串
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4Int(Integer curPeriod, Integer lastPeriod) {
        if (curPeriod == null || lastPeriod == null) {
            return RATE_ZERO;
        }
        return calRate4Long(curPeriod.longValue(), lastPeriod.longValue());
    }


    /**
     * 两个long值计算同环比增长率，保留两位小数，返回百分比字符串
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4Long(Long curPeriod, Long lastPeriod) {
        if (curPeriod == null || lastPeriod == null) {
            return RATE_ZERO;
        }
        if (curPeriod == 0 && lastPeriod == 0) {
            return RATE_ZERO;
        }

        if (curPeriod == 0) {
            return RATE_HUNDRED_NEGATIVE;
        }

        if (lastPeriod == 0) {
            return RATE_HUNDRED_POSITIVE;
        }

        double result = ((double) curPeriod / lastPeriod) - 1;
        return percentFormat.format(result);
    }


    /**
     * 两个bigdecimal值计算同环比增长率，保留两位小数，返回百分比字符串
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%；
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4Decimal(BigDecimal curPeriod, BigDecimal lastPeriod) {
        if (Objects.isNull(curPeriod) || Objects.isNull(lastPeriod)) {
            throw new NullPointerException();
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0 && lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_ZERO;
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_NEGATIVE;
        }

        if (lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_POSITIVE;
        }

        //保留5位小数避免丢失进度
        BigDecimal result = curPeriod.divide(lastPeriod, 5, RoundingMode.HALF_UP).subtract(new BigDecimal(1));
        return percentFormat.format(result);
    }



    public static String calPercentStr2Decimal(BigDecimal numerator, BigDecimal denominator) {
        return percentFormat.format(calPercent4Decimal(numerator, denominator)).replace("%", "");
    }


    /**
     * 两个bigdecimal值计算占比，保留两位小数，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent2Decimal(BigDecimal numerator, BigDecimal denominator) {
        if (Objects.isNull(numerator) || Objects.isNull(denominator)) {
            throw new NullPointerException();
        }

        if (numerator.compareTo(BigDecimal.ZERO) == 0 || denominator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        //保留2位小数避免丢失进度
        return numerator.divide(denominator, 2, RoundingMode.HALF_UP);
    }


    /**
     * bigdecimal除以int值计算占比，保留两位小数，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent2DecimalAndInt(BigDecimal numerator, int denominator) {
        return calPercent2Decimal(numerator, new BigDecimal(denominator));
    }


    /**
     * 两个bigdecimal值计算同环比增长率，保留两位小数，返回百分比数字字符串，不带%号
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%；
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4DecimalNoPercent(BigDecimal curPeriod, BigDecimal lastPeriod) {
        if (Objects.isNull(curPeriod) || Objects.isNull(lastPeriod)) {
            throw new NullPointerException();
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0 && lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_ZERO_NO_PERCENT;
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_NEGATIVE_NO_PERCENT;
        }

        if (lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_POSITIVE_NO_PERCENT;
        }

        //保留5位小数避免丢失进度
        BigDecimal result = curPeriod.divide(lastPeriod, 5, RoundingMode.HALF_UP).subtract(new BigDecimal(1));
        return percentFormat.format(result).replace("%", "");
    }


    /**
     * 两个long值计算同环比增长率，保留两位小数，返回百分比字符串 不带百分号
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4LongNoPercent(Long curPeriod, Long lastPeriod) {
        if (curPeriod == null || lastPeriod == null) {
            return RATE_ZERO_NO_PERCENT;
        }
        if (curPeriod == 0 && lastPeriod == 0) {
            return RATE_ZERO_NO_PERCENT;
        }

        if (curPeriod == 0) {
            return RATE_HUNDRED_NEGATIVE_NO_PERCENT;
        }

        if (lastPeriod == 0) {
            return RATE_HUNDRED_POSITIVE_NO_PERCENT;
        }

        double result = ((double) curPeriod / lastPeriod) - 1;
        return percentFormat.format(result).replace("%", "");
    }



    /**
     * 两个int值计算同环比增长率，保留两位小数，返回百分比字符串
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4IntNoPercent(Integer curPeriod, Integer lastPeriod) {
        if (curPeriod == null || lastPeriod == null) {
            return RATE_ZERO_NO_PERCENT;
        }
        return calRate4LongNoPercent(curPeriod.longValue(), lastPeriod.longValue());
    }


    /**
     * 两个long值计算占比，返回百分比字符串 不带百分号
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4LongNoPercent(long numerator, long denominator) {
        return percentFormat.format(calPercent4Long(numerator, denominator)).replace("%", "");
    }

    /**
     * 两个int值计算占比，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4IntNoPercent(int numerator, int denominator) {
        return percentFormat.format(calPercent4Int(numerator, denominator)).replace("%", "");
    }



    /**
     * 百分比 不带% 号
     * @param value
     * @return
     */
    public static String formatPercentNoPercent(BigDecimal value) {
        return percentFormat.format(value).replace("%", "");
    }


    /**
     * 百分比 不带% 号
     * @param value
     * @return
     */
    public static String formatPercentNoPercent(String value) {
        if (StringUtils.isNotBlank(value) && !"null".equalsIgnoreCase(value)
                && !"-".equalsIgnoreCase(value) && !" ".equalsIgnoreCase(value)) {
            try {
                return percentFormat.format(new BigDecimal(value)).replace("%", "");
            } catch (Exception e) {
                return value;
            }
        } else {
            return value;
        }

    }



    /**
     * 百分比 不带% 号
     * @param value
     * @return
     */
    public static String ratioConversionValue(String value) {
        if (StringUtils.isNotBlank(value) && !"null".equalsIgnoreCase(value)
                && !"-".equalsIgnoreCase(value) && !" ".equalsIgnoreCase(value)) {
            try {
                String replace = value.replace("%", "");
                return MathUtil.divide(new BigDecimal(replace), BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toPlainString();
            } catch (Exception e) {
                return value;
            }
        } else {
            return value;
        }

    }



    /**
     * 4位 精度计算
     * 两个bigdecimal值计算占比，保留两位小数，返回百分比字符串
     * 计算精度保留
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4DecimalScale4(BigDecimal numerator, BigDecimal denominator) {
        return percentFormat.format(calPercent4DecimalScale4(numerator, denominator));
    }


    /**
     * 两个bigdecimal值计算占比，四位精度
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4DecimalScale4(BigDecimal numerator, BigDecimal denominator) {
        if (Objects.isNull(numerator) || Objects.isNull(denominator)) {
            return BigDecimal.ZERO;
        }

        if (numerator.compareTo(BigDecimal.ZERO) == 0 || denominator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        //保留5位小数避免丢失进度
        return numerator.divide(denominator, 4, RoundingMode.HALF_UP);
    }

    /**
     * 两个int值计算占比，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4IntNoPercentScale4(int numerator, int denominator) {
        return percentFormat.format(calPercent4IntScale4(numerator, denominator)).replace("%", "");
    }


    /**
     * 4位 精度计算
     * 两个int值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4IntScale4(Integer numerator, Integer denominator) {
        if (numerator == null || denominator == null) {
            return BigDecimal.ZERO;
        }
        if (numerator == 0 || denominator == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 4, RoundingMode.HALF_UP);
    }


    /**
     * 4位精度计算
     * 两个int值计算占比，返回百分比字符串
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static String calPercentStr4IntScale4(int numerator, int denominator) {
        return percentFormat.format(calPercent4IntScale4(numerator, denominator));
    }


    /**
     * 保留4位小数
     * 两个int值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4IntScale4(Integer numerator, Long denominator) {
        if (numerator == null || denominator == null) {
            return BigDecimal.ZERO;
        }
        if (numerator == 0 || denominator == 0L) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 4, RoundingMode.HALF_UP);
    }



    /**
     * bigdecimal除以int值计算占比，保留两位小数，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4DecimalAndIntScale4(BigDecimal numerator, Integer denominator) {
        if (denominator == null) {
            return BigDecimal.ZERO;
        }
        return calPercent4DecimalScale4(numerator, new BigDecimal(denominator));
    }


    /**
     * 四位精度计算
     * 两个bigdecimal值计算同环比增长率，保留两位小数，返回百分比字符串
     * 计算环比和同比增长率时，如分子为0（分母不为0），记作↓100.00%；如分母为0（分子不为0），记作↑100.00%；如分子分母都为0，记作0.00%；
     * @param curPeriod 当期值
     * @param lastPeriod 同环比期值
     * @return
     */
    public static String calRate4DecimalScale4(BigDecimal curPeriod, BigDecimal lastPeriod) {
        if (Objects.isNull(curPeriod) || Objects.isNull(lastPeriod)) {
            throw new NullPointerException();
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0 && lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_ZERO;
        }

        if (curPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_NEGATIVE;
        }

        if (lastPeriod.compareTo(BigDecimal.ZERO) == 0) {
            return RATE_HUNDRED_POSITIVE;
        }

        //保留4位小数避免丢失进度
        BigDecimal result = curPeriod.divide(lastPeriod, 4, RoundingMode.HALF_UP).subtract(new BigDecimal(1));
        return percentFormat.format(result);
    }


    /**
     * 两个long值计算占比，返回数值
     * 计算占比时，如分子 或 分母 为0，占比记作0.00%
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    public static BigDecimal calPercent4LongScale4(long numerator, long denominator) {
        if (numerator == 0 || denominator == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(numerator).divide(new BigDecimal(denominator), 4, RoundingMode.HALF_UP);
    }



}
