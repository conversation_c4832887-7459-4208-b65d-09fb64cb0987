package com.meiyunji.sponsored.service.kafka.consumer;

import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.kafka.message.AdTargetTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 18:38
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.ad-target-task.enabled", havingValue = "true")
public class AdTargetTaskKafkaConsumer {
    
    @Autowired
    private IAdTargetTaskService adTargetTaskService;
    @Autowired
    private RedissonClient redissonClient;
    
    @KafkaListener(topics = "${kafka.consumers.ad-target-task.topic}", containerFactory = "adTargetTaskKafkaConsumerFactory")
    public void consumer(ConsumerRecord<?, byte[]> record) throws Exception {
        try {
            if (record == null || record.value() == null) {
                log.error("ad-target-task record is null");
                return;
            }
            AdTargetTaskMessage message = JSONUtil.jsonToObject(new String(record.value()), AdTargetTaskMessage.class);
            if (message == null) {
                log.error("ad-target-task message is null");
                return;
            }
            String lockKey = String.format(RedisConstant.AD_TARGET_TASK_KEY, message.getTaskId());
            RLock lock = redissonClient.getLock(lockKey);
            boolean b = lock.tryLock();
            if (!b) {
                log.info("AdTargetTaskKafkaConsumer 任务执行中, taskId:{},", message.getTaskId());
                return;
            }
            try {
                adTargetTaskService.doExecuteTask(message.getTaskId());
            } catch (Exception e) {
                log.error("AdTargetTaskKafkaConsumer taskId:{} 任务处理失败:", message.getTaskId(), e);
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("ad target task kafka message consumer error:",e);
        }
    }
}
