package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdProductResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdProductReqVo;

import java.util.List;


public interface IDashboardAdProductService {
    DashboardAdProductResponseVo.Page queryAdProductCharts(DashboardAdProductReqVo req);

    List<String> exportAdProductCharts(DashboardAdProductReqVo reqVo);

}
