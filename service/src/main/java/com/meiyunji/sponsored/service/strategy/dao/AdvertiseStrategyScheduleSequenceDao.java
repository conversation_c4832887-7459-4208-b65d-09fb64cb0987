package com.meiyunji.sponsored.service.strategy.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyScheduleSequence;

import java.util.List;

public interface AdvertiseStrategyScheduleSequenceDao extends IAdBaseDao<AdvertiseStrategyScheduleSequence> {

    Long genId();

    List<Long> batchGenId(Integer size);

}