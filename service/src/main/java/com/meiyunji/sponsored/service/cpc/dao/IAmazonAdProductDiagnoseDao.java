package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductDiagnose;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.DeleteDiagnoseReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.ListDiagnoseDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.ListDiagnoseReqVo;

import java.util.List;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */
public interface IAmazonAdProductDiagnoseDao extends IBaseShardingDao<AmazonAdProductDiagnose> {

    int saveDiagnose(AmazonAdProductDiagnose diagnose);

    int deleteDiagnose(DeleteDiagnoseReqVo reqVo);

    int countByUid(Integer puid, Integer uid, Byte moduleType, boolean containDelete);

    void batchSaveDiagnose(Integer puid, List<AmazonAdProductDiagnose> collect);

    Page<ListDiagnoseDto> pageList(ListDiagnoseReqVo reqVo);

    void updatePositionStatus(Integer puid, Integer uid, Byte moduleType);

    int updatePositionStatusById(Integer puid, Integer uid, Long id, Byte positionStatus);
}