package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * amazon广告投放定位表(OdsAmazonAdTargeting)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Data
@DbTable("ods_t_amazon_ad_netargeting_sb")
public class OdsAmazonAdSbNeTargeting implements Serializable {


	/**
	 * 主商户uid
	 */
	@DbColumn(value = "puid")
	private Integer puid;

	/**
	 * 店铺ID
	 */
	@DbColumn(value = "shop_id")
	private Integer shopId;

	/**
	 * 站点
	 */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;

	/**
	 * 配置ID
	 */
	@DbColumn(value = "profile_id")
	private String profileId;

	/**
	 * 投放id
	 */
	@DbColumn(value = "target_id")
	private String targetId;

	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;

	/**
	 * 广告组id
	 */
	@DbColumn(value = "ad_group_id")
	private String adGroupId;

	/**
	 * 投放表达式类型：category、asin
	 */
	@DbColumn(value = "type")
	private String type;

	/**
	 * 冗余字段：投放内容，值为asin或分类
	 */
	@DbColumn(value = "target_text")
	private String targetText;

	/**
	 * [{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
	 */
	@DbColumn(value = "expression")
	private String expression;

	/**
	 * [{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
	 */
	@DbColumn(value = "resolved_expression")
	private String resolvedExpression;

	/**
	 * 状态（enabled, paused, pending, archived, draft）
	 */
	@DbColumn(value = "state")
	private String state;

	/**
	 * asin标题（asin投放时）
	 */
	@DbColumn(value = "title")
	private String title;

	/**
	 * asin图片（asin投放时）
	 */
	@DbColumn(value = "img_url")
	private String imgUrl;

	/**
	 * 1在amzup创建，0从amazon同步
	 */
	@DbColumn(value = "create_in_amzup")
	private Integer createInAmzup;

	/**
	 * 创建人id
	 */
	@DbColumn(value = "create_id")
	private Integer createId;

	/**
	 * 修改人id
	 */
	@DbColumn(value = "update_id")
	private Integer updateId;
	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

	/**
	 * 平台的创建时间前30天
	 */
	@DbColumn(value = "creation_before_date")
	private LocalDate creationBeforeDate;

	/**
	 * 平台的创建时间后30天
	 */
	@DbColumn(value = "creation_after_date")
	private LocalDate creationAfterDate;

	/**
	 * 平台的创建时间
	 */
	@DbColumn(value = "creation_date")
	private LocalDateTime creationDate;


	/**
	 * 平台创建站点时间
	 */
	@DbColumn(value = "amazon_create_time")
	private LocalDateTime amazonCreateTime;

}

