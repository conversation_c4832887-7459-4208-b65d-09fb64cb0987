package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcKeywordsApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageSyncSpKeywordProcess extends AbstractSyncServerStatusProcessStrategy {


    @Resource
    private CpcKeywordsApiService cpcKeywordsApiService;


    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageSyncSpKeywordProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration, redisService);

    }



    @Override
    public int getMaxCount() {
        return StreamConstants.SP_MAX_KEYWORD_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        Boolean syncManageProxy = dynamicRefreshConfiguration.getSyncManageProxy();
        cpcKeywordsApiService.syncKeywords(shopAuth, null, null, ids, null, true, Boolean.TRUE.equals(syncManageProxy));
    }
}
