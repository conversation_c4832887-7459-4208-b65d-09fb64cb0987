package com.meiyunji.sponsored.service.autoRule.vo;

import com.meiyunji.sponsored.service.cpc.po.*;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2024-08-20  11:29
 */
@Data
public class PageAdControlledTargetProcessDto {
    //投放受控对象id集合
    private Set<String> targetIdSet = new HashSet<>();

    //sp关键词map
    private Map<String, AmazonAdKeyword> spkeywordMap = new HashMap<>();
    //sp投放map
    private Map<String, AmazonAdTargeting> spTargetMap = new HashMap<>();
    //sb关键词map
    private Map<String, AmazonSbAdKeyword> sbkeywordMap = new HashMap<>();
    //sb投放map
    private Map<String, AmazonSbAdTargeting> sbTargetMap = new HashMap<>();
    //sd投放map
    private Map<String, AmazonSdAdTargeting> sdTargetMap = new HashMap<>();


    //targetId -> sp组id集合
    private Set<String> spGroupIdSet = new HashSet<>();
    //sb组id集合
    private Set<String> sbGroupIdSet = new HashSet<>();
    //sd组id集合
    private Set<String> sdGroupIdSet = new HashSet<>();

    //sp组map
    private Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
    //sb组map
    private Map<String, AmazonSbAdGroup> sbGroupMap = new HashMap<>();
    //sd组map
    private Map<String, AmazonSdAdGroup> sdGroupMap = new HashMap<>();


    //活动id集合
    private Set<String> campaignIdSet = new HashSet<>();
    //活动map
    private Map<String, AmazonAdCampaignAll> campaignMap = new HashMap<>();


    //组合id集合
    private Set<String> portfolioIdSet = new HashSet<>();
    //组合map
    private Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();

}
