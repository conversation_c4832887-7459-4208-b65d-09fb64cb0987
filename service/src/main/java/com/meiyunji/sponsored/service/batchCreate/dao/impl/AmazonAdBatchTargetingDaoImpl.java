package com.meiyunji.sponsored.service.batchCreate.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.UpdateBuilder;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTargetingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.TaskStatusSetDto;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchGroup;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTargeting;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.TargetingTypeV3;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */

@Repository
public class AmazonAdBatchTargetingDaoImpl extends BaseShardingDaoImpl<AmazonAdBatchTargeting> implements IAmazonAdBatchTargetingDao {


    @Override
    public void insertList(Integer puid, List<AmazonAdBatchTargeting> targetingList) {
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (id, puid, shop_id, profile_id, marketplace_id, task_id, campaign_id, group_id, expression_type, type, state, select_type, asin, img_url, title,");
        sql.append(" category_id, category, category_name, brand, brand_name, min_price, max_price, min_review_rating, max_review_rating, prime_shipping_eligible,");
        sql.append(" bid, suggested, range_start, range_end, targeting_value, expression, task_status, execute_count, next_retry_time, create_time, create_id) values ");
        List<Object> argsList = new ArrayList<>(targetingList.size());
        for (AmazonAdBatchTargeting targeting : targetingList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(targeting.getId());
            argsList.add(targeting.getPuid());
            argsList.add(targeting.getShopId());
            argsList.add(targeting.getProfileId());
            argsList.add(targeting.getMarketplaceId());
            argsList.add(targeting.getTaskId());
            argsList.add(targeting.getCampaignId());
            argsList.add(targeting.getGroupId());
            argsList.add(targeting.getExpressionType());
            argsList.add(targeting.getType());
            argsList.add(Optional.ofNullable(targeting.getState()).filter(StringUtils::isNotEmpty).orElse(Constants.AUTO_TARGETING_STATE_ENABLE));
            argsList.add(targeting.getSelectType());
            argsList.add(targeting.getAsin());
            argsList.add(targeting.getImgUrl());
            argsList.add(targeting.getTitle());
            argsList.add(targeting.getCategoryId());
            argsList.add(targeting.getCategory());
            argsList.add(targeting.getCategoryName());
            argsList.add(targeting.getBrand());
            argsList.add(targeting.getBrandName());
            argsList.add(targeting.getMinPrice());
            argsList.add(targeting.getMaxPrice());
            argsList.add(targeting.getMinReviewRating());
            argsList.add(targeting.getMaxReviewRating());
            argsList.add(targeting.getPrimeShippingEligible());
            argsList.add(targeting.getBid());
            argsList.add(targeting.getSuggested());
            argsList.add(targeting.getRangeStart());
            argsList.add(targeting.getRangeEnd());
            argsList.add(targeting.getTargetingValue());
            argsList.add(targeting.getExpression());
            argsList.add(targeting.getTaskStatus());
            argsList.add(targeting.getExecuteCount());
            argsList.add(targeting.getNextRetryTime());
            argsList.add(targeting.getCreateTime());
            argsList.add(targeting.getCreateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateStatusByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> idList, Byte status) {
        StringBuilder sb = new StringBuilder("update ");
        sb.append(getJdbcHelper().getTable());
        sb.append(" set task_status = ? , update_time = now() where puid = ? and shop_id = ? and task_id = ? ");
        List<Object> args = new ArrayList<>();
        args.add(status);
        args.add(puid);
        args.add(shopId);
        args.add(taskId);
        if (!org.springframework.util.CollectionUtils.isEmpty(idList)) {
            SqlStringUtil.dealInList("id", idList, args);
        }
        sb.append(" and task_status = 0 ");
        getJdbcTemplate(puid).update(sb.toString(), status, puid, shopId, taskId);
    }

    @Override
    public void updateErrTaskStatusByIdList(Integer puid, Map<Long, String> idErrMsgMap, boolean updateExecuteCount) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `err_msg` = ?, `task_status` = 3, `update_time` = now() ");
        if (updateExecuteCount) {
            sql.append(", `execute_count` = execute_count + 1");
        }
        sql.append(" where puid = ? and id = ? and task_status in (0, 2, 4)");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (Map.Entry<Long, String> entry : idErrMsgMap.entrySet()) {
            batchArg = new Object[]{
                    entry.getValue(),
                    puid,
                    entry.getKey()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public void updateRetryTaskStatusByIdList(Integer puid, List<Long> idList, Date nextRetryTime) {
        UpdateBuilder ub = new UpdateBuilder(this.getJdbcHelper().getTable());
        ub.forColumn("task_status", SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode())
                .forColumnWithDbFunc("execute_count", "execute_count + 1")
                .forColumnWithDbFunc("update_time", "now()");
        if (nextRetryTime != null) {
            ub.forColumn("next_retry_time", nextRetryTime);
        }
        ub.where("puid", "=", puid)
                .andIn("id", idList)
                .andIn("task_status", Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
        getJdbcTemplate(puid).update(ub.toSql(), ub.getQueryValues());
    }

    @Override
    public void updateSuccTaskStatusByIdList(Integer puid, Map<Long, String> idAdIdMap) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `amazon_ad_target_id` = ?, `execute_count` = `execute_count` + 1, `task_status` = 1, `update_time` = now() ")
                .append(" where puid = ? and id = ? and task_status in (0, 2, 4) ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (Map.Entry<Long, String> entry : idAdIdMap.entrySet()) {
            batchArg = new Object[]{
                    entry.getValue(),
                    puid,
                    entry.getKey()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<Long> listIdByGroupIdList(Integer puid, Long taskId, List<Long> groupIdList, String expressionType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("task_id", taskId)
                .in("group_id", groupIdList.toArray());
        if (StringUtils.isNotBlank(expressionType)) {
            builder.equalTo("expression_type", expressionType);
        }
        return listDistinctFieldByCondition(puid, "id", builder.build(), Long.class);
    }

    @Override
    public Map<Long, String> listIdByGroupIdListAndStatus(Integer puid, Long taskId, List<Long> groupIdList, List<Integer> status) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("task_id", taskId)
                .in("group_id", groupIdList.toArray());
        if (CollectionUtils.isNotEmpty(status)) {
            builder.in("task_status", status.toArray());
        }
        List<AmazonAdBatchTargeting> targetingList = listByCondition(puid, builder.build());
        if (CollectionUtils.isEmpty(targetingList)) {
            return new HashMap<>();
        }
        return targetingList.parallelStream().filter(t -> Objects.nonNull(t.getAmazonAdTargetId())).collect(Collectors.toMap(AmazonAdBatchTargeting::getId, AmazonAdBatchTargeting::getAmazonAdTargetId));
    }

    @Override
    public List<AmazonAdBatchTargeting> listByGroupIdList(Integer puid, Integer shopId, Long taskId, List<Long> groupIdList, List<Byte> taskStatus, String expressionType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .in("group_id", groupIdList.toArray());
        if (CollectionUtils.isNotEmpty(taskStatus)) {
            builder.in("task_status", taskStatus.toArray());
        }
        if (StringUtils.isNotBlank(expressionType)) {
            builder.equalTo("expression_type", expressionType);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdBatchTargeting> listByShopIdAndTaskId(Integer puid, Integer shopId, Long taskId, List<Byte> taskStatus, String expressionType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId);
        if (CollectionUtils.isNotEmpty(taskStatus)) {
            builder.in("task_status", taskStatus.toArray());
        }
        if (StringUtils.isNotBlank(expressionType)) {
            builder.equalTo("expression_type", expressionType);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdBatchTargeting> listByIdList(Integer puid, Integer shopId, List<Long> idList, List<Byte> taskStatus, String expressionType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("id", idList.toArray());
        if (CollectionUtils.isNotEmpty(taskStatus)) {
            builder.in("task_status", taskStatus.toArray());
        }
        if (StringUtils.isNotBlank(expressionType)) {
            builder.equalTo("expression_type", expressionType);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchUpdateCampaignId(List<AmazonAdBatchCampaign> batchCampaignList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `amazon_campaign_id` = ?, `update_time` = now() ")
                .append(" where puid = ? and shop_id = ? and task_id = ? and campaign_id = ? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdBatchCampaign campaign : batchCampaignList) {
            batchArg = new Object[]{campaign.getAmazonCampaignId(), campaign.getPuid(), campaign.getShopId(), campaign.getTaskId(), campaign.getId()};
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(batchCampaignList.get(0).getPuid()).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public void batchUpdateAdGroupId(List<AmazonAdBatchGroup> batchGroupList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `amazon_ad_group_id` = ?, `update_time` = now() ")
                .append(" where puid = ? and shop_id = ? and task_id = ? and campaign_id = ? and group_id = ? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdBatchGroup group : batchGroupList) {
            batchArg = new Object[]{group.getAmazonAdGroupId(), group.getPuid(), group.getShopId(), group.getTaskId(), group.getCampaignId(), group.getId()};
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(batchGroupList.get(0).getPuid()).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<TaskStatusSetDto> distinctStatusByTaskId(Integer puid, Integer shopId, Long taskId) {
        StringBuilder sql = new StringBuilder("select campaign_id campaignId, group_concat(distinct task_status) statusStr from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? and task_id = ? group by campaign_id");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(TaskStatusSetDto.class), puid, shopId, taskId);
    }

    @Override
    public List<AmazonAdBatchTargeting> selectNeedRetryAmazon(Integer puid, Integer shopId, Long taskId, List<Long> groupIds, String expressionType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .isNotNull("amazon_campaign_id")
                .isNotNull("amazon_ad_group_id")
                .equalTo("expression_type", expressionType)
                .in("group_id", groupIds.toArray())
                .in("task_status", new Object[]{SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()})
                .lessThan("next_retry_time", LocalDateTime.now());

        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateAutoStatusSuccess(Integer puid, Integer shopId, Long taskId, List<Long> groupIdList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set `task_status` = 1, `update_time` = now() ")
                .append(" where puid = ? and shop_id = ? and task_id = ? ")
                .append(" and group_id in (")
                .append(StringUtils.join(groupIdList, ","))
                .append(" ) ")
                .append(" and expression_type = 'AUTO'");
        getJdbcTemplate(puid).update(sql.toString(), puid, shopId, taskId);
    }

    @Override
    public List<AmazonAdBatchTargeting> selectNeedRetryAmazonByAutoTarget(Integer puid, Integer shopId, Long taskId, List<Long> groupIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .isNotNull("amazon_campaign_id")
                .isNotNull("amazon_ad_group_id")
                .equalTo("expression_type", TargetingTypeV3.AUTO.getValue())
                .in("group_id", groupIds.toArray())
                .in("task_status", new Object[]{SpBatchCreateAdLevelStatusEnum.STOP.getCode()})
                .lessThan("next_retry_time", LocalDateTime.now());

        return listByCondition(puid, builder.build());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}