package com.meiyunji.sponsored.service.reportImport.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.amazonaws.util.json.Jackson;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportPlatformDao;
import com.meiyunji.sponsored.service.reportImport.dao.ICpcReportsImportTaskScheduleDao;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportTaskSchedule;
import com.meiyunji.sponsored.service.reportImport.enums.LxExcelAdType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.BaseLxReport;
import com.meiyunji.sponsored.service.reportImport.model.LxAdGroupReport;
import com.meiyunji.sponsored.service.reportImport.processor.AbstractLxReportImportProcessor;
import com.meiyunji.sponsored.service.reportImport.vo.ReportImportDetailVo;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * lx报告导入处理抽象类
 * 处理具体报告需要实现AbstractLxReportReadListener和AbstractLxReportImportProcessor
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractLxReportReadListener<T extends BaseLxReport> extends AnalysisEventListener<T> {

    private final AbstractLxReportImportProcessor<T> abstractLxReportImportProcessor;
    private final ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao;
    private final ICpcReportsImportPlatformDao cpcReportsImportPlatformDao;

    private Map<String, ShopAuth> shopNameMap;
    private AdReportImportMessage message;
    //记录每一个shopId对应读取到的行数
    private final Map<Integer, Integer> rowsMap = new HashMap<>();


    /**
     * 每隔500条存储数据库，实际使用中可以500条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;

    /**
     * 缓存的数据
     */
    private List<T> cachedDataList = Lists.newArrayListWithExpectedSize(BATCH_COUNT);

    public AbstractLxReportReadListener(AbstractLxReportImportProcessor<T> abstractLxReportImportProcessor,
                                        ICpcReportsImportTaskScheduleDao cpcReportsImportTaskScheduleDao, ICpcReportsImportPlatformDao cpcReportsImportPlatformDao) {
        this.abstractLxReportImportProcessor = abstractLxReportImportProcessor;
        this.cpcReportsImportTaskScheduleDao = cpcReportsImportTaskScheduleDao;
        this.cpcReportsImportPlatformDao = cpcReportsImportPlatformDao;
    }


    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {
        log.info("wade-report-import puid {} schedule_id {} report-type {} 所有数据解析开始解析！", message.getPuid(), message.getScheduleId(), message.getReportType());

        //校验必需字段,缺少字段直接报错
        List<String> importHeads = headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList());
        //获取类型上泛型的具体值
        ParameterizedType genericSuperclass = (ParameterizedType) this.getClass().getGenericSuperclass();
        Class<T> actualTypeArgument = (Class<T>) genericSuperclass.getActualTypeArguments()[0];


        Field[] declaredFields = actualTypeArgument.getDeclaredFields();
        List<String> needHeads = new ArrayList<>();
        //获取所有字段注解
        for (Field declaredField : declaredFields) {
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
            if (annotation != null) {
                needHeads.add(annotation.value()[0]);
            }
        }
        //获取抽象类上的字段
        Field[] superDeclaredFields = actualTypeArgument.getSuperclass().getDeclaredFields();
        for (Field superDeclaredField : superDeclaredFields) {
            ExcelProperty annotation = superDeclaredField.getAnnotation(ExcelProperty.class);
            if (annotation != null) {
                needHeads.add(annotation.value()[0]);
            }
        }
        needHeads.removeAll(importHeads);

        if (CollectionUtils.isNotEmpty(needHeads)) {
            String errMessage = "缺少表头: " + StringUtils.join(needHeads, "、");
            throw new BizServiceException(ReportImportErrType.HEAD_INVALID.name(), errMessage);
        }

        super.invokeHead(headMap, context);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {


        //发生异常,记录到数据库
        if (exception != null) {
            log.info("AbstractLxReportReadListener execute onException,context:{}，row index : {} ,errsg:{}",JSONUtil.objectToJson(context),context.readRowHolder().getRowIndex(), JSONUtil.objectToJson(exception));
            log.error("report-import-wade puid {}, report-type: {} fileId: {}  error: ", message.getPuid(), message.getReportType()
                    ,message.getFileId() ,exception);
            ReportImportErrType errType = ReportImportErrType.EXCEPTION;
            String errMsg = "";
            if (exception instanceof BizServiceException) {
                BizServiceException bizServiceException = (BizServiceException) exception;
                errType = ReportImportErrType.getByValue(bizServiceException.getCode());
                errMsg = bizServiceException.getMessage();
                log.info("AbstractLxReportReadListener execute BizServiceException，errMsg: {}", JSONUtil.objectToJson(exception.getStackTrace()));
            } else {
                errMsg = exception.getMessage();
                if (exception instanceof NumberFormatException) {
                    NumberFormatException numberFormatException = (NumberFormatException) exception;
                    errMsg = "数据格式存在错误 (" + numberFormatException.getMessage() + ")";
                }
            }
            log.info("AbstractLxReportReadListener execute errType: {}", errType);

            //组合错误信息
            Integer rowIndex = context.readRowHolder().getRowIndex();

            int successRows = 0;
            int num = (rowIndex - 1) % BATCH_COUNT;
            if (num == 0) {
                successRows = rowIndex - BATCH_COUNT;
            } else {
                successRows = rowIndex - num;
            }
            updateTaskStatus(errMsg, successRows, errType);
        }
        throw exception;
    }

    private void updateTaskStatus(String errMsg, int successRow, ReportImportErrType errType) {
        CpcReportsImportTaskSchedule schedule =
                cpcReportsImportTaskScheduleDao.getById(message.getPuid(), message.getScheduleId());

        ReportImportDetailVo fileVo = buildReportFileVo(schedule.getFileName(), successRow, errType,
                Collections.singletonList(StringUtils.isEmpty(errMsg) ? "未知错误" :  errMsg));

        cpcReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                ReportImportStatus.FATAL, Jackson.toJsonString(fileVo), ReportImportStatus.PROCESSING);
    }


    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        Integer rowIndex = analysisContext.readRowHolder().getRowIndex();
        if (t.hasNull()) {
            //如果必须字段有null,直接丢弃数据
            log.error("report-import-wade puid {} report-type {} fileId {} 字段存在null值",
                    message.getPuid(), message.getReportType(), message.getFileId());
            throw new BizServiceException("[行号 "+ (rowIndex + 1) +"] " + "字段对应值不能为空,请检查数据是否完整");
        }


        if (!getShopNameMap().containsKey(t.getShopName())) {
            log.info("导入数据不包含些店铺数据, 直接丢弃. puid: {} 店铺名称 {}", getAdReportImportMessage().getPuid(), t.getShopName());
            Map<String, String> map = new HashMap<>();
            map.put("shopName", t.getShopName());
            map.put("country", t.getCountryCode());

            throw new BizServiceException(ReportImportErrType.SHOP_ERROR.name(), Jackson.toJsonString(map));
        }

        //填充puid和shopId
        ShopAuth shopAuth = getShopNameMap().get(t.getShopName());
        t.setPuid(getAdReportImportMessage().getPuid());
        t.setShopId(shopAuth.getId());
        t.setMarketplaceId(shopAuth.getMarketplaceId());
        //设置行号
        t.setRowNumber(rowIndex);

        //领星的广告类型sb2,sbv转换成sb
        if (t.getAdType() != null && LxExcelAdType.parse(t.getAdType())) {
            t.setAdType(LxExcelAdType.SB.getName());
        }

        rowsMap.put(t.getShopId(), rowsMap.getOrDefault(t.getShopId(), 0) + 1);

        cachedDataList.add(t);
        if (cachedDataList.size() >= BATCH_COUNT) {
            abstractLxReportImportProcessor.importReport(getAdReportImportMessage(), cachedDataList, shopNameMap);
            // 存储完成清理 list
            cachedDataList = Lists.newArrayListWithExpectedSize(BATCH_COUNT);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        Integer rowIndex = context.readRowHolder().getRowIndex();

        try {
            if (CollectionUtils.isNotEmpty(cachedDataList)) {
                abstractLxReportImportProcessor.importReport(getAdReportImportMessage(), cachedDataList, shopNameMap);
            }

            cpcReportsImportTaskScheduleDao.updateStatusById(message.getPuid(), message.getScheduleId(),
                    ReportImportStatus.SUCCESS,  StringUtils.EMPTY, ReportImportStatus.PROCESSING);
            //excel有导入数据更新到
            Set<Integer> shopIdSet = rowsMap.keySet();
            cpcReportsImportPlatformDao.updateHasImportReportStatus(message.getPuid(), new ArrayList<>(shopIdSet), message.getTaskId());

        } catch (Exception e) {
            log.error("report-import-wade puid {}, report-type: {} fileId: {}  error: ", message.getPuid(), message.getReportType()
                    ,message.getFileId() ,e);
            //组合错误信息
            int successRows = 0;
            int num = (rowIndex - 1) % BATCH_COUNT;
            if (num == 0) {
                successRows = rowIndex - BATCH_COUNT;
            } else {
                successRows = rowIndex - num;
            }

            ReportImportErrType errType = ReportImportErrType.EXCEPTION;
            if (e instanceof BizServiceException) {
                BizServiceException bizServiceException = (BizServiceException) e;
                ReportImportErrType byValue = ReportImportErrType.getByValue(bizServiceException.getCode());
                if (byValue != null) {
                    errType =  byValue;
                }
            }
            updateTaskStatus(e.getMessage(), successRows, errType);
        }
        log.info("wade-report-import puid {} schedule_id {} report-type {} 所有数据解析完成！",
                message.getPuid(), message.getScheduleId(), message.getReportType());
    }

    public void setAdReportImportMessage(AdReportImportMessage message) {
        this.message = message;
    }

    public AdReportImportMessage getAdReportImportMessage() {
        return message;
    }

    public void setShopNameMap(Map<String, ShopAuth> shopNameMap) {
        this.shopNameMap = shopNameMap;
    }

    public Map<String, ShopAuth> getShopNameMap() {
        return shopNameMap;
    }


    private ReportImportDetailVo buildReportFileVo(String fileName, int successRow, ReportImportErrType errType, List<String> errMessages) {
        ReportImportDetailVo fileVo = new ReportImportDetailVo();
        fileVo.setStatus(ReportImportStatus.FATAL.name());
        fileVo.setFileName(fileName);
        fileVo.setRowNumber(Math.max(successRow, 1));
        fileVo.setErrType(errType == null ? ReportImportErrType.EXCEPTION.name() :  errType.name());
        fileVo.setErrInfos(errMessages);
        return fileVo;
    }
}
