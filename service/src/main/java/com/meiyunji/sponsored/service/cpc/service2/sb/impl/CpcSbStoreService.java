package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.pageAisn.PageAsinsResult;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sb.store.*;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.ICpcProductApiService;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.AdTypeEnum;
import com.meiyunji.sponsored.service.enums.ProductEligibilityStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * @Author: hejh
 * @Date: 2024/8/7 14:11
 */
@Slf4j
@Service
public class CpcSbStoreService {

    @Autowired
    private CpcSbStoreInfoApiService cpcSbStoreInfoApiService;
    @Autowired
    private CpcSbPageAsinsApiService pageAsinsApiService;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private ICpcProductApiService cpcProductApiService;
    @Autowired
    private DynamicRefreshConfiguration configuration;
    @Autowired
    private RedisService redisService;

    /**
     * 通过店铺子页面url分页查询asin列表，asin基础信息从t_product表查询，合格状态调用亚马逊接口获取
     * @param shop shop
     * @param profile profile
     * @param request request
     * @return List<SbAsinInfo>
     */
    public Result<PageSbAsinInfos> getAsinsByPages(ShopAuth shop, AmazonAdProfile profile, GetAsinInfoByPageReq request) {
        PageSbAsinInfos.Builder builder = PageSbAsinInfos.newBuilder();
        builder.setPageNo(request.getPageNo());
        builder.setPageSize(request.getPageSize());

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Result<PageAsinsResult> asinListResult;
        //放进缓存
        String cacheKey = String.valueOf(shop.getPuid()) + shop.getId() + request.getStorePageUrl();
        Object cacheValue = redisService.get(cacheKey);
        if (Objects.nonNull(cacheValue)) {
            try {
                asinListResult = (Result<PageAsinsResult>)cacheValue;
            } catch (Exception e) {
                asinListResult = pageAsinsApiService.getAsinList(shop, profile, request.getStorePageUrl());
                redisService.set(cacheKey, asinListResult, configuration.getAsinListByUrlCacheTimeSeconds());
            }
        } else {
            asinListResult = pageAsinsApiService.getAsinList(shop, profile, request.getStorePageUrl());
            redisService.set(cacheKey, asinListResult, configuration.getAsinListByUrlCacheTimeSeconds());
        }
        stopWatch.stop();
        log.info("puid:{}, shopId:{}, 单个getAsinList花费时间{}毫秒", shop.getPuid(), shop.getId(), stopWatch.getTotalTimeMillis());
        if (asinListResult.error()) {
            return ResultUtil.error(asinListResult.getMsg());
        }
        if (asinListResult.getData() == null || CollectionUtils.isEmpty(asinListResult.getData().getAsinList())) {
            builder.setTotalSize(0);
            builder.addAllAsins(Collections.emptyList());
            builder.setHasNextPage(false);
            return ResultUtil.success(builder.build());
        }
        List<String> asinList = asinListResult.getData().getAsinList();
        //如果搜索字段为asin，则在这里进行过滤，省的接下来查询很多asin
        if (StringUtils.isNotBlank(request.getSearchField()) && StringUtils.isNotBlank(request.getSearchValue()) && "asin".equalsIgnoreCase(request.getSearchField())) {
            for (String asin : asinList) {
                if (request.getSearchValue().equalsIgnoreCase(asin)) {
                    completeAsinInfo(shop, profile, Collections.singletonList(asin), builder, request);
                    return ResultUtil.returnSucc(builder.build());
                }
            }
            builder.setTotalSize(0);
            builder.addAllAsins(Collections.emptyList());
            builder.setHasNextPage(false);
            return ResultUtil.success(builder.build());
        }
        completeAsinInfo(shop, profile, asinList, builder, request);
        return ResultUtil.returnSucc(builder.build());
    }

    /**
     * 完善asin列表信息
     * @param shop shop
     * @param profile profile
     * @param asinList 需要完善的asin列表
     * @param resultBuilder 完善之后的asin列表保存在这里面
     * @param request request
     */
    private void completeAsinInfo(ShopAuth shop, AmazonAdProfile profile, List<String> asinList, PageSbAsinInfos.Builder resultBuilder, GetAsinInfoByPageReq request) {
        List<SbAsinInfo> sbAsinInfos = new ArrayList<>();
        String storePageUrl = request.getStorePageUrl();
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //1，从在线产品表中查询asin信息
        List<OdsProduct> odsProducts;
        if (StringUtils.isNotBlank(request.getSearchField()) && StringUtils.isNotBlank(request.getSearchValue()) && "title".equalsIgnoreCase(request.getSearchField())) {
            odsProducts = odsProductDao.listByAsin(shop.getPuid(), shop.getId(), asinList, request.getSearchValue());
        } else {
            odsProducts = odsProductDao.listByAsin(shop.getPuid(), shop.getId(), asinList, null);
        }
        stopWatch.stop();
        //最多ms
        log.info("puid:{}, shopId:{},odsProductDao.listByAsin接口，页面：{}时间花费{}毫秒", shop.getPuid(), shop.getId(), storePageUrl, stopWatch.getLastTaskTimeMillis());
        if (CollectionUtils.isEmpty(odsProducts)) {
            log.info("{}页面下查询在线产品表为空，puid：{}，shopId：{}, asinList:{}", storePageUrl, shop.getPuid(), shop.getId(), asinList);
            resultBuilder.setTotalSize(0);
            resultBuilder.addAllAsins(Collections.emptyList());
            resultBuilder.setHasNextPage(false);
            return;
        }
        //所有的产品记录根据asin去重
        Set<String> existedAsins = new HashSet<>();
        List<OdsProduct> uniqueAsinProducts = new ArrayList<>();//asin去重之后的产品列表
        for (OdsProduct product : odsProducts) {
            if (existedAsins.contains(product.getAsin())) {
                continue;
            }
            existedAsins.add(product.getAsin());
            uniqueAsinProducts.add(product);
        }
        //得到根据asin去重之后的产品列表的分页列表
        List<OdsProduct> finalProducts = getPage(uniqueAsinProducts, pageNo, pageSize);//最终分页后的产品列表

        List<String> needAsins = finalProducts.stream().map(OdsProduct::getAsin).collect(Collectors.toList());
        //一个asin包含多个sku，查询finalProducts中包含的asin的所有sku维度的产品列表，目的是查询产品的合格性（必须要以sku维度查询）。
        List<OdsProduct> queryEligibilityProducts = new ArrayList<>();
        for (OdsProduct product : odsProducts) {
            if (needAsins.contains(product.getAsin())) {
                queryEligibilityProducts.add(product);
            }
        }

        //2，调用亚马逊接口查询合格状态，并把asin的合格状态放进asinEligibilityStatusSetMap中
        stopWatch.start();
        List<ProductStatusDto> productStatusList = cpcProductApiService.getProductEligibility(shop, profile, AdTypeEnum.sb.getCampaignType(), null, queryEligibilityProducts);
        Map<String, Set<String>> asinEligibilityStatusSetMap = new HashMap<>();
        //设置合格状态：亚马逊查询合格状态维度为sku，而此接口是以asin维度返回的，一个asin可能包含多个sku，所以一个asin可能有多个合格状态:
        for (ProductStatusDto productStatus : productStatusList) {
            String asin = productStatus.getAsin();
            if (asinEligibilityStatusSetMap.containsKey(asin)) {
                asinEligibilityStatusSetMap.get(asin).add(productStatus.getStatus());
                continue;
            }
            Set<String> eligibilityStatusSet = new HashSet<>();
            eligibilityStatusSet.add(productStatus.getStatus());
            asinEligibilityStatusSetMap.put(asin, eligibilityStatusSet);
        }
        stopWatch.stop();
        //最多ms
        log.info("puid:{}, shopId:{},cpcProductApiService.getProductEligibility接口，页面：{}时间花费{}毫秒", shop.getPuid(), shop.getId(), storePageUrl, stopWatch.getLastTaskTimeMillis());

        //3，过滤重复asin，组装结果参数
        stopWatch.start();
        if (CollectionUtils.isNotEmpty(finalProducts)) {
            String domain = AmznEndpoint.getByMarketplaceId(finalProducts.get(0).getMarketplaceId()).getDomain();
            for (OdsProduct product : finalProducts) {
                String asin = product.getAsin();
                SbAsinInfo.Builder sbAsinInfo = SbAsinInfo.newBuilder();
                sbAsinInfo.setAsin(asin);
                sbAsinInfo.setDomain(domain);
                sbAsinInfo.setOnlineStatus(product.getOnlineStatus());
                sbAsinInfo.setTitle(product.getTitle());
                sbAsinInfo.setImgUrl(product.getMainImage());
                // 这里产品(林伟腾)规定只要asin中出现“ELIGIBLE”或者”ELIGIBLE_WITH_WARNING“，则为合格，否则为不合格
                if (asinEligibilityStatusSetMap.containsKey(asin)) {
                    Set<String> eligibilityStatus = asinEligibilityStatusSetMap.get(asin);
                    if (eligibilityStatus.contains(ProductEligibilityStatusEnum.ELIGIBLE.getValue()) || eligibilityStatus.contains(ProductEligibilityStatusEnum.ELIGIBLE_WITH_WARNING.getValue())) {
                        sbAsinInfo.setEligibilityStatus(ProductEligibilityStatusEnum.ELIGIBLE.getValue());
                    } else {
                        sbAsinInfo.setEligibilityStatus(ProductEligibilityStatusEnum.INELIGIBLE.getValue());
                    }
                }
                sbAsinInfos.add(sbAsinInfo.build());
            }
        }

        resultBuilder.addAllAsins(sbAsinInfos);
        resultBuilder.setTotalSize(uniqueAsinProducts.size());
        resultBuilder.setHasNextPage(hasNextPage(odsProducts, pageNo, pageSize));
        stopWatch.stop();
        //最多ms
        log.info("puid:{}, shopId:{},组装数据，页面：{}时间花费{}毫秒", shop.getPuid(), shop.getId(), storePageUrl, stopWatch.getLastTaskTimeMillis());
    }

    private <T> List<T> getPage(List<T> list, int pageNo, int pageSize) {
        List<T> page = new ArrayList<>();

        if (pageNo <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Page number and page size must be greater than zero.");
        }

        int totalSize = list.size();
        int totalPage = totalSize == 0 ? 0 : totalSize / pageSize + 1;
        //如果当前页大于总页数,则显示最后一页
        pageNo = Math.min(pageNo, totalPage);
        //如果当前页小于0,则显示第一页
        pageNo = (Math.max(pageNo, 1));

        int start = (pageNo - 1) * pageSize;
        int end = Math.min(totalSize, start + pageSize);

        if (start >= totalSize) {
            return page; // Return empty list if start index is beyond the end of the list
        }

        page.addAll(list.subList(start, end));
        return page;
    }

    public <T> boolean hasNextPage(List<T> list, int pageNo, int pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            throw new IllegalArgumentException("Page number and page size must be greater than zero.");
        }

        int totalSize = list.size();
        int start = (pageNo - 1) * pageSize;
        return start + pageSize < totalSize;
    }

    /**
     * 查询品牌旗舰店列表
     *
     * @param shop    shop
     * @param profile profile
     * @param request request
     * @return Result<List < StoresResult>>
     */
    public Result<List<StoresResult>> queryBrandStoreList(ShopAuth shop, AmazonAdProfile profile, QueryBrandStoreListRequest request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<StoresResult> resultList = new ArrayList<>();
        //2，查询店铺
        Result<List<com.amazon.advertising.sb.entity.storeInfo.StoresResult>> storeInfoResult = cpcSbStoreInfoApiService.getStoreInfo(shop, profile);
        stopWatch.stop();
        //最多花费567ms
        log.info("puid:{}, shopId:{},cpcSbStoreInfoApiService.getStoreInfo接口，查询店铺，时间花费{}毫秒", shop.getPuid(), shop.getId(), stopWatch.getLastTaskTimeMillis());

       /* if (shop.getId() == 4636) {
            storeInfoResult = mockData();
        }*/
        if (!storeInfoResult.success()) {
            return ResultUtil.error("查询店铺信息失败:" + storeInfoResult.getMsg());
        }
        List<com.amazon.advertising.sb.entity.storeInfo.StoresResult> storesList = storeInfoResult.getData();
        if (CollectionUtils.isEmpty(storesList)) {
            log.info("puid:{}, shopId{}, 查询品牌旗舰店为空", shop.getPuid(), shop.getId());
            return ResultUtil.success(resultList);
        }

        stopWatch.start();
        //3，多线程处理所有店铺
        List<CompletableFuture<Void>> futureList = new ArrayList<>(storesList.size());
        for (com.amazon.advertising.sb.entity.storeInfo.StoresResult storeResult : storesList) {
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                dealSingleStore(storeResult, request, shop, profile, resultList);
            }, ThreadPoolUtil.getStoreSpotlightStorePool()).exceptionally((e) -> {
                log.error("deal single storeResult error", e);
                return null;
            });
            futureList.add(voidCompletableFuture);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while waiting for CompletableFuture completion", e);
        }
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("puid:{}, shopId{}, 没有符合条件的品牌旗舰店", shop.getPuid(), shop.getId());
        }
        stopWatch.stop();
        //最多花费23820ms
        log.info("puid:{}, shopId:{},多线程处理所有店铺，时间花费{}毫秒", shop.getPuid(), shop.getId(), stopWatch.getLastTaskTimeMillis());
        return ResultUtil.success(resultList);
    }

    /**
     * 处理单个店铺
     * @param store 单个店铺
     * @param request request
     * @param shop shop
     * @param profile profile
     * @param collect 收集结果
     */
    private void dealSingleStore(com.amazon.advertising.sb.entity.storeInfo.StoresResult store, QueryBrandStoreListRequest request, ShopAuth shop, AmazonAdProfile profile, List<com.meiyunji.sponsored.rpc.sb.store.StoresResult> collect) {
        com.meiyunji.sponsored.rpc.sb.store.StoresResult.Builder resultBuilder = com.meiyunji.sponsored.rpc.sb.store.StoresResult.newBuilder();
        //校验页面数量，是否有首页，设置首页
        List<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo> storePageList = store.getStorePageInfo();
        if (!checkPageCount(store.getStoreName(), storePageList, shop, request.getMinSubPageCount()) || !checkContainsIndexPageAndSetIndexPage(store.getStoreName(), storePageList, shop, resultBuilder)) {
            return;
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //多线程处理所有页面
        List<com.meiyunji.sponsored.rpc.sb.store.StorePageInfo> storePageVoList = new ArrayList<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>(storePageList.size());
        //店铺页面-asin产品映射
        Map<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo, OdsProduct> storePageInfoOdsProductMap = new ConcurrentHashMap<>();
        List<OdsProduct> odsProductList = Collections.synchronizedList(Lists.newArrayList());
        for (com.amazon.advertising.sb.entity.storeInfo.StorePageInfo storePageInfo : storePageList) {
            if (Objects.isNull(storePageInfo) || StringUtils.isBlank(storePageInfo.getStorePageUrl()) || "HOME".equalsIgnoreCase(storePageInfo.getStorePageName())) {
                continue;
            }
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                dealSinglePageInfo(storePageInfo, shop, profile, request, storePageInfoOdsProductMap, odsProductList);
            }, ThreadPoolUtil.getStoreSpotlightPagePool()).exceptionally((e) -> {
                log.error("deal single storePageInfo error", e);
                return null;
            });
            futureList.add(voidCompletableFuture);
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while waiting for CompletableFuture completion", e);
        }
        stopWatch.stop();
        //最多23820ms
        log.info("puid:{}, shopId:{},多线程处理所有店铺页面，时间花费{}毫秒", shop.getPuid(), shop.getId(), stopWatch.getLastTaskTimeMillis());

        //3，查询合格状态并组装数据
        stopWatch.start();
        List<ProductStatusDto> productStatusList = cpcProductApiService.getProductEligibility(shop, profile, AdTypeEnum.sb.getCampaignType(), null, odsProductList);
        stopWatch.stop();
        //最多1297ms
        log.info("puid:{}, shopId{},getProductEligibility接口,时间花费{}毫秒", shop.getPuid(), shop.getId(), stopWatch.getLastTaskTimeMillis());

        storePageInfoOdsProductMap.forEach((storeSubPage, odsProduct) -> {
            SbAsinInfo.Builder sbAsinInfo = SbAsinInfo.newBuilder();
            sbAsinInfo.setAsin(odsProduct.getAsin());
            sbAsinInfo.setDomain(AmznEndpoint.getByMarketplaceId(odsProduct.getMarketplaceId()).getDomain());
            sbAsinInfo.setOnlineStatus(odsProduct.getOnlineStatus());
            sbAsinInfo.setTitle(odsProduct.getTitle());
            sbAsinInfo.setImgUrl(odsProduct.getMainImage());
            if (CollectionUtils.isNotEmpty(productStatusList)) {
                ProductStatusDto targetProductStatusDto = null;
                for (ProductStatusDto productStatusDto : productStatusList) {
                    if ((odsProduct.getAsin() + odsProduct.getSku()).equalsIgnoreCase((productStatusDto.getAsin() + productStatusDto.getSku()))) {
                        targetProductStatusDto = productStatusDto;
                        break;
                    }
                }
                if (Objects.nonNull(targetProductStatusDto)) {
                    if (ProductEligibilityStatusEnum.ELIGIBLE.getValue().equalsIgnoreCase(targetProductStatusDto.getStatus()) || ProductEligibilityStatusEnum.ELIGIBLE_WITH_WARNING.getValue().equalsIgnoreCase(targetProductStatusDto.getStatus())) {
                        sbAsinInfo.setEligibilityStatus(ProductEligibilityStatusEnum.ELIGIBLE.getValue());
                    } else {
                        sbAsinInfo.setEligibilityStatus(ProductEligibilityStatusEnum.INELIGIBLE.getValue());
                    }
                }
            }

            com.meiyunji.sponsored.rpc.sb.store.StorePageInfo.Builder rpcStorePageInfo = com.meiyunji.sponsored.rpc.sb.store.StorePageInfo.newBuilder();
            if (StringUtils.isNotBlank(storeSubPage.getStorePageId())) {
                rpcStorePageInfo.setStorePageId(storeSubPage.getStorePageId());
            }
            if (StringUtils.isNotBlank(storeSubPage.getStorePageUrl())) {
                rpcStorePageInfo.setStorePageUrl(storeSubPage.getStorePageUrl());
            }
            if (StringUtils.isNotBlank(storeSubPage.getStorePageName())) {
                rpcStorePageInfo.setStorePageName(storeSubPage.getStorePageName());
            }
            rpcStorePageInfo.addAllSbAsinInfos(Collections.singletonList(sbAsinInfo.build()));
            storePageVoList.add(rpcStorePageInfo.build());
        });

        //除首页之外最少要有request.getMinSubPageCount() - 1个子页面
        if (storePageVoList.size() < request.getMinSubPageCount() - 1) {
            log.info("除首页之外最少要有{}个子页面，puid:{}，shopId:{}, storeName:{}, storePageVoList:{}", request.getMinSubPageCount() - 1, shop.getPuid(), shop.getId(), store.getStoreName(), storePageVoList);
            return;
        }

        //结果builder设置值
        resultBuilder.addAllStorePageInfo(storePageVoList);
        if (StringUtils.isNotBlank(store.getBrandEntityId())) {
            resultBuilder.setEntityId(store.getEntityId());
        }
        if (StringUtils.isNotBlank(store.getStoreName())) {
            resultBuilder.setStoreName(store.getStoreName());
        }
        if (StringUtils.isNotBlank(store.getBrandEntityId())) {
            resultBuilder.setBrandEntityId(store.getBrandEntityId());
        }
        collect.add(resultBuilder.build());
    }

    /**
     * 处理单个子页面
     * @param storeSubPage 店铺子页面
     * @param shop shop
     * @param profile profile
     * @param request request
     * @param storePageVoList 收集结果
     */
    private void dealSinglePageInfo(com.amazon.advertising.sb.entity.storeInfo.StorePageInfo storeSubPage, ShopAuth shop, AmazonAdProfile profile, QueryBrandStoreListRequest request,
        Map<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo, OdsProduct> storePageInfoOdsProductMap, List<OdsProduct> odsProductList) {
        String storePageUrl = storeSubPage.getStorePageUrl();
        String storePageName = storeSubPage.getStorePageName();
        if (StringUtils.isBlank(storePageUrl)) {
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        //1，通过页面查包含的asin
        Result<PageAsinsResult> asinListResult;
        String cachekey = String.valueOf(shop.getPuid()) + shop.getId() + storePageUrl;
        Object cacheValue = redisService.get(cachekey);
        if (Objects.nonNull(cacheValue)) {
            try {
                asinListResult = (Result<PageAsinsResult>)cacheValue;
            } catch (Exception e) {
                asinListResult = pageAsinsApiService.getAsinList(shop, profile, storePageUrl);
                redisService.set(cachekey, asinListResult, configuration.getAsinListByUrlCacheTimeSeconds());
            }
        } else {
            asinListResult = pageAsinsApiService.getAsinList(shop, profile, storePageUrl);
            redisService.set(cachekey, asinListResult, configuration.getAsinListByUrlCacheTimeSeconds());
        }
        PageAsinsResult asinListResultData = asinListResult.getData();
        if (asinListResult.error() || Objects.isNull(asinListResultData) || CollectionUtils.isEmpty(asinListResultData.getAsinList()) || asinListResultData.getAsinList().size() < request.getMinAsinCountForSubPage()) {
            log.info("{}页面下查询亚马逊ASIN个数小于{}个，puid：{}，shopId：{}", storePageName, request.getMinAsinCountForSubPage(), shop.getPuid(), shop.getId());
            return;
        }
        List<String> asinList = asinListResultData.getAsinList();
        stopWatch.stop();
        //最多8642ms,页面：Hair Claw Clips
        log.info("puid:{}, shopId{},pageAsinsApiService.getAsinList接口，页面：{}时间花费{}毫秒", shop.getPuid(), shop.getId(), storePageName, stopWatch.getLastTaskTimeMillis());

        //2，asin必须存在于在线产品表
        stopWatch.start();
        List<OdsProduct> products = odsProductDao.listByAsin(shop.getPuid(), shop.getId(), asinList, null);
        stopWatch.stop();
        //最多61ms
        log.info("puid:{}, shopId:{},odsProductDao.listByAsin接口，页面：{}时间花费{}毫秒", shop.getPuid(), shop.getId(), storePageName, stopWatch.getLastTaskTimeMillis());
        if (CollectionUtils.isEmpty(products)) {
            log.info("{}页面下查询在线产品表为空，puid：{}，shopId：{}, asinList:{}", storePageName, shop.getPuid(), shop.getId(), asinList);
            return;
        }
        if (products.size() < request.getMinAsinCountForSubPage()) {
            log.info("{}页面下查询ods_t_product表ASIN个数小于{}个，puid：{}，shopId：{}, asinList:{}", storePageName, request.getMinAsinCountForSubPage(), shop.getPuid(), shop.getId(), asinList);
            return;
        }

        OdsProduct odsProduct = products.get(0);
        odsProductList.add(odsProduct);
        storePageInfoOdsProductMap.put(storeSubPage, odsProduct);
    }



    /**
     * 校验是否包含首页
     * @param storePageList 店铺页面列表
     * @param shop shop：用于日志打印
     * @return 是否包含首页
     */
    private boolean checkContainsIndexPageAndSetIndexPage(String storeName, List<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo> storePageList, ShopAuth shop, com.meiyunji.sponsored.rpc.sb.store.StoresResult.Builder builder){
        boolean containsIndexPage = false;
        for (com.amazon.advertising.sb.entity.storeInfo.StorePageInfo storePageInfo : storePageList) {
            if ("HOME".equalsIgnoreCase(storePageInfo.getStorePageName())) {
                containsIndexPage = true;

                com.meiyunji.sponsored.rpc.sb.store.StorePageInfo.Builder indexPageInfo = com.meiyunji.sponsored.rpc.sb.store.StorePageInfo.newBuilder();
                if (StringUtils.isNotBlank(storePageInfo.getStorePageId())) {
                    indexPageInfo.setStorePageId(storePageInfo.getStorePageId());
                }
                if (StringUtils.isNotBlank(storePageInfo.getStorePageUrl())) {
                    indexPageInfo.setStorePageUrl(storePageInfo.getStorePageUrl());
                }
                if (StringUtils.isNotBlank(storePageInfo.getStorePageName())) {
                    indexPageInfo.setStorePageName(storePageInfo.getStorePageName());
                }
                builder.setHomePage(indexPageInfo.build());

                break;
            }
        }
        if (!containsIndexPage) {
            log.info("此店铺不包含首页，puid：{}，shopId：{}, storeName:{}, storePageList:{}", shop.getPuid(), shop.getId(), storeName, storePageList);
        }
        return containsIndexPage;
    }

    /**
     * 校验子页面数量
     * @param storePageList 店铺页面列表
     * @param shop shop：用于日志打印
     * @param minSubPageCount 最小子页面数量
     * @return 是否达标
     */
    private boolean checkPageCount(String storeName, List<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo> storePageList, ShopAuth shop, int minSubPageCount){
        if (CollectionUtils.isEmpty(storePageList) || storePageList.size() < minSubPageCount) {
            log.info("店铺页面为空或店铺页面数量小于{}个，puid：{}，shopId：{}, storeName:{}, storePageList:{}", minSubPageCount, shop.getPuid(), shop.getId(), storeName, storePageList);
            return false;
        }
        return true;
    }
    private Result<List<com.amazon.advertising.sb.entity.storeInfo.StoresResult>> mockData(){
        com.amazon.advertising.sb.entity.storeInfo.StoresResult cpcVo = new com.amazon.advertising.sb.entity.storeInfo.StoresResult();
        cpcVo.setBrandEntityId("ENTITY3HTKDT57JW4ZD");
        cpcVo.setEntityId("ENTITY3HTKDT57JW4ZD");
        cpcVo.setStoreName("bose旗舰店");
        com.amazon.advertising.sb.entity.storeInfo.StorePageInfo page1 = new com.amazon.advertising.sb.entity.storeInfo.StorePageInfo();
        List<com.amazon.advertising.sb.entity.storeInfo.StorePageInfo> cpcInfoList = new ArrayList<>();
        page1.setStorePageName("NEW ULTRA OPEN EARBUDS");
        page1.setStorePageUrl("https://www.amazon.com/stores/page/E9FD8681-6DEA-42EF-AE66-63B8DD677FE8");
        cpcInfoList.add(page1);
        com.amazon.advertising.sb.entity.storeInfo.StorePageInfo page2 = new com.amazon.advertising.sb.entity.storeInfo.StorePageInfo();
        page2.setStorePageName("BEST SELLERS");
        page2.setStorePageUrl("https://www.amazon.com/stores/page/80B5AF51-ED3A-4363-BFB2-2F14A871DB51");
        cpcInfoList.add(page2);
        com.amazon.advertising.sb.entity.storeInfo.StorePageInfo page3 = new com.amazon.advertising.sb.entity.storeInfo.StorePageInfo();
        page3.setStorePageName("HEADPHONES");
        page3.setStorePageUrl("https://www.amazon.com/stores/page/EB5AA138-CCD5-4A01-9EA9-FB83C5B5A0B3");
        cpcInfoList.add(page3);
        com.amazon.advertising.sb.entity.storeInfo.StorePageInfo page4 = new com.amazon.advertising.sb.entity.storeInfo.StorePageInfo();
        page4.setStorePageName("PORTABLE SPEAKERS");
        page4.setStorePageUrl("https://www.amazon.com/stores/page/9FE5D838-B82C-4132-BC9C-FB6CF2099377");
        cpcInfoList.add(page4);
        com.amazon.advertising.sb.entity.storeInfo.StorePageInfo page5 = new com.amazon.advertising.sb.entity.storeInfo.StorePageInfo();
        page5.setStorePageName("HOME");
        page5.setStorePageUrl("https://www.amazon.com/stores/page/9E24AFD5-B420-4859-BD2F-A993BA579B20");
        cpcInfoList.add(page5);

        cpcVo.setStorePageInfo(cpcInfoList);
        return ResultUtil.returnSucc(Arrays.asList(cpcVo));
    }

}
