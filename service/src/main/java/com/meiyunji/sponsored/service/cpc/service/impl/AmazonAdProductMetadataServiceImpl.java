package com.meiyunji.sponsored.service.cpc.service.impl;

import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductMetadataDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductMetadata;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AmazonAdProductMetadataServiceImpl implements IAmazonAdProductMetadataService {

    @Autowired
    private IAmazonAdProductMetadataDao amazonAdProductMetadataDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public List<AmazonAdProductMetadata> getAsinBySkus(Integer puid,Integer shopId, List<String> skus,List<String> asins) {
        // 从数据库中获取数据
        List<AmazonAdProductMetadata> list = null;
        try {
            list = amazonAdProductMetadataDao.selectAmazonAdProductMetadataList(puid,shopId,skus,asins);
        }catch (Exception e) {
            log.error("puid={} shopId={} 查询广告商产品元数据异常{}",puid,shopId,e);
        }
        return list;
    }

    @Override
    public List<AmazonAdProductMetadata> getByAsinSkus(Integer puid, String marketplaceId, List<String> skus, List<String> asins) {
        // 从数据库中获取数据
        List<AmazonAdProductMetadata> list = null;
        try {
            list = amazonAdProductMetadataDao.selectAmazonAdProductMetadataList(puid, marketplaceId, skus, asins);
        } catch (Exception e) {
            log.error("puid={} marketplaceId={} 查询广告商产品元数据异常{}", puid, marketplaceId, e);
        }
        return list;
    }

    @Override
    public AmazonAdProductMetadata getImageByAsin(Integer puid,Integer shopId,String asin,String sku) {
        // 查询profileId
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid,shopId);
        // 从数据库中获取数据
        AmazonAdProductMetadata amazonAdProductMetadata = amazonAdProductMetadataDao.selectAmazonAdProductMetadata(puid,amazonAdProfile.getProfileId(),asin,sku);
        return amazonAdProductMetadata;
    }
}
