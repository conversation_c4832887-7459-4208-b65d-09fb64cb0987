package com.meiyunji.sponsored.service.batchCreate.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.template.MaxTemplateNoDto;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNameTemplate;

import java.util.List;

/**
 * @author: sunlinfeng
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-16  09:30
 */
public interface IAmazonAdBatchNameTemplateDao extends IBaseShardingDao<AmazonAdBatchNameTemplate> {
    MaxTemplateNoDto getCountAndMaxTemplateNo(Integer puid, Integer uid, Byte templateType);
    MaxTemplateNoDto getCountByPuidAndUid(Integer puid, Integer uid, Byte templateType);

    List<AmazonAdBatchNameTemplate> queryList(Integer puid, Integer uid, Byte templateType);

    List<AmazonAdBatchNameTemplate> queryListByPuidAndUid(Integer puid, Integer uid);
    AmazonAdBatchNameTemplate getById(Integer puid, Long id);
    int updateByPuidAndTemplateId(Integer puid, AmazonAdBatchNameTemplate template);

    int deleteByIdList(Integer puid, List<Long> idList);

    AmazonAdBatchNameTemplate getByUidAndNameAndTempType(Integer puid, Integer uid, String tempName, Integer templateType);

    int getDefaultByUidAndTempType(Integer puid, Integer uid, Integer templateType);
}
