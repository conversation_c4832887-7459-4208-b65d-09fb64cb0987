package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;
import org.omg.CORBA.PRIVATE_MEMBER;

import java.math.BigDecimal;

@Data
public class UpdateStrategyVo {
    private String rule;
    private Long statusId;
    private String status;
    private String itemType;
    private String itemId;
    private BigDecimal budgetValue;
    private BigDecimal biddingValue;
    private BigDecimal adPlaceTopValue;
    private BigDecimal adPlaceProductValue;
    private String state;
    private String strategy;
    private Integer version;
    private BigDecimal originBudgetValue;
    private BigDecimal originBiddingValue;
    private BigDecimal originAdPlaceTopValue;
    private BigDecimal originAdPlaceProductValue;
    private BigDecimal originAdOtherValue;
    private String originStrategy;
    private String originState;
    private Integer shopId;
    private String targetType;
    private String adType;
    private Integer taskAction;
    private Long recordId;
    private BigDecimal adOtherValue;

}
