package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.CpcAggregateIdsTemporary;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * @author: ys
 * @date: 2023/10/20 16:15
 * @describe:
 */
public interface ICpcAggregateIdsTemporaryDao extends IAdBaseDao<CpcAggregateIdsTemporary> {

    void setIdsTemporary(String pageSign, String ids, String saleInfo);

    String getIdsTemporary(String pageSign);

    Long getMaxLongId(LocalDateTime dateTime);

    int deleteAggregateIdsTemporary(Long idMin);
}
