package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;

import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregatePlacementIdsTemporary;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-03-13  13:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlacementAggregateHourParam {
    private Integer puid;
    private String uuid;
    private String marketplaceId;
    private List<Integer> shopIdList;
    @ApiModelProperty(value = "开始时间", required = true)
    private String startDate;
    @ApiModelProperty(value = "结束时间", required = true)
    private String endDate;
    @ApiModelProperty("对比开始时间")
    private String startDateCompare;
    @ApiModelProperty("对比结束时间")
    private String endDateCompare;
    @ApiModelProperty("星期逗号分隔")
    private String weeks;
    @ApiModelProperty("是否对比")
    private Integer isCompare;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("排序类型")
    private String orderType;
    @ApiModelProperty(value = "模式(小时-HOURLY,日-DAILY,周-WEEKLY,月-MONTHLY) SB,SD不支持小时级数据", required = true)
    private ReportDateModelPb.ReportDateModel dateModel;
    @ApiModelProperty(value = "广告类型(SP,SB,SD)", required = true)
    private String adType;
    @ApiModelProperty("搜索字段")
    private String queryField;
    @ApiModelProperty("搜索值")
    private String queryValue;
    @ApiModelProperty("搜索类型，模糊、精确")
    private String queryType;
    private String pageSign;
    @ApiModelProperty("汇总类型（关键词）")
    private String aggregateType;
    @ApiModelProperty("广告活动id")
    private String campaignIds;
    @ApiModelProperty("竞价策略")
    private String strategyType;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    private AggregatePlacementIdsTemporary aggregatePlacementIdsTemporary;
}
