package com.meiyunji.sponsored.service.syncAd.task.init.sb;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcNeKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sb + ShopDataSyncConstant.netargeting)
public class SbNeTargetingTask extends AdShopDataSyncTask {

    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;

    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sb);
        setTaskType(ShopDataSyncTaskTypeEnum.NETARGETING);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步否定关键词
        cpcSbNeKeywordApiService.syncNeKeywords(shop, null, task.getAdGroupId(), null, null, true);
        //同步否定投放
        cpcSbNeTargetApiService.syncNeTargets(shop, null, task.getAdGroupId(), null, true);
        //计算adGroupType
        cpcAdSyncService.confirmAdGroupTargetType(shop, null, task.getAdGroupId(), true, false);
    }
}
