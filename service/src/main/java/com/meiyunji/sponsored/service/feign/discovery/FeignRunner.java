//package com.meiyunji.sponsored.service.feign.discovery;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//
///**
// * @author: chenzimeng
// * @email: <EMAIL>
// * @date: 2024-03-18  11:26
// */
//@Slf4j
//@Configuration
//public class FeignRunner {
//    /**
//     * 工具服务-服务发现名称
//     */
//    @Value("${nacos.discovery.sellfoxTools:sellfox-tools}")
//    private String toolsServiceName;
//
//    /**
//     * 工具服务-服务发现组名
//     */
//    @Value("${nacos.discovery.sellfoxToolsGroup:DEFAULT_GROUP}")
//    private String toolsServiceGroup;
//
//    /**
//     * 工具服务-固定实例IP（开发环境使用，设置后不会再使用服务发现）
//     */
//    @Value("${nacos.discovery.sellfoxTools.fixedIp:}")
//    private String toolsFixedIp;
//
//    /**
//     * 工具服务-固定实例端口（开发环境使用，设置后不会再使用服务发现）
//     */
//    @Value("${nacos.discovery.sellfoxTools.fixedPort:0}")
//    private Integer toolsFixedPort;
//
//
//}
