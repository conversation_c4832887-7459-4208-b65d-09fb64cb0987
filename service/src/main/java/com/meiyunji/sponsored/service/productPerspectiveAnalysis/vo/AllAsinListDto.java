package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: sunlinfeng
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-01  10:16
 */

@Data
public class AllAsinListDto {
    //asin编码
    private List<String> asin;
    //父asin
    private List<String> parentAsin;
    //msku
    private List<String> msku;
    //店铺id
    private Integer shopId;
    //站点id
    private String marketplaceId;
    //图片
    private String mainImage;
}
