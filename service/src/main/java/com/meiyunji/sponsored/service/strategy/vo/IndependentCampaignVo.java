package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class IndependentCampaignVo {
    private Long id;
    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    private Long templateId;
    private String templateName;
    private BigDecimal originalBudget;
    private String currency;
    private String status;
    private String updateStatus;
    private String type;
    List<CampaignRuleVo> campaignRuleVoList;
    List<PeriodCampaignVo> periodCampaignVoList;
    private Long taskId;
    private String campaignId;
    private String childrenItemType;
}
