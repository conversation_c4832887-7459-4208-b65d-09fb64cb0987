package com.meiyunji.sponsored.service.dataWarehouse.statsDo;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.util.CurrencyConversion;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
public class ShopSaleDto implements Cloneable {

    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    @CurrencyConversion
    private BigDecimal sumRange;
    private Integer saleNum;
    private String currency;
    private LocalDate nowDate;
    /**
     * 总销量
     */
    private Long  sumSaleNum;

    public Long getSumSaleNum() {
        return sumSaleNum;
    }

    public void setSumSaleNum(Long sumSaleNum) {
        this.sumSaleNum = sumSaleNum;
    }

    public Integer getPuid() {
        return puid;
    }

    public void setPuid(Integer puid) {
        this.puid = puid;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public BigDecimal getSumRange() {
        return sumRange;
    }

    public void setSumRange(BigDecimal sumRange) {
        this.sumRange = sumRange;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getNowDate() {
        return nowDate;
    }

    public void setNowDate(LocalDate nowDate) {
        this.nowDate = nowDate;
    }

    public ShopSaleDto(BigDecimal sumRange) {
        this.sumRange = sumRange;
    }

    public Integer getSaleNum() {
        return saleNum;
    }

    public void setSaleNum(Integer saleNum) {
        this.saleNum = saleNum;
    }

    public ShopSaleDto merge(ShopSaleDto obj) {
        if (obj != null) {
            this.sumRange = MathUtil.add(this.sumRange, obj.getSumRange());
            this.saleNum = MathUtil.add(this.saleNum, obj.getSaleNum());
        }


        return this;
    }


    @SneakyThrows
    @Override
    public ShopSaleDto clone() {
        return (ShopSaleDto) super.clone();
    }
}
