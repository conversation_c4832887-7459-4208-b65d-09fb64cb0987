package com.meiyunji.sponsored.service.strategyTask.api.strategy;

import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sellfox.aadas.types.exception.AmazonDuplicateAdItemIdException;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.config.IndexStrategyConfig;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.helper.AdvertiseStrategyGroupTargetBidHelper;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.api.StrategyApi;
import com.meiyunji.sponsored.service.strategyTask.api.StrategyCommonApi;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyRealTimeBidDao;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordSequenceDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyRealTimeBid;
import com.meiyunji.sponsored.service.strategyTask.vo.DeleteStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.TransferStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.UpdateStrategyResponseVo;
import com.meiyunji.sponsored.service.strategyTask.vo.UpdateStrategyStateResponseVo;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-10  11:18
 */
@Component
@Slf4j
public class PortfolioHourBudgetStrategy extends StrategyCommonApi implements StrategyApi {

    protected PortfolioHourBudgetStrategy(AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao, AdvertiseStrategyStatusDao advertiseStrategyStatusDao, AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao, AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao, IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonSbAdKeywordDao amazonSbAdKeywordDao, IAmazonSbAdTargetingDao amazonSbAdTargetingDao, IAmazonSdAdTargetingDao amazonSdAdTargetingDao, AadasApiFactory aadasApiFactory, AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao, AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAdManageOperationLogService adManageOperationLogService, IAmazonAdPortfolioDao portfolioDao, AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao, RedisService redisService, IndexStrategyConfig indexStrategyConfig, AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao, AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao, AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao, RedissonClient redissonClient, AdvertiseStrategyGroupTargetBidHelper advertiseStrategyGroupTargetBidHelper) {
        super(advertiseStrategyTemplateDao, advertiseStrategyStatusDao, advertiseStrategyStatusDeleteDao, advertiseStrategyScheduleDao, amazonAdCampaignAllDao, amazonSbAdKeywordDao, amazonSbAdTargetingDao, amazonSdAdTargetingDao, aadasApiFactory, advertiseStrategyStatusSequenceDao, advertiseStrategyScheduleSequenceDao, amazonAdGroupDao, amazonSbAdGroupDao, amazonSdAdGroupDao, adManageOperationLogService, portfolioDao, advertiseStrategyTemplateSequenceDao, redisService, indexStrategyConfig, advertiseStrategyAdGroupDao, advertiseStrategyRealTimeBidDao, advertiseStrategyTaskRecordSequenceDao, redissonClient, advertiseStrategyGroupTargetBidHelper);
    }

    @Override
    public boolean checkValid(String itemType) {
        return Constants.PORTFOLIO_HOUR.equals(itemType);
    }

    @Override
    public AddStrategyVo submitStrategy(Integer puid, List<SubmitStrategyVo> submitStrategyVos, Long templateId, Integer updateId, String loginIp, String traceId) {
        return null;
    }

    private final static String ENABLED = "ENABLED";

    @Override
    public List<UpdateStrategyResponseVo> updateStrategy(Integer puid, Long templateId, List<UpdateStrategyVo> updateStrategyVoList, AdvertiseStrategyTemplate vo, Integer updateId, String traceId, Integer taskAction, Map<Long, AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBidMap) {
        List<UpdateStrategyResponseVo> responseVoList = Lists.newArrayList();
        try {
            List<Long> statusIdList = updateStrategyVoList.stream().map(UpdateStrategyVo::getStatusId).collect(Collectors.toList());
            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, statusIdList);
            Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity()));
            for (UpdateStrategyVo updateStrategyVo : updateStrategyVoList) {
                List<AdvertiseStrategySchedule> list = Lists.newArrayList();
                UpdateStrategyResponseVo responseVo = new UpdateStrategyResponseVo();
                responseVo.setPuid(puid);
                responseVo.setShopId(updateStrategyVo.getShopId());
                responseVo.setItemId(updateStrategyVo.getItemId());
                responseVo.setItemType(updateStrategyVo.getItemType());
                responseVo.setTargetType(updateStrategyVo.getTargetType());
                responseVo.setAdType(updateStrategyVo.getAdType());
                responseVo.setStatusId(updateStrategyVo.getStatusId());
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(updateStrategyVo.getStatusId());
                if (advertiseStrategyStatus == null) {
                    log.info("puid:{} statusId:{} 分时调价修改受控对象找不到", puid, updateStrategyVo.getStatusId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("分时调价修改受控对象找不到");
                    responseVoList.add(responseVo);
                    continue;
                }
                String originValueJson = vo.getReturnValue();
                List<PortfolioHourRuleVo> ruleVoList = JSONUtil.jsonToArray(vo.getRule(), PortfolioHourRuleVo.class);
                if (ruleVoList == null) {
                    log.info("puid:{} statusId:{} 序列化规则错误", puid, updateStrategyVo.getStatusId());
                    continue;
                }
                for (PortfolioHourRuleVo portfolioRuleVo : ruleVoList) {
                    AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                    advertiseStrategySchedule.setPuid(puid);
                    advertiseStrategySchedule.setShopId(advertiseStrategyStatus.getShopId());
                    advertiseStrategySchedule.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                    advertiseStrategySchedule.setItemType(advertiseStrategyStatus.getItemType());
                    advertiseStrategySchedule.setItemId(advertiseStrategyStatus.getItemId());
                    advertiseStrategySchedule.setType(vo.getType());
                    advertiseStrategySchedule.setAdType(advertiseStrategyStatus.getAdType());
                    advertiseStrategySchedule.setTaskId(advertiseStrategyStatus.getTaskId());
                    OriginValueVo newValueVo = new OriginValueVo();
                    newValueVo.setAmount(portfolioRuleVo.getAmount());
                    newValueVo.setPolicy(portfolioRuleVo.getPolicy());
                    String newValueJson = JSONUtil.objectToJson(newValueVo);
                    advertiseStrategySchedule.setOriginValue(advertiseStrategyStatus.getOriginValue());
                    advertiseStrategySchedule.setNewValue(newValueJson);
                    advertiseStrategySchedule.setReturnValue(originValueJson);
                    advertiseStrategySchedule.setChildrenItemType(vo.getChildrenItemType());
                    advertiseStrategySchedule.setDay(portfolioRuleVo.getSiteDate());
                    advertiseStrategySchedule.setStart(portfolioRuleVo.getStartTimeSite() * 60);
                    advertiseStrategySchedule.setEnd(portfolioRuleVo.getEndTimeSite() * 60);
                    list.add(advertiseStrategySchedule);
                }
                try {
                    if (ENABLED.equals(advertiseStrategyStatus.getStatus())) {
                        aadasApiFactory.getStrategyApi(TaskTimeType.portfolioHourAmount).setSchedule(advertiseStrategyStatus.getTaskId(),
                                templateId, list, true);
                    }
                    advertiseStrategyStatusDao.updateHourStatus(puid, templateId, vo.getType(), updateStrategyVo.getStatusId(), vo.getRule(), advertiseStrategyStatus.getStatus(), vo.getVersion(), advertiseStrategyStatus.getOriginValue(), vo.getReturnValue(), vo.getChildrenItemType());
                    List<Long> ids = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                    if (CollectionUtils.isNotEmpty(ids)) {
                        advertiseStrategyScheduleDao.deleteStrategyByIds(puid, ids);
                    }
                    List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
                    for (int i = 0; i < longIdList.size(); i++) {
                        list.get(i).setId(longIdList.get(i));
                    }
                    advertiseStrategyScheduleDao.batchInsert(puid, list);
                } catch (Exception e) {
                    if (e instanceof AmazonDuplicateAdItemIdException) {
                        responseVo.setMsg(e.getMessage());
                        responseVo.setIsGrayPlacement(false);
                    } else {
                        responseVo.setMsg("API接口超时，请重试");
                        responseVo.setIsGrayPlacement(true);
                    }
                    responseVoList.add(responseVo);
                    log.error("traceId:{} puid:{} 分时调价任务修改异常", traceId, puid, e);
                }
            }
            addSyncTemplateOperationLog(puid, vo, updateStrategyVoList, updateId);
        } catch (Exception e) {
            log.error("puid={} 分时广告组合预算任务修改异常:", puid, e);
        }
        return responseVoList;
    }

    @Override
    public List<UpdateStrategyStateResponseVo> updateStrategyStatus(Integer puid, Long templateId, List<Long> statusIdList, String status, String loginIp, Integer uid, String traceId) {
        List<UpdateStrategyStateResponseVo> responseVoList = Lists.newArrayList();
        List<AdManageOperationLog> adManageOperationLogList = new ArrayList<>();
        try {
            List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, templateId, statusIdList);
            Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity(), (e1, e2) -> e1));

            for (Long statusId : statusIdList) {
                UpdateStrategyStateResponseVo responseVo = new UpdateStrategyStateResponseVo();
                responseVo.setStatusId(statusId);
                if (!advertiseStrategyStatusMap.containsKey(statusId)) {
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("当前受控对象找不到");
                    responseVoList.add(responseVo);
                    continue;
                }
                // 新增策略开启/暂停的
                AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
                OperationContent operationContent = new OperationContent();
                try {
                    AdvertiseStrategyStatus strategyStatus = advertiseStrategyStatusMap.get(statusId);
                    List<AdvertiseStrategySchedule> list = advertiseStrategyScheduleDao.listByTaskId(puid, strategyStatus.getShopId(), strategyStatus.getTaskId(),
                            strategyStatus.getItemType());
                    addControlledObjectsLog(puid, uid, loginIp, strategyStatus.getItemType(), null, adManageOperationLog, false);
                    filterBaseMessage(strategyStatus.getAdType(), strategyStatus.getMarketplaceId(),
                            strategyStatus.getCampaignId(), strategyStatus.getAdGroupId(), strategyStatus.getItemId(), adManageOperationLog, strategyStatus.getItemId());
                    adManageOperationLog.setShopId(strategyStatus.getShopId());
                    // 回写受控对象表数据
                    if (ENABLED.equals(status)) {
                        operationContent.setTitle("开启分时广告组合预算任务");
                        // 推送数据到aads调度服务
                        aadasApiFactory.getStrategyApi(TaskTimeType.portfolioHourAmount).setSchedule(strategyStatus.getTaskId(),
                                strategyStatus.getTemplateId(), list, true);
                        // 修改状态
                        advertiseStrategyStatusDao.updateStrategyStatusById(puid, strategyStatus.getId(), status);
                        portfolioDao.updateStatePricing(puid, strategyStatus.getShopId(),
                                strategyStatus.getItemId(), 1, 1, uid);
                    } else {
                        operationContent.setTitle("暂停分时广告组合预算任务");
                        // 将aads调度服务数据删除
                        aadasApiFactory.getStrategyApi(TaskTimeType.portfolioHourAmount).removeSchedule(puid, strategyStatus.getShopId(),
                                strategyStatus.getTaskId(), true);
                        //修改状态
                        advertiseStrategyStatusDao.updateStrategyStatusById(puid, strategyStatus.getId(), status);
                        portfolioDao.updateStatePricing(puid, strategyStatus.getShopId(),
                                strategyStatus.getItemId(), 1, 0, uid);
                    }
                    adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
                    adManageOperationLog.setResult(0);
                } catch (Exception e) {
                    log.error("traceId:{} puid:{} statusId:{} 分时调价任务修改状态异常", traceId, puid, statusId, e);
                    adManageOperationLog.setResult(1);
                    adManageOperationLog.setResultInfo("分时调价任务修改状态异常:" + e.getMessage());
                }
                adManageOperationLogList.add(adManageOperationLog);
            }
        } catch (Exception e) {
            log.error("traceId:{} puid:{} statusId:{} 分时调价任务修改状态异常", traceId, puid, statusIdList, e);

        }
        if (CollectionUtils.isNotEmpty(adManageOperationLogList)) {
            adManageOperationLogService.printAdOtherOperationLog(adManageOperationLogList);
        }
        return responseVoList;
    }

    @Override
    public List<TransferStrategyResponseVo> transferStrategy(Integer puid, Long templateId, List<Long> statusIdList, Integer operation, String loginIp, Integer uid, String traceId) {
        List<TransferStrategyResponseVo> responseVoList = Lists.newArrayList();
        // TODO 广告组合没有转移的前端
        // 其他操作日志记录(编辑:转移受控对象 包含移除和添加)
        return responseVoList;
    }

    @Override
    public List<DeleteStrategyResponseVo> removeStrategy(Integer puid, Long templateId, List<RemoveStrategyVo> removeStrategyVoList, Integer updateId, String loginIp, String traceId) {
        List<DeleteStrategyResponseVo> responseVoList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(removeStrategyVoList)) {
            return responseVoList;
        }
        // 其他操作日志记录(编辑:移除受控对象)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<String> portfolioIdList = Lists.newArrayList();
        addControlledObjectsLog(puid, updateId, loginIp, removeStrategyVoList.get(0).getItemType(), null, adManageOperationLog, true);
        List<Long> ids = removeStrategyVoList.stream().map(RemoveStrategyVo::getStatusId).collect(Collectors.toList());
        List<AdvertiseStrategyStatus> strategyStatusList = advertiseStrategyStatusDao.getByStatusIds(puid, templateId, ids);
        Map<Long, AdvertiseStrategyStatus> advertiseStrategyStatusMap = strategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getId, Function.identity()));

        if (strategyStatusList.size() > 0) {
            adManageOperationLog.setTemplateId(strategyStatusList.get(0).getTemplateId());
            adManageOperationLog.setTargetId(String.valueOf(strategyStatusList.get(0).getTemplateId()));
            adManageOperationLog.setShopId(strategyStatusList.get(0).getShopId());
            adManageOperationLog.setMarketplaceId(strategyStatusList.get(0).getMarketplaceId());
        }
        try {
            for (RemoveStrategyVo removeStrategyVo : removeStrategyVoList) {
                DeleteStrategyResponseVo deleteStrategyResponseVo = new DeleteStrategyResponseVo();
                deleteStrategyResponseVo.setStatusId(removeStrategyVo.getStatusId());
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(removeStrategyVo.getStatusId());
                if (advertiseStrategyStatus == null) {
                    deleteStrategyResponseVo.setMsg("删除失败，受控对象不存在");
                    deleteStrategyResponseVo.setIsRetry(1);
                    responseVoList.add(deleteStrategyResponseVo);
                    continue;
                }
                // 日志信息收集
                aadasApiFactory.getStrategyApi(TaskTimeType.portfolioHourAmount).removeSchedule(puid, removeStrategyVo.getShopId(), removeStrategyVo.getTaskId(), true);
                portfolioIdList.add(advertiseStrategyStatus.getItemId());
                //任务调度服务删除策略
                //广告服务删除策略
                Long statusId = advertiseStrategyStatus.getId();
                Long taskId = advertiseStrategyStatus.getTaskId();
                advertiseStrategyStatusDao.deleteStrategyStatus(puid, statusId, advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(puid, advertiseStrategyStatus.getShopId(), taskId, advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(puid, scheduleIds);
                }
                advertiseStrategyStatusDeleteDao.insetStrategyStatus(puid, advertiseStrategyStatus);
                portfolioDao.updateStatePricing(puid, advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getItemId(), 0, 0, updateId);
            }

            // 获取campaignName
            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                List<AmazonAdPortfolio> amazonAdPortfolios = portfolioDao.getPortfolioList(puid, removeStrategyVoList.get(0).getShopId(), portfolioIdList);
                if (CollectionUtils.isNotEmpty(amazonAdPortfolios)) {
                    List<String> portfolioNames = amazonAdPortfolios.stream().filter(Objects::nonNull).map(AmazonAdPortfolio::getName).distinct().collect(Collectors.toList());
                    operationContent.setNewValue(StringUtils.join(portfolioNames, "、"));
                    adManageOperationLog.setPortfolioId(StringUtils.join(portfolioIdList, "、"));
                }
                operationContent.setTitle("移除受控对象");
                adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            }
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo("转移受控对象异常:" + e.getMessage());
            log.error("traceId:{} puid:{} 移除策略异常", traceId, puid, e);
        }
        List<AdManageOperationLog> list = new ArrayList<>();
        list.add(adManageOperationLog);
        adManageOperationLogService.printAdOtherOperationLog(list);
        return responseVoList;
    }

}
