package com.meiyunji.sponsored.service.reportDiffMonitor.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 广告层级类型：1-活动、2-组、3-投放、4-广告产品
 *
 * @Author: hejh
 * @Date: 2024/5/30 19:11
 */
public enum LevelTypeEnum {

    CAMPAIGN(1, "活动", TimeLevel.SP_CAMPAIGN_TIME_LEVEL, TimeLevel.SD_CAMPAIGN_TIME_LEVEL),
    GROUP(2, "广告组", TimeLevel.SP_GROUP_TIME_LEVEL, TimeLevel.SD_GROUP_TIME_LEVEL),
    TARGETING(3, "投放", TimeLevel.SP_TARGET_TIME_LEVEL, TimeLevel.SD_TARGET_TIME_LEVEL),
    PRODUCT(4, "广告产品", TimeLevel.SP_PRODUCT_TIME_LEVEL, TimeLevel.SD_PRODUCT_TIME_LEVEL),
    ;

    private final int value;
    private final String desc;
    private final Map<String, Integer> spTimeLevel;
    private final Map<String, Integer> sdTimeLevel;

    LevelTypeEnum(int value, String desc, Map<String, Integer> spTimeLevel, Map<String, Integer> sdTimeLevel) {
        this.value = value;
        this.desc = desc;
        this.spTimeLevel = spTimeLevel;
        this.sdTimeLevel = sdTimeLevel;
    }

    public static LevelTypeEnum valueOf(int value) {
        for (LevelTypeEnum myEnum : LevelTypeEnum.values()) {
            if (myEnum.value == value) {
                return myEnum;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }

    public static List<LevelTypeEnum> spLevelTypeEnums = Arrays.asList(CAMPAIGN, GROUP, TARGETING, PRODUCT);
    public static List<LevelTypeEnum> sdLevelTypeEnums = Arrays.asList(CAMPAIGN, GROUP, TARGETING, PRODUCT);

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public Map<String, Integer> getSpTimeLevel() {
        return spTimeLevel;
    }

    public Map<String, Integer> getSdTimeLevel() {
        return sdTimeLevel;
    }
}
