package com.meiyunji.sponsored.service.syncAd.task.init.sd;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbAdsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdProductApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sd + ShopDataSyncConstant.product)
public class SdProductTask extends AdShopDataSyncTask {

    @Autowired
    private CpcSdProductApiService cpcSdProductApiService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sd);
        setTaskType(ShopDataSyncTaskTypeEnum.PRODUCT);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步广告产品，查询所有状态
        cpcSdProductApiService.syncProductAds(shop, null, task.getAdGroupId(), null, null, true);
    }
}
