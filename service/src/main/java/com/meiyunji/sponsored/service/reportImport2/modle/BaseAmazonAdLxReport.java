package com.meiyunji.sponsored.service.reportImport2.modle;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.mode.report.CommonReport;
import com.amazon.advertising.mode.report.ReportAdProduct;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class BaseAmazonAdLxReport {
    /**
     * 曝光量
     */
    @JsonProperty("impressions")
    private Integer impressions;

    /**
     * 点击量
     */
    @JsonProperty("clicks")
    private Integer clicks;

    /**
     * 广告花费
     */
    @JsonProperty("spends")
    private BigDecimal spends;

    /**
     * 广告订单量
     */
    @JsonProperty("orders")
    private Integer orders;

    /**
     * 本广告产品订单量
     */
    @JsonProperty("direct_orders")
    private Integer directOrders;

    /**
     * 广告销售额
     */
    @JsonProperty("sales")
    private BigDecimal sales;

    /**
     * 本广告产品销售额
     */
    @JsonProperty("direct_sales")
    private BigDecimal directSales;

    /**
     * 广告销量
     */
    @JsonProperty("ad_units")
    private Integer adUnits;

    /**
     * 本广告产品销量
     */
    @JsonProperty("direct_units")
    private Integer directUnits;

    /**
     * 可见展示次数
     */
    @JsonProperty("view_impressions")
    private Integer viewImpressions;


    public void readFromJsonReader(String key, JSONReader jsonReader, BaseAmazonAdLxReport commonReport) {
        switch (key) {
            case "impressions":
                this.setImpressions(jsonReader.readInteger());
                break;
            case "clicks":
                this.setClicks(jsonReader.readInteger());
                break;
            case "spends":
                String spends = jsonReader.readString();
                this.setSpends(StringUtils.isNotBlank(spends) ? new BigDecimal(spends) : BigDecimal.ZERO);
                break;
            case "orders":
                this.setOrders(jsonReader.readInteger());
                break;
            case "direct_orders":
                this.setDirectOrders(jsonReader.readInteger());
                break;
            case "sales":
                String sales = jsonReader.readString();
                this.setSales(StringUtils.isNotBlank(sales) ? new BigDecimal(sales) : BigDecimal.ZERO);
                break;
            case "direct_sales":
                String direct_sales = jsonReader.readString();
                this.setDirectSales(StringUtils.isNotBlank(direct_sales) ? new BigDecimal(direct_sales) : BigDecimal.ZERO);
                break;
            case "ad_units":
                this.setAdUnits(jsonReader.readInteger());
                break;
            case "direct_units":
                this.setDirectUnits(jsonReader.readInteger());
                break;
            default:
                log.info("import-report,skip key:{}", key);
                jsonReader.readString();
                break;

        }
    }

    public boolean isValid() {
        return this.getImpressions() != null && this.getImpressions() != 0
                || this.getClicks() != null && this.getClicks() != 0
                || this.getSpends() != null && this.getSpends().compareTo(BigDecimal.ZERO) > 0
                || this.getSales() != null && this.getSales().compareTo(BigDecimal.ZERO) > 0
                || this.getOrders() != null && this.getOrders() != 0;
    }


}
