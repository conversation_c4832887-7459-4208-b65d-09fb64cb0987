package com.meiyunji.sponsored.service.batchCreate.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.*;
import com.meiyunji.sponsored.service.batchCreate.dao.*;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoNekeywordDto;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoNetargetingDto;
import com.meiyunji.sponsored.service.batchCreate.dto.submit.BaseInfoProductDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskTraceDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdNeTargetingTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.*;
import com.meiyunji.sponsored.service.batchCreate.vo.BatchCreateAutoTargetingVO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetingTypeV3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-17  16:31
 */

@Component
@Slf4j
public class SaveBatchDataTask {

    @Autowired
    private SpBatchCreateTaskComposeExecutor taskExecutor;

    @Autowired
    private IAmazonAdBatchSequenceDao sequenceDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private IAmazonAdBatchBaseInfoDao amazonAdBatchBaseInfoDao;

    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonAdBatchKeywordDao amazonAdBatchKeywordDao;

    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;

    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;

    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;

    @Autowired
    private IAmazonAdBatchTaskSupportDao amazonAdBatchTaskSupportDao;

    private final String ENABLE = "enable";
    private final String PAUSE = "pause";

    /**
     * 保存数据并触发异步调用亚马逊接口
     * @return
     */
    public void run(Map<Integer, Long> shopTaskIdMap, SpBatchCreateSubmitRequest request) {

        log.info("sp batch create, start save batch data, batch tarceId: {}", request.getTraceId());
        StopWatch sw = new StopWatch();
        sw.start();

        //准备profile数据
        List<AmazonAdProfile> profileList = amazonAdProfileDao.getByPuidAndShopIdList(request.getPuid(), new ArrayList<>(shopTaskIdMap.keySet()));
        Map<Integer, String> shopProfileIdMap = profileList.stream()
                .collect(Collectors.groupingBy(AmazonAdProfile::getShopId,
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getProfileId())));

        //保存基础信息、广告活动、组、产品、投放等数据
        List<List<AmazonAdBatchCampaign>> campaignListList = saveBaseInfoAndCampaign(shopTaskIdMap, request, shopProfileIdMap);

        //更新或保存puid操作时间用于支持定时任务
        updateTaskSupport(request.getPuid());

        //调用亚马逊接口
        for (List<AmazonAdBatchCampaign> campaignList : campaignListList) {
            log.info("sp batch create, start call amazon api, batch traceId: {}, taskId: {}", request.getTraceId(), campaignList.get(0).getTaskId());
            CallAmazonApiTaskTraceDto traceDto = new CallAmazonApiTaskTraceDto(request.getTraceId(), new Date());
            CallAmazonApiTaskResultDto resultDto = new CallAmazonApiTaskResultDto();
            resultDto.setTraceDto(traceDto);
            resultDto.setPuid(campaignList.get(0).getPuid());
            resultDto.setShopId(campaignList.get(0).getShopId());
            resultDto.setLoginIp(resultDto.getLoginIp());
            resultDto.setTaskId(campaignList.get(0).getTaskId());
            resultDto.setSuccessIdList(campaignList.stream().map(AmazonAdBatchCampaign::getId).collect(Collectors.toList()));
            //提交初始化为false
            resultDto.setCurrentLevel(false);
            taskExecutor.executeAmazonBatchCampaign(resultDto);
        }

        sw.stop();
        log.info("sp batch create, trigger amazon async api success, batch tarceId: {}, cost time: {}s", request.getTraceId(), sw.getTotalTimeSeconds());
    }

    /**
     * 保存广告活动
     * @param shopTaskIdMap 店铺id映射taskid集合
     * @param request 数据
     * @param shopProfileIdMap 店铺id映射profileId集合
     */
    private List<List<AmazonAdBatchCampaign>> saveBaseInfoAndCampaign(Map<Integer, Long> shopTaskIdMap, SpBatchCreateSubmitRequest request, Map<Integer, String> shopProfileIdMap) {
        List<List<AmazonAdBatchCampaign>> campaignListList = new ArrayList<>(request.getBatchDataList().size());
        //店铺循环
        for (SpBatchCreateSubmitTaskRequest taskRequest : request.getBatchDataList()) {

            try {
                //shopId
                Integer shopId = taskRequest.getBaseInfo().getShopId();
                //profileId
                String profileId = shopProfileIdMap.get(shopId);
                if (StringUtils.isBlank(profileId)) {
                    log.error("sp batch create, save ad data profileId not found, shopId: {}", shopId);
                    continue;
                }
                //taskId
                Long taskId = shopTaskIdMap.get(shopId);
                //baseInfo
                SpBatchCreatePreviewBaseInfoRequest baseInfoRequest = taskRequest.getBaseInfo();
                //一个店铺下请求的广告数据
                List<SpBatchCreatePreviewCampaignVo> campaignRequestList = taskRequest.getCampaignsList();

                //保存该店铺下的基本信息
                saveBaseInfo(baseInfoRequest, request.getPuid(), profileId, taskId, request.getUid());

                //保存该店铺下的所有广告数据
                List<AmazonAdBatchCampaign> campaignList = saveCampaign(campaignRequestList, baseInfoRequest, request.getPuid(), shopId, profileId, taskId, request.getUid());
                campaignListList.add(campaignList);
            } catch (Exception e) {
                log.error(String.format("sp batch create, save ad data exception, request data: %s", JSON.toJSONString(taskRequest)), e);
            }
        }

        log.info("sp batch create, save ad data finish, batch tarceId: {}", request.getTraceId());

        return campaignListList;
    }

    /**
     * 保存基本信息
     * @param baseInfoRequest 基本信息
     * @param puid puid
     * @param profileId profileId
     * @param taskId 任务id
     * @param uid uid
     */
    private void saveBaseInfo(SpBatchCreatePreviewBaseInfoRequest baseInfoRequest,
                              Integer puid,
                              String profileId,
                              Long taskId,
                              Integer uid) {
        Long id = sequenceDao.genId();
        AmazonAdBatchBaseInfo baseInfo = new AmazonAdBatchBaseInfo();
        baseInfo.setId(id);
        baseInfo.setPuid(puid);
        baseInfo.setShopId(baseInfoRequest.getShopId());
        baseInfo.setProfileId(profileId);
        baseInfo.setMarketplaceId(baseInfoRequest.getMarketplaceId());
        baseInfo.setTaskId(taskId);
        baseInfo.setBudget(new BigDecimal(baseInfoRequest.getDailyBudget()));
        baseInfo.setStartDate(DateUtil.strToDate(baseInfoRequest.getStartDateStr(), DateUtil.PATTERN));
        if (StringUtils.isNotBlank(baseInfoRequest.getEndDateStr())) {
            baseInfo.setEndDate(DateUtil.strToDate(baseInfoRequest.getEndDateStr(), DateUtil.PATTERN));
        }
        baseInfo.setStrategy(baseInfoRequest.getStrategy());
        baseInfo.setDefaultBid(new BigDecimal(baseInfoRequest.getDefaultBid()));
        if (StringUtils.isNotBlank(baseInfoRequest.getPlacementTop())) {
            baseInfo.setAdjustmentsTop(Integer.parseInt(baseInfoRequest.getPlacementTop()));
        }
        if (StringUtils.isNotBlank(baseInfoRequest.getPlacementProductPage())) {
            baseInfo.setAdjustmentsProduct(Integer.parseInt(baseInfoRequest.getPlacementProductPage()));
        }
        if (StringUtils.isNotBlank(baseInfoRequest.getPlacementRestOfSearch())) {
            baseInfo.setAdjustmentsOther(Integer.parseInt(baseInfoRequest.getPlacementRestOfSearch()));
        }
        baseInfo.setProductJson(getProductJson(baseInfoRequest.getProductsList()));
        baseInfo.setNekeywordJson(getNekeywordJson(baseInfoRequest.getNekeywordsList()));
        baseInfo.setNetargetingJson(getNetargetingJson(baseInfoRequest.getNetargetingsList()));
        try {
            Optional.of(baseInfoRequest).map(SpBatchCreatePreviewBaseInfoRequest::getCampaignNameJson).map(JSON::parseObject).map(JSON::toString).ifPresent(baseInfo::setCampaignNameTemplateJson);
            Optional.of(baseInfoRequest).map(SpBatchCreatePreviewBaseInfoRequest::getGroupNameJson).map(JSON::parseObject).map(JSON::toString).ifPresent(baseInfo::setGroupNameTemplateJson);
        } catch (Exception e) {
            log.error("batch task submit error, campaign name json or group name json format error", e);
            throw new ServiceException("模板内容不正确");
        }
        baseInfo.setCreateTime(new Date());
        baseInfo.setCreateId(uid);

        //批量插入
        amazonAdBatchBaseInfoDao.insertList(puid, Collections.singletonList(baseInfo));
    }



    /**
     * 保存一个店铺的批量创建数据
     * @param campaignRequestList 广告活动数据
     * @param baseInfoRequest 基础数据
     * @param puid puid
     * @param shopId 店铺id
     * @param profileId profileId
     * @param taskId 任务id
     * @param uid 用户id
     */
    private List<AmazonAdBatchCampaign> saveCampaign(List<SpBatchCreatePreviewCampaignVo> campaignRequestList,
                                     SpBatchCreatePreviewBaseInfoRequest baseInfoRequest,
                                     Integer puid,
                                     Integer shopId,
                                     String profileId,
                                     Long taskId,
                                     Integer uid) {
        //所有广告数据的主键
        List<Long> batchIdList = getBatchIdMap(baseInfoRequest, campaignRequestList);
        //主键遍历下标
        int idindex = 0;
        //存储到数据库的广告活动实体集合
        List<AmazonAdBatchCampaign> campaignList = new ArrayList<>(campaignRequestList.size());
        //存储到数据库的广告组实体集合
        List<AmazonAdBatchGroup> groupList = new ArrayList<>();
        //存储到数据库的广告产品实体集合
        List<AmazonAdBatchProduct> productList = new ArrayList<>();
        //存储到数据库的关键词投放集合
        List<AmazonAdBatchKeyword> keywordList = new ArrayList<>();
        //存储到数据库的商品投放&自动投放集合
        List<AmazonAdBatchTargeting> targetingList = new ArrayList<>();
        //存储到数据库的否定关键词集合
        List<AmazonAdBatchNekeyword> nekeywordList = new ArrayList<>();
        //存储到数据库的否定商品集合
        List<AmazonAdBatchNetargeting> netargetingList = new ArrayList<>();

        Date createTime = new Date();

        //活动循环
        for (SpBatchCreatePreviewCampaignVo campaignRequest : campaignRequestList) {
            //广告活动信息
            AmazonAdBatchCampaign campaign = new AmazonAdBatchCampaign();
            campaign.setId(batchIdList.get(idindex++));
            fillCampaignInfo(campaignRequest, campaign, puid, shopId, profileId, taskId, uid, createTime);
            campaignList.add(campaign);

            if (CollectionUtils.isEmpty(campaignRequest.getGroupsList())) {
                continue;
            }

            //组循环
            for (SpBatchCreatePreviewGroupVo groupRequest : campaignRequest.getGroupsList()) {
                AmazonAdBatchGroup group = new AmazonAdBatchGroup();
                group.setId(batchIdList.get(idindex++));
                fillGroupInfo(groupRequest, group, campaign);
                groupList.add(group);
                //广告产品
                for (SpBatchCreatePreviewProductRequest productRequest : baseInfoRequest.getProductsList()) {
                    AmazonAdBatchProduct product = new AmazonAdBatchProduct();
                    product.setId(batchIdList.get(idindex++));
                    fillProductInfo(productRequest, product, group);
                    productList.add(product);
                }

                //关键词投放
                if (AmazonAd.AdGroupTypeEnum.KEYWORD.getType().equals(groupRequest.getType()) && !CollectionUtils.isEmpty(groupRequest.getKeywordsList())) {
                    for (SpBatchCreateSubmitKeywordVo keywordRequest : groupRequest.getKeywordsList()) {
                        AmazonAdBatchKeyword keyword = new AmazonAdBatchKeyword();
                        keyword.setId(batchIdList.get(idindex++));
                        fillKeywordInfo(keywordRequest, keyword, group);
                        keywordList.add(keyword);
                    }
                }

                //商品投放
                if (AmazonAd.AdGroupTypeEnum.TARGETING.getType().equals(groupRequest.getType()) && !CollectionUtils.isEmpty(groupRequest.getTargetingsList())) {
                    for (SpBatchCreateSubmitTargetingVo targetingRequest : groupRequest.getTargetingsList()) {
                        AmazonAdBatchTargeting targeting = new AmazonAdBatchTargeting();
                        targeting.setId(batchIdList.get(idindex++));
                        fillTargetingInfo(targetingRequest, targeting, group);
                        targetingList.add(targeting);
                    }
                }

                //自动投放
                if (AmazonAd.AdGroupTypeEnum.AUTO.getType().equals(groupRequest.getType())) {
                    for (AutoTargetTypeEnum autoTargetTypeEnum : AutoTargetTypeEnum.values()) {
                        AmazonAdBatchTargeting targeting = new AmazonAdBatchTargeting();
                        targeting.setId(batchIdList.get(idindex++));
                        fillAutoTargetingInfo(autoTargetTypeEnum, targeting, group);
                        targetingList.add(targeting);
                    }
                }

                //否定关键词-作用于关键词投放的广告组
                if (!AmazonAd.AdGroupTypeEnum.TARGETING.getType().equals(groupRequest.getType()) && !CollectionUtils.isEmpty(baseInfoRequest.getNekeywordsList())) {
                    for (SpBatchCreatePreviewNekeywordRequest nekeywordRequest : baseInfoRequest.getNekeywordsList()) {
                        AmazonAdBatchNekeyword nekeyword = new AmazonAdBatchNekeyword();
                        nekeyword.setId(batchIdList.get(idindex++));
                        fillNekeywordInfo(nekeywordRequest, nekeyword, group);
                        nekeywordList.add(nekeyword);
                    }
                }

                //否定投放-作用于商品投放广告组和自动投放广告组（注意自动投放中只有否定商品没有否定品牌）
                if (!AmazonAd.AdGroupTypeEnum.KEYWORD.getType().equals(groupRequest.getType()) && !CollectionUtils.isEmpty(baseInfoRequest.getNetargetingsList())) {
                    for (SpBatchCreatePreviewNetargetingRequest netargetingRequest : baseInfoRequest.getNetargetingsList()) {
                        if (AdNeTargetingTypeEnum.ASIN.getCode().equals(netargetingRequest.getType()) || AmazonAd.AdGroupTypeEnum.TARGETING.getType().equals(groupRequest.getType())) {
                            AmazonAdBatchNetargeting netargeting = new AmazonAdBatchNetargeting();
                            netargeting.setId(batchIdList.get(idindex++));
                            fillNetargetingInfo(netargetingRequest, netargeting, group);
                            netargetingList.add(netargeting);
                        }
                    }
                }
            }
        }
        //批量插入广告活动
        amazonAdBatchCampaignDao.insertList(puid, campaignList);

        //批量插入广告组
        if (!CollectionUtils.isEmpty(groupList)) {
            amazonAdBatchGroupDao.insertList(puid, groupList);
        }

        //批量广告产品
        if (!CollectionUtils.isEmpty(productList)) {
            amazonAdBatchProductDao.insertList(puid, productList);
        }

        //批量插入关键词投放
        if (!CollectionUtils.isEmpty(keywordList)) {
            amazonAdBatchKeywordDao.insertList(puid, keywordList);
        }

        //批量插入商品投放&自动投放
        if (!CollectionUtils.isEmpty(targetingList)) {
            amazonAdBatchTargetingDao.insertList(puid, targetingList);
        }

        //批量插入否定关键词
        if (!CollectionUtils.isEmpty(nekeywordList)) {
            amazonAdBatchNekeywordDao.insertList(puid, nekeywordList);
        }

        //批量插入否定投放
        if (!CollectionUtils.isEmpty(netargetingList)) {
            amazonAdBatchNetargetingDao.insertList(puid, netargetingList);
        }

        return campaignList;
    }


    /**
     * 根据当前任务的活动列表，生成一批主键id，含活动，组，产品，各种投放
     * @param baseInfoRequest
     * @param campaignRequestList
     * @return
     */
    private List<Long> getBatchIdMap(SpBatchCreatePreviewBaseInfoRequest baseInfoRequest, List<SpBatchCreatePreviewCampaignVo> campaignRequestList) {
        //活动
        int totalSize = campaignRequestList.size();
        for (SpBatchCreatePreviewCampaignVo campaign : campaignRequestList) {

            if (CollectionUtils.isEmpty(campaign.getGroupsList())) {
                continue;
            }

            //组
            totalSize += campaign.getGroupsList().size();
            for (SpBatchCreatePreviewGroupVo group : campaign.getGroupsList()) {
                if (!CollectionUtils.isEmpty(baseInfoRequest.getProductsList())) {
                    //产品
                    totalSize += baseInfoRequest.getProductsList().size();
                }
                if (AmazonAd.AdGroupTypeEnum.KEYWORD.getType().equals(group.getType()) && !CollectionUtils.isEmpty(group.getKeywordsList())) {
                    //关键词
                    totalSize += group.getKeywordsList().size();
                }
                if (AmazonAd.AdGroupTypeEnum.TARGETING.getType().equals(group.getType()) && !CollectionUtils.isEmpty(group.getTargetingsList())) {
                    //商品
                    totalSize += group.getTargetingsList().size();
                }
                if (AmazonAd.AdGroupTypeEnum.AUTO.getType().equals(group.getType())) {
                    //自动投放
                    totalSize += 4;
                }
                if (!CollectionUtils.isEmpty(baseInfoRequest.getNekeywordsList())) {
                    //否定关键词
                    totalSize += baseInfoRequest.getNekeywordsList().size();
                }
                if (!CollectionUtils.isEmpty(baseInfoRequest.getNetargetingsList())) {
                    //否定商品
                    totalSize += baseInfoRequest.getNetargetingsList().size();
                }
            }
        }
        return sequenceDao.batchGenId(totalSize);
    }


    /**
     * 填充广告活动信息
     * @param campaignRequest 广告活动请求数据
     * @param campaign 广告活动实体
     * @param puid puid
     * @param shopId 店铺id
     * @param profileId profileId
     * @param taskId 任务id
     * @param uid uid
     */
    private void fillCampaignInfo(SpBatchCreatePreviewCampaignVo campaignRequest,
                                  AmazonAdBatchCampaign campaign,
                                  Integer puid,
                                  Integer shopId,
                                  String profileId,
                                  Long taskId,
                                  Integer uid,
                                  Date createTime) {
        campaign.setPuid(puid);
        campaign.setShopId(shopId);
        campaign.setProfileId(profileId);
        campaign.setMarketplaceId(campaignRequest.getMarketplaceId());
        campaign.setTaskId(taskId);
        if (StringUtils.isNotBlank(campaignRequest.getPortfolioId())) {
            campaign.setPortfolioId(campaignRequest.getPortfolioId());
        }
        campaign.setName(campaignRequest.getName());
        campaign.setCampaignType(CampaignTypeEnum.sp.getCampaignType());
        campaign.setTargetingType(campaignRequest.getTargetingType());
        campaign.setBudget(new BigDecimal(campaignRequest.getDailyBudget()));
        campaign.setStartDate(DateUtil.strToDate(campaignRequest.getStartDateStr(), DateUtil.PATTERN));
        if (StringUtils.isNotBlank(campaignRequest.getEndDateStr())) {
            campaign.setEndDate(DateUtil.strToDate(campaignRequest.getEndDateStr(), DateUtil.PATTERN));
        }
        campaign.setStrategy(campaignRequest.getStrategy());
        //广告位调整竞价
        List<Adjustment> list = Lists.newArrayListWithExpectedSize(2);
        if (StringUtils.isNotBlank(campaignRequest.getPlacementProductPage())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
            adjustment.setPercentage(Double.parseDouble(campaignRequest.getPlacementProductPage()));
            list.add(adjustment);
            campaign.setAdjustmentsProduct(Integer.parseInt(campaignRequest.getPlacementProductPage()));
        }
        if (StringUtils.isNotBlank(campaignRequest.getPlacementTop())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTTOP.value());
            adjustment.setPercentage(Double.parseDouble(campaignRequest.getPlacementTop()));
            list.add(adjustment);
            campaign.setAdjustmentsTop(Integer.parseInt(campaignRequest.getPlacementTop()));
        }
        if (StringUtils.isNotBlank(campaignRequest.getPlacementRestOfSearch())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
            adjustment.setPercentage(Double.parseDouble(campaignRequest.getPlacementRestOfSearch()));
            list.add(adjustment);
            campaign.setAdjustmentsOther(Integer.parseInt(campaignRequest.getPlacementRestOfSearch()));
        }
        if (StringUtils.isNotBlank(campaignRequest.getPlacementSiteAmazonBusiness()) && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(campaign.getMarketplaceId())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.SITEAMAZONBUSINESS.value());
            adjustment.setPercentage(Double.parseDouble(campaignRequest.getPlacementSiteAmazonBusiness()));
            list.add(adjustment);
        }
        campaign.setAdjustments(JSONUtil.objectToJson(list));

        //tag
        JSONObject tag = new JSONObject();
        tag.put(SpBatchConstants.CAMPAIGN_TAG_KEY, UUID.randomUUID().toString());
        campaign.setTags(tag.toJSONString());

        campaign.setStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        campaign.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        campaign.setExecuteCount(0);
        campaign.setNextRetryTime(DateUtil.addSecond(createTime, SpBatchConstants.RETRY_AFTER_SECONDS));
        campaign.setCreateTime(createTime);
        campaign.setCreateId(uid);
    }

    /**
     * 填充广告组信息
     * @param groupRequest 广告组提交请求信息
     * @param group 广告组实体
     * @param campaign 广告活动实体
     */
    private void fillGroupInfo(SpBatchCreatePreviewGroupVo groupRequest, AmazonAdBatchGroup group, AmazonAdBatchCampaign campaign) {
        group.setPuid(campaign.getPuid());
        group.setShopId(campaign.getShopId());
        group.setProfileId(campaign.getProfileId());
        group.setMarketplaceId(campaign.getMarketplaceId());
        group.setTaskId(campaign.getTaskId());
        group.setCampaignId(campaign.getId());
        group.setName(groupRequest.getName());
        group.setDefaultBid(new BigDecimal(groupRequest.getDefaultBid()));
        group.setAdGroupType(groupRequest.getType());
        group.setMatchType(groupRequest.getMatchType());
        group.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        group.setExecuteCount(0);
        group.setNextRetryTime(campaign.getNextRetryTime());
        group.setCreateTime(campaign.getCreateTime());
        group.setCreateId(campaign.getCreateId());
        group.setAutoTargetingVO(buildVo(groupRequest));
    }

    private BatchCreateAutoTargetingVO buildVo(SpBatchCreatePreviewGroupVo groupRequest) {
        SpBatchCreatePreviewAutoTargetingVo autoTargeting = groupRequest.getAutoTargetings();
        return BatchCreateAutoTargetingVO.builder()
                .queryHighRelMatchesState(Optional.of(autoTargeting.getQueryHighRelMatchesState()).orElse(true))
                .queryBroadRelMatchesState(Optional.of(autoTargeting.getQueryBroadRelMatchesState()).orElse(true))
                .asinAccessoryRelatedState(Optional.of(autoTargeting.getAsinAccessoryRelatedState()).orElse(true))
                .asinSubstituteRelatedState(Optional.of(autoTargeting.getAsinSubstituteRelatedState()).orElse(true))
                .queryHighRelMatchesBid(Optional.of(autoTargeting.getQueryHighRelMatchesBid()).orElse(""))
                .queryBroadRelMatchesBid(Optional.of(autoTargeting.getQueryBroadRelMatchesBid()).orElse(""))
                .asinAccessoryRelatedBid(Optional.of(autoTargeting.getAsinAccessoryRelatedBid()).orElse(""))
                .asinSubstituteRelatedBid(Optional.of(autoTargeting.getAsinSubstituteRelatedBid()).orElse(""))
                .build();
    }

    /**
     * 填充广告产品信息
     * @param productRequest 提交的广告产品
     * @param product 广告产品实体
     * @param group 广告产品所属广告组
     */
    private void fillProductInfo(SpBatchCreatePreviewProductRequest productRequest, AmazonAdBatchProduct product, AmazonAdBatchGroup group) {
        product.setPuid(group.getPuid());
        product.setShopId(group.getShopId());
        product.setProfileId(group.getProfileId());
        product.setMarketplaceId(group.getMarketplaceId());
        product.setTaskId(group.getTaskId());
        product.setCampaignId(group.getCampaignId());
        product.setGroupId(group.getId());
        product.setAsin(productRequest.getAsin());
        product.setSku(productRequest.getSku());
        product.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        product.setExecuteCount(0);
        product.setNextRetryTime(group.getNextRetryTime());
        product.setCreateTime(group.getCreateTime());
        product.setCreateId(group.getCreateId());
    }

    /**
     * 填充关键词信息
     * @param keywordRequest 提交的关键词
     * @param keyword 关键词实体
     * @param group 所属广告组
     */
    private void fillKeywordInfo(SpBatchCreateSubmitKeywordVo keywordRequest, AmazonAdBatchKeyword keyword, AmazonAdBatchGroup group) {
        keyword.setPuid(group.getPuid());
        keyword.setShopId(group.getShopId());
        keyword.setProfileId(group.getProfileId());
        keyword.setMarketplaceId(group.getMarketplaceId());
        keyword.setTaskId(group.getTaskId());
        keyword.setCampaignId(group.getCampaignId());
        keyword.setGroupId(group.getId());
        keyword.setKeywordText(keywordRequest.getKeywordText());
        keyword.setMatchType(keywordRequest.getMatchType());
        if (StringUtils.isNotBlank(keywordRequest.getBid())) {
            keyword.setBid(new BigDecimal(keywordRequest.getBid()));
        }
        if (StringUtils.isNotBlank(keywordRequest.getSuggested())) {
            keyword.setSuggested(new BigDecimal(keywordRequest.getSuggested()));
        }
        if (StringUtils.isNotBlank(keywordRequest.getRangeStart())) {
            keyword.setRangeStart(new BigDecimal(keywordRequest.getRangeStart()));
        }
        if (StringUtils.isNotBlank(keywordRequest.getRangeEnd())) {
            keyword.setRangeEnd(new BigDecimal(keywordRequest.getRangeEnd()));
        }
        keyword.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        keyword.setExecuteCount(0);
        keyword.setNextRetryTime(group.getNextRetryTime());
        keyword.setCreateTime(group.getCreateTime());
        keyword.setCreateId(group.getCreateId());
    }

    /**
     * 填充商品投放信息
     * @param targetingRequest 提交的商品投放
     * @param targeting 商品投放实体
     * @param group 所属广告组
     */
    private void fillTargetingInfo(SpBatchCreateSubmitTargetingVo targetingRequest, AmazonAdBatchTargeting targeting, AmazonAdBatchGroup group) {
        targeting.setPuid(group.getPuid());
        targeting.setShopId(group.getShopId());
        targeting.setProfileId(group.getProfileId());
        targeting.setMarketplaceId(group.getMarketplaceId());
        targeting.setTaskId(group.getTaskId());
        targeting.setCampaignId(group.getCampaignId());
        targeting.setGroupId(group.getId());
        targeting.setExpressionType(TargetingTypeV3.MANUAL.getValue());
        targeting.setType(targetingRequest.getType());
        targeting.setSelectType(targetingRequest.getExpressionType());
        targeting.setAsin(targetingRequest.getAsin());
        targeting.setImgUrl(targetingRequest.getImgUrl());
        targeting.setTitle(targetingRequest.getTitle());
        targeting.setCategoryId(targetingRequest.getCategoryId());
        targeting.setCategory(targetingRequest.getCategory());
        targeting.setCategoryName(targetingRequest.getName());
        targeting.setBrand(targetingRequest.getBrand());
        targeting.setBrandName(targetingRequest.getBrandName());
        if(StringUtils.isNotBlank(targetingRequest.getMinPrice())) {
            targeting.setMinPrice(new BigDecimal(targetingRequest.getMinPrice()));
        }
        if(StringUtils.isNotBlank(targetingRequest.getMaxPrice())) {
            targeting.setMaxPrice(new BigDecimal(targetingRequest.getMaxPrice()));
        }
        if(targetingRequest.hasMinReviewRating()) {
            targeting.setMinReviewRating(targetingRequest.getMinReviewRating());
        }
        if(targetingRequest.hasMaxReviewRating()) {
            targeting.setMaxReviewRating(targetingRequest.getMaxReviewRating());
        }
        if(targetingRequest.hasPrimeShippingEligible()) {
            targeting.setPrimeShippingEligible(targetingRequest.getPrimeShippingEligible());
        }
        if (StringUtils.isNotBlank(targetingRequest.getBid())) {
            targeting.setBid(new BigDecimal(targetingRequest.getBid()));
        }
        if (StringUtils.isNotBlank(targetingRequest.getSuggested())) {
            targeting.setSuggested(new BigDecimal(targetingRequest.getSuggested()));
        }
        if (StringUtils.isNotBlank(targetingRequest.getRangeStart())) {
            targeting.setRangeStart(new BigDecimal(targetingRequest.getRangeStart()));
        }
        if (StringUtils.isNotBlank(targetingRequest.getRangeEnd())) {
            targeting.setRangeEnd(new BigDecimal(targetingRequest.getRangeEnd()));
        }
        //设置expression和targeting_value
        List<Expression> expressions = new ArrayList<>();
        Expression expression;
        //商品投放
        if (TargetTypeEnum.asin.name().equals(targeting.getType())) {
            expression = new Expression();
            if(StringUtils.isNotBlank(targeting.getSelectType())){
                expression.setType(targeting.getSelectType());
            } else {
                expression.setType(ExpressionEnum.asinSameAs.value());
            }
            expression.setValue(targeting.getAsin());
            expressions.add(expression);
            targeting.setTargetingValue(targeting.getAsin());
        } else {
            //品类投放
            expression = new Expression();
            expression.setType(ExpressionEnum.asinCategorySameAs.value());
            expression.setValue(targeting.getCategoryId());
            expressions.add(expression);
            targeting.setTargetingValue(targeting.getCategory());

            if (StringUtils.isNotBlank(targeting.getBrand())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinBrandSameAs.value());
                expression.setValue(targeting.getBrand());
                expressions.add(expression);
            }
            if (targeting.getMinPrice() != null || targeting.getMaxPrice() != null) {
                expression = new Expression();
                if (targeting.getMinPrice() == null) {
                    expression.setType(ExpressionEnum.asinPriceLessThan.value());
                    expression.setValue(targeting.getMaxPrice().toString());
                    expressions.add(expression);
                } else if (targeting.getMaxPrice() == null) {
                    expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                    expression.setValue(targeting.getMinPrice().toString());
                    expressions.add(expression);
                } else {
                    expression.setType(ExpressionEnum.asinPriceBetween.value());
                    expression.setValue(targeting.getMinPrice().toString() + "-" + targeting.getMaxPrice().toString());
                    expressions.add(expression);
                }
            }
            if (targeting.getMinReviewRating() != null || targeting.getMaxReviewRating() != null) {
                expression = new Expression();
                if (targeting.getMinReviewRating() == null) {
                    expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                    expression.setValue(String.valueOf(targeting.getMaxReviewRating()));
                    expressions.add(expression);
                } else if (targeting.getMaxReviewRating() == null) {
                    expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                    expression.setValue(String.valueOf(targeting.getMinReviewRating()));
                    expressions.add(expression);
                } else {
                    expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                    expression.setValue(targeting.getMinReviewRating() + "-" + targeting.getMaxReviewRating());
                    expressions.add(expression);
                }
            }
            if (targeting.getPrimeShippingEligible() != null) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                expression.setValue(targeting.getPrimeShippingEligible().toString());
                expressions.add(expression);
            }
        }
        targeting.setExpression(JSONUtil.objectToJson(expressions));

        targeting.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        targeting.setExecuteCount(0);
        targeting.setNextRetryTime(group.getNextRetryTime());
        targeting.setCreateTime(group.getCreateTime());
        targeting.setCreateId(group.getCreateId());
    }


    /**
     * 填充自动投放信息
     * @param autoTargetTypeEnum 自动投放类型
     * @param targeting 自动投放实体
     * @param group 搜书广告组
     */
    private void fillAutoTargetingInfo(AutoTargetTypeEnum autoTargetTypeEnum, AmazonAdBatchTargeting targeting, AmazonAdBatchGroup group) {
        targeting.setPuid(group.getPuid());
        targeting.setShopId(group.getShopId());
        targeting.setProfileId(group.getProfileId());
        targeting.setMarketplaceId(group.getMarketplaceId());
        targeting.setTaskId(group.getTaskId());
        targeting.setCampaignId(group.getCampaignId());
        targeting.setGroupId(group.getId());
        targeting.setExpressionType(TargetingTypeV3.AUTO.getValue());
        targeting.setType(Constants.TARGETING_TYPE_AUTO);
        Expression expression = new Expression();
        expression.setType(autoTargetTypeEnum.getAutoTargetType());
        targeting.setExpression(JSONUtil.objectToJson(Collections.singletonList(expression)));
        targeting.setState(autoTargetingExpressionHandler(group.getAutoTargetingVO(), expression));
        targeting.setTargetingValue(autoTargetTypeEnum.getAutoTargetType());
        targeting.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        targeting.setExecuteCount(0);
        targeting.setNextRetryTime(group.getNextRetryTime());
        targeting.setCreateTime(group.getCreateTime());
        targeting.setCreateId(group.getCreateId());
    }

    private String autoTargetingExpressionHandler(BatchCreateAutoTargetingVO autoTargeting,
                                               Expression expression) {
        if (AutoTargetTypeEnum.queryHighRelMatches.getAutoTargetType().equals(expression.getType())
                && Objects.nonNull(autoTargeting) && !autoTargeting.isQueryHighRelMatchesState()) {
            return Constants.AUTO_TARGETING_STATE_PAUSE;
        }
        if (AutoTargetTypeEnum.queryBroadRelMatches.getAutoTargetType().equals(expression.getType())
                && Objects.nonNull(autoTargeting) && !autoTargeting.isQueryBroadRelMatchesState()) {
            return Constants.AUTO_TARGETING_STATE_PAUSE;
        }
        if (AutoTargetTypeEnum.asinAccessoryRelated.getAutoTargetType().equals(expression.getType())
                && Objects.nonNull(autoTargeting) && !autoTargeting.isAsinAccessoryRelatedState()) {
            return Constants.AUTO_TARGETING_STATE_PAUSE;
        }
        if (AutoTargetTypeEnum.asinSubstituteRelated.getAutoTargetType().equals(expression.getType())
                && Objects.nonNull(autoTargeting) && !autoTargeting.isAsinSubstituteRelatedState()) {
            return Constants.AUTO_TARGETING_STATE_PAUSE;
        }
        return Constants.AUTO_TARGETING_STATE_ENABLE;
    }

    /**
     * 填充否定关键词信息
     * @param nekeywordRequest 基础数据中的否定关键词
     * @param nekeyword 否定关键词实体
     * @param group 所属广告组
     */
    private void fillNekeywordInfo(SpBatchCreatePreviewNekeywordRequest nekeywordRequest, AmazonAdBatchNekeyword nekeyword, AmazonAdBatchGroup group) {
        nekeyword.setPuid(group.getPuid());
        nekeyword.setShopId(group.getShopId());
        nekeyword.setProfileId(group.getProfileId());
        nekeyword.setMarketplaceId(group.getMarketplaceId());
        nekeyword.setTaskId(group.getTaskId());
        nekeyword.setCampaignId(group.getCampaignId());
        nekeyword.setGroupId(group.getId());
        nekeyword.setKeywordText(nekeywordRequest.getKeywordText());
        nekeyword.setMatchType(nekeywordRequest.getMatchType());
        nekeyword.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        nekeyword.setExecuteCount(0);
        nekeyword.setNextRetryTime(group.getNextRetryTime());
        nekeyword.setCreateTime(group.getCreateTime());
        nekeyword.setCreateId(group.getCreateId());
    }

    /**
     * 填充否定投放信息
     * @param netargetingRequest 基础数据中的否定投放
     * @param netargeting 否定投放实体
     * @param group 所属广告组
     */
    private void fillNetargetingInfo(SpBatchCreatePreviewNetargetingRequest netargetingRequest, AmazonAdBatchNetargeting netargeting, AmazonAdBatchGroup group) {
        netargeting.setPuid(group.getPuid());
        netargeting.setShopId(group.getShopId());
        netargeting.setProfileId(group.getProfileId());
        netargeting.setMarketplaceId(group.getMarketplaceId());
        netargeting.setTaskId(group.getTaskId());
        netargeting.setCampaignId(group.getCampaignId());
        netargeting.setGroupId(group.getId());
        netargeting.setType(netargetingRequest.getType());

        //expression和targeting_value
        List<Expression> expressions = new ArrayList<>();
        Expression expression = new Expression();
        if (TargetTypeEnum.asin.name().equals(netargetingRequest.getType())) {
            expression.setType(ExpressionEnum.asinSameAs.value());
            expression.setValue(netargetingRequest.getAsin());
            netargeting.setAsin(netargetingRequest.getAsin());
            netargeting.setTitle(netargetingRequest.getTitle());
            netargeting.setImgUrl(netargetingRequest.getImgUrl());
            netargeting.setTargetingValue(netargetingRequest.getAsin());
        }

        if (TargetTypeEnum.brand.name().equals(netargetingRequest.getType())) {
            expression.setType(ExpressionEnum.asinBrandSameAs.value());
            expression.setValue(netargetingRequest.getBrandId());
            netargeting.setBrandId(netargetingRequest.getBrandId());
            netargeting.setBrandName(netargetingRequest.getBrandName());
            netargeting.setTargetingValue(netargetingRequest.getBrandName());
        }
        expressions.add(expression);
        netargeting.setExpression(JSONUtil.objectToJson(expressions));
        netargeting.setTaskStatus(SpBatchCreateAdLevelStatusEnum.DOING.getCode());
        netargeting.setExecuteCount(0);
        netargeting.setNextRetryTime(group.getNextRetryTime());
        netargeting.setCreateTime(group.getCreateTime());
        netargeting.setCreateId(group.getCreateId());
    }

    /**
     * 更新puid的最后操作时间
     * @param puid
     */
    private void updateTaskSupport(int puid) {
        amazonAdBatchTaskSupportDao.insertOrUpdate(puid);
    }

    /**
     * 基础信息广告产品转成json保存用于复制
     * @param productsList
     * @return
     */
    private String getProductJson(List<SpBatchCreatePreviewProductRequest> productsList) {
        List<BaseInfoProductDto> list = productsList.stream().map(x -> {
            BaseInfoProductDto dto = new BaseInfoProductDto();
            dto.setId(x.getId());
            dto.setAsin(x.getAsin());
            dto.setSku(x.getSku());
            dto.setTitle(x.getTitle());
            dto.setDomain(x.getDomain());
            dto.setImageUrl(x.getImgUrl());
            return dto;
        }).collect(Collectors.toList());
        return JSON.toJSONString(list);
    }

    private List<BaseInfoProductDto> getProductList(String productJson) {
        if (StringUtils.isEmpty(productJson)) {
            return Collections.emptyList();
        }
        try {
            List<BaseInfoProductDto> result = JSONObject.parseArray(productJson, BaseInfoProductDto.class);
            Set<String> asinList = result.parallelStream().map(BaseInfoProductDto::getAsin).collect(Collectors.toSet());
            return result;
        } catch (Exception e) {
            log.error("sp batch create error, product format error:{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<BaseInfoNekeywordDto> getNeKeywordList(String neKeyword) {
        if (StringUtils.isEmpty(neKeyword)) {
            return Collections.emptyList();
        }
        try {
            List<BaseInfoNekeywordDto> result = JSONObject.parseArray(neKeyword, BaseInfoNekeywordDto.class);
            Set<String> neKeywordTextList = result.parallelStream().map(BaseInfoNekeywordDto::getKeywordText).collect(Collectors.toSet());
            return result;
        } catch (Exception e) {
            log.error("sp batch create error, nekeyword format error:{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<BaseInfoNetargetingDto> getNeTargeting(String neTargeting) {
        if (StringUtils.isEmpty(neTargeting)) {
            return Collections.emptyList();
        }
        try {
            List<BaseInfoNetargetingDto> result = JSONObject.parseArray(neTargeting, BaseInfoNetargetingDto.class);
            Set<String> neTargetingAsinList = result.parallelStream().map(BaseInfoNetargetingDto::getAsin).collect(Collectors.toSet());
            return result;
        } catch (Exception e) {
            log.error("sp batch create error, neTargeting format error:{}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private boolean isBeforeCurrent(LocalDate time) {
        return LocalDate.now().isAfter(time);
    }

    /**
     * 基础信息否定关键词转成json保存用于复制
     * @param nekeywordsList
     * @return
     */
    private String getNekeywordJson(List<SpBatchCreatePreviewNekeywordRequest> nekeywordsList) {
        if (CollectionUtils.isEmpty(nekeywordsList)) {
            return null;
        }
        List<BaseInfoNekeywordDto> list = nekeywordsList.stream().map(x -> new BaseInfoNekeywordDto(x.getKeywordText(), x.getMatchType())).collect(Collectors.toList());
        return JSON.toJSONString(list);
    }

    /**
     * 基础信息否定投放转成json保存用于复制
     * @param netargetingsList
     * @return
     */
    private String getNetargetingJson(List<SpBatchCreatePreviewNetargetingRequest> netargetingsList) {

        if (CollectionUtils.isEmpty(netargetingsList)) {
            return null;
        }

        List<BaseInfoNetargetingDto> list = netargetingsList.stream().map(x -> {
            BaseInfoNetargetingDto dto = new BaseInfoNetargetingDto();
            dto.setType(x.getType());
            dto.setAsin(x.getAsin());
            dto.setTitle(x.getTitle());
            dto.setImgUrl(x.getImgUrl());
            dto.setBrandId(x.getBrandId());
            dto.setBrandName(x.getBrandName());
            return dto;
        }).collect(Collectors.toList());
        return JSON.toJSONString(list);
    }
}
