package com.meiyunji.sponsored.service.newDashboard.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdYoyMomValueDataDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2024/4/9 16:39
 * @describe:
 */
@Data
public class CampaignOrGroupOrPortfolioDto extends DashboardAdYoyMomValueDataDto {
    private String shopId;
    private String marketplaceId;
    private String campaignId;
    private String portfolioId;
    private String groupId;
    @ExcelProperty("广告活动名称")
    private String campaignName;
    @ExcelProperty("广告组名称")
    private String groupName;
    @ExcelProperty("广告组合名称")
    private String portfolioName;
    @ExcelProperty("站点")
    private String marketplaceName;
    @ExcelProperty("店铺")
    private String shopName;
    private String type;
}
