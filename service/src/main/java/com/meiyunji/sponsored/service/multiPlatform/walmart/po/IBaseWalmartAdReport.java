package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description
 */
public interface IBaseWalmartAdReport {

    Double getAdCost();

    void setAdCost(Double adCost);

    Double getAdSale();

    void setAdSale(Double adSale);

    Integer getClicks();

    void setClicks(Integer clicks);

    Integer getImpressions();

    void setImpressions(Integer impressions);

    Integer getAdOrderNum();

    void setAdOrderNum(Integer adOrderNum);

    Integer getAdSaleNum();

    void setAdSaleNum(Integer adSaleNum);
}
