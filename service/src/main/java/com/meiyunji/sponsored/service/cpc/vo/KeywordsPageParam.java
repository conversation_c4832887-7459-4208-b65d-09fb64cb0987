package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 关键词列表页入参
 */
@Data
@ApiModel
public class KeywordsPageParam {

    @ApiModelProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty(value = "shopId", required = true)
    private Integer shopId;
    @ApiModelProperty("商户ID")
    private Integer puid;
    @ApiModelProperty("子用户ID")
    private Integer uid;
    @ApiModelProperty("redis中任务ID")
    private String uuid;
    @ApiModelProperty("广告组ID,多个用逗号分隔")
    private String groupId;
    @ApiModelProperty("搜索字段 目前支持 name 关键字")
    private String searchField;
    @ApiModelProperty("搜索值")
    private String searchValue;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("匹配类型 EXACT->精确匹配  BROAD->广泛匹配  PHRASE->词组匹配")
    private String matchType;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("desc asc")
    private String orderType;
    @ApiModelProperty("活动ID,多个用逗号分隔")
    private String campaignId;
    @ApiModelProperty("广告类型 sp sb sd")
    private String type;
    @ApiModelProperty("广告标签id")
    private Long adTagId;
    private List<Long> adTagIdList;
    private List<String> keywordIds;
    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;
    @ApiModelProperty(value = "词根")
    private String wordRoot;
    private List<String> wordRootKeywordIds;

    @ApiModelProperty("导出排序字段")
    private String exportSortField;

    @ApiModelProperty("冻结前num列")
    private Integer freezeNum;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "高级搜索默认竞价最小")
    private BigDecimal bidMin;
    @ApiModelProperty(value = "高级搜索默认竞价最大")
    private BigDecimal bidMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "高级搜索广告花费占比最小")
    private BigDecimal adCostPercentageMin;
    @ApiModelProperty(value = "高级搜索广告花费占比最大")
    private BigDecimal adCostPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销售额占比最小")
    private BigDecimal adSalePercentageMin;
    @ApiModelProperty(value = "高级搜索广告销售额占比最大")
    private BigDecimal adSalePercentageMax;
    @ApiModelProperty(value = "高级搜索广告订单量占比最小")
    private BigDecimal adOrderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告订单量占比最大")
    private BigDecimal adOrderNumPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销量占比最小")
    private BigDecimal orderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告销量占比最大")
    private BigDecimal orderNumPercentageMax;

    /*********************************高级筛选新增指标**********************************************/

    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;

    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单转化率最小值")
    private BigDecimal brandNewBuyerOrderConversionRateMin;

    @ApiModelProperty(value = "“品牌新买家”订单转化率最大值")
    private BigDecimal brandNewBuyerOrderConversionRateMax;

    @ApiModelProperty("5秒观看次数最小值")
    private Integer video5SecondViewsMin;
    @ApiModelProperty("5秒观看次数最大值")
    private Integer video5SecondViewsMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;
    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;
    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;
    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("品牌搜索次数最小值")
    private Integer brandedSearchesMin;
    @ApiModelProperty("品牌搜索次数最大值")
    private Integer brandedSearchesMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    @ApiModelProperty("搜索结果首页首位IS最小值")
    private BigDecimal topImpressionShareMin;
    @ApiModelProperty("搜索结果首页首位IS最大值")
    private BigDecimal topImpressionShareMax;

    @ApiModelProperty("搜索词排名最小值")
    private Integer searchFrequencyRankMin;
    @ApiModelProperty("搜索词排名最大值")
    private Integer searchFrequencyRankMax;

    @ApiModelProperty("搜索词排名周变化率最小值")
    private BigDecimal weekRatioMin;
    @ApiModelProperty("搜索词排名周变化率最大值")
    private BigDecimal weekRatioMax;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty(value = "搜索类型")
    private String searchType;

    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;
    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;

    private String pageSign;

    private String currency;
    private String icon;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;
    // 站点最近的周排名日期
    private String lastWeekSearchTermsRankDate;
    private String marketplaceId;
    // 查询是否与aba排名表联表查询
    private boolean queryJoinSearchTermsRank;

    @ApiModelProperty(value = "广告策略类型")
    private List<String> adStrategyTypeList;
    @ApiModelProperty(value = "经过广告策略筛选后的广告投放id集合")
    private List<String> autoRuleIds;
    @ApiModelProperty(value = "经过广告策略筛选后的广告组合id集合")
    private List<String> autoRuleGroupIds;

    public enum SearchFieldEnum implements BaseEnum {
        Name("name", "keyword_text", "关键词");;
        private String field;
        private String column;
        private String desc;

        SearchFieldEnum(String field, String column, String desc) {
            this.field = field;
            this.column = column;
            this.desc = desc;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }

    //关键词投放-》增加批量搜索
    public List<String> getListSearchValue() {
        if (StringUtils.isNotBlank(this.searchValue)) {
            return com.meiyunji.sponsored.common.util.StringUtil.splitStr(this.searchValue.trim(), "%±%");
        }
        return new ArrayList<>();
    }

    public ReportAdvancedFilterBaseQo buildReportAdvancedFilterBaseQo() {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(this, qo);
        return qo;
    }
}
