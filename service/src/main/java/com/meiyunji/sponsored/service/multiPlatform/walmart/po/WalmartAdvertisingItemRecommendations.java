package com.meiyunji.sponsored.service.multiPlatform.walmart.po;


import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: ys
 * @date: 2025/3/4 15:45
 * @describe:
 */
@Data
@DbTable(value = "t_walmart_advertising_item_recommendations")
public class WalmartAdvertisingItemRecommendations extends BasePo {



    /**
     * id
     */
    @DbColumn(value = "id",autoIncrement=true,key = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 店铺code
     */
    @DbColumn(value = "marketplace_code")
    private String marketplaceCode;

    /**
     * 报告日期
     */
    @DbColumn(value = "report_date")
    private Date reportDate;

    /**
     * 产品id
     */
    @DbColumn(value = "item_id")
    private String itemId;

    /**
     * 产品名称
     */
    @DbColumn(value = "item_name")
    private String itemName;

    /**
     * 建议成本
     */
    @DbColumn(value = "suggested_bid")
    private Double suggestedBid;

    /**
     * 产品品牌名称
     */
    @DbColumn(value = "item_brand_name")
    private String itemBrandName;

    /**
     * 部门名称
     */
    @DbColumn(value = "super_department_name")
    private String superDepartmentName;

    /**
     * 部门名称
     */
    @DbColumn(value = "department_name")
    private String departmentName;

    /**
     * 类别
     */
    @DbColumn(value = "category")
    private String category;

    /**
     * 子类别
     */
    @DbColumn(value = "sub_category")
    private String subCategory;



}
