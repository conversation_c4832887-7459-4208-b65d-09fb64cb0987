package com.meiyunji.sponsored.service.syncAd.enums;

/**
 * @author: wade
 * @date: 2021/9/8 17:18
 * @describe:
 */
public class ShopDataSyncConstant {

    public static final String sp = "sp";

    public static final String sb = "sb";

    public static final String sd = "sd";

    public static final String campaign = "campaign";

    public static final String group = "group";

    public static final String product = "product";

    public static final String targeting = "targeting";

    public static final String netargeting = "netargeting";

    public static final String portfolioBudget = "portfolioBudget";

    public static final String campaignNetargeting = "campaignNetargeting";

    public static final String distributedLockPrefix = "shop_data_init_sync:%s:%s:%s:%s";

    public static final String DATA_SYNC_CACHE = "%s:%s:%s:%s";

    public static final Integer TRY_LOCK_SECONDS = 0;

    public static final Integer LOCK_SECONDS = 60;

    public static final Integer threadpoolTaskQueueWaitLimit = 200000;

    public static final String warningUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fedac8bb-2773-4e8c-a0f3-8446b8a348d1";

}

