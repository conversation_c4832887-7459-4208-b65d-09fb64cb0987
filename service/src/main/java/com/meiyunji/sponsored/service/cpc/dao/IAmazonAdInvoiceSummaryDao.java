package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceSummary;
import com.meiyunji.sponsored.service.cpc.vo.InvoicePageParam;

import java.util.List;

public interface IAmazonAdInvoiceSummaryDao {

    void insertOrUpdateList(int puid, List<AmazonAdInvoiceSummary> list);

    List<AmazonAdInvoiceSummary> getList(Integer puid, Integer shopId);

    List<AmazonAdInvoiceSummary> getListTask(Integer puid, Integer shopId, String startDate, String endDate);

    Page<AmazonAdInvoiceSummary> getPageList(Integer puid, InvoicePageParam param, boolean isDetails);

    AmazonAdInvoiceSummary getInvoiceByInvoiceId(Integer puid, Integer shopId, String invoiceId);

    List<AmazonAdInvoiceSummary> getInvoiceByInvoiceIds(Integer puid, List<Integer> shopIds, List<String> invoiceIds);

    List<AmazonAdInvoiceSummary> getSummary(Integer puid, InvoicePageParam param);

    void deleteByInvoiceId(Integer puid, Integer shopId, String invoiceId);
}
