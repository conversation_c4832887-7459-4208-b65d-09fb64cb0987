package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.util.DataFormatUtil;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReportKeyword;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * asin报告列表页数据
 * <AUTHOR> on 2021/4/29
 */
@Data
public class AsinReportPageVo {

    private Integer shopId;
    private String asin;
    private String asinImage;
    private String sku;
    private String otherAsin;
    private String otherAsinImage;

    /**
     * 投放值
     * 如果是关键词投放产生的，targetValue=keyword_text
     * 如果是产品投放产生的，targetValue=targeting_text
     */
    private String targetValue;

    // 如果是关键词产生的才有值：broad，phrase，exact
    private String matchType;


    private Integer otherSkuOrderNum;
    private String otherSkuSale;

    public AsinReportPageVo() {
    }

    public AsinReportPageVo(AmazonAdAsinReport report) {
        this.shopId = report.getShopId();
        this.asin = report.getAsin();
        this.asinImage = report.getAsinImage();
        this.sku = report.getSku();
        this.otherAsin = report.getOtherAsin();
        this.otherAsinImage = report.getOtherAsinImage();
        this.otherSkuOrderNum = report.getAttributedUnitsOrdered7dOtherSKU();
        if (report.getAttributedSales7dOtherSKU() != null) {
            this.otherSkuSale = DataFormatUtil.scale(report.getAttributedSales7dOtherSKU(), 2);
        }
    }

    public AsinReportPageVo(AmazonAdAsinReportKeyword report) {
        this.shopId = report.getShopId();
        this.asin = report.getAsin();
        this.asinImage = report.getAsinImage();
        this.sku = report.getSku();
        this.otherAsin = report.getOtherAsin();
        this.otherAsinImage = report.getOtherAsinImage();
        this.targetValue = report.getKeywordText();
        if (StringUtils.isNotBlank(report.getMatchType())) {
            this.matchType = report.getMatchType().toLowerCase();
        }
        this.otherSkuOrderNum = report.getAttributedUnitsOrdered7dOtherSKU();
        if (report.getAttributedSales7dOtherSKU() != null) {
            this.otherSkuSale = DataFormatUtil.scale(report.getAttributedSales7dOtherSKU(), 2);
        }
    }
}