package com.meiyunji.sponsored.service.attribution.entity;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_amazon_ad_attribution_report")
public class AmazonAdAttributionReport implements Serializable {

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn("shop_id")
    private Integer shopId;

    /**
     * 站点
     */
    @DbColumn("marketplace_id")
    private String marketplaceId;

    /**
     * 统计日期
     */
    @DbColumn("count_date")
    private String countDate;

    /**
     * 广告主Id
     */
    @DbColumn("advertiser_id")
    private String advertiserId;

    /**
     * 广告主名称
     */
    @DbColumn("advertiser_name")
    private String advertiserName;

    /**
     * 渠道id
     */
    @DbColumn("publisher_id")
    private String publisherId;

    /**
     * 渠道名称
     */
    @DbColumn("publisher_name")
    private String publisherName;

    @DbColumn("campaign_id")
    private String campaignId;

    @DbColumn("adGroup_id")
    private String adGroupId;

    @DbColumn("creative_id")
    private String creativeId;

    /**
     * 点击量
     */
    @DbColumn("click")
    private Long click;

    /**
     * 曝光量
     */
    @DbColumn("views")
    private Long views;

    /**
     * 总曝光量
     */
    @DbColumn("total_views")
    private Long totalViews;

    /**
     * 添加购物车数量
     */
    @DbColumn("add_to_cartNum")
    private Long addToCartNum;

    /**
     * 添加购物车总数量
     */
    @DbColumn("total_add_to_cartNum")
    private Long totalAddToCartNum;

    /**
     * 销量
     */
    @DbColumn("sold")
    private Long sold;

    /**
     * 总销量
     */
    @DbColumn("total_sold")
    private Long totalSold;

    /**
     * 购买次数
     */
    @DbColumn("purchases")
    private Long purchases;

    /**
     * 总购买次数
     */
    @DbColumn("total_purchases")
    private Long totalPurchases;

    /**
     * 销售额
     */
    @DbColumn("sales")
    private BigDecimal sales;

    /**
     * 总销售额
     */
    @DbColumn("total_sales")
    private BigDecimal totalSales;

    /**
     * 创建时间
     */
    @DbColumn("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @DbColumn("update_time")
    private LocalDateTime updateTime;
}