package com.meiyunji.sponsored.service.dbcompare.dto;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2023-11-06  20:23
 */

@Data
public class IgnoreDto {

    //忽略的表名
    private Set<String> ignoreTableSet = new HashSet<>();

    //忽略的字段, 格式为表名@字段名
    private Set<String> ignoreColumnSet = new HashSet<>();

    //忽略的索引, 格式为表名@索引名
    private Set<String> ignoreIndexSet = new HashSet<>();

}
