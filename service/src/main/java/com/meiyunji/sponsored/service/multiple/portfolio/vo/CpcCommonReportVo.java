package com.meiyunji.sponsored.service.multiple.portfolio.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by xp on 2021/4/13.
 * 广告列表页显示的公共字段：广告数据这些字段
 */
@Data
public class CpcCommonReportVo implements Serializable {
    /**
     * 曝光量:广告被展示的次数
     */
    private Long impressions;
    /**
     * 点击量:广告被点击的次数
     */
    private Long clicks;

    /**
     * 广告订单数:是指通过点击广告售出产品的订单数量
     */
    private Long adOrderNum;

    /**
     * 广告花费:广告活动的广告总花费
     */
    private BigDecimal adCost;

    /**
     * 广告销售额:广告销售额是指通过广告售出产品的销售额
     */
    private BigDecimal adSale;

    /**
     * 币种
     */
    private String currency;
    /**
     * 本广告产品订单量
     */
    private Long adSaleNum;

}
