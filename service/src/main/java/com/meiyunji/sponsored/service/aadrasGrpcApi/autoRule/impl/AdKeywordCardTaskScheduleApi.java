package com.meiyunji.sponsored.service.aadrasGrpcApi.autoRule.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.api.entry.RuleSchedulePb;
import com.meiyunji.sellfox.aadras.api.enumeration.*;
import com.meiyunji.sellfox.aadras.api.service.*;
import com.meiyunji.sellfox.aadras.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AadrasStubConfig;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AbstractAdAutoRuleApi;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.util.AutoRuleCallAadrasGrayHelper;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.kafka.AutoRuleConsistencyMessageKafkaProducer;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class AdKeywordCardTaskScheduleApi extends AbstractAdAutoRuleApi {

    @Autowired
    private AadrasStubConfig aadrasStubConfig;

    @Autowired
    private AutoRuleConsistencyMessageKafkaProducer autoRuleConsistencyMessageKafkaProducer;

    @Autowired
    private AutoRuleCallAadrasGrayHelper autoRuleCallAadrasGrayHelper;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    protected AdKeywordCardTaskScheduleApi(ShopAuthServiceImpl shopAuthService, ManagedChannel aadrasApiManagedChannel) {
        super(shopAuthService, aadrasApiManagedChannel);
    }

    @Override
    public boolean checkValid(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType advertiseRuleTaskType) {
        return advertiseRuleTaskType == AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.KEYWORD_TARGET;
    }

    @Override
    public void setAutoRuleTask(Long taskId, AdvertiseAutoRuleStatus advertiseAutoRuleStatus, ShopAuth shopAuth) throws Exception {
        String adType = advertiseAutoRuleStatus.getAdType();
        String keywordId = advertiseAutoRuleStatus.getItemId();
        SetKeywordTakePositionRuleTaskScheduleRequestPb.
                SetKeywordTakePositionRuleTaskScheduleRequest.Builder builder = SetKeywordTakePositionRuleTaskScheduleRequestPb
                .SetKeywordTakePositionRuleTaskScheduleRequest.newBuilder();
        builder.setPuid(advertiseAutoRuleStatus.getPuid());
        builder.setProfileId(advertiseAutoRuleStatus.getProfileId());
        builder.setShopId(shopAuth.getId());
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        builder.setTaskId(taskId);
        builder.setTemplateId(advertiseAutoRuleStatus.getTemplateId());
        builder.setRuleType(advertiseAutoRuleStatus.getRuleType());
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.Builder builder1 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.newBuilder();
        OriginValueVo originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(),OriginValueVo.class);
        builder1.setAsin(advertiseAutoRuleStatus.getAsin());
        builder1.setKeywordId(keywordId);
        builder1.setKeywordText(advertiseAutoRuleStatus.getKeywordText());
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getCampaignId())) {
            builder1.setCampaignId(advertiseAutoRuleStatus.getCampaignId());
        } else {
            List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.getByKeywordIds(advertiseAutoRuleStatus.getPuid(),
                    shopAuth.getId(), Lists.newArrayList(keywordId), Constants.BIDDABLE);
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                builder1.setCampaignId(amazonAdKeywords.get(0).getCampaignId());
            }
        }
        if (originValueVo !=null && originValueVo.getBiddingValue() != null) {
            builder1.setOriginalBid(originValueVo.getBiddingValue().doubleValue());
        }
        //todo hqw 提交参数
        if (originValueVo!=null && originValueVo.getPlacementTopBidRatio()!= null) {
            builder1.setOriginalPlacementTopBidRatio(originValueVo.getPlacementTopBidRatio());
        }
        builder1.setPostcode(advertiseAutoRuleStatus.getPostalCodeSettings());
        builder1.setFrequencyCheck(advertiseAutoRuleStatus.getCheckFrequency());
        builder1.setStartDate(advertiseAutoRuleStatus.getStartDate().format(DateTimeFormatter.ISO_DATE));
        if (advertiseAutoRuleStatus.getEndDate() != null) {
            builder1.setEndDate(advertiseAutoRuleStatus.getEndDate().format(DateTimeFormatter.ISO_DATE));
        }
        List<TimeRuleJson> timeRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getTimeRule(),TimeRuleJson.class);
        timeRuleJsonList.forEach(e->{
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.TimeRuleItem.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.TimeRuleItem.newBuilder();
            builder2.setSiteDate(e.getSiteDate());
            builder2.setStartTimeSite(e.getStartTimeSite());
            builder2.setEndTimeSite(e.getEndTimeSite());
            builder1.addTimeRuleItem(builder2.build());
        });
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAdDataRule())) {
            List<AdDataRuleJson> adDataRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getAdDataRule(),AdDataRuleJson.class);
            adDataRuleJsonList.forEach(e->{
                RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DataRuleItem.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DataRuleItem.newBuilder();
                if (e.getDay() != null) {
                    builder2.setDay(e.getDay());
                }
                if (e.getExcludeDay() != null) {
                    builder2.setExcludeDay(e.getExcludeDay());
                }
                if (StringUtils.isNotBlank(e.getRuleIndex())) {
                    builder2.setRuleIndex(RuleIndexTypePb.RuleIndexType.valueOf(e.getRuleIndex()));
                }
                if (StringUtils.isNotBlank(e.getRuleOperator())) {
                    builder2.setRuleOperatorType(RuleOperatorTypePb.RuleOperatorType.valueOf(e.getRuleOperator()));
                }
                if (StringUtils.isNotBlank(e.getRuleStatisticalModeType())) {
                    builder2.setRuleStatisticalModeType(RuleStatisticalModeTypePb.RuleStatisticalModeType.valueOf(e.getRuleStatisticalModeType()));
                }
                if (StringUtils.isNotBlank(e.getRuleValue())) {
                    builder2.setRuleValue(e.getRuleValue());
                }
                builder1.addDataRuleItem(builder2.build());
            });
        }
        // todo hqw 自动加价提交参数
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAdDataOperate())) {
            AdDataOperateJson adDataOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAdDataOperate(),AdDataOperateJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.newBuilder();
            if (Objects.nonNull(adDataOperateJson.getBid()) || Objects.nonNull(adDataOperateJson.getPlacementTopBidRatio())) {
                Optional.ofNullable(toPerformOperation(adDataOperateJson.getBid())).ifPresent(builder1::addPerformOperation);
                Optional.ofNullable(toPerformOperation(adDataOperateJson.getPlacementTopBidRatio())).ifPresent(builder1::addPerformOperation);
            } else {
                if (StringUtils.isNotBlank(adDataOperateJson.getRuleAction())) {
                    builder2.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(adDataOperateJson.getRuleAction()));
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getRuleAdjust())) {
                    builder2.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(adDataOperateJson.getRuleAdjust()));
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getAdJustValue())) {
                    builder2.setAdJustValue(adDataOperateJson.getAdJustValue());
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getLimitValue())) {
                    builder2.setLimitValue(adDataOperateJson.getLimitValue());
                }
                builder1.addPerformOperation(builder2.build());
            }
        }
        // todo hqw 自动降价提交参数
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAutoPriceRule())) {
            AutoPriceRuleJson autoPriceRuleJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAutoPriceRule(), AutoPriceRuleJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.AutoPriceReduction.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.AutoPriceReduction.newBuilder();
            if (autoPriceRuleJson.getPage() != null) {
                builder2.setPage(autoPriceRuleJson.getPage());
            }
            if (autoPriceRuleJson.getRank() != null) {
                builder2.setRank(autoPriceRuleJson.getRank());
            }
            if (autoPriceRuleJson.getContinuousFrequency() != null) {
                builder2.setContinuousFrequency(autoPriceRuleJson.getContinuousFrequency());
            }
            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAutoPriceOperate())) {
                AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAutoPriceOperate(),AutoPriceOperateJson.class);
                RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.Builder performOperation =  RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.newBuilder();
                if (Objects.nonNull(autoPriceOperateJson.getBid()) || Objects.nonNull(autoPriceOperateJson.getPlacementTopBidRatio())) {
                    Optional.ofNullable(toPerformOperation(autoPriceOperateJson.getBid())).ifPresent(builder2::addPerformOperations);
                    Optional.ofNullable(toPerformOperation(autoPriceOperateJson.getPlacementTopBidRatio())).ifPresent(builder2::addPerformOperations);
                } else {
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getRuleAction())) {
                        performOperation.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(autoPriceOperateJson.getRuleAction()));
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getRuleAdjust())) {
                        performOperation.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(autoPriceOperateJson.getRuleAdjust()));
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getAdJustValue())) {
                        performOperation.setAdJustValue(autoPriceOperateJson.getAdJustValue());
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getLimitValue())) {
                        performOperation.setLimitValue(autoPriceOperateJson.getLimitValue());
                    }
                    builder2.addPerformOperations(performOperation.build());
                }
            }
            builder1.setAutoPriceReduction(builder2.build());
        }
        // todo hqw 回调设置提交参数
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getBiddingCallbackOperate())) {
            BiddingCallbackOperateJson biddingCallbackOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getBiddingCallbackOperate(),BiddingCallbackOperateJson.class);
            if (Objects.nonNull(biddingCallbackOperateJson.getBid()) || Objects.nonNull(biddingCallbackOperateJson.getPlacementTopBidRatio())) {
                Optional.ofNullable(toRecoveryAdjustment(biddingCallbackOperateJson.getBid(), RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.bid)).ifPresent(builder1::addRecoveryAdjustments);
                Optional.ofNullable(toRecoveryAdjustment(biddingCallbackOperateJson.getPlacementTopBidRatio(), RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.placementTopBidRatio)).ifPresent(builder1::addRecoveryAdjustments);
            } else if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdjustType()))  {
                RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.newBuilder();
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdjustType())) {
                    builder2.setAdjustType(RuleKeywordTakePositionAdjustTypePb.RuleKeywordTakePositionAdjustType.
                            valueOf(biddingCallbackOperateJson.getAdjustType()));
                }
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdJustValue())) {
                    builder2.setAdJustValue(biddingCallbackOperateJson.getAdJustValue());
                }
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getLimitValue())) {
                    builder2.setLimitValue(biddingCallbackOperateJson.getLimitValue());
                }
                builder2.setRecoveryType(RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.bid);
                builder1.addRecoveryAdjustments(builder2.build());
            }
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getDesiredPosition())) {
            DesiredPositionJson positionJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getDesiredPosition(),DesiredPositionJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DesiredPosition.Builder builder2 =  RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DesiredPosition.newBuilder();
            if (positionJson.getPage() != null) {
                builder2.setPage(positionJson.getPage());
            }
            if (positionJson.getFrom() != null) {
                builder2.setFrom(positionJson.getFrom());
            }
            if (positionJson.getTo() != null) {
                builder2.setTo(positionJson.getTo());
            }
            builder1.setDesiredPosition(builder2.build());
        }
        builder.setTaskScheduler(builder1.build());
        SetKeywordTakePositionRuleTaskScheduleRequestPb.
                SetKeywordTakePositionRuleTaskScheduleRequest request =  builder.build();

        if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
            try {
                log.info("自动化规则-抢排名提交请求参数: {}", request);
                AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                SetKeywordTakePositionRuleTaskScheduleResponsePb.SetKeywordTakePositionRuleTaskScheduleResponse response =
                        stub.setKeywordTakePositionRuleTaskSchedule(request);
            } catch (StatusRuntimeException e) {
                throw GrpcExceptionUtil.unWrapException(e);
            }
        } else {
            try {
                if (!autoRuleCallAadrasGrayHelper.isGray(advertiseAutoRuleStatus.getPuid())) {
                    log.info("自动化规则-抢排名提交请求参数: {}", request);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    SetKeywordTakePositionRuleTaskScheduleResponsePb.SetKeywordTakePositionRuleTaskScheduleResponse response =
                            stub.setKeywordTakePositionRuleTaskSchedule(request);
                }
            } catch (Exception e) {
                log.error("异步更新受控对象异常", e);
            } finally {
                String jsonMessage = ProtoBufUtil.toJsonStr(request);
                AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(advertiseAutoRuleStatus.getShopId(),
                        AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.KEYWORD_TARGET.name(), false, false, jsonMessage);
                log.info("异步更新受控对象, 发送异步消息, message: {}", consistencyDto);
                autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
            }
        }
    }

    @Override
    public void removeAutoRuleTask(Long taskId, String targetType, String itemStatus, ShopAuth shopAuth) throws Exception {
        RemoveKeywordTakePositionRuleTaskScheduleRequestPb.RemoveKeywordTakePositionRuleTaskScheduleRequest.Builder builder = RemoveKeywordTakePositionRuleTaskScheduleRequestPb.RemoveKeywordTakePositionRuleTaskScheduleRequest.newBuilder();
        builder.setPuid(shopAuth.getPuid());
        builder.setShopId(shopAuth.getId());
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setTaskId(taskId);
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RemoveKeywordTakePositionRuleTaskScheduleRequestPb.RemoveKeywordTakePositionRuleTaskScheduleRequest request = builder.build();
        if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
            try {
                log.info("抢排名-删除受控对象: {}", request);
                AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                RemoveKeywordTakePositionRuleTaskScheduleResponsePb.RemoveKeywordTakePositionRuleTaskScheduleResponse response =
                        stub.removeKeywordTakePositionRuleTaskSchedule(request);
            } catch (StatusRuntimeException e) {
                throw GrpcExceptionUtil.unWrapException(e);
            }
        } else {
            try {
                if (!autoRuleCallAadrasGrayHelper.isGray(shopAuth.getPuid())) {
                    log.info("抢排名-删除受控对象: {}", request);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    RemoveKeywordTakePositionRuleTaskScheduleResponsePb.RemoveKeywordTakePositionRuleTaskScheduleResponse response =
                            stub.removeKeywordTakePositionRuleTaskSchedule(request);
                }
            } catch (Exception e) {
                log.error("异步删除受控对象异常", e);
            } finally {
                String jsonMessage = ProtoBufUtil.toJsonStr(request);
                AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(shopAuth.getId(),
                        AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.KEYWORD_TARGET.name(), true, false, jsonMessage);
                log.info("异步删除受控对象, 发送异步消息, message: {}", consistencyDto);
                autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
            }
        }
    }

    @Override
    public void executeRuleTask(Integer puid, Integer shopId, String marketplaceId, Long recordId, Long taskId, AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType advertiseRuleTaskType) throws Exception {

    }

    @Override
    public void batchSetAutoRuleTask(List<AdvertiseAutoRuleStatus> advertiseAutoRuleStatusList, ShopAuth shopAuth) throws Exception {
        BatchSetKeywordTakePositionRuleTaskScheduleRequestPb.BatchSetKeywordTakePositionRuleTaskScheduleRequest.Builder request = BatchSetKeywordTakePositionRuleTaskScheduleRequestPb.BatchSetKeywordTakePositionRuleTaskScheduleRequest.newBuilder();
        for (AdvertiseAutoRuleStatus advertiseAutoRuleStatus : advertiseAutoRuleStatusList) {
            keywordTakePositionRuleTaskScheduleDataAssembly(shopAuth, advertiseAutoRuleStatus);
        }
    }

    private void keywordTakePositionRuleTaskScheduleDataAssembly (ShopAuth shopAuth, AdvertiseAutoRuleStatus advertiseAutoRuleStatus) throws Exception {
        String adType = advertiseAutoRuleStatus.getAdType();
        String keywordId = advertiseAutoRuleStatus.getItemId();
        SetKeywordTakePositionRuleTaskScheduleRequestPb.
                SetKeywordTakePositionRuleTaskScheduleRequest.Builder builder = SetKeywordTakePositionRuleTaskScheduleRequestPb
                .SetKeywordTakePositionRuleTaskScheduleRequest.newBuilder();
        builder.setPuid(advertiseAutoRuleStatus.getPuid());
        builder.setProfileId(advertiseAutoRuleStatus.getProfileId());
        builder.setShopId(shopAuth.getId());
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        builder.setTaskId(advertiseAutoRuleStatus.getTaskId());
        builder.setTemplateId(advertiseAutoRuleStatus.getTemplateId());
        builder.setRuleType(advertiseAutoRuleStatus.getRuleType());
        builder.setMarketplace(MarketplacePb.Marketplace.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).name()));
        RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.Builder builder1 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.newBuilder();
        OriginValueVo originValueVo = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getOriginValue(),OriginValueVo.class);
        builder1.setAsin(advertiseAutoRuleStatus.getAsin());
        builder1.setKeywordId(keywordId);
        builder1.setKeywordText(advertiseAutoRuleStatus.getKeywordText());
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getCampaignId())) {
            builder1.setCampaignId(advertiseAutoRuleStatus.getCampaignId());
        } else {
            List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.getByKeywordIds(advertiseAutoRuleStatus.getPuid(),
                    shopAuth.getId(), Lists.newArrayList(keywordId), Constants.BIDDABLE);
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                builder1.setCampaignId(amazonAdKeywords.get(0).getCampaignId());
            }
        }

//        if (originValueVo !=null && originValueVo.getBiddingValue() != null) {
//            builder1.setOriginalBid(originValueVo.getBiddingValue().doubleValue());
//        }
        if (Objects.nonNull(originValueVo)) {
            if (Objects.nonNull(originValueVo.getBiddingValue())) {
                builder1.setOriginalBid(originValueVo.getBiddingValue().doubleValue());
            }
            if (Objects.nonNull(originValueVo.getPlacementTopBidRatio())) {
                builder1.setOriginalPlacementTopBidRatio(originValueVo.getPlacementTopBidRatio());
            }
        }

        builder1.setPostcode(advertiseAutoRuleStatus.getPostalCodeSettings());
        builder1.setFrequencyCheck(advertiseAutoRuleStatus.getCheckFrequency());
        builder1.setStartDate(advertiseAutoRuleStatus.getStartDate().format(DateTimeFormatter.ISO_DATE));
        if (advertiseAutoRuleStatus.getEndDate() != null) {
            builder1.setEndDate(advertiseAutoRuleStatus.getEndDate().format(DateTimeFormatter.ISO_DATE));
        }
        List<TimeRuleJson> timeRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getTimeRule(),TimeRuleJson.class);
        timeRuleJsonList.forEach(e->{
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.TimeRuleItem.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.TimeRuleItem.newBuilder();
            builder2.setSiteDate(e.getSiteDate());
            builder2.setStartTimeSite(e.getStartTimeSite());
            builder2.setEndTimeSite(e.getEndTimeSite());
            builder1.addTimeRuleItem(builder2.build());
        });
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAdDataRule())) {
            List<AdDataRuleJson> adDataRuleJsonList = JSONUtil.jsonToArray(advertiseAutoRuleStatus.getAdDataRule(),AdDataRuleJson.class);
            adDataRuleJsonList.forEach(e->{
                RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DataRuleItem.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DataRuleItem.newBuilder();
                if (e.getDay() != null) {
                    builder2.setDay(e.getDay());
                }
                if (e.getExcludeDay() != null) {
                    builder2.setExcludeDay(e.getExcludeDay());
                }
                if (StringUtils.isNotBlank(e.getRuleIndex())) {
                    builder2.setRuleIndex(RuleIndexTypePb.RuleIndexType.valueOf(e.getRuleIndex()));
                }
                if (StringUtils.isNotBlank(e.getRuleOperator())) {
                    builder2.setRuleOperatorType(RuleOperatorTypePb.RuleOperatorType.valueOf(e.getRuleOperator()));
                }
                if (StringUtils.isNotBlank(e.getRuleStatisticalModeType())) {
                    builder2.setRuleStatisticalModeType(RuleStatisticalModeTypePb.RuleStatisticalModeType.valueOf(e.getRuleStatisticalModeType()));
                }
                if (StringUtils.isNotBlank(e.getRuleValue())) {
                    builder2.setRuleValue(e.getRuleValue());
                }
                builder1.addDataRuleItem(builder2.build());
            });
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAdDataOperate())) {
            // 加价 兼容老数据 加价操作本来就是list
            AdDataOperateJson adDataOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAdDataOperate(),AdDataOperateJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.newBuilder();
            if (Objects.nonNull(adDataOperateJson.getBid()) || Objects.nonNull(adDataOperateJson.getPlacementTopBidRatio())) {
                Optional.ofNullable(toPerformOperation(adDataOperateJson.getBid())).ifPresent(builder1::addPerformOperation);
                Optional.ofNullable(toPerformOperation(adDataOperateJson.getPlacementTopBidRatio())).ifPresent(builder1::addPerformOperation);
            } else {
                if (StringUtils.isNotBlank(adDataOperateJson.getRuleAction())) {
                    builder2.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(adDataOperateJson.getRuleAction()));
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getRuleAdjust())) {
                    builder2.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(adDataOperateJson.getRuleAdjust()));
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getAdJustValue())) {
                    builder2.setAdJustValue(adDataOperateJson.getAdJustValue());
                }
                if (StringUtils.isNotBlank(adDataOperateJson.getLimitValue())) {
                    builder2.setLimitValue(adDataOperateJson.getLimitValue());
                }
                builder1.addPerformOperation(builder2.build());
            }
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAutoPriceRule())) {
            AutoPriceRuleJson autoPriceRuleJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAutoPriceRule(), AutoPriceRuleJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.AutoPriceReduction.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.AutoPriceReduction.newBuilder();
            if (autoPriceRuleJson.getPage() != null) {
                builder2.setPage(autoPriceRuleJson.getPage());
            }
            if (autoPriceRuleJson.getRank() != null) {
                builder2.setRank(autoPriceRuleJson.getRank());
            }
            if (autoPriceRuleJson.getContinuousFrequency() != null) {
                builder2.setContinuousFrequency(autoPriceRuleJson.getContinuousFrequency());
            }
            if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getAutoPriceOperate())) {
                // 降价 兼容老数据 降价操作本来是单个 换成list
                AutoPriceOperateJson autoPriceOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getAutoPriceOperate(), AutoPriceOperateJson.class);
                if (Objects.nonNull(autoPriceOperateJson.getBid()) || Objects.nonNull(autoPriceOperateJson.getPlacementTopBidRatio())) {
                    Optional.ofNullable(toPerformOperation(autoPriceOperateJson.getBid())).ifPresent(builder2::addPerformOperations);
                    Optional.ofNullable(toPerformOperation(autoPriceOperateJson.getPlacementTopBidRatio())).ifPresent(builder2::addPerformOperations);
                } else {
                    RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.Builder performOperation = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.newBuilder();
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getRuleAction())) {
                        performOperation.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(autoPriceOperateJson.getRuleAction()));
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getRuleAdjust())) {
                        performOperation.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(autoPriceOperateJson.getRuleAdjust()));
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getAdJustValue())) {
                        performOperation.setAdJustValue(autoPriceOperateJson.getAdJustValue());
                    }
                    if (StringUtils.isNotBlank(autoPriceOperateJson.getLimitValue())) {
                        performOperation.setLimitValue(autoPriceOperateJson.getLimitValue());
                    }
                    builder2.addPerformOperations(performOperation.build());
                }
            }
            builder1.setAutoPriceReduction(builder2.build());
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getBiddingCallbackOperate())) {
            // 回调 兼容老数据 之前是单个 现在改成list 并添加字段区分是什么回调
            BiddingCallbackOperateJson biddingCallbackOperateJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getBiddingCallbackOperate(), BiddingCallbackOperateJson.class);
            if (Objects.nonNull(biddingCallbackOperateJson.getBid()) || Objects.nonNull(biddingCallbackOperateJson.getPlacementTopBidRatio())) {
                Optional.ofNullable(toRecoveryAdjustment(biddingCallbackOperateJson.getBid(), RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.bid))
                        .ifPresent(builder1::addRecoveryAdjustments);
                Optional.ofNullable(toRecoveryAdjustment(biddingCallbackOperateJson.getPlacementTopBidRatio(), RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.placementTopBidRatio))
                        .ifPresent(builder1::addRecoveryAdjustments);
            } else if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdjustType())) {
                RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.Builder builder2 = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.newBuilder();
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdjustType())) {
                    builder2.setAdjustType(RuleKeywordTakePositionAdjustTypePb.RuleKeywordTakePositionAdjustType.
                            valueOf(biddingCallbackOperateJson.getAdjustType()));
                }
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getAdJustValue())) {
                    builder2.setAdJustValue(biddingCallbackOperateJson.getAdJustValue());
                }
                if (StringUtils.isNotBlank(biddingCallbackOperateJson.getLimitValue())) {
                    builder2.setLimitValue(biddingCallbackOperateJson.getLimitValue());
                }
                builder2.setRecoveryType(RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType.bid);
                builder1.addRecoveryAdjustments(builder2.build());
            }
        }
        if (StringUtils.isNotBlank(advertiseAutoRuleStatus.getDesiredPosition())) {
            DesiredPositionJson positionJson = JSONUtil.jsonToObject(advertiseAutoRuleStatus.getDesiredPosition(),DesiredPositionJson.class);
            RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DesiredPosition.Builder builder2 =  RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.DesiredPosition.newBuilder();
            if (positionJson.getPage() != null) {
                builder2.setPage(positionJson.getPage());
            }
            if (positionJson.getFrom() != null) {
                builder2.setFrom(positionJson.getFrom());
            }
            if (positionJson.getTo() != null) {
                builder2.setTo(positionJson.getTo());
            }
            builder1.setDesiredPosition(builder2.build());
        }
        builder.setTaskScheduler(builder1.build());
        SetKeywordTakePositionRuleTaskScheduleRequestPb.
                SetKeywordTakePositionRuleTaskScheduleRequest request =  builder.build();

        if (!dynamicRefreshNacosConfiguration.isAutoruleCallAadrasUseKafkaConsistency()) {
            try {
                log.info("自动化规则-抢排名提交请求参数: {}", request);
                AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                SetKeywordTakePositionRuleTaskScheduleResponsePb.SetKeywordTakePositionRuleTaskScheduleResponse response =
                        stub.setKeywordTakePositionRuleTaskSchedule(request);
            } catch (StatusRuntimeException e) {
                throw GrpcExceptionUtil.unWrapException(e);
            }
        } else {
            try {
                if (!autoRuleCallAadrasGrayHelper.isGray(advertiseAutoRuleStatus.getPuid())) {
                    log.info("自动化规则-抢排名提交请求参数: {}", request);
                    AadrasApiGrpc.AadrasApiBlockingStub stub = aadrasStubConfig.getStub();
                    SetKeywordTakePositionRuleTaskScheduleResponsePb.SetKeywordTakePositionRuleTaskScheduleResponse response =
                            stub.setKeywordTakePositionRuleTaskSchedule(request);
                }
            } catch (Exception e) {
                log.error("异步提交受控对象异常", e);
            } finally {
                String jsonMessage = ProtoBufUtil.toJsonStr(request);
                AutoRuleConsistencyDto consistencyDto = new AutoRuleConsistencyDto(advertiseAutoRuleStatus.getShopId(),
                        AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.KEYWORD_TARGET.name(), false, false, jsonMessage);
                log.info("异步提交受控对象, 发送异步消息, message: {}", consistencyDto);
                try {
                    autoRuleConsistencyMessageKafkaProducer.send(consistencyDto);
                } catch (Exception e) {
                    log.info("异步提交受控对象, 发送异步消息异常", e);
                }
            }
        }
    }

    private RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation toPerformOperation(BidDataOperate bidDataOperate) {
        if (Objects.isNull(bidDataOperate)) {
            return null;
        }
        RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.Builder builder = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.PerformOperation.newBuilder();
        if (StringUtils.isNotBlank(bidDataOperate.getRuleAction())) {
            builder.setRuleAction(RuleActionTypePb.RuleActionType.valueOf(bidDataOperate.getRuleAction()));
        }
        if (StringUtils.isNotBlank(bidDataOperate.getRuleAdjust())) {
            builder.setRuleAdjust(RuleAdjustTypePb.RuleAdjustType.valueOf(bidDataOperate.getRuleAdjust()));
        }
        if (StringUtils.isNotBlank(bidDataOperate.getAdJustValue())) {
            builder.setAdJustValue(bidDataOperate.getAdJustValue());
        }
        if (StringUtils.isNotBlank(bidDataOperate.getLimitValue())) {
            builder.setLimitValue(bidDataOperate.getLimitValue());
        }
        return builder.build();
    }

    private RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment toRecoveryAdjustment(BidDataOperate bidDataOperate,
                                                                                                       RuleKeywordTakePositionRecoveryTypePb.RuleKeywordTakePositionRecoveryType recoveryType) {
        if (Objects.isNull(bidDataOperate) || StringUtils.isBlank(bidDataOperate.getAdjustType())) {
            return null;
        }
        RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.Builder builder = RuleSchedulePb.RuleKeywordTakePositionTaskSchedule.RecoveryAdjustment.newBuilder();
        if (StringUtils.isNotBlank(bidDataOperate.getAdjustType())) {
            builder.setAdjustType(RuleKeywordTakePositionAdjustTypePb.RuleKeywordTakePositionAdjustType.
                    valueOf(bidDataOperate.getAdjustType()));
        }
        if (StringUtils.isNotBlank(bidDataOperate.getAdJustValue())) {
            builder.setAdJustValue(bidDataOperate.getAdJustValue());
        }
        if (StringUtils.isNotBlank(bidDataOperate.getLimitValue())) {
            builder.setLimitValue(bidDataOperate.getLimitValue());
        }
        builder.setRecoveryType(recoveryType);
        return builder.build();
    }

}
