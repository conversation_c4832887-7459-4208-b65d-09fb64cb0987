package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.rpc.asins.AsinPageVo;
import com.meiyunji.sponsored.service.cpc.bo.KeywordLibMarkupAsinBo;
import com.meiyunji.sponsored.service.cpc.dao.IAsinsLibMarkupAsinDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinLibMarkupAsin;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibAsinTagPageListVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: ys
 * @date: 2024/12/3 15:32
 * @describe:
 */
@Repository
public class AsinsLibMarkupAsinDaoImpl extends BaseShardingDaoImpl<AmazonAdAsinLibMarkupAsin> implements IAsinsLibMarkupAsinDao {
    @Override
    public int batchInsert(List<AmazonAdAsinLibMarkupAsin> list) {
        StringBuilder sql = new StringBuilder("insert into `t_ad_asin_lib_markup_asin` (`puid`, `uid`, `asin_lib_id`, `marketplace_id`, " +
                "`asin`, `create_id`, `update_id`) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdAsinLibMarkupAsin e : list) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?),");
            argsList.add(e.getPuid());
            argsList.add(e.getUid());
            argsList.add(e.getAsinLibId());
            argsList.add(e.getMarketplaceId());
            argsList.add(e.getAsin());
            argsList.add(e.getCreateId());
            argsList.add(e.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `update_id`= values(update_id)");
        return getJdbcTemplate(list.get(0).getPuid()).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdAsinLibMarkupAsin> listByUidAndAsinIds(Integer puid, List<Integer> uidList, List<Long> asinIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .in("uid", uidList.toArray())
                .in("asin_lib_id", asinIdList.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<Long> listAsinIdByAsin(Integer puid, List<Integer> uid, String marketplaceId, String asin) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("asin", asin);
        if (CollectionUtils.isNotEmpty(uid)) {
            builder.in("uid", uid.toArray());
        }
        return listDistinctFieldByCondition(puid, "asin_lib_id", builder.build(), Long.class);
    }

    @Override
    public int deleteByAsinLibId(int puid, int uid, List<Long> ids) {
        StringBuilder sql = new StringBuilder("delete from t_ad_asin_lib_markup_asin where puid = ? and uid = ? ");
        List<Object> args = Lists.newArrayList(puid, uid);
        sql.append(SqlStringUtil.dealInList("asin_lib_id", ids, args));
        return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public List<AmazonAdAsinLibMarkupAsin> getListByAsinsLibId(Integer puid, List<Integer> uid, List<Long> idList) {
        StringBuilder sb = new StringBuilder("select * from " + this.getJdbcHelper().getTable() + " where ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(uid)) {
            sb.append(SqlStringUtil.dealInList("uid", uid, argsList));
        }
        sb.append(SqlStringUtil.dealInList("asin_lib_id", idList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), getRowMapper());
    }

    @Override
    public Page<AmazonAdAsinLibMarkupAsin> getAsinTagPageList(Integer puid, List<Integer> uidList, List<String> marketplaceIdList, AsinLibAsinTagPageListVo req) {
        StringBuilder selectSql = new StringBuilder("select distinct marketplace_id,asin from ").append(this.getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(*) from (select distinct marketplace_id, asin from ").append(this.getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(uidList)) {
            whereSql.append(SqlStringUtil.dealInList("uid", uidList, argsList));
        }
        whereSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, argsList));

        if (req.getAsinLibId() != null) {
            whereSql.append(" and asin_lib_id = ? ");
            argsList.add(req.getAsinLibId());
        }
        if (StringUtils.isNotBlank(req.getAsin())) {
            whereSql.append(" and asin = ? ");
            argsList.add(req.getAsin());
        }

        selectSql.append(whereSql).append(" order by id ");
        countSql.append(whereSql).append(") t");
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(puid, req.getPageNo(), req.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdAsinLibMarkupAsin.class);

    }

    @Override
    public int deleteByKeywordsLibIdAndAsin(int puid, List<Integer> uidList, List<Long> idList, String marketplaceId, String asin) {
        StringBuilder sql = new StringBuilder("delete from t_ad_asin_lib_markup_asin where puid = ? ");
        sql.append(" and marketplace_id = ? and asin = ? ");
        List<Object> args = Lists.newArrayList(puid, marketplaceId, asin);
        sql.append(SqlStringUtil.dealInList("asin_lib_id", idList, args));
        if (CollectionUtils.isNotEmpty(uidList)) {
            sql.append(SqlStringUtil.dealInList("uid", uidList, args));
        }
        return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }
}
