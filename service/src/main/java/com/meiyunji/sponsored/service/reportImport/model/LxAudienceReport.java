package com.meiyunji.sponsored.service.reportImport.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import com.meiyunji.sponsored.service.reportImport.listener.converter.OtherStringNumberConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LxAudienceReport extends BaseLxReport {

    /**
     * 广告组
     */
    @ExcelProperty(value = "广告组", converter = CustomStringNumberConverter.class)
    private String adGroupName;
    /**
     * 投放
     */
    @ExcelProperty(value = "投放", converter = CustomStringNumberConverter.class)
    private String target;

    /**
     * sd投放类型 exactProduct->您推广的商品;similarProduct->与您推广商品类似的商品;category->类型
     */
    @ExcelIgnore
    private String targetType;

    @ExcelIgnore
    private String categoryName;

    /**
     *  花费-本币
     */
    @ExcelProperty(value = "花费-本币", converter = CustomStringNumberConverter.class)
    private String adCost;

    /**
     * 曝光量
     */
    @ExcelProperty(value = "曝光量", converter = CustomStringNumberConverter.class)
    private String impressions;

    /**
     * 点击量
     */
    @ExcelProperty(value = "点击", converter = CustomStringNumberConverter.class)
    private String clicks;

    /**
     * 广告订单
     */
    @ExcelProperty(value = "广告订单", converter = CustomStringNumberConverter.class)
    private String adOrder;

    /**
     * 直接成交订单
     */
    @ExcelProperty(value = "直接成交订单", converter = CustomStringNumberConverter.class)
    private String adSelfOrder;

    /**
     * 间接成交订单
     */
    @ExcelProperty(value = "间接成交订单", converter = CustomStringNumberConverter.class)
    private String adOtherOrder;

    /**
     * 销售额-本币
     */
    @ExcelProperty(value = "销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSales;

    /**
     * 直接成交销售额-本币
     */
    @ExcelProperty(value = "直接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSelfSales;

    /**
     * 间接成交销售额-本币
     */
    @ExcelProperty(value = "间接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adOtherSales;

    /**
     * 广告销量
     */
    @ExcelProperty(value = "广告销量", converter = CustomStringNumberConverter.class)
    private String adSaleNum;

    /**
     * 可见展示次数
     */
    @ExcelProperty(value = "可见展示次数", converter = OtherStringNumberConverter.class)
    private String viewImpressions;

    /**
     * 可见展示次数
     */
    @ExcelProperty(value = "\"品牌新买家\"订单数量", converter = OtherStringNumberConverter.class)
    private String ordersNewToBrand14d;

    /**
     * 可见展示次数
     */
    @ExcelProperty(value = "\"品牌新买家\" 销售额-本币", converter = OtherStringNumberConverter.class)
    private String salesNewToBrand14d;


    /**
     * 可见展示次数
     */
    @ExcelProperty(value = "\"品牌新买家\" 销量", converter = OtherStringNumberConverter.class)
    private String unitsOrderedNewToBrand14d;

}
