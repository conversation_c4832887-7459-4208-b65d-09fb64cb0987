package com.meiyunji.sponsored.service.attribution.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionReportSyncStatusDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionReportSyncStatus;
import lombok.SneakyThrows;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/21 9:54
 * @describe:
 */
@Repository
public class AmazonAdAttributionReportSyncStatusDao extends AdBaseDaoImpl<AmazonAdAttributionReportSyncStatus> implements IAmazonAdAttributionReportSyncStatusDao {
    @Override
    public AmazonAdAttributionReportSyncStatus getByShopId(Integer puid, Integer shopId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .limit(1).build();
        return getByCondition(conditionBuilder);
    }

    @SneakyThrows
    @Override
    public Long insertStatus(AmazonAdAttributionReportSyncStatus status) {
        return save(status);
    }

    @Override
    public List<AmazonAdAttributionReportSyncStatus> getNeedSyncAttributionReportShop(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null) {
            builder.equalTo("puid",puid);
        }
        if (shopId != null) {
            builder.equalTo("shop_id",shopId);
        }
        builder.lessThan("next_sync_time", LocalDateTime.now());
        return listByCondition(builder.build());
    }
}
