package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.budget.analysis.BudgetAnalysisQueryGrpcRequest;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetAnalysisService;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisExportDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisPageDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisQueryRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.vo.AdvertisingCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service(AdManagePageExportTaskConstant.BUDGET_ANALYSIS)
@Slf4j
public class BudgetAnalysisExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Resource
    private IAmazonAdBudgetAnalysisService amazonAdBudgetAnalysisService;
    @Autowired
    private StringRedisService stringRedisService;

    @Autowired
    private IExcelService excelService;

    @Override
    public void export(AdManagePageExportTask task) {
        BudgetAnalysisQueryRequest param = JSONUtil.jsonToObjectIgnoreUnKnown(task.getParam(), BudgetAnalysisQueryRequest.class);
        if (param == null) {
            log.error(String.format("BUDGET_ANALYSIS export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Page<BudgetAnalysisPageDto> page = amazonAdBudgetAnalysisService.listExport(param);
        if (page == null || CollectionUtils.isEmpty(page.getRows())) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        List<String> urlList = new ArrayList<>();
        String fileName = "预算分析" + "_" + param.getStartDate().replace("-", "") + "_" + param.getEndDate().replace("-", "") + "_" + task.getId().toString();
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(BudgetAnalysisExportDto.class);
        urlList.add(excelService.easyExcelHandlerExport(task.getPuid(),
                page.getRows().stream().map(BudgetAnalysisExportDto::setAll).collect(Collectors.toList()), fileName, BudgetAnalysisExportDto.class, build, Lists.newArrayList()));
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
}
