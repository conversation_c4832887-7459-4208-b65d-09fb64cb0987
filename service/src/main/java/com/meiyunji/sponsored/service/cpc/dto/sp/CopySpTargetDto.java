package com.meiyunji.sponsored.service.cpc.dto.sp;

import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.vo.SpNeTargetingVo;
import lombok.Data;

import java.util.List;

/**
 * sp投放创建info
 *
 * @Author: hejh
 * @Date: 2024/7/10 19:50
 */
@Data
public class CopySpTargetDto {
    private Integer uid;
    private Integer puid;
    private Integer shopId;
    private String loginIp;

    private String groupId;
    /**
     * 是否自动投放
     */
    private Boolean autoTarget;
    /**
     * 是否关键词投放
     */
    private Boolean keywordTarget;
    /**
     * 关键词投放特有
     */
    private List<AmazonAdKeyword> keywords;
    /**
     * 产品投放特有
     */
    private List<AmazonAdTargeting> targetings;
    /**
     * 否定投放关键词
     */
    private List<AmazonAdKeyword> neKeywords;
    /**
     * 否定投放产品
     */
    private List<SpNeTargetingVo> neTargetings;
    //自动投放特有
    private Boolean queryHighRelMatchesState; //紧密匹配
    private String queryHighRelMatchesBid;
    private Boolean queryBroadRelMatchesState; //宽泛匹配
    private String queryBroadRelMatchesBid;
    private Boolean asinSubstituteRelatedState; //同类商品
    private String asinSubstituteRelatedBid;
    private Boolean asinAccessoryRelatedState; //关联商品
    private String asinAccessoryRelatedBid;
}
