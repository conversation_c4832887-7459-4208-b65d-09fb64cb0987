package com.meiyunji.sponsored.service.syncAd.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 任务同步缓存，guava-cache实现
 * @Author: hejh
 * @Date: 2024/5/6 11:00
 */
public class TaskSyncCache {

    private static final int TTL_HOURS = 6;
    private static final String COMMON_VALUE = "0";


    /**
     * 缓存数据
     */
    private static final Cache<String, String> CACHE = CacheBuilder.newBuilder()
        //设置缓存有效时间
        .expireAfterWrite(TTL_HOURS, TimeUnit.HOURS)
        .build();

    public static void put(String key) {
        CACHE.put(key, COMMON_VALUE);
    }

    public static void clear(String key) {
        CACHE.invalidate(key);
    }

    public static boolean contains(String key) {
        return Objects.nonNull(CACHE.getIfPresent(key));
    }
}
