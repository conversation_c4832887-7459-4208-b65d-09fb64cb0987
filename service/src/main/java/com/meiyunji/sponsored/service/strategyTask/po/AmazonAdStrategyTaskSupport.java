package com.meiyunji.sponsored.service.batchCreate.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-17  17:32
 */

@Data
@DbTable(value = "t_amazon_ad_strategy_task_support")
public class AmazonAdStrategyTaskSupport implements Serializable {

    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "last_operate_time")
    private Date lastOperateTime;

    @DbColumn(value = "create_time")
    private Date createTime;

    @DbColumn(value = "update_time")
    private Date updateTime;
}