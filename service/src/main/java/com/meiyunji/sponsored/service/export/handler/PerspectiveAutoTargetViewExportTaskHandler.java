package com.meiyunji.sponsored.service.export.handler;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.TargetingDataRequest;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.AutoTargetViewRequest;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.AutoTargetViewResponse;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.AutoTargetViewExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.CampaignViewExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.TargetViewExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.ICategoryTargetViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IViewManageService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_AUTO_TARGET_VIEW)
public class PerspectiveAutoTargetViewExportTaskHandler implements AdManagePageExportTaskHandler {

    /**
     * 透视接口
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getAutoTargetView(AutoTargetViewRequest, StreamObserver)
     * @see ViewManageServiceImpl#getAutoTargetView(Integer, TargetViewParam)
     * 广告管理下载
     * @see com.meiyunji.sponsored.api.export.AdExportRpcService#targetingData(TargetingDataRequest, StreamObserver)
     */

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IViewManageService viewManageService;


    @Override
    public void export(AdManagePageExportTask task) {
        TargetViewParam param = JSONUtil.jsonToObject(task.getParam(), TargetViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 商品投放视图列表页 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        AutoTargetViewResponse.TargetVo targetVo = viewManageService.getAutoTargetView(param.getPuid(), param);
        if (Objects.isNull(targetVo) || CollectionUtils.isEmpty(targetVo.getPage().getRowsList())) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();

        List<AutoTargetViewResponse.TargetVo.Page.TargetingPageVo> rowsList = targetVo.getPage().getRowsList();
        List<AutoTargetViewExcelVO> dataList = rowsList.stream().map(i -> new AutoTargetViewExcelVO(currency, i)).collect(Collectors.toList());

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), AutoTargetViewExcelVO.class,
                build.currencyNew(AutoTargetViewExcelVO.class), Collections.emptyList()));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));

    }

}
