package com.meiyunji.sponsored.service.export.vo.perspective;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeywordViewHourOfProductExcelVO {

    @ExcelProperty(value = "广告产品")
    private String asin;

    @ExcelProperty(value = "小时")
    private String label;

    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cost;

    @ExcelProperty(value = "广告曝光量")
    private Long impressions;

    @ExcelProperty(value = "广告点击量")
    private Long clicks;

    @ExcelProperty(value = "CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;

    @ExcelProperty(value = "CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpc;

    @ExcelProperty(value = "广告点击率")
    private String ctr;

    @ExcelProperty(value = "广告转化率")
    private String cvr;

    @ExcelProperty(value = "ACoS")
    private String acos;

    @ExcelProperty(value = "ROAS")
    private BigDecimal roas;

    @ExcelProperty(value = "广告订单量")
    private Integer adOrderNum;
    @ExcelProperty(value = "本广告产品订单量")
    private Integer selfAdOrderNum;
    @ExcelProperty(value = "其他产品广告订单量")
    private Integer otherAdOrderNum;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;
    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSelfSale;
    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSale;

    @ExcelProperty(value = "广告销量")
    private Integer adSaleNum;
    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;
    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;

    public KeywordViewHourOfProductExcelVO(String currency, AdKeywordAndTargetHourVo vo) {
        this.asin = vo.getAsin();
        this.label = vo.getLabel();
        this.cost = vo.getAdCost() == null ? currency + 0 : currency + vo.getAdCost().setScale(2, RoundingMode.HALF_UP);
        this.impressions = vo.getImpressions();
        this.clicks = vo.getClicks();
        this.cpa = vo.getCpa() == null ? currency + 0 : currency + vo.getCpa().setScale(2, RoundingMode.HALF_UP);
        this.cpc = vo.getAdCostPerClick() == null ? currency + 0 : currency + vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP);
        this.ctr = vo.getCtr() == null ? null : vo.getCtr().setScale(2, RoundingMode.HALF_UP) + "%";
        this.cvr = vo.getCvr() == null ? null : vo.getCvr().setScale(2, RoundingMode.HALF_UP) + "%";
        this.acos = vo.getAcos() == null ? null : vo.getAcos().setScale(2, RoundingMode.HALF_UP) + "%";
        this.roas = vo.getRoas() == null ? null : vo.getRoas().setScale(2, RoundingMode.HALF_UP);
        this.adOrderNum = vo.getAdOrderNum();
        this.selfAdOrderNum = vo.getSelfAdOrderNum();
        this.otherAdOrderNum = vo.getOtherAdOrderNum();
        this.adSale = vo.getAdSale() == null ? currency + 0 : currency + vo.getAdSale().setScale(2, RoundingMode.HALF_UP);
        this.adSelfSale = vo.getAdSelfSale() == null ? currency + 0 : currency + vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP);
        this.adOtherSale = vo.getAdOtherSale() == null ? currency + 0 : currency + vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP);
        this.adSaleNum = vo.getAdSaleNum();
        this.adSelfSaleNum = vo.getAdSelfSaleNum();
        this.adOtherSaleNum = vo.getAdOtherSaleNum();
    }

}
