package com.meiyunji.sponsored.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: ys
 * @date: 2024/8/20 21:58
 * @describe:
 */
@Getter
public enum SDCreateErrorEnum {
    KEYWORD_IS_INVALID("Keyword is invalid", "keyword", "%s：单个否定词最多由%s个单词组成"),
    KEYWORD_EXCEEDS_LENGTH_CONSTRAINTS("Keyword exceeds length constraints", "Keyword", "%s：单个否定词最多由%s个单词组成"),
    THIS_PRODUCT_IS_INELIGIBLE_FOR_ADVERTISING("This product is ineligible for advertising", "product", "此产品不符合广告推广条件"),
    NON_ARCHIVED_EXCEED_LIMIT("number of targeting clause (keyword) non archived exceed limit per ad agroup", "-", "未归档投放超出广告组投放数量上限"),
    NAME_ALREADY_EXISTS("An entity with that name already exists", "-", "名称已存在"),
    TARGETINGCLAUSE_IS_INVALID("Targetingclause (keyword) is invalid", "keyword", "无效关键词，请检查关键词格式"),
    KEYWORD_DID_NOT_SPECIFY("Keyword did not specify ad group id for create operation", "keyword", "网络延迟，请稍后重试"),
    CANNOT_FIND_ENTITY("Cannot find entity with id", "-", "网络延迟，请稍后重试"),
    PARENT_ENTITY_IS_ARCHIVED("Parent entity is archived", "-", "已归档设置不可操作"),
    TRY_AGAIN_LATER("Please try again later", "-", "网络延迟，请稍后重试"),
    START_DATE_PAST("Start date cannot be in the past", "开始日期", "开始日期不可设置为过去日期"),
    RATE_EXCEEDED_AUTHORIZATION("Rate exceeded for request authorization", "", "网络延迟，请稍后重试"),
    ENTITY_ID_DOES_NOT_EXIST("Entity ID does not exist", "-", "网络延迟，请稍后重试"),
    CAMPAIGN_TYPE_CANNOT_CHANGED("Campaign type cannot be changed", "广告活动", "广告活动名称已存在"),
    CAMPAIGN_TYPE_CANNOT_CHANGED_2("Campaign type cannot be changed Cannot change targeting type", "广告活动", "广告活动名称已存在"),
    ENTITY_NAME_ALREADY_EXIST("An entity with that name already exist", "广告对象", "广告对象已存在"),
    CAMPAIGN_NAME_INVALID("Campaign name invalid characters", "广告活动名称", "广告活动名称存在无效字符"),
    ARCHIVED_ENTITY_CANNOT_MODIFIED("Archived entity cannot be modified", "-", "已归档设置不可操作"),
    AD_GROUP_NAME_EXISTS("Ad group name already exists for campaign.", "广告组名称", "广告组名称已存在"),
    ALREADY_EXISTS("already exists", "-", "历史已存在相同的投放，请检查"),
    ONLY_NEGATIVE_ALLOWED("Only negative keywords and negative product targets are allowed in auto-targeting campaigns", "", "亚马逊不支持将品牌否定投放到自动投放的广告组"),

    //ID related errors
    CAMPAIGN_ID_INVALID("Invalid value for column: 'Campaign ID', which can't be found in any parent entity row", "campaign id", "广告活动id不存在"),
    GROUP_ID_INVALID("Invalid value for column: 'Ad Group ID', which can't be found in any parent entity row", "ad Group id", "广告组id不存在"),
    CAMPAIGN_ID_NON_NUMERIC("This Create operation requires you to specify a temporary 'Campaign ID' (any non-numeric text)", "campaign id", "广告活动id不能为数字"),
    GROUP_ID_NON_NUMERIC("This Create operation requires you to specify a temporary 'Ad Group ID' (any non-numeric text)", "ad Group id", "广告组id不能为数字"),
    UPDATE_CAMPAIGN_ID_NOT_EXISTS("This Update operation requires you to specify an actual 'Campaign ID', rather than a temporary ID", "campaign id", "广告活动id不存在"),
    PARENT_ENTITY_NOT_FOUND("Parent entity not found", "ad group id", "对应的广告组id不存在"),
    KEYWORD_ID_NOT_EXIST("Could not find keyword with id: OBJECT_ID", "keyword", "关键词id不存在"),

    //campaign create
    CAMPAIGN_NAME_EXISTS("campaign name already exists for campaign", "广告活动名称", "广告活动名称已存在"),
    CAMPAIGN_SAVE_FAIL("campaign save fail", "-", "广告活动保存失败"),
    START_DATA_BEFORE_TODAY("'startDate' should not be before today midnight.", "开始/结束日期", "开始时间不能早于今天"),
    CAMPAIGN_NOT_EXIST("campaign not exist", "-", "对应的广告活动不存在或已归档"),
    CAMPAIGN_NAME_HAVE_INVALID_CHARACTERS("Campaign name contains invalid characters.", "广告活动名称", "广告活动名称包含非法字符"),
    BUDGET_VALUE_REQUIRED("Budget value is required", "预算", "广告预算不能为空"),
    CAMPAIGN_SPECIFIED_NAME_ALREADY_EXISTS("Campaign with the specified name already exists.", "广告活动名称", "广告活动名称已存在"),
    //adGroup create
    CREATE_LIMIT_OLD_VERSION("create limit cause old version campaign", "-","该广告活动为旧版sb活动，只能添加一个广告组，请重新选择广告活动"),
    AD_GROUP_SAVE_FAIL("ad group save fail", "-", "广告组保存失败"),
    ADGROUP_NAME_INVALID_CHARACTERS("AdGroup name has invalid characters.", "广告组名称", "广告组名称包含特殊字符"),
    ADGROUP_DEFAULT_BIN_OVER_HALF_BUDGET("Bid must be less than half the value of your budget", "广告组默认竞价", "展示型广告的默认竞价必须少于预算金额的一半"),

    //ads create
    NOT_BEEN_AUTHORIZED("sellfox sponsored has not been authorized", "广告创意", "没有CPC授权"),
    NOT_HAVE_PROFILE("marketplace don't have config profile", "广告创意", "没有站点对应的配置信息"),
    ADS_LANDING_PAGE_FORMAT_ERROR("landing page format error", "广告创意", "广告格式着陆页配置异常"),
    ADS_CREATIVE_FORMAT_ERROR("creative format error", "广告创意", "广告创意配置异常"),
    ADS_NOT_EXIST("ads not exist", "-", "对应的广告创意不存在"),
    AD_NAME_INVALID_CHARACTER("AD_NAME_INVALID_CHARACTER", "广告创意名称", "广告创意名称包含非法字符"),
    AD_CREATIVE_BRAND_VIDEO_INVALID("AD_CREATIVE_BRAND_VIDEO_INVALID", "广告创意", "SBV广告创意创建失败"),
    AD_CREATIVE_STORE_SPOTLIGHT_INVALID("AD_CREATIVE_STORE_SPOTLIGHT_INVALID", "广告创意", "创建失败，请检查广告创意内容"),
    AD_CREATIVE_PRODUCT_COLLECTION_INVALID("AD_CREATIVE_PRODUCT_COLLECTION_INVALID", "广告创意", "创建失败，请检查广告创意内容"),
    AD_CREATIVE_VIDEO_INVALID("AD_CREATIVE_VIDEO_INVALID", "广告创意", "SBV广告创意创建失败"),
    GROUP_NOT_EXIST("ad group not exist", "-", "对应的广告组不存在"),
    LANDING_PAGE_NO_URL_NO_ASINS("LANDING_PAGE_NO_URL_NO_ASINS", "落地页", "落地页缺少URL或ASINS"),
    AD_CREATIVE_COLLECTION_CUSTOM_IMAGES_INVALID_SIZE("AD_CREATIVE_COLLECTION_CUSTOM_IMAGES_INVALID_SIZE", "广告创意-自定义图片", "自定义图片错误，请检查"),
    LANDING_PAGE_URL_ASINS_IS_INVALID("LANDING_PAGE_URL_ASINS_IS_INVALID", "落地页", "落地页URL或ASIN无效"),
    LANDING_PAGE_ASINS_COUNT_INVALID("LANDING_PAGE_ASINS_COUNT_INVALID", "落地页", "落地页asin数量不符合要求"),

    //product create
    PRODUCT_NOT_EXIST("ad product not exist", "-", "对应的广告产品不存在"),
    PRODUCT_CREATE_ERROR("ad product create error", "-", "广告产品创建失败"),

    //target targeting
    TARGETING_REQ_PARAM_IS_NULL("targeting request param is null", "-", "商品投放参数不能为空"),
    TARGETING_TYPE_IS_NULL("targeting request type is null", "-", "商品投放类型参数不能为空"),
    TARGETING_CONTENT_IS_NULL("targeting content is null", "-", "商品投放内容不能为空"),
    TARGETING_TYPE_ERROR("cpc.none.targeting", "投放", "投放类型异常"),


    //themes create
    THEME_TYPE_CREATE_LIMIT("every type of theme can be create in group only once", "", "设置主题投放异常，一个广告组只允许设置同一类型的主题投放只允许设置一个"),
    THEMES_INFO_ERROR("themes info error", "-", "主题投放参数异常"),

    //neKeyword create
    NEKEYWORD_INFO_ERROR("nekeyword info error", "关键词", "关键词对象不存在"),

    UNKNOWN_ERROR("unknown system error", "-", "未知错误"),

    //netargeting
    NETARGETING_REQ_PARAM_IS_NULL("neTargeting request param is null", "-", "否定商品投放参数不能为空"),
    NETARGETING_TYPE_IS_NULL("neTargeting request type is null", "-", "否定商品投放类型参数不能为空"),
    NETARGETING_CONTENT_IS_NULL("neTargeting content is null", "-", "否定商品投放内容不能为空"),

    //asset error
    SD_PORTRAIT_VIDEO_ASPECT_RATIO_ERROR("Aspect ratio is not one of", "Sponsored Display纵向视频", "分辨率不符合其中之一:{0}."),
    SD_VIDEO_AUDIO_BIT_ERROR("Audio bit rate is less than", "Sponsored Display视频", "视频中音频的比特率低于:{0} Kbps. 实际为:{1}"),
    SD_BRANDLOGO_SIZE_ERROR("Validation failed for BRANDLOGO image asset metadata or image properties: brandlogo width", "Sponsored Display视频", "品牌logo图片分辨率异常,实际为:{0} 低于:{1}. "),
    SD_CUSTOM_IMAGE_ASSET_ERROR("Validation failed for HORIZONTAL image asset metadata or image properties: horizontal width is less than horizontal image width over height does not satisfy aspect ratio", "Sponsored Display创意", "自定义图片宽度为{0}，小于要求{1}，且宽度为:{2}，高度为:{3},不满足图片比例{4}:{5}的要求。"),
    SD_CUSTOM_IMAGE_ASSET_ERROR2("Validation failed for HORIZONTAL image asset metadata or image properties: horizontal width", "Sponsored Display创意", "自定义图片宽度为{0}，小于{1}要求。宽度为{2}，高度为{3}，不满足图片比例{4}:{5}的要求。"),
    SD_CUSTOM_IMAGE_ASSET_ERROR3("Validation failed for HORIZONTAL image asset metadata or image properties: horizontal image width over height does not satisfy aspect ratio", "", "自定义图片宽度为{0}，高度为{1}，不满足图片比例{2}:{3}的要求。"),
    SD_CUSTOM_IMAGE_ASSET_ERROR4("Validation failed for HORIZONTAL image asset metadata or image properties: horizontal image width over height does not satisfy aspect ratio horizontal width is less than", "", "自定义图片宽度为{0}，高度为{1}，不满足图片比例{2}:{3}的要求。宽度为{4}，小于要求{5}"),
    ;

    private String msg;
    private String field;
    private String translatedCn;

    SDCreateErrorEnum(String msg, String field, String translatedCn) {
        this.msg = msg;
        this.field = field;
        this.translatedCn = translatedCn;
    }

    public static SDCreateErrorEnum getSDCreateErrorEnumByMsg(String msg) {
        for (SDCreateErrorEnum en : SDCreateErrorEnum.values()) {
            if (en.getMsg().equals(msg)) {
                return en;
            }
        }
        return null;
    }

    public static SDCreateErrorEnum getEnByMsgContains(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return null;
        }
        for (SDCreateErrorEnum en : SDCreateErrorEnum.values()) {
            if (msg.contains(en.getMsg())) {
                return en;
            }
        }
        return null;
    }

    public static SDCreateErrorEnum getEnByMsgNumberContains(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return null;
        }
        Pattern pattern = Pattern.compile("(([0-9]*):[0-9]*(\\.[0-9]{1,2}))(\\,*)|([0-9]*(\\.[0-9]{1,2}):[0-9]*)(\\,*)|([0-9]*(\\.[0-9]{1,2})(\\,*))|([0-9]*)(\\,*)");
        Matcher matcher = pattern.matcher(msg);
        String[] spltStr = msg.split(" ");
        List<String> list = new LinkedList<>(Arrays.asList(spltStr));
        while(matcher.find()) {
            if (StringUtils.isNotEmpty(matcher.group())) {
                list.remove(matcher.group());
            }
        }
        String compareStr = StringUtils.join(list, " ");
        for (SDCreateErrorEnum en : SDCreateErrorEnum.values()) {
            if (en.getMsg().equals(compareStr)) {
                return en;
            }
        }
        return null;
    }
}
