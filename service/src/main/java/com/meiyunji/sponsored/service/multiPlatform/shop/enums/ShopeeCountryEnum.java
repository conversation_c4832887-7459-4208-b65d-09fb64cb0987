package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

public enum ShopeeCountryEnum {
    MY("MY", "马来西亚"),
    ID("ID", "印尼"),
    TH("TH", "泰国"),
    PH("PH", "菲律宾"),
    SG("SG", "新加坡"),
    VN("VN", "越南"),
    BR("BR", "巴西"),
    MX("MX", "墨西哥"),
    CO("CO", "哥伦比亚"),
    CL("CL", "智利"),
    TW("TW", "中国台湾"),
    ;

    private String code;
    private String name;

    ShopeeCountryEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ShopeeCountryEnum getByCode(String code) {
        for (ShopeeCountryEnum value : ShopeeCountryEnum.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

}
