package com.meiyunji.sponsored.service.multiPlatform.walmart.dto;

/**
 * 取消订单产品参数类
 */
public class CancelProductDTO {
    /**
     *  产品id
     */
    private  String  productId;


    /**
     * 取消原因
     */
    private  String cancelReason;


    /**
     *  取消数量
     */
    private  Integer cancelNum;

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Integer getCancelNum() {
        return cancelNum;
    }

    public void setCancelNum(Integer cancelNum) {
        this.cancelNum = cancelNum;
    }
}
