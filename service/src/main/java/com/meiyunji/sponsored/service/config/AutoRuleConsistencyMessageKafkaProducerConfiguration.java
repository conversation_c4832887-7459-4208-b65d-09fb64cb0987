package com.meiyunji.sponsored.service.config;

import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.service.kafka.AutoRuleConsistencyMessageKafkaProducer;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@ConditionalOnProperty(name = "spring.kafka.autorule-consistency-message.enabled", havingValue = "true")
@Slf4j
public class AutoRuleConsistencyMessageKafkaProducerConfiguration {

    @Value("${spring.kafka.autorule-consistency-message.bootstrap-servers}")
    private String bootstrapServers;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    private ProducerFactory<String,  String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    @Bean
    public KafkaTemplate<String,  String> autoRuleConsistencyMessageKafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 10);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        int requestSize = dynamicRefreshNacosConfiguration.getAutoruleCallAadrasUseKafkaConsistencyMaxRequestSize() * 1024 * 1024;
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, requestSize);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
        return props;
    }




    @Bean(name = "autoRuleConsistencyMessageKafkaProducer")
    @ConditionalOnProperty(name = "kafka.producers.autorule-consistency-message.enabled", havingValue = "true")
    public AutoRuleConsistencyMessageKafkaProducer autoRuleConsistencyMessageKafkaProducer(
            KafkaProperties kafkaProperties, KafkaTemplate<String, String> autoRuleConsistencyMessageKafkaTemplate) {
        KafkaProperties.ProducerProperties producerProperty = kafkaProperties.getProducers().get("autorule-consistency-message");
        return new AutoRuleConsistencyMessageKafkaProducer(producerProperty.getTopic(), autoRuleConsistencyMessageKafkaTemplate);
    }
}
