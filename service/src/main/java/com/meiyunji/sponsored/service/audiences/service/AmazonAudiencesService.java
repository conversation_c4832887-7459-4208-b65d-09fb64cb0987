package com.meiyunji.sponsored.service.audiences.service;

import com.meiyunji.sponsored.service.audiences.entity.AmazonAdAudience;
import com.meiyunji.sponsored.service.enums.AudienceCategoryTypeEnum;

import java.util.List;

/**
 * AmazonAudiencesService
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20
 */
public interface AmazonAudiencesService {
    /**
     * 批量新增
     *
     * @param amazonAdAudiences
     */
    void batchAdd(List<AmazonAdAudience> amazonAdAudiences);

    /**
     * 批量更新
     *
     * @param amazonAdAudiences
     */
    void batchUpdate(List<AmazonAdAudience> amazonAdAudiences);

    /**
     * 查询全部
     *
     * @return
     */
    List<AmazonAdAudience> listAll();

    /**
     * 根据audienceId列表查询列表
     *
     * @return
     */
    List<AmazonAdAudience> listByAudienceIds(List<String> audienceIds);

    AmazonAdAudience getByAudienceName(String audienceName);

    AudienceCategoryTypeEnum getCategoryByAudienceName(String audienceName);
}
