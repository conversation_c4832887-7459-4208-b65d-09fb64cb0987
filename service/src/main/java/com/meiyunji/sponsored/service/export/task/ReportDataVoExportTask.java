package com.meiyunji.sponsored.service.export.task;

import com.meiyunji.sponsored.service.export.AdReportExportExecutorHelper;
import com.meiyunji.sponsored.service.export.HelperResult;
import com.meiyunji.sponsored.service.export.ThreadPoolDbPageQueryFunction;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.convert.ReportDataVoExportConvert;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.report.export.CommonReportExportRequest;
import com.meiyunji.sponsored.rpc.vo.ReportDataRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.base.AdReportBeanConvertProcess;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-08  10:38
 */
public class ReportDataVoExportTask implements Callable<HelperResult> {
    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    private Map<Integer, ShopAuth> shopMap;
    private CommonReportExportRequest request;
    private SearchVo searchVo;
    private int pageNo;
    private int pageSize;
    private ThreadPoolDbPageQueryFunction<Integer, SearchVo, Page<ReportDataVo>, Page<ReportDataVo>> f;

    private IExcelService excelService;

    private AdReportExportExecutorHelper exportExecutorHelper;

    private ReportDataVoExportConvert convert;

    private Class clazz;

    private Semaphore semaphore;

    public ReportDataVoExportTask(Map<Integer, ShopAuth> shopMap,
                                  CommonReportExportRequest request,
                                  SearchVo searchVo,
                                  int pageNo,
                                  int pageSize,
                                  ThreadPoolDbPageQueryFunction<Integer, SearchVo, Page<ReportDataVo>, Page<ReportDataVo>> f,
                                  IExcelService excelService,
                                  AdReportExportExecutorHelper exportExecutorHelper,
                                  ReportDataVoExportConvert convert,
                                  Class clazz,
                                  Semaphore semaphore) {
        this.shopMap = shopMap;
        this.request = request;
        this.searchVo = searchVo;
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.f = f;
        this.excelService = excelService;
        this.exportExecutorHelper = exportExecutorHelper;
        this.convert = convert;
        this.clazz = clazz;
        this.semaphore = semaphore;
    }

    @Override
    public HelperResult call() throws Exception {
        try {
            log.info("开始导出数据, reportId: {}, pageNo: {}", request.getReportId(), pageNo);
            StopWatch watch = new StopWatch();
            watch.start();
            //查询数据
            Page<ReportDataVo> page = new Page<>(pageNo, pageSize);
            Page<ReportDataVo> resPage = f.apply(request.getPuid().getValue(), searchVo, page);
            List<ReportDataVo> rows = resPage.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                watch.stop();
                log.info("查询无数据, reportId: {}, pageNo: {}, 耗时: {}秒", request.getReportId(), pageNo, (double) watch.getTotalTimeMillis() / 1000);
                return new HelperResult(null, 0, 0);
            }

            //数据处理
            //excel数据组装，设置excel表头和格式
            List list = new LinkedList<>();
            WriteHandlerBuild build = exportExecutorHelper.setWriteHandlerBuild(clazz);
            List<String> voExcludeFileds = exportExecutorHelper.setVoExcludeFileds(request.getTabType());
            if (StringUtils.equals(convert.getReportExportType().getReportType(), AdReportExportTypeEnum.SD_MATCHED.getReportType())) {
                //sd匹配的目标报告处理，直接使用数据库数据进行转换即可
                for (ReportDataVo reportDataVo : rows) {
                    convert.processExcelDataList(reportDataVo, list, shopMap, searchVo);
                }
            } else {
                //其他数据处理
                List<ReportDataRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    ReportDataRpcVo.Builder voBuilder = convert.convertVoToReportDataRpcVoMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                for (ReportDataRpcVo reportDataRpcVo : rpcVos) {
                    ReportDataVo vo = AdReportBeanConvertProcess.convertRpcVoToReportDataVo(reportDataRpcVo);
                    convert.processExcelDataList(vo, list, shopMap, searchVo);
                }
            }

            //数据写入excel并上传cos
            String downloadUrl = excelService.easyExcelHandlerDownload(request.getPuid().getValue(), list, request.getFileName() + "(" + pageNo + ")", request.getReportName() + "-" + pageNo, clazz, build, voExcludeFileds);
            watch.stop();
            log.info("导出数据成功, reportId: {}, pageNo: {}, 耗时: {}秒", request.getReportId(), pageNo, (double) watch.getTotalTimeMillis() / 1000);
            //返回下载链接
            return new HelperResult(downloadUrl, resPage.getTotalPage(), resPage.getTotalSize());
        } catch (Exception e) {
            log.error(String.format("报告下载中心，报告数据查询失败, reportId: %s, pageNo: %s", request.getReportId(), pageNo), e);
            return new HelperResult(null, 0, 0);
        } finally {
            if (semaphore != null) {
                semaphore.release();
                log.info("release semaphore success, reportId: {}, pageNo: {}", request.getReportId(), pageNo);
            }
        }
    }
}
