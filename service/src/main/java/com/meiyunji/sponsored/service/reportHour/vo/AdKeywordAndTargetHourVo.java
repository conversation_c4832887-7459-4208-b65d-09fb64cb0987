package com.meiyunji.sponsored.service.reportHour.vo;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.util.ReflectionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@SuperBuilder
@AllArgsConstructor
public class AdKeywordAndTargetHourVo extends AdAnalysisAndCompareVo{
    private String adId;
    private String asin;
    private String placement;
    private Integer placementOrder;
    //用户小时字段排序
    private Integer hour;
    //用户日,周,月字段排序
    private String date;
    private String label;
    /**
     * 广告花费/广告订单量
     */
    private BigDecimal cpa;
    private BigDecimal adCostPerClick;
    private BigDecimal adCostPerClickCompare = BigDecimal.ZERO;
    private BigDecimal adCostPerClickCompareRate = BigDecimal.ZERO;
    private BigDecimal ctr;
    private BigDecimal ctrCompare = BigDecimal.ZERO;
    private BigDecimal ctrCompareRate = BigDecimal.ZERO;
    private BigDecimal cvr;
    private BigDecimal cvrCompare = BigDecimal.ZERO;
    private BigDecimal cvrCompareRate = BigDecimal.ZERO;
    private BigDecimal acos;
    private BigDecimal acosCompare = BigDecimal.ZERO;
    private BigDecimal acosCompareRate = BigDecimal.ZERO;
    private BigDecimal roas;
    private BigDecimal roasCompare = BigDecimal.ZERO;
    private BigDecimal roasCompareRate = BigDecimal.ZERO;
    private BigDecimal acots;
    private BigDecimal asots;
    private Integer selfAdOrderNum;
    private Integer otherAdOrderNum;
    private BigDecimal adSelfSale;
    private BigDecimal adOtherSale;
    private Integer adSelfSaleNum;
    private Integer adOtherSaleNum;
    private Integer viewableImpressions;
    private BigDecimal vrt;
    private BigDecimal vCtr;
    private Integer ordersNewToBrand;
    private Integer unitsOrderedNewToBrand;
    private BigDecimal salesNewToBrand;
    private BigDecimal advertisingUnitPrice;
    private BigDecimal advertisingProductUnitPrice;
    private BigDecimal advertisingOtherProductUnitPrice;
    private BigDecimal ordersNewToBrandPercentage;
    private BigDecimal unitsOrderedNewToBrandPercentage;
    private BigDecimal salesNewToBrandPercentage;
    private BigDecimal vcpm;
    private BigDecimal adCostPercentage;
    private BigDecimal adSalePercentage;
    private BigDecimal adOrderNumPercentage;
    private BigDecimal orderNumPercentage;
    private String costType;
    private Integer weekDay;

    /**
     * vcpm 花费
     */
    private BigDecimal vcpmCost;
    /**
     * vcpm 展示
     */
    private Long vcpmImpressions;
    /**
     * 辅助字段
     */
    private Long totalImpressions;
    /**
     * 辅助字段
     */
    private Long totalClicks;
    /**
     * 统计非vcpm的本广告产品销售额
     */
    private BigDecimal totalAdSelfSale;
    /**
     * 统计非vcpm的广告销售额
     */
    private BigDecimal totalAdSale;


    private String bidAdjust;

    private BigDecimal bidAdjustMax;

    private String title;


    public AdKeywordAndTargetHourVo(){
        this.clicks = 0L ;
        this.acos = BigDecimal.ZERO;
        this.acosCompare = BigDecimal.ZERO;
        this.acosCompareRate = BigDecimal.ZERO;
        this.cpa = BigDecimal.ZERO;
        this.adCostPerClick = BigDecimal.ZERO;
        this.adCostPerClickCompare = BigDecimal.ZERO;
        this.adCostPerClickCompareRate = BigDecimal.ZERO;
        this.adCost = BigDecimal.ZERO;
        this.adCostCompare = BigDecimal.ZERO;
        this.adCostCompareRate = BigDecimal.ZERO;
        this.impressions = 0L;
        this.impressionsCompare = 0L;
        this.impressionsCompareRate = BigDecimal.ZERO;
        this.clicksCompare = 0L;
        this.clicksCompareRate = BigDecimal.ZERO;
        this.ctr = BigDecimal.ZERO;
        this.ctrCompare = BigDecimal.ZERO;
        this.ctrCompareRate = BigDecimal.ZERO;
        this.cvr = BigDecimal.ZERO;
        this.cvrCompare = BigDecimal.ZERO;
        this.cvrCompareRate = BigDecimal.ZERO;
        this.roas = BigDecimal.ZERO;
        this.roasCompare = BigDecimal.ZERO;
        this.roasCompareRate = BigDecimal.ZERO;
        this.acots = BigDecimal.ZERO;
        this.asots = BigDecimal.ZERO;
        this.adOrderNum = 0;
        this.adOrderNumCompare = 0;
        this.adOrderNumCompareRate = BigDecimal.ZERO;
        this.selfAdOrderNum = 0;
        this.otherAdOrderNum = 0;
        this.adSale = BigDecimal.ZERO;
        this.adSaleCompare = BigDecimal.ZERO;
        this.adSaleCompareRate = BigDecimal.ZERO;
        this.adSelfSale = BigDecimal.ZERO;
        this.adOtherSale = BigDecimal.ZERO;
        this.adSaleNum = 0;
        this.adSaleNumCompare = 0;
        this.adSaleNumCompareRate = BigDecimal.ZERO;
        this.adSelfSaleNum = 0;
        this.adOtherSaleNum = 0;
        this.viewableImpressions = 0;
        this.vrt = BigDecimal.ZERO;
        this.vCtr = BigDecimal.ZERO;
        this.ordersNewToBrand = 0;
        this.unitsOrderedNewToBrand = 0;
        this.salesNewToBrand = BigDecimal.ZERO;
        this.advertisingUnitPrice = BigDecimal.ZERO;
        this.advertisingProductUnitPrice = BigDecimal.ZERO;
        this.advertisingOtherProductUnitPrice = BigDecimal.ZERO;
        this.ordersNewToBrandPercentage = BigDecimal.ZERO;
        this.unitsOrderedNewToBrandPercentage = BigDecimal.ZERO;
        this.salesNewToBrandPercentage = BigDecimal.ZERO;
        this.vcpm = BigDecimal.ZERO;
        this.adCostPercentage = BigDecimal.ZERO;
        this.adSalePercentage = BigDecimal.ZERO;
        this.adOrderNumPercentage = BigDecimal.ZERO;
        this.orderNumPercentage = BigDecimal.ZERO;
    }

    public void calculateCompareRate() {
        super.afterPropertiesSet();
        this.adCostPerClickCompareRate = MathUtil.calculateCompareRate(this.adCostPerClick, this.adCostPerClickCompare);
        this.ctrCompareRate = MathUtil.calculateCompareRate(this.ctr, this.ctrCompare);
        this.cvrCompareRate = MathUtil.calculateCompareRate(this.cvr, this.cvrCompare);
        this.acosCompareRate = MathUtil.calculateCompareRate(this.acos, this.acosCompare);
        this.roasCompareRate = MathUtil.calculateCompareRate(this.roas, this.roasCompare);
    }

    public void compareDataSet(AdKeywordAndTargetHourVo compare) {
        if (Objects.isNull(compare)) {
            ReflectionUtil.setCompareNull(this);
            return;
        }
        super.compareDataSet(compare);
        this.adCostPerClickCompare = compare.getAdCostPerClick();
        this.acosCompare = compare.getAcos();
        this.roasCompare = compare.getRoas();
        this.cvrCompare = compare.getCvr();
        this.ctrCompare = compare.getCtr();
        calculateCompareRate();
    }

}
