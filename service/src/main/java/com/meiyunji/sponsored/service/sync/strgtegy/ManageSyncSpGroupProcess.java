package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
@Slf4j
public class ManageSyncSpGroupProcess extends AbstractSyncServerStatusProcessStrategy {


    @Resource
    private CpcAdGroupApiService cpcAdGroupApiService;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageSyncSpGroupProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration, redisService);

    }


    @Override
    public int getMaxCount() {
        return StreamConstants.SP_MAX_GROUP_IDS_COUNT;
    }

    @SneakyThrows
    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        Boolean syncManageProxy = dynamicRefreshConfiguration.getSyncManageProxy();
        cpcAdGroupApiService.syncAdGroups(shopAuth, null, ids, null, true, null, Boolean.TRUE.equals(syncManageProxy));

    }
}
