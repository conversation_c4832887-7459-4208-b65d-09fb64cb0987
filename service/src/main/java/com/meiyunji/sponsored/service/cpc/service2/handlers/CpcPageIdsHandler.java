package com.meiyunji.sponsored.service.cpc.service2.handlers;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcAggregateIdsTemporaryDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignAndReportSearchDTO;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregateIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregatePlacementIdsTemporary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.elasticsearch.index.seqno.RetentionLeaseActions;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/10/20 10:43
 * @describe: 列表页面id处理类，负责将聚合数据存入缓存或者db
 */

@Component
@Slf4j
public class CpcPageIdsHandler {

    @Resource
    private ICpcAggregateIdsTemporaryDao cpcAggregateIdsTemporaryDaoImpl;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDaoImpl;

    private static final int MAX_FAIL_CNT = 3;

    private static final long EXPIRE_TIME = 60L; //一分钟

    public void addIdsTemporarySync(Integer puid, Collection<String> coll,
                                    String pageSign, String searchParamStr) {
        String md5Str = Optional.ofNullable(searchParamStr).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String searchMD5Str = getPageIdsKeyBySign(pageSign, md5Str);
        //去重
        Set<String> set = new HashSet<>(coll);
        ExecutorService threadPool = ThreadPoolUtil.getCpcAggregateIdsSyncPool();
        threadPool.execute(() -> setCampaignIdsTemporary(puid, set, searchMD5Str));//异步线程中失败了怎么办？
    }

    private void setCampaignIdsTemporary(Integer puid, Set<String> coll,
                                         String pageSignKey) {
        if(StringUtils.isBlank(pageSignKey)){
            log.info("insert pageSignKey is empty, pageSign: {}", pageSignKey);
            return;
        }
        String ids = JSONObject.toJSONString(coll);
        try {
            cpcAggregateIdsTemporaryDaoImpl.setIdsTemporary(pageSignKey, ids, "");
        } catch (Exception e) {
            log.info("insert pageSign relate ids to db error", e);
        }
    }

    public void setTemporaryJsonData(Integer puid, String coll,
                                         String pageSignKey) {
        if(StringUtils.isBlank(pageSignKey)){
            log.info("insert pageSignKey is empty, pageSign: {}", pageSignKey);
            return;
        }
        try {
            cpcAggregateIdsTemporaryDaoImpl.setIdsTemporary(pageSignKey, coll, "");
        } catch (Exception e) {
            log.info("insert pageSign relate ids to db error", e);
        }
    }

    public String getTemporaryJsonData(String pageSign, String searchMD5Str) {
        searchMD5Str = Optional.ofNullable(searchMD5Str).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String key = getPageIdsKeyBySign(pageSign, searchMD5Str);
        if (StringUtil.isEmpty(key)) {
            return "no such TemporaryJsonData";
        }
        return cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(key);
    }

    public <T> List<String> getCampaignIdsTemporary(Integer puid, String pageSign,
                                                    String searchMD5Str, Integer shopId,
                                                    T[] queryParam) {
        CampaignAndReportSearchDTO queryDTO = new CampaignAndReportSearchDTO();
        for(T param : queryParam){
            BeanUtils.copyProperties(param, queryDTO);
        }
        queryDTO.setShopId(shopId);
        return getCampaignIdsTemporary(puid, pageSign, searchMD5Str, queryDTO);
    }

    public List<String> getCampaignIdsTemporary(Integer puid, String pageSign,
                                                String searchMD5Str, CampaignAndReportSearchDTO searchDTO) {
        String key = getPageIdsKeyBySign(pageSign, searchMD5Str);
        if (StringUtil.isEmpty(key)) {
            return Collections.EMPTY_LIST;
        }
        String idsTemporary = cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(pageSign);
        List<String> result = new ArrayList<>();
        if (StringUtils.isEmpty(idsTemporary)) {
            //当没有暂存数据时只有可能是数据还在插入的过程中，因为即使是业务数据为空，同样也会插入空数据至暂存表
            //还有就是插入暂存数据时失败，需要重新让用户刷新页面或者同步查一遍，那么需要维护用户获取暂存数据失败次数，当达到一定次数后，同步查询业务id
            //并暂存至暂存表
            log.error("do not get campaignIds temporary, pageSign: {}", pageSign);
            Integer failCnt = getIdsTemporaryFailCnt(getRedisKey(puid, pageSign));
            if (Objects.nonNull(failCnt) && failCnt > MAX_FAIL_CNT) {
                //check request param
                if (Objects.isNull(searchDTO) ||
                        StringUtils.isEmpty(searchDTO.getStartDate()) || StringUtils.isEmpty(searchDTO.getEndDate()))  {
                    return Collections.EMPTY_LIST;

                }
                //check query param by annotation
                log.info("begin insert campaignIds to temporary, pageSign:{}, time:{} /ms", pageSign, System.currentTimeMillis());
                //query all id to db sync
                try {
                    List<AdHomePerformancedto> queryList = amazonAdCampaignAllReportDaoImpl.getReportBySearchParam(puid, searchDTO);
                    if (CollectionUtils.isNotEmpty(queryList)) {
                        List<String> idList = queryList.parallelStream().
                                map(AdHomePerformancedto::getCampaignId).collect(Collectors.toList());
                        String idsStr = JSONObject.toJSONString(idList);
                        cpcAggregateIdsTemporaryDaoImpl.setIdsTemporary(key, idsStr, "");
                        log.info("finish insert campaignIds to temporary, pageSign:{}, time:{}  /ms", pageSign, System.currentTimeMillis());
                    }
                } catch (Exception e) {
                    log.error("sync query campaignIds to temporary is error", e);
                }
            } else {
                setIdsTemporaryFailCnt(getRedisKey(puid, pageSign));
            }
        }

        try {
            result = JSONObject.parseArray(idsTemporary, String.class);
        } catch (Exception e) {
            log.error("get campaignIds is error", e);
        }

        return result;
    }

    public AggregateIdsTemporary getAggregateIdsTemporary(String pageSign, String searchMD5Str) {
        searchMD5Str = Optional.ofNullable(searchMD5Str).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String key = getPageIdsKeyBySign(pageSign, searchMD5Str);
        if (StringUtil.isEmpty(key)) {
            return new AggregateIdsTemporary();
        }
        String json = cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(key);
        AggregateIdsTemporary aggregateIdsTemporary = new AggregateIdsTemporary();
        try {
            aggregateIdsTemporary = JSONObject.parseObject(json, AggregateIdsTemporary.class);
        } catch (Exception e) {
            log.error("get aggregateIdsTemporary is error", e);
        }
        return aggregateIdsTemporary;
    }

    public AggregatePlacementIdsTemporary getAggregatePlacementIdsTemporary(String pageSign, String searchMD5Str) {
        searchMD5Str = Optional.ofNullable(searchMD5Str).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String key = getPageIdsKeyBySign(pageSign, searchMD5Str);
        if (StringUtil.isEmpty(key)) {
            return new AggregatePlacementIdsTemporary();
        }
        String json = cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(key);
        AggregatePlacementIdsTemporary aggregateIdsTemporary = new AggregatePlacementIdsTemporary();
        try {
            aggregateIdsTemporary = JSONObject.parseObject(json, AggregatePlacementIdsTemporary.class);
        } catch (Exception e) {
            log.error("get AggregatePlacementIdsTemporary is error", e);
        }
        return aggregateIdsTemporary;
    }

    public void addIdsTemporarySynchronize(Integer puid, Collection<String> coll, String pageSign, String searchParamStr) {
        if (Objects.isNull(coll)) {
            return;
        }
        String md5Str = Optional.ofNullable(searchParamStr).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String searchMD5Str = getPageIdsKeyBySign(pageSign, md5Str);
        setCampaignIdsTemporary(puid, new HashSet<>(coll), searchMD5Str);
    }
    public List<String> getTemporaryAggregateIds(String pageSign, String searchMD5Str) {
        searchMD5Str = Optional.ofNullable(searchMD5Str).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String key = getPageIdsKeyBySign(pageSign, searchMD5Str);
        if (StringUtil.isEmpty(key)) {
            return Collections.emptyList();
        }
        List<String> ids = new ArrayList<>();
        try {
            ids =  JSONObject.parseArray(cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(key), String.class);
        } catch (Exception e) {
            log.error("get aggregateIdsTemporary is error", e);
        }
        return ids;
    }

    public void addAggregatePlacementIdsTemporarySynchronize(AggregatePlacementIdsTemporary aggregateIdsTemporary, String pageSign, String searchParamStr) {
        String md5Str = Optional.ofNullable(searchParamStr).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String pageSignKey = getPageIdsKeyBySign(pageSign, md5Str);
        if (StringUtils.isBlank(pageSignKey)) {
            log.info("insert pageSignKey is empty, pageSignKey: {}", pageSignKey);
            return;
        }
        String objectJson = JSONObject.toJSONString(aggregateIdsTemporary);
        try {
            cpcAggregateIdsTemporaryDaoImpl.setIdsTemporary(pageSignKey, objectJson, "");
        } catch (Exception e) {
            log.info("insert pageSign relate objects to db error", e);
        }
    }

    public void addAggregateIdsTemporarySynchronize(AggregateIdsTemporary aggregateIdsTemporary, String pageSign, String searchParamStr) {
        String md5Str = Optional.ofNullable(searchParamStr).filter(StringUtils::isNotBlank).map(MD5Util::getMD5).orElse("");
        String pageSignKey = getPageIdsKeyBySign(pageSign, md5Str);
        if (StringUtils.isBlank(pageSignKey)) {
            log.info("insert pageSignKey is empty, pageSignKey: {}", pageSignKey);
            return;
        }
        String objectJson = JSONObject.toJSONString(aggregateIdsTemporary);
        try {
            cpcAggregateIdsTemporaryDaoImpl.setIdsTemporary(pageSignKey, objectJson, "");
        } catch (Exception e) {
            log.info("insert pageSign relate objects to db error", e);
        }
    }

    private String getPageIdsKeyBySign(String sign, String searchMD5Str) {
        return sign + searchMD5Str;
    }

    private String getRedisKey(Integer puid, String pageKeyBySign) {
        return "pageSignKey:" + puid + ":" + pageKeyBySign;
    }

    private Integer getIdsTemporaryFailCnt(String redisKey) {
        BoundValueOperations<String, String> valBound = stringRedisTemplate.boundValueOps(redisKey);
        return Optional.ofNullable(valBound.get()).map(Integer::valueOf).orElse(0);
    }

    private void setIdsTemporaryFailCnt(String redisKey) {
        //当数据量大于某个阈值时，需要考虑将数据存入db中
        BoundValueOperations<String, String> valBound = stringRedisTemplate.boundValueOps(redisKey);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
            valBound.increment();
            valBound.expire(EXPIRE_TIME, TimeUnit.SECONDS);
        } else {
            valBound.increment();
        }
    }


    /**
     * 清理一天前的数据
     */
    public void deleteAggregateIdsTemporary() {

        Long maxId = cpcAggregateIdsTemporaryDaoImpl.getMaxLongId(LocalDateTime.now().plusDays(-1L).withHour(23).withMinute(59).withSecond(59));
        if (maxId == null || maxId < 1) {
            return;
        }
        cpcAggregateIdsTemporaryDaoImpl.deleteAggregateIdsTemporary(maxId);
    }

    public boolean getTemporaryIdsReady(String pageSign, String searchMD5Str) {
        if (StringUtils.isEmpty(pageSign)) return false;
        String idsTemporary = cpcAggregateIdsTemporaryDaoImpl.getIdsTemporary(pageSign);
        return StringUtils.isNotEmpty(idsTemporary);
    }
}
