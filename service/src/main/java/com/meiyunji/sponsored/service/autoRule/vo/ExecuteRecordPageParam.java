package com.meiyunji.sponsored.service.autoRule.vo;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ExecuteRecordPageParam {
    private Integer puid;
    private List<Integer> shopIdList;
    private String itemSearchValue;
    private String templateSearchValue;
    private LocalDate startDate;
    private LocalDate endDate;
    private Integer pageNo;
    private Integer pageSize;
    private List<String> codes;
    private List<String> ruleTypes;
    private List<String> portfolioIdList;
    private List<String> campaignIdList;
    private List<String> adGroupIdList;
    private List<String> itemTypeList;
    private String traceId;
    private List<String> adTypeList;
}
