package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-20  22:44
 */
@Data
@DbTable(value = "ods_t_amazon_ad_word_root_keyword_sp")
public class OdsAmazonAdWordRootKeywordSp extends BasePo {
    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "keyword_id")
    private String keywordId;

    @DbColumn(value = "keyword_text")
    private String keywordText;

    @DbColumn(value = "word_root")
    private String wordRoot;

    @DbColumn(value = "word_frequency_type")
    private Integer wordFrequencyType;

    @DbColumn(value = "word_root_cn")
    private String wordRootCn;
}
