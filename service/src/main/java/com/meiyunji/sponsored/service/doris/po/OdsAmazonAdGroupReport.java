package com.meiyunji.sponsored.service.doris.po;

import java.util.Date;
import java.io.Serializable;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

/**
 * amazon广告组报告表(OdsAmazonAdGroupReport)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:17
 */
@Data
@DbTable("ods_t_amazon_ad_group_report")
public class OdsAmazonAdGroupReport  implements Serializable {
	/**
     * 商户uid
     */    
	@DbColumn(value = "puid")
    private Integer puid;

	/**
     * 店铺ID
     */    
	@DbColumn(value = "shop_id")
    private Integer shopId;

	/**
     * 站点
     */    
	@DbColumn(value = "marketplace_id")
    private String marketplaceId;

	/**
     * 活动id
     */    
	@DbColumn(value = "campaign_id")
    private String campaignId;

	/**
     * 广告组id
     */    
	@DbColumn(value = "ad_group_id")
    private String adGroupId;

	/**
     * 统计日期,date格式,yyyyMMdd
     */    
	@DbColumn(value = "count_day")
    private Date countDay;

	/**
     * 统计日期,月
     */    
	@DbColumn(value = "count_month")
    private Integer countMonth;

	/**
     * 统计日期
     */    
	@DbColumn(value = "count_date")
    private String countDate;

	/**
     * 广告组名称
     */    
	@DbColumn(value = "ad_group_name")
    private String adGroupName;

	/**
     * 活动名称
     */    
	@DbColumn(value = "campaign_name")
    private String campaignName;

	/**
     * 花费
     */    
	@DbColumn(value = "cost")
    private Double cost;

	/**
     * 花费人民币
     */    
	@DbColumn(value = "cost_rmb")
    private Double costRmb;

	/**
     * 花费美元
     */    
	@DbColumn(value = "cost_usd")
    private Double costUsd;

	/**
     * 广告销售额
     */    
	@DbColumn(value = "total_sales")
    private Double totalSales;

	/**
     * 广告销售额人民币
     */    
	@DbColumn(value = "total_sales_rmb")
    private Double totalSalesRmb;

	/**
     * 广告销售额美元
     */    
	@DbColumn(value = "total_sales_usd")
    private Double totalSalesUsd;

	/**
     * 本广告产品销售额
     */    
	@DbColumn(value = "ad_sales")
    private Double adSales;

	/**
     * 本广告产品销售额人民币
     */    
	@DbColumn(value = "ad_sales_rmb")
    private Double adSalesRmb;

	/**
     * 本广告产品销售额美元
     */    
	@DbColumn(value = "ad_sales_usd")
    private Double adSalesUsd;

	/**
     * 曝光量
     */    
	@DbColumn(value = "impressions")
    private Integer impressions;

	/**
     * 点击量
     */    
	@DbColumn(value = "clicks")
    private Integer clicks;

	/**
     * 广告销量
     */    
	@DbColumn(value = "order_num")
    private Integer orderNum;

	/**
     * 本广告产品销量
     */    
	@DbColumn(value = "ad_order_num")
    private Integer adOrderNum;

	/**
     * 广告订单量
     */    
	@DbColumn(value = "sale_num")
    private Integer saleNum;

	/**
     * 本广告产品订单量
     */    
	@DbColumn(value = "ad_sale_num")
    private Integer adSaleNum;

	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

}

