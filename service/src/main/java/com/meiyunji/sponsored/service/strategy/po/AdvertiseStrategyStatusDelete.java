package com.meiyunji.sponsored.service.strategy.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 广告策略状态表
 */
@Data
@DbTable(value = "t_advertise_strategy_status_delete")
public class AdvertiseStrategyStatusDelete implements Serializable {

    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点ID
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 广告类型:SP,SB，SD
     */
    @DbColumn(value = "ad_type")
    private String adType;


    @DbColumn(value = "task_id")
    private Long taskId;

    /**
     * 对象类型：CAMPAIGN->活动 CAMPAIGN_PLACEMENT->广告位，TARGET-> 投放
     */
    @DbColumn(value = "item_type")
    private String itemType;

    /**
     * 周期类型: DAILY->每日，WEEKLY->每周
     */
    @DbColumn(value = "type")
    private String type;

    /**
     * 投放类型(autoTarget:自动投放,keywordTarget:关键词投放,productTarget:商品投放,audienceTarget:受众投放)
     */
    @DbColumn(value = "target_type")
    private String targetType;


    /**
     * 投放名称
     */
    @DbColumn(value = "target_name")
    private String targetName;

    /**
     * 操作对象ID
     */
    @DbColumn(value = "item_id")
    private String itemId;

    /**
     * 操作对象名称
     */
    private String itemName;

    /**
     * 原始值[json对象]
     */
    @DbColumn(value = "origin_value")
    private String originValue;

    /**
     * 还原值
     */
    @DbColumn(value = "return_value")
    private String returnValue;

    /**
     * 模板ID
     */
    @DbColumn(value = "template_id")
    private Long templateId;

    /**
     * 规则(json数组)
     */
    @DbColumn(value = "rule")
    private String rule;

    /**
     * 版本号
     */
    @DbColumn(value = "version")
    private Integer version;

    /**
     * 状态: 可用，不可用
     */
    @DbColumn(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_at")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @DbColumn(value = "last_update_at")
    private LocalDateTime lastUpdateAt;


    @DbColumn(value = "campaign_id")
    private String campaignId;

    @DbColumn(value = "ad_group_id")
    private String adGroupId;

    /**
     * 预算模板子类型
     */
    @DbColumn(value = "children_item_type")
    private String childrenItemType;

    /**
     * 启停对象类型
     */
    @DbColumn(value = "start_stop_item_type")
    private String startStopItemType;

    /**
     * asin
     */
    @DbColumn(value = "asin")
    private String asin;

    /**
     * sku
     */
    @DbColumn(value = "sku")
    private String sku;

    //前端展示
    private String campaignName;
    //前端展示
    private String groupName;

    //每日预算
    private BigDecimal budgetValue;
    //竞价
    private BigDecimal biddingValue;
    //广告位
    private BigDecimal adPlaceTopValue;
    //首页 数值
    private BigDecimal adPlaceProductValue;
    //产品页面 数值
    private String strategy; //竞价策略
    private String updateStatus; //受控对象启停状态
    private String state; //受控对象状态
    private String portfolioId;
    private String portfolioName;
    private String adTargetType;
    private String matchType;//匹配类型
    private String matchName;//匹配类型名称
    private BigDecimal amount;
    private String policy;
    private String budgetStartDate;
    private String budgetEndDate;
    private String servingStatus;
    private String servingStatusName;

    public enum StrategyStatus {
        ENABLE,
        DISABLE
    }

}