package com.meiyunji.sponsored.service.log.dao.impl;

import com.baomidou.mybatisplus.core.mapper.Mapper;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.service.log.dao.IAdvertisingOperationLogDao;
import com.meiyunji.sponsored.service.log.po.AdvertisingOperationLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * AdvertisingOperationLogDaoImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 */
@Repository
@Slf4j
public class AdvertisingOperationLogDaoImpl extends BaseShardingDaoImpl<AdvertisingOperationLog> implements IAdvertisingOperationLogDao {

    @Override
    public int batchInsert(int puid, List<AdvertisingOperationLog> list) {
        StringBuilder sql = new StringBuilder()
                .append(" insert into ")
                .append(this.getJdbcHelper().getTable())
                .append(" (`puid`, `from`, `content`, `create_time`) values (?, ?, ?, now()) ");
        List<Object[]> args = list.stream().map(e -> new Object[]{e.getPuid(), e.getFrom(), e.getContent()}).collect(Collectors.toList());
        return Arrays.stream(getJdbcTemplate(puid).batchUpdate(sql.toString(), args)).sum();
    }

    @Override
    public int deleteByIds(JdbcTemplate jdbcTemplate, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)
                || CollectionUtils.isEmpty(ids = ids.stream().filter(Objects::nonNull).collect(Collectors.toList()))) {
            return 0;
        }

        StringBuilder sql = new StringBuilder()
                .append(" delete from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where ");
        sql.append(ids.stream().map(i -> "?").collect(Collectors.joining(", ", " id in (", ")")));
        List<Object> args = Lists.newArrayList(ids);
        return jdbcTemplate.update(sql.toString(), args.toArray());
    }

    @Override
    public int deleteByCreateTime(JdbcTemplate jdbcTemplate, LocalDateTime endDate, int limit) {
        StringBuilder sql = new StringBuilder()
                .append(" delete from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where `create_time` < ? limit ?");
        Object[] args = {endDate, limit};
        return jdbcTemplate.update(sql.toString(), args);
    }

    @Override
    public List<AdvertisingOperationLog> listPage(JdbcTemplate jdbcTemplate, int pageNo, int pageSize) {
        StringBuilder sql = new StringBuilder()
                .append(" SELECT `id`, `puid`, `from`, `content`, `create_time` FROM ")
                .append(this.getJdbcHelper().getTable())
                .append(" limit ?, ?");
        Object[] args = {(pageNo - 1) * pageSize, pageSize};

        return jdbcTemplate.query(sql.toString(), (rs, i) ->
                AdvertisingOperationLog.builder()
                        .id(rs.getLong("id"))
                        .puid(rs.getInt("puid"))
                        .from(rs.getString("from"))
                        .content(rs.getString("content"))
                        .createTime(rs.getTimestamp("create_time").toLocalDateTime())
                        .build(), args);
    }

    @Override
    public long countAll(JdbcTemplate jdbcTemplate, LocalDateTime endDate) {
        StringBuilder sql = new StringBuilder()
                .append(" SELECT count(*) FROM ")
                .append(this.getJdbcHelper().getTable());
        List<Object> args = Lists.newArrayList();
        if (endDate != null){
            sql.append(" where `create_time` < ? ");
            args.add(endDate);
        }
        try {
            return jdbcTemplate.queryForObject(sql.toString(), Long.class, args.toArray());
        } catch (Exception e) {
            return 0;
        }
    }
}
