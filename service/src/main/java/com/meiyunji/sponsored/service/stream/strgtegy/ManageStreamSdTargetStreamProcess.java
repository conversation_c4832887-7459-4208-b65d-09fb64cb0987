package com.meiyunji.sponsored.service.stream.strgtegy;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageStreamSdTargetStreamProcess extends AbstractManageStreamRetryProcessStrategy {


    @Resource
    private CpcSdTargetingApiService cpcSdTargetingApiService;


    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageStreamSdTargetStreamProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration);
    }


    @Override
    public int getMaxCount() {
        return StreamConstants.SD_MAX_TARGET_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcSdTargetingApiService.syncTargetings(shopAuth, null, null, ids, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
    }
}
