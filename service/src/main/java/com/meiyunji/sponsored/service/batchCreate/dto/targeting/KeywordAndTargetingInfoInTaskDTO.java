package com.meiyunji.sponsored.service.batchCreate.dto.targeting;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2023/11/23 19:22
 * @describe: 批量新建SP-产品视图投放tab页列表DTO
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KeywordAndTargetingInfoInTaskDTO {
    private String marketplaceId;
    private String campaignId;
    private Long id;
    private String name;
    private String type;
    private Long groupId;
    private String manualType;
    private String title;
    private String matchType;
    private String imgUrl;
    private String brand;
    private String taskStatus;
    private String errMsg;
    private String categoryName;
}
