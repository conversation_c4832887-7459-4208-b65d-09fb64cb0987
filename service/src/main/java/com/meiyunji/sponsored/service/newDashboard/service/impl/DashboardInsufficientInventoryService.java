package com.meiyunji.sponsored.service.newDashboard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductSdDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonSbAdsDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonSbAds;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardInsufficientInventoryDataDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardInsufficientInventoryService;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardInsufficientInventoryReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinCampaignNumDto;
import com.meiyunji.sponsored.service.sellfoxApi.IFbaInventoryManageApi;
import com.meiyunji.sponsored.service.sellfoxApi.qo.FbaReplenishmentApiQo;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaInTransitApiVO;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaReplenishmentApiVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存不足
 *
 * @author: zzh
 * @date: 2025-03-12  10:42
 */
@Service
@Slf4j
public class DashboardInsufficientInventoryService implements IDashboardInsufficientInventoryService {

    @Resource
    private IFbaInventoryManageApi fbaInventoryManageApi;

    @Resource
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;

    @Resource
    private IOdsAmazonAdProductSdDao odsAmazonAdProductSdDao;

    @Resource
    private IOdsAmazonSbAdsDao odsAmazonSbAdsDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IExcelService excelService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;

    private static final List<String> baseHeaderList = Arrays.asList(
            "asin","mskus", "marketplaceName", "shopName", "campaignNum",
            "inStockInTransitSellableDays", "inStockSellableDays", "sellableDays",
            "outOfStockAfterArrival", "fbaStock", "fbaInTransit", "dailySale", "saleYesterday", "saleDay3",
            "saleDay7", "saleDay14", "saleDay30", "saleDay60", "saleDay90");


    @Override
    public List<DashboardInsufficientInventoryDataDto> queryInsufficientInventoryData(DashboardInsufficientInventoryReqVo reqVo) {
        // 调用fba接口获取库存不足信息
        List<FbaReplenishmentApiVO> fbaReplenishmentApiVOS = null;
        boolean isNeedQuery = true;
        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(reqVo.getShopIdList());
            reqVo.setShopIdList(Lists.newArrayList((reqVo.getShopIdList())));
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                reqVo.getShopIdList().removeAll(listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
                log.info("剔除vc店铺后无数据puid:{}", reqVo.getPuid());
                isNeedQuery = false;
            }
        }
        if (isNeedQuery) {
            FbaReplenishmentApiQo qo = BeanUtil.copyProperties(reqVo, FbaReplenishmentApiQo.class);
            qo.setShopId(reqVo.getShopIdList());
            qo.setMarketplaceId(reqVo.getMarketplaceIdList());
            fbaReplenishmentApiVOS = fbaInventoryManageApi.getAdReplenishList(qo);
        }
        fbaReplenishmentApiVOS = fbaReplenishmentApiVOS == null ? new ArrayList<>() : fbaReplenishmentApiVOS;
        // 根据店铺+asin 统计存在的广告活动数量
        Map<String, Integer> asinCampaignNumMap = getAsinCampaignNumMap(reqVo, fbaReplenishmentApiVOS);
        // 获取店铺详情
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(reqVo.getPuid(), reqVo.getShopIdList());
        Map<Integer, ShopAuth> shopMap = StreamUtil.toMap(shopAuths, ShopAuth::getId);
        List<DashboardInsufficientInventoryDataDto> inventoryDataDtos = new ArrayList<>();
        for (FbaReplenishmentApiVO fbaReplenishmentApiVO : fbaReplenishmentApiVOS) {
            // 构建参数
            buildDto(fbaReplenishmentApiVO, asinCampaignNumMap, shopMap, inventoryDataDtos);
        }
        // 根据字段排序
        sortByField(reqVo, inventoryDataDtos);
        return inventoryDataDtos;
    }

    private static void buildDto(FbaReplenishmentApiVO fbaReplenishmentApiVO, Map<String, Integer> asinCampaignNumMap, Map<Integer, ShopAuth> shopMap, List<DashboardInsufficientInventoryDataDto> inventoryDataDtos) {
        DashboardInsufficientInventoryDataDto inventoryDataDto = BeanUtil.copyProperties(fbaReplenishmentApiVO, DashboardInsufficientInventoryDataDto.class);
        BeanUtil.copyProperties(fbaReplenishmentApiVO.getSaleVO(), inventoryDataDto);
        // 广告活动数量
        inventoryDataDto.setCampaignNum(asinCampaignNumMap.getOrDefault(inventoryDataDto.getAsin() + "-" + fbaReplenishmentApiVO.getShopId(), 0));
        // 店铺站点信息
        ShopAuth shopAuth = shopMap.get(fbaReplenishmentApiVO.getShopId());
        if (shopAuth != null) {
            inventoryDataDto.setShopName(shopAuth.getName());
            inventoryDataDto.setMarketplaceId(shopAuth.getMarketplaceId());
            inventoryDataDto.setMarketplaceName(shopAuth.getMarketplaceName());
        }
        if(CollectionUtil.isNotEmpty(fbaReplenishmentApiVO.getMsku())){
            inventoryDataDto.setMskus(String.join(",", fbaReplenishmentApiVO.getMsku()));
        }
        // 计算距离当天天数
        if (StringUtil.isNotEmpty(inventoryDataDto.getOutOfStockAfterArrival())) {
            long between = 0;
            if (DateUtil.compare(DateUtil.parseDate(inventoryDataDto.getOutOfStockAfterArrival()), DateUtil.beginOfDay(new Date())) >= 0) {
                between = DateUtil.between(DateUtil.parseDate(inventoryDataDto.getOutOfStockAfterArrival()), new Date(), DateUnit.DAY) + 1;
            }
            inventoryDataDto.setBetweenDay(between);
        }
        if(CollectionUtil.isNotEmpty(inventoryDataDto.getFbaInTransitDetail())){
            for (FbaInTransitApiVO vo : inventoryDataDto.getFbaInTransitDetail()) {
                if(StringUtil.isNotEmpty(vo.getExpectArrivalDate())){
                    long between = DateUtil.between(DateUtil.parseDate(vo.getExpectArrivalDate()), new Date(), DateUnit.DAY);
                    if (DateUtil.compare(DateUtil.parseDate(vo.getExpectArrivalDate()), DateUtil.beginOfDay(new Date())) < 0) {
                        between = -between;
                    }
                    vo.setBetweenArrivalTime(between);
                }
            }
        }
        inventoryDataDtos.add(inventoryDataDto);
    }

    private static void sortByField(DashboardInsufficientInventoryReqVo reqVo, List<DashboardInsufficientInventoryDataDto> inventoryDataDtos) {
        // 默认排序
        if (StringUtils.isEmpty(reqVo.getListOrderField())) {
            reqVo.setListOrderField("sellableDays");
        }
        if (StringUtils.isEmpty(reqVo.getListOrderType())) {
            reqVo.setListOrderType("asc");
        }
        if ("outOfStockAfterArrival".equals(reqVo.getListOrderField())) {
            // 日期字段排序特殊处理
            inventoryDataDtos.sort(Comparator.comparing(
                    data -> {
                        if (data.getOutOfStockAfterArrival() == null || data.getOutOfStockAfterArrival().isEmpty()) {
                            return null;
                        }
                        return LocalDate.parse(data.getOutOfStockAfterArrival(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    },
                    "asc".equals(reqVo.getListOrderType()) ? Comparator.nullsLast(Comparator.<LocalDate>naturalOrder()) :
                            Comparator.nullsLast(Comparator.<LocalDate>naturalOrder().reversed())
            ));
        } else {
            OrderByUtil.sortedByOrderField(inventoryDataDtos, reqVo.getListOrderField(), reqVo.getListOrderType(), "sellableDays");
        }
    }

    @Override
    public List<String> exportInsufficientInventoryData(DashboardInsufficientInventoryReqVo reqVo) {

        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(reqVo.getShopIdList());
            reqVo.setShopIdList(com.google.common.collect.Lists.newArrayList(reqVo.getShopIdList()));
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                reqVo.getShopIdList().removeAll(listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
                log.info("剔除vc店铺后无数据puid:{}", reqVo.getPuid());
                return new ArrayList<>();
            }
        }
        //获取数据
        List<DashboardInsufficientInventoryDataDto> inventoryDataDtos = this.queryInsufficientInventoryData(reqVo);
        for (DashboardInsufficientInventoryDataDto inventoryDataDto : inventoryDataDtos) {
            if(StringUtil.isNotEmpty(inventoryDataDto.getOutOfStockAfterArrival())){
                inventoryDataDto.setOutOfStockAfterArrival(inventoryDataDto.getOutOfStockAfterArrival()+" ("+inventoryDataDto.getBetweenDay()+"天)");
            }
        }
        List<String> list = new ArrayList<>();
        list.add(excelService.easyExcelHandlerDownload(reqVo.getPuid(), inventoryDataDtos, "库存不足", DashboardInsufficientInventoryDataDto.class, baseHeaderList, true));
        return list;
    }

    /**
     * 获取活动数量
     * key：asin-shopId
     * value：sp+sb+sd活动数量
     */
    private Map<String, Integer> getAsinCampaignNumMap(DashboardInsufficientInventoryReqVo reqVo, List<FbaReplenishmentApiVO> fbaReplenishmentApiVOS) {
        List<String> asinList = StreamUtil.toListDistinct(fbaReplenishmentApiVOS, FbaReplenishmentApiVO::getAsin);
        List<Integer> shopIdList = StreamUtil.toListDistinct(fbaReplenishmentApiVOS, FbaReplenishmentApiVO::getShopId);
        List<String> asinShopList = StreamUtil.toListDistinct(fbaReplenishmentApiVOS, it -> it.getAsin() + "-" + it.getShopId());

        Map<String, Integer> asinCampaignNumMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(asinList)) {
            // 1.统计sp asin 活动数量
            List<AsinCampaignNumDto> asinSpCampaignNum = odsAmazonAdProductDao.getAsinCampaignNum(reqVo.getPuid(), shopIdList, asinList);
            Map<String, Integer> spMap = StreamUtil.toMap(asinSpCampaignNum, it -> it.getAsin() + "-" + it.getShopId(), AsinCampaignNumDto::getCampaignNum);
            // 2.统计sd asin 活动数量
            List<AsinCampaignNumDto> asinSdCampaignNum = odsAmazonAdProductSdDao.getAsinCampaignNum(reqVo.getPuid(), shopIdList, asinList);
            Map<String, Integer> sdMap = StreamUtil.toMap(asinSdCampaignNum, it -> it.getAsin() + "-" + it.getShopId(), AsinCampaignNumDto::getCampaignNum);
            // 3.统计sb asin 活动数量
            List<OdsAmazonSbAds> sbAdsList = odsAmazonSbAdsDao.getAsinCampaignNum(reqVo.getPuid(), shopIdList, asinList);
            Map<String, Set<String>> sbMap = new HashMap<>();
            for (OdsAmazonSbAds odsAmazonSbAds : sbAdsList) {
                String[] asins = odsAmazonSbAds.getAsins().split(",");
                for (String asin : asins) {
                    Set<String> campaignIdSet = sbMap.getOrDefault(asin + "-" + odsAmazonSbAds.getShopId(), new HashSet<>());
                    campaignIdSet.add(odsAmazonSbAds.getCampaignId());
                    sbMap.put(asin + "-" + odsAmazonSbAds.getShopId(), campaignIdSet);
                }
            }
            for (String asinShop : asinShopList) {
                Integer spNum = spMap.getOrDefault(asinShop, 0);
                Integer sdNum = sdMap.getOrDefault(asinShop, 0);
                Set<String> campaignIdSet = sbMap.getOrDefault(asinShop, new HashSet<>());
                Integer campaignNum = spNum + sdNum + campaignIdSet.size();
                asinCampaignNumMap.put(asinShop, campaignNum);
            }
        }
        return asinCampaignNumMap;
    }
}
