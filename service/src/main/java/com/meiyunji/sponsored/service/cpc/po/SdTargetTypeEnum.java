package com.meiyunji.sponsored.service.cpc.po;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/8.
 */
public enum SdTargetTypeEnum {

    asin("asin", "商品投放"),

    category("category", "分类投放"),

    exactProduct("exactProduct", "您推广的商品"),

    similarProduct("similarProduct", "与您推广商品类似的商品"),

    relatedProduct("relatedProduct", "与推广的商品相关"),

    brand("brand", "品牌投放"),
    audience("audience", "受众投放"),
    audience_InMarket("InMarket", "受众投放-场内客群"),
    contentCategory("contentCategorySameAs", "内容分类");


    private String value;
    private String desc;

    SdTargetTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<String, SdTargetTypeEnum> getMap() {
        return Arrays.stream(SdTargetTypeEnum.values()).collect(Collectors.toMap(SdTargetTypeEnum::getValue, v1 -> v1, (old, current) -> current));
    }
}
