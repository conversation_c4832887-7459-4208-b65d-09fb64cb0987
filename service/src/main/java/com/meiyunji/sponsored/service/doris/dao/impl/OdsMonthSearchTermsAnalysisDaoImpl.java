package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.doris.dao.IOdsMonthSearchTermsAnalysisDao;
import com.meiyunji.sponsored.service.doris.po.OdsMonthSearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-08-05 20:19
 */
@Repository
public class OdsMonthSearchTermsAnalysisDaoImpl extends DorisBaseDaoImpl<OdsMonthSearchTermsAnalysis> implements IOdsMonthSearchTermsAnalysisDao {
    @Override
    public List<OdsMonthSearchTermsAnalysis> getTrend(SearchTermsAnalysisTrendQo qo, String minDay) {
        if (CollectionUtils.isEmpty(qo.getSearchTerms())) {
            return new ArrayList<>();
        }
        List<String> searchTerms = qo.getSearchTerms();
        List<Object> argsList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        builder.append("select start_date ,end_date ,marketplace_id ,department_name ,search_term ,search_frequency_rank from ");
        builder.append(this.getJdbcHelper().getTable());
        builder.append(" where marketplace_id = ? and end_date <= ? and start_date >= ? ");
        argsList.add(qo.getMarketplaceId());
        argsList.add(qo.getEndDate());
        argsList.add(minDay);
        if (CollectionUtils.isNotEmpty(searchTerms)) {
            builder.append(SqlStringUtil.dealInList("search_term", searchTerms, argsList));
        }
        return getJdbcTemplate().query(builder.toString(), argsList.toArray(), getRowMapper());
    }
}
