package com.meiyunji.sponsored.service.reportImport2.modle;

import com.alibaba.fastjson.JSONReader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LxAmazonAdCampaignPlacementReport extends BaseAmazonAdLxReport {

    /**
     * profileId
     */
    @JsonProperty("profile_id")
    private String profileId;

    /**
     * 广告活动id
     */
    @JsonProperty("campaign_id")
    private String campaignId;

    /**
     * 广告活动名称
     */
    @JsonProperty("campaign_name")
    private String campaignName;

    @JsonProperty("placement")
    private String placement;

    @JsonProperty("predicate")
    private String predicate;

    public BaseAmazonAdLxReport readFromJsonReader(JSONReader jsonReader) {
        while (jsonReader.hasNext()) {
            String key = jsonReader.readString();
            switch (key) {
                case "profile_id":
                    this.setProfileId(jsonReader.readString());
                    break;
                case "campaign_id":
                    this.setCampaignId(jsonReader.readString());
                    break;
                case "campaign_name":
                    this.setCampaignName(jsonReader.readString());
                    break;
                case "placement":
                    this.setPlacement(jsonReader.readString());
                    break;
                case "predicate":
                    this.setPredicate(jsonReader.readString());
                    break;
                default:
                    super.readFromJsonReader(key, jsonReader, this);
                    break;

            }
        }
        return this;
    }

}
