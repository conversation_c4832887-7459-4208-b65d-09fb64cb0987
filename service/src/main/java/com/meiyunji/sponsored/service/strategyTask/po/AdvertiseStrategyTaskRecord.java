package com.meiyunji.sponsored.service.strategyTask.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:31
 */
@DbTable(value = "t_advertise_strategy_task_record")
@Data
public class AdvertiseStrategyTaskRecord implements Serializable {

    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    @DbColumn(value = "task_id")
    private Long taskId;

    @DbColumn(value = "status_id")
    private Long statusId;

    @DbColumn(value = "status_task_id")
    private Long statusTaskId;

    @DbColumn(value = "target_type")
    private String targetType;

    @DbColumn(value = "status")
    private String status;

    @DbColumn(value = "origin_budget_Value")
    private BigDecimal originBudgetValue;

    @DbColumn(value = "origin_adPlaceTopValue")
    private BigDecimal originAdPlaceTopValue;

    @DbColumn(value = "origin_adPlaceProductValue")
    private BigDecimal originAdPlaceProductValue;

    @DbColumn(value = "origin_strategy")
    private String originStrategy;

    @DbColumn(value = "origin_bidding_value")
    private BigDecimal originBiddingValue;

    @DbColumn(value = "origin_state")
    private String originState;

    @DbColumn(value = "ad_type")
    private Integer adType;

    @DbColumn(value = "state_error")
    private String stateError;

    @DbColumn(value = "item_type")
    private Integer itemType;

    @DbColumn(value = "retry_count")
    private Integer retryCount;

    @DbColumn(value = "state")
    private Integer state;

    @DbColumn(value = "item_id")
    private String itemId;

    @DbColumn(value = "item_name")
    private String itemName;

    @DbColumn(value = "is_retry")
    private Integer isRetry;

    @DbColumn(value = "create_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime createTime;

    @DbColumn(value = "update_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime updateTime;

    private String adGroupName;

    private String addWayType;
}
