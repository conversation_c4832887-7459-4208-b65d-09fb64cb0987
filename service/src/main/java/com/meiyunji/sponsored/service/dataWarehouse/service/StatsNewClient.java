package com.meiyunji.sponsored.service.dataWarehouse.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meiyunji.sponsored.common.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class StatsNewClient {

    private static final String Log_Get = " STAT CLIENT : GET {} DURING [{}] ms PARAM {} ";
    private static final String Log_Get_Re = "RETRY STAT CLIENT : GET {} DURING [{}] ms PARAM {} ";
    private static final String Log_ToJson = " STAT CLIENT :  PROCESS JSON {} DURING [{}] ms ";
    private static final String Log_Err = " STAT CLIENT :  ERROR {} PARAM {} ";
    private static final String Log_Post = "STAT CLIENT : POST {} DURING [{}] ms PARAM {} ";

    @Value("${services.statsNew.prefix:http://10.200.0.4:10095}")
    private String endpoint;



    <T> T get(String path, Map<String, String> params, TypeReference<T> typeReference) {

        try {
            long s = System.currentTimeMillis();
            String result = HTTPClient.get(endpoint + "/api/v1/amzup/" + path, params);
            log.info(Log_Get, path, System.currentTimeMillis() - s, params);
            if (StringUtils.isNotBlank(result) && !result.contains("Internal Server Error")) {
                s = System.currentTimeMillis();
                T obj = JSONUtil.jsonToObjects(result, typeReference);
                log.info(Log_ToJson, path, System.currentTimeMillis() - s);
                return obj;
            }
        } catch (IOException e) {
            log.error(Log_Err, path, e);
            if (e.getMessage().contains("Failed to connect") || e.getMessage().contains("timed out")) {
                log.info(" RETRY STAT API GET path: {} ", path);
                return reGet(path, params, typeReference);
            }
        }
        return null;
    }

    <T> T reGet(String path, Map<String, String> params, TypeReference<T> typeReference) {

        try {
            long s = System.currentTimeMillis();
            String result = HTTPClient.get(endpoint + "/api/v1/amzup/" + path, params);
            log.info(Log_Get_Re, path, System.currentTimeMillis() - s, params);
            if (StringUtils.isNotBlank(result) && !result.contains("Internal Server Error")) {
                s = System.currentTimeMillis();
                T obj = JSONUtil.jsonToObjects(result, typeReference);
                log.info(Log_ToJson, path, System.currentTimeMillis() - s);
                return obj;
            }
        } catch (IOException e) {
            log.error(Log_Err, path, e);
        }
        return null;
    }

    <T> List<T> post(String path, String params, Class<T> clazz) {

        log.info(" start client connect post request");
        try {
            RequestBody body = new FormBody.Builder()
                    .add("qo", params)
                    .build();

            long s = Instant.now().toEpochMilli();
            String result = HTTPClient.post(endpoint + "/api/v1/" + path, body);
            log.info(Log_Post, path, Instant.now().toEpochMilli() - s, params);

            if (StringUtils.isNotBlank(result) && !result.contains("Internal Server Error")) {
                s = Instant.now().toEpochMilli();
                List<T> objs = JSONUtil.jsonToArray(result, clazz);
                log.info(Log_ToJson, path, Instant.now().toEpochMilli() - s);
                return objs;
            }
        } catch (IOException e) {
            log.error(Log_Err, path, e);
        } finally {
            log.info(" end client post request ");
        }
        return null;
    }

    <T> List<T> postJsonForReturnList(String path, String params, Class<T> clazz) {

        log.info(" start client connect post request");
        try {

            MediaType JSON = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(JSON, params);

            long s = Instant.now().toEpochMilli();
            String result = HTTPClient.post(endpoint + path, body);
            log.info(Log_Post, path, Instant.now().toEpochMilli() - s, params);

            if (StringUtils.isNotBlank(result) && !result.contains("Internal Server Error")) {
                s = Instant.now().toEpochMilli();
                List<T> objs = JSONUtil.jsonToArray(result, clazz);
                log.info(Log_ToJson, path, Instant.now().toEpochMilli() - s);
                return objs;
            }
        } catch (IOException e) {
            log.error(Log_Err, path, e);
        } finally {
            log.info(" end client post request ");
        }
        return null;
    }

    public <T> T postJson(String path, String params, Class<T> clazz) {

        // Create MediaType and RequestBody
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, params);

        long startTime = Instant.now().toEpochMilli();
        String result = "";
        try {
            // Perform POST request
            result = HTTPClient.post(endpoint + path, body);
            // Check result and convert mediaType to Object
            if (StringUtils.isNotBlank(result) && !result.contains("Internal Server Error")) {
                T responseObject = JSON.parseObject(result, clazz);
                if (responseObject != null) {
                    log.info("Post request success. Path: {}, param：{}, result: {}, Duration: {} ms", path, params, result, Instant.now().toEpochMilli() - startTime);
                    return responseObject;
                }
            }
        } catch (IOException e) {
            log.error("Error during POST request. Path: {}, Params: {}, result:{}", path, params, result, e);
            return null;
        }
        log.info("Post request fail. Path: {}, param：{}, result: {}, Duration: {} ms", path, params, result, Instant.now().toEpochMilli() - startTime);
        return null;
    }
}
