package com.meiyunji.sponsored.service.reportImport.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportImportAddParam;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportImportPageListParam;
import com.meiyunji.sponsored.service.reportImport.dto.CpcReportsImportPlatformDto;
import com.meiyunji.sponsored.service.reportImport.entity.CpcReportsImportPlatform;

/**
 * <AUTHOR>
 */
public interface ICpcReportsImportPlatformService {

    /**
     * 分页列表
     * @param puid 店铺id
     * @param page 分页对象
     * @param param 查询参数对象
     * @return
     */
    Page<CpcReportsImportPlatformDto> getPageList(int puid, Page<CpcReportsImportPlatformDto> page, ReportImportPageListParam param);

    /**
     * 添加导入报告
     * @param puid 店铺id
     * @param reportImportAddParam 导入参数对象
     */
    void addReportImportTask(int puid, ReportImportAddParam reportImportAddParam);
}
