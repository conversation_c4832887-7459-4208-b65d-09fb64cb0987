package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageSyncSdTargetProcess extends AbstractSyncServerStatusProcessStrategy {


    @Resource
    private CpcSdTargetingApiService cpcSdTargetingApiService;



    public ManageSyncSdTargetProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration, redisService);

    }


    @Override
    public int getMaxCount() {
        return StreamConstants.SD_MAX_TARGET_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        Boolean syncManageProxy = dynamicRefreshConfiguration.getSyncManageProxy();
        cpcSdTargetingApiService.syncTargetings(shopAuth, null, null, ids, null,true, Boolean.TRUE.equals(syncManageProxy));
    }
}
