package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告组下的投放列表页入参
 */
@Data
@ApiModel
public class TargetingPageParam {

    @ApiModelProperty("类型 sp sb sd")
    private String type;
    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    @ApiModelProperty
    private Integer pageNo;
    @ApiModelProperty
    private Integer pageSize;

    @ApiModelProperty(value = "shopId", required = true)
    private Integer shopId;

    private String marketplaceId;

    @ApiModelProperty(value = "puid", required = true)
    private Integer puid;

    private Integer uid;
    private String uuid;

    @ApiModelProperty("广告组ID,多个广告组使用逗号分隔")
    private String groupId;

    @ApiModelProperty
    private String status;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("搜索字段 目前只支持 name")
    private String searchField;

    @ApiModelProperty("搜索字段内容")
    private String searchValue;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序方式 desc asc")
    private String orderType;

    @ApiModelProperty("广告活动ID,多个活动使用逗号分隔")
    private String campaignId;
    @ApiModelProperty("广告标签id")
    private Long adTagId;
    private List<Long> adTagIdList;

    private List<String> targetIds;

    @ApiModelProperty(value = "用于仅展示正在投放字段 勾选后传值 enabled")
    private String servingStatus;

    @ApiModelProperty("导出排序字段")
    private String exportSortField;

    @ApiModelProperty("冻结前num列")
    private Integer freezeNum;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;

    @ApiModelProperty(value = "投放类型")
    private String filterTargetType;
    @ApiModelProperty(value = "SP页面选择的投放类型(自动投放：auto 与 商品投放：targeting)  SD页面选择的投放类型(商品投放：T00020 与 受众投放：T00030)")
    private String chosenTargetType;
    @ApiModelProperty(value = "高级搜索默认竞价最小")
    private BigDecimal bidMin;
    @ApiModelProperty(value = "高级搜索默认竞价最大")
    private BigDecimal bidMax;

    @ApiModelProperty(value = "高级搜索店铺销售额")
    private BigDecimal shopSales;

    @ApiModelProperty(value = "高级搜索广告花费占比最小")
    private BigDecimal adCostPercentageMin;
    @ApiModelProperty(value = "高级搜索广告花费占比最大")
    private BigDecimal adCostPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销售额占比最小")
    private BigDecimal adSalePercentageMin;
    @ApiModelProperty(value = "高级搜索广告销售额占比最大")
    private BigDecimal adSalePercentageMax;
    @ApiModelProperty(value = "高级搜索广告订单量占比最小")
    private BigDecimal adOrderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告订单量占比最大")
    private BigDecimal adOrderNumPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销量占比最小")
    private BigDecimal orderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告销量占比最大")
    private BigDecimal orderNumPercentageMax;


    /*********************************广告组新增高级筛选新增指标**********************************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单转化率最小值")
    private BigDecimal brandNewBuyerOrderConversionRateMin;

    @ApiModelProperty(value = "“品牌新买家”订单转化率最大值")
    private BigDecimal brandNewBuyerOrderConversionRateMax;


    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty("加购次数最小值")
    private Integer addToCartMin;
    @ApiModelProperty("加购次数最大值")
    private Integer addToCartMax;

    @ApiModelProperty("5秒观看次数最小值")
    private Integer video5SecondViewsMin;
    @ApiModelProperty("5秒观看次数最大值")
    private Integer video5SecondViewsMax;

    @ApiModelProperty("视频完整播放次数最小值")
    private Integer videoCompleteViewsMin;
    @ApiModelProperty("视频完整播放次数最大值")
    private Integer videoCompleteViewsMax;

    @ApiModelProperty("观看率最小值")
    private BigDecimal viewabilityRateMin;
    @ApiModelProperty("观看率最大值")
    private BigDecimal viewabilityRateMax;

    @ApiModelProperty("观看点击率最小值")
    private BigDecimal viewClickThroughRateMin;
    @ApiModelProperty("观看点击率最大值")
    private BigDecimal viewClickThroughRateMax;

    @ApiModelProperty("品牌搜索次数最小值")
    private Integer brandedSearchesMin;
    @ApiModelProperty("品牌搜索次数最大值")
    private Integer brandedSearchesMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    @ApiModelProperty("搜索结果首页首位IS最小值")
    private BigDecimal topImpressionShareMin;
    @ApiModelProperty("搜索结果首页首位IS最大值")
    private BigDecimal topImpressionShareMax;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    @ApiModelProperty(value = "广告组合下的活动的广告组id")
    private List<String> groupIdList;

    @ApiModelProperty(value = "广告策略类型")
    private List<String> adStrategyTypeList;
    @ApiModelProperty(value = "经过广告策略筛选后的广告投放id集合")
    private List<String> autoRuleIds;
    @ApiModelProperty(value = "经过广告策略筛选后的广告组合id集合")
    private List<String> autoRuleGroupIds;

    /**
     * 仅限sp 商品投放查询数据
     * 赛选条件
     */
    private String selectType;

    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;
    private String pageSign;
    /**
     *  只查询数量 简化查询
     */
    private Boolean onlyCount;

    private String icon;
    private String currency;

    public enum SearchFieldEnum implements BaseEnum {
        Name("name", "", "asin,类目或投放名称");;
        private String field;
        private String column;
        private String desc;

        SearchFieldEnum(String field, String column, String desc) {
            this.field = field;
            this.column = column;
            this.desc = desc;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return desc;
        }
    }

    public ReportAdvancedFilterBaseQo buildReportAdvancedFilterBaseQo() {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(this, qo);
        return qo;
    }
}
