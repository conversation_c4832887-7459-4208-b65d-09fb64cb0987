package com.meiyunji.sponsored.service.autoRuleTask.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-10  16:55
 */
public enum SpecialBudgetValueEnum {
    IN("A21TJRUUN4KGV",21000000.00 , 50.00, 100.00),
    BR("A2Q3Y263D00KWC",5300000.00,1.32, 4.00),
    SG("A19VAU5U5O7RUS", 1300000.00, 1.39, 4.00);


    private final String marketplaceId;
    private final Double maxValue;
    private final Double spAndSdminValue;
    private final Double sbMinValue;

    SpecialBudgetValueEnum (String marketplaceId, Double maxValue, Double spAndSdminValue, Double sbMinValue) {
        this.marketplaceId = marketplaceId;
        this.maxValue = maxValue;
        this.spAndSdminValue = spAndSdminValue;
        this.sbMinValue = sbMinValue;
    }


    public String getMarketplaceId() {
        return this.marketplaceId;
    }

    public Double getMaxValue() {
        return this.maxValue;
    }

    public Double getSpAndSdminValue() {
        return spAndSdminValue;
    }

    public Double getSbMinValue() {
        return sbMinValue;
    }



    /**
     * 根据站点获取最大值
     * @param marketplaceId
     * @return
     */
    public static SpecialBudgetValueEnum getSpecialBudgetValueEnum(String marketplaceId) {
        SpecialBudgetValueEnum result = null;
        for (SpecialBudgetValueEnum budgetValueEnum : SpecialBudgetValueEnum.values()) {
            if (StringUtils.equals(budgetValueEnum.getMarketplaceId(),marketplaceId)) {
                result = budgetValueEnum;
                break;
            }
        }
        return result;
    }

    /**
     * 根据站点获取最大值
     * @param marketplaceId
     * @return
     */
    public static Double getMaxValue(String marketplaceId) {
        Double result = null;
        for (SpecialBudgetValueEnum budgetValueEnum : SpecialBudgetValueEnum.values()) {
            if (StringUtils.equals(budgetValueEnum.getMarketplaceId(),marketplaceId)) {
                result = budgetValueEnum.getMaxValue();
                break;
            }
        }
        return result;
    }

    /**
     * 根据站点获取最大值
     * @param marketplaceId
     * @return
     */
    public static Double getSpAndSdminValue(String marketplaceId) {
        Double result = null;
        for (SpecialBudgetValueEnum budgetValueEnum : SpecialBudgetValueEnum.values()) {
            if (StringUtils.equals(budgetValueEnum.getMarketplaceId(),marketplaceId)) {
                result = budgetValueEnum.getSpAndSdminValue();
                break;
            }
        }
        return result;
    }

    /**
     * 根据站点获取最大值
     * @param marketplaceId
     * @return
     */
    public static Double getSbMinValue(String marketplaceId) {
        Double result = null;
        for (SpecialBudgetValueEnum budgetValueEnum : SpecialBudgetValueEnum.values()) {
            if (StringUtils.equals(budgetValueEnum.getMarketplaceId(),marketplaceId)) {
                result = budgetValueEnum.getSbMinValue();
                break;
            }
        }
        return result;
    }
}
