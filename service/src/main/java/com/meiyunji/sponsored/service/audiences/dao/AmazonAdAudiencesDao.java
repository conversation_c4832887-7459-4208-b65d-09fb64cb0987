package com.meiyunji.sponsored.service.audiences.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.audiences.entity.AmazonAdAudience;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;

import java.util.List;

/**
 * AmazonAdAudiencesDao
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20
 */
public interface AmazonAdAudiencesDao extends IAdBaseDao<AmazonAdAudience> {

    void batchAdd(List<AmazonAdAudience> list);

    void batchUpdate(List<AmazonAdAudience> list);

    List<AmazonAdAudience> listByAudienceIds(List<String> audienceIds);

    AmazonAdAudience getByAudienceName(String audienceName);
}
