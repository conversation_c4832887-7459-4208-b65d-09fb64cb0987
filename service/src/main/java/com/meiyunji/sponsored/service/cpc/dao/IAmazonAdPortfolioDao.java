package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.strategy.vo.AdPortfolioStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategy.vo.StrategyPortfolioVo;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;

import java.util.List;

public interface IAmazonAdPortfolioDao  extends IBaseShardingDao<AmazonAdPortfolio> {

    void insertOrUpdateList(Integer puid, List<AmazonAdPortfolio> list);

    void updateAdPortfolio(Integer puid, ScheduleTaskFinishedVo messageVo);

    List<AmazonAdPortfolio> getPortfolioListNew(Integer puid, PortfolioPageParam param);

    List<AmazonAdPortfolio> getPortfolioListNew(Integer puid, PortfolioListParam param);

    List<AmazonAdPortfolio> getPortfolioListNews(Integer puid, PortfolioPageParam param);

    Page getPortfolioPageList(Integer puid, PortfolioPageParam param, Page page);

    Page getPortfolioPageList(AdPortfolioStrategyParam param);

    void batchUpdateIsHidden(Integer puid, Integer uid, Integer shopId, boolean isHidden, List<String> portfolioIdList);

    List<AmazonAdPortfolio> getPortfolioList(Integer puid, Integer shopId,  List<String> portfolioIdList);

    List<AmazonAdPortfolio> listByShopId(Integer puid, List<Integer> shopIds,  List<String> portfolioIdList);

    AmazonAdPortfolio getByPortfolioId(Integer puid, Integer shopId, String portfolioId);

    List<AmazonAdPortfolio> getByPortfolioIds(Integer puid, Integer shopId, List<String> portfolioId);

    void updateNameById(Integer puid, Integer uid, Integer shopId, String name, Long id);

    /**
     * 编辑更新单个广告组合（更新预算上限类型、预算金额、预算开始结束日期）
     */
    void updatePortfolio(Integer puid, Integer shopId, Integer uid, Long id, PortfolioEditParam param);

    /**
     * 编辑更新单个广告组合的预算状态 (正在投放:enabled 已暂停:paused 已结束:ended 已归档:archived 未开始:pending)
     */
    void updatePortfolioState(Integer puid, Integer shopId, Integer uid, Long id, String state);

    void updatePortfolioRank(Integer puid, Integer shopId, String portfolioId, Integer rank);

    void sortPortfolioRankByRankLastUpdateTime(Integer puid, Integer shopId);

    void sortPortfolioRankByRankUpdateTime(Integer puid, Integer shopId, Integer rank);

    void sortPortfolioRankByRankUpdateTimeAsc(Integer puid, Integer shopId, Integer rank);

    Integer sumRank(Integer puid, Integer shopId);

    List<AmazonAdPortfolio> getPortfolioListAutoRule(Integer puid, Integer shopId,String marketplaceId,String portfolioName,Integer pageSize, Integer pageNo);

    void updateStatePricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId);

    List<String> queryPortfolioId(ControlledObjectParam param);

    List<AmazonAdPortfolio> getPortfolioSumList(Integer puid, PortfolioPageParam param);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<StrategyPortfolioVo> listByCampaignIdList(Integer puid, List<Integer> shopIdList, List<String> campaignIds);

    List<String> getPortfolioIdList(ProcessTaskParam param);

    Page<AmazonAdPortfolio> getMultiShopPortfolioList(Integer puid, MultiShopPortfolioListParam param);

    List<AmazonAdPortfolio> getByShopPortfolioPair(Integer puid, List<PortfolioListParam> paramList);

    List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds, List<String> portfolioIds);

    Page<KeywordLibsPortfolioListVO> getPortfolioName(Integer puid, KeywordLibsPageParam param);

    List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds);
}
