package com.meiyunji.sponsored.service.multiPlatform.walmart.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeyword;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordRecommendations;


import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingKeywordRecommendationsDao extends IBaseShardingDao<WalmartAdvertisingKeywordRecommendations> {

    Long add(WalmartAdvertisingKeywordRecommendations keywordRecommendations);

    int update(WalmartAdvertisingKeywordRecommendations keywordRecommendations);

    int delete(Integer puid, Long id);

    WalmartAdvertisingKeywordRecommendations getById(Integer puid, Long id);

    List<WalmartAdvertisingKeywordRecommendations> getByIds(Integer puid, List<Long> ids);

    Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    int getByReportDate(int puid, Long shopId, String reportDate);

    int deleteByReportDate(int puid, Long shopId, String reportDate);

    WalmartAdvertisingKeywordRecommendations getLastKeywordRecommendation(int puid);

    //    private static Logger logger = LoggerFactory.getLogger(WalmartAdvertisingKeywordRecommendationsDaoImpl.class);
    //    private WalmartAdvertisingKeywordRecommendationsMapper mapper = new WalmartAdvertisingKeywordRecommendationsMapper();
    //
    //    @Override
    //    protected String getSeqTable() {
    //        return "t_dxm_walmart_advertising_seq";
    //    }
    //
    //    @Override
    //    public Long add(WalmartAdvertisingKeywordRecommendations keywordRecommendations) {
    //        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keywordRecommendations.getPuid()));
    //        Long id = getId(keywordRecommendations.getPuid());
    //        StringBuilder sb = new StringBuilder();
    //        sb.append("insert into t_walmart_advertising_keyword_recommendations  (id,puid,shop_id,report_date,campaign_id,campaign_name,ad_group_id,ad_group_name,keyword_text,suggested_bid,match_type,create_time,update_time )");
    //        sb.append(" values (?,?,?,?,?,?,?,?,?,?,?,now(),now() )");
    //        List<Object> argsList = new ArrayList<>();
    //        argsList.add(id);
    //        argsList.add(keywordRecommendations.getPuid());
    //        argsList.add(keywordRecommendations.getShopId());
    //        argsList.add(keywordRecommendations.getReportDate());
    //        argsList.add(keywordRecommendations.getCampaignId());
    //        argsList.add(StringUtil.cutLimitedStr(keywordRecommendations.getCampaignName(),255));
    //        argsList.add(keywordRecommendations.getAdGroupId());
    //        argsList.add(StringUtil.cutLimitedStr(keywordRecommendations.getAdGroupName(),255));
    //        argsList.add(StringUtil.cutLimitedStr(keywordRecommendations.getKeywordText(),100));
    //        argsList.add(keywordRecommendations.getSuggestedBid());
    //        argsList.add(keywordRecommendations.getMatchType());
    //        int t = update(jdbcTemplate, sb.toString(), argsList.toArray());
    //        return t > 0 ? id : 0L;
    //    }
    //
    //    @Override
    //    public int update(WalmartAdvertisingKeywordRecommendations keywordRecommendations) {
    //        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keywordRecommendations.getPuid()));
    //        Map<String, Object> params = getParamMap(keywordRecommendations);
    //        return updateTable(jdbcTemplate, "t_walmart_advertising_keywordRecommendations", params, "where `id` = " + keywordRecommendations.getId());
    //    }
    //
    //
    //    private Map<String, Object> getParamMap(WalmartAdvertisingKeywordRecommendations keywordRecommendations) {
    //        Map<String, Object> params = new HashMap<>();
    //        params.put("keyword_text",keywordRecommendations.getKeywordText());
    //        params.put("match_type",keywordRecommendations.getMatchType());
    //        params.put("suggested_bid",keywordRecommendations.getSuggestedBid());
    //        params.put("update_time", "now()");
    //        return params;
    //    }
    //
    //    @Override
    //    public int delete(Integer puid, Long id) {
    //        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(puid));
    //        return jdbcTemplate.update("DELETE from t_walmart_advertising_keyword_recommendations where puid = ? and id = ?", puid, id);
    //    }
    //
    //    @Override
    //    public WalmartAdvertisingKeywordRecommendations getById(Integer puid, Long id) {
    //        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
    //        String sql = "select * from `t_walmart_advertising_keyword_recommendations` where `id`=? and puid = ?";
    //        List<WalmartAdvertisingKeywordRecommendations> list = jdbcTemplate.query(sql, new Object[]{id, puid}, mapper);
    //        if (list != null && list.size() > 0) {
    //            return list.get(0);
    //        }
    //        return null;
    //    }
    //
    //    @Override
    //    public List<WalmartAdvertisingKeywordRecommendations> getByIds(Integer puid, List<Long> ids) {
    //        return new NamedParameterJdbcTemplate(getJdbcTemplate((long) puid)).query("select * from t_walmart_advertising_keyword_recommendations " +
    //                "where puid =:puid and id in (:id)", new MapSqlParameterSource() {{
    //            addValue("puid", puid).addValue("id", ids);
    //        }}, mapper);
    //    }
    //
    //    @Override
    //    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
    //        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
    //
    //        String startSql = "select * from t_walmart_advertising_keyword_recommendations p INNER JOIN (";
    //
    //        String selectStr = "select t1.id from ";
    //        String countStr = "select count(*) from ";
    //
    //        StringBuilder tableSql = new StringBuilder();
    //        tableSql.append("t_walmart_advertising_keyword_recommendations t1");
    //
    //        List<Object> argsList = new ArrayList<>();
    //
    //        StringBuilder whereSql = new StringBuilder(" where t1.puid = " + puid);
    //
    //        // 组合Sql
    //        combinationSqlString(queryParams, argsList, whereSql);
    //
    //        StringBuilder countSql = new StringBuilder();
    //        countSql.append(countStr);
    //        countSql.append(tableSql);
    //        countSql.append(whereSql);
    //
    //        StringBuilder selectSql = new StringBuilder();
    //        selectSql.append(selectStr);
    //        selectSql.append(tableSql);
    //        selectSql.append(whereSql);
    //
    //        String endSql = ") a ON p.id=a.id ";
    //
    //        Object[] args = argsList.toArray();
    //
    //        logger.info("{}", countSql);
    //        logger.info("{}", selectSql);
    //        logger.info(Arrays.toString(args));
    //        return getNewPageResult(jdbcTemplate, pageNo, pageSize, countSql.toString(), args, selectSql.toString(),
    //                startSql, endSql, args, mapper);
    //    }
    //
    int getByReportDate(int puid, Integer shopId, String reportDate);

    int deleteByReportDate(int puid, Integer shopId, String reportDate);

    int insertOrUpdate(int puid, List<WalmartAdvertisingKeywordRecommendations> list);
}
