package com.meiyunji.sponsored.service.batchCreate.service.impl;

import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchCampaignDao;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchGroupDao;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchProductDao;
import com.meiyunji.sponsored.service.batchCreate.dto.group.GroupInfoInTaskDTO;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchProduct;
import com.meiyunji.sponsored.service.batchCreate.service.IProductBatchSpService;
import com.meiyunji.sponsored.service.batchCreate.vo.ProductBatchSpVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/3/18 18:35
 * @describe:
 */
@Service
@Slf4j
public class ProductBatchSpServiceImpl implements IProductBatchSpService {

    @Autowired
    private IAmazonAdBatchProductDao amazonAdBatchProductDao;

    @Autowired
    private IAmazonAdBatchGroupDao amazonAdBatchGroupDao;

    @Autowired
    private IAmazonAdBatchCampaignDao amazonAdBatchCampaignDao;

    @Override
    public List<ProductBatchSpVO> getProductByTaskIdAndCampaignId(Integer puid, Integer shopId,
                                                                  Long taskId, Long campaignId) {
        if (Objects.isNull(puid) || Objects.isNull(shopId) || Objects.isNull(taskId)) {
            log.error("param is null, puid:{}, taskId:{}", puid, taskId);
            return Collections.emptyList();
        }
        StopWatch sw = new StopWatch();
        sw.start("query product list by task and campaign id");
        List<AmazonAdBatchProduct> queryList = Optional.ofNullable(amazonAdBatchProductDao.
                getByTaskIdAndCampaignId(puid, shopId, taskId, campaignId)).orElseGet(Collections::emptyList);

        Set<Long> groupIdSet = queryList.stream().map(AmazonAdBatchProduct::getGroupId).collect(Collectors.toSet());

        Map<Long, String> campaignNameMap = new HashMap<>();
        if (Objects.isNull(campaignId) || campaignId == 0){
            Set<Long> campaignIdList = queryList.stream().map(AmazonAdBatchProduct::getCampaignId).collect(Collectors.toSet());
            List<AmazonAdBatchCampaign> campaignNameList = amazonAdBatchCampaignDao.getCampaignBasicInfoByIds(puid, new ArrayList<>(campaignIdList));
            campaignNameMap.putAll(campaignNameList.parallelStream().collect(Collectors.toMap(AmazonAdBatchCampaign::getId,
                    AmazonAdBatchCampaign::getName, (old, current) -> current)));
        }

        //批量查询广告组名称进行返回
        List<GroupInfoInTaskDTO> groupBasicInfoList = amazonAdBatchGroupDao.getGroupBasicInfoById(puid, groupIdSet);
        Map<Long, String> groupInfoMap = groupBasicInfoList.parallelStream().collect(Collectors
                .toMap(GroupInfoInTaskDTO::getId, GroupInfoInTaskDTO::getName, (old, current) -> current));
        return queryList.stream().map(p -> ProductBatchSpVO.builder()
                .id(p.getId())
                .shopId(p.getShopId())
                .profileId(p.getProfileId())
                .marketplaceId(p.getMarketplaceId())
                .taskId(p.getTaskId())
                .campaignId(p.getCampaignId())
                .campaignName(Optional.ofNullable(campaignNameMap.get(p.getCampaignId())).orElse(""))
                .groupName(Optional.ofNullable(groupInfoMap.get(p.getGroupId())).orElse(""))
                .asin(p.getAsin())
                .sku(p.getSku())
                .taskStatus(p.getTaskStatus())
                .errMsg(p.getErrMsg())
                .build()).collect(Collectors.toList());
    }
}
