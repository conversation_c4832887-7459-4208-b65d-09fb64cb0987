package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisQueryRequest;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsageDay;

import java.util.List;

public interface IOdsAmazonAdBudgetUsageDayDao extends IDorisBaseDao<OdsAmazonAdBudgetUsageDay> {


    Page<OdsAmazonAdBudgetUsageDay> listPage(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest);

    int onlyCount(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest);

    List<OdsAmazonAdBudgetUsageDay> listByBudgetScopeIdAndDate(Integer puid, List<Integer> shopId, List<String> ids, List<String> date);
}
