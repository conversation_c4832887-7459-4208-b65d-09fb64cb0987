package com.meiyunji.sponsored.service.gps.service.impl;

import com.beust.jcommander.internal.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.adCampaign.enums.AdSyncRecord;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.gps.service.AmazonAdGpsService;
import com.meiyunji.sponsored.service.gps.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdGpsServiceImpl implements AmazonAdGpsService {

    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Override
    public Result<List<String>> getSkuList(Integer puid,Integer shopId) {
        Result<List<String>> result = new Result<>();
        try {
            List<String> skuList = amazonAdProductDao.getSkuList(puid,shopId);
            if (CollectionUtils.isNotEmpty(skuList)) {
                skuList = skuList.stream().distinct().collect(Collectors.toList());
            }
            result.setData(skuList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("广告GPS查询sku异常");
            log.error("广告GPS查询sku异常:",e);
        }
        return result;
    }

    @Override
    public Result<List<GpsVo>> getGpsList(GpsParam param) {
        Result<List<GpsVo>> result = new Result<>();
        try {
            List<GpsVo> gpsVoList = Lists.newArrayList();
            Map<String,AmazonAdGroup> amazonAdGroupMap = new HashMap<>();
            Map<String,AmazonAdCampaignAll> amazonAdCampaignAllMap = new HashMap<>();
            List<AmazonAdProduct> amazonAdProducts = new ArrayList<>();
            param.getGpsAsinAndAdIdList().forEach(e->{
                AmazonAdProduct amazonAdProduct = amazonAdProductDao.getProductList(param.getPuid(),param.getShopId(),e.getAsin(),e.getAdId());
                if (amazonAdProduct != null) {
                    amazonAdProducts.add(amazonAdProduct);
                }
            });
            if (CollectionUtils.isNotEmpty(amazonAdProducts)) {
                List<String> campaignIds = amazonAdProducts.stream().map(AmazonAdProduct::getCampaignId).distinct().collect(Collectors.toList());
                List<String> adGroupIds = amazonAdProducts.stream().map(AmazonAdProduct::getAdGroupId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignId(param.getPuid(),param.getShopId(),campaignIds,"sp");
                    if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                        amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
                    }
                }
                if (CollectionUtils.isNotEmpty(adGroupIds)) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getListByAdGroupIds(param.getPuid(), param.getShopId(),adGroupIds);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupMap = amazonAdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
                    }
                }
                Map<String, AmazonAdGroup> finalAmazonAdGroupMap = amazonAdGroupMap;
                Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                amazonAdProducts.forEach(e->{
                    GpsVo gpsVo = new GpsVo();
                    gpsVo.setShopId(e.getShopId());
                    gpsVo.setAdGroupId(e.getAdGroupId());
                    gpsVo.setCampaignId(e.getCampaignId());
                    gpsVo.setAsin(e.getAsin());
                    gpsVo.setAdId(e.getRowAdId());
                    if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(e.getCampaignId())) {
                        gpsVo.setCampaignName(finalAmazonAdCampaignAllMap.get(e.getCampaignId()).getName());
                    }
                    if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(e.getAdGroupId())) {
                        gpsVo.setAdGroupName(finalAmazonAdGroupMap.get(e.getAdGroupId()).getName());
                    }
                    gpsVoList.add(gpsVo);
                });
            }
            result.setData(gpsVoList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("广告GPS查询asin信息异常");
            log.error("广告GPS查询asin信息异常:",e);
        }
        return result;
    }

    @Override
    public Result<String> saveGps(List<GpsInsertVo> gpsVoList) {
        Result<String> result = new Result<>();
        try {
            if (CollectionUtils.isNotEmpty(gpsVoList)) {
                List<GpsCampaign> gpsCampaignList = amazonAdCampaignAllDao.getGpsCampaignByName(gpsVoList.get(0).getPuid(), gpsVoList.stream().map(GpsInsertVo::getCampaignName).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(gpsCampaignList)) {
                    return result;
                }
                List<GpsAdGroup> gpsAdGroupList = amazonAdGroupDao.getGpsAdGroupByName(gpsVoList.get(0).getPuid(), gpsCampaignList.stream().map(GpsCampaign::getCampaignId).collect(Collectors.toList()), gpsVoList.stream().map(GpsInsertVo::getAdGroupName).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(gpsAdGroupList)) {
                    return result;
                }
                List<AmazonAdProduct> gpsAdProductList = amazonAdProductDao.getGpsProduct(gpsVoList.get(0).getPuid(), gpsCampaignList.stream().map(GpsCampaign::getCampaignId).collect(Collectors.toList()),
                        gpsAdGroupList.stream().map(GpsAdGroup::getAdGroupId).collect(Collectors.toList()),
                                gpsVoList.stream().map(GpsInsertVo::getSku).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(gpsAdProductList)) {
                    return result;
                }
                Map<String, GpsCampaign> gpsCampaignMap = gpsCampaignList.stream().collect(Collectors.toMap(GpsCampaign::getCampaignName, Function.identity(), (e1,e2) -> e2));
                gpsVoList.forEach(e->{
                    if (gpsCampaignMap.containsKey(e.getCampaignName())) {
                        e.setCampaignId(gpsCampaignMap.get(e.getCampaignName()).getCampaignId());
                    }
                });
                Map<String, GpsAdGroup> gpsAdGroupMap = gpsAdGroupList.stream().collect(Collectors.toMap(e->e.getCampaignId()+e.getAdGroupName(), Function.identity(), (e1,e2) -> e2));
                gpsVoList.forEach(e->{
                    if (gpsAdGroupMap.containsKey(e.getCampaignId()+e.getAdGroupName())) {
                        e.setAdGroupId(gpsAdGroupMap.get(e.getCampaignId()+e.getAdGroupName()).getAdGroupId());
                    }
                });
                Map<String, GpsInsertVo> gpsInsertVoMap = gpsVoList.stream().collect(Collectors.toMap(e->e.getAdGroupId()+e.getSku(), Function.identity(), (e1,e2) -> e2));
                gpsAdProductList.forEach(e->{
                    if (gpsInsertVoMap.containsKey(e.getAdGroupId()+e.getSku())) {
                        e.setRowAdId(gpsInsertVoMap.get(e.getAdGroupId()+e.getSku()).getAdId());
                    }
                });
                amazonAdProductDao.insertOnDuplicateKeyUpdateGps(gpsVoList.get(0).getPuid(), gpsAdProductList);
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("广告GPS修改adId信息异常");
            log.error("广告GPS修改adId信息异常:",e);
        }
        return result;
    }

    @Override
    public Result<List<GpsVo>> queryCampaignAndGroupByAdId(GpsParam param) {
        Result<List<GpsVo>> result = new Result<>();
        try {
            List<GpsVo> gpsVoList = Lists.newArrayList();
            List<AmazonAdProduct> amazonAdProductList = new ArrayList<>();
            param.getGpsAsinAndAdIdList().forEach(e->{
                AmazonAdProduct amazonAdProduct = amazonAdProductDao.getProductList(param.getPuid(),e.getAsin(),e.getAdId());
                if (amazonAdProduct != null) {
                    amazonAdProductList.add(amazonAdProduct);
                }
            });
            if (CollectionUtils.isNotEmpty(amazonAdProductList)) {
                amazonAdProductList.forEach(amazonAdProduct->{
                    GpsVo gpsVo = new GpsVo();
                    gpsVo.setShopId(amazonAdProduct.getShopId());
                    gpsVo.setAdGroupId(amazonAdProduct.getAdGroupId());
                    gpsVo.setCampaignId(amazonAdProduct.getCampaignId());
                    gpsVo.setAsin(amazonAdProduct.getAsin());
                    gpsVo.setAdId(amazonAdProduct.getRowAdId());
                    gpsVo.setAdType(AdSyncRecord.AdTypeEnum.SP.name().toLowerCase());
                    if (StringUtils.isNotBlank(amazonAdProduct.getCampaignId())) {
                        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(amazonAdProduct.getPuid(), amazonAdProduct.getShopId(), amazonAdProduct.getCampaignId());
                        if (amazonAdCampaignAll != null) {
                            gpsVo.setCampaignName(amazonAdCampaignAll.getName());
                        }
                    }
                    if (StringUtils.isNotBlank(amazonAdProduct.getAdGroupId())) {
                        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(amazonAdProduct.getPuid(), amazonAdProduct.getShopId(), amazonAdProduct.getAdGroupId());
                        if (amazonAdGroup != null) {
                            gpsVo.setAdGroupName(amazonAdGroup.getName());
                        }
                    }
                    gpsVoList.add(gpsVo);
                });
            }
            result.setData(gpsVoList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("广告GPS查询asin信息异常");
            log.error("广告GPS查询asin信息异常:",e);
        }
        return result;
    }
}
