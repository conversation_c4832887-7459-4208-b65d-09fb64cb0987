package com.meiyunji.sponsored.service.kafka.consumer;

import com.amazon.advertising.mode.Adjustment;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleTypeEnum;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskDao;
import com.meiyunji.sponsored.service.autoRuleTask.dao.AutoRuleTaskRecordDao;
import com.meiyunji.sponsored.service.autoRuleTask.enums.TaskItemType;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecord;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.kafka.message.AutoRuleProcessTaskMessage;
import com.meiyunji.sponsored.service.kafka.service.AutoRuleProcessTaskMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @date: 2023/05/23 10:33
 * @describe: 定时任务结果监听类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.advertise-auto-rule-process-task.enabled", havingValue = "true")
public class AutoRuleProcessTaskMessageConsumer {

    @Autowired
    private AutoRuleTaskDao autoRuleTaskDao;
    @Autowired
    private AutoRuleTaskRecordDao autoRuleTaskRecordDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AutoRuleProcessTaskMessageService autoRuleProcessTaskMessageService;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @KafkaListener(topics = "${kafka.consumers.advertise-auto-rule-process-task.topic}", containerFactory = "advertiseAutoRuleProcessTaskConsumerFactory")
    public void consumer(ConsumerRecord<?, byte[]> record) throws Exception {
        try {
            if (record == null || record.value() == null) {
                log.error("advertise-auto-rule-process-task record is null");
                return;
            }
            AutoRuleProcessTaskMessage message = JSONUtil.jsonToObject(new String(record.value()), AutoRuleProcessTaskMessage.class);
            if (message == null) {
                log.error("advertise-auto-rule-process-task message is null");
                return;
            }
            String lockKey = String.format(RedisConstant.SPONSORED_AUTO_RULE_PROCESS_TASK_KEY, message.getTemplateId());
            RLock lock = redissonClient.getLock(lockKey);
            boolean b = lock.tryLock();
            if (!b) {
                log.info("任务执行中, puid:{}, shopId:{}, taskId:{}, templateId:{},", message.getPuid(), message.getShopId(), message.getTaskId(), message.getTemplateId());
                return;
            }
            try {
                List<AutoRuleTask> taskList = autoRuleTaskDao.queryListByTemplateIdAsc(message.getPuid(), message.getShopId(), message.getTemplateId());
                if (CollectionUtils.isEmpty(taskList)) {
                    return;
                }
                for (AutoRuleTask task : taskList) {
                    Long id = 0L;
                    while (true) {
                        //指针分页取法
                        List<AutoRuleTaskRecord> recordList = autoRuleTaskRecordDao.getListByTaskId(message.getPuid(), message.getShopId(), id, task.getId());
                        if (CollectionUtils.isEmpty(recordList)) {
                            break;
                        }
                        processPlacementTopBidRatio(recordList);
                        id = recordList.get(recordList.size() - 1).getId();
                        //自动化规则：0 全部更新 1 批量更新 2 批量删除 3 全部删除 7 批量修改原始竞价 12 批量暂停 13 批量开启 14 全部暂停 15 全部开启
                        //分时：0 全部更新 1 批量更新 2 批量删除 3 全部删除 4 批量转移 5 全部转移 6 批量应用实时竞价 7 批量修改原始竞价 8 批量应用实时预算
                        //     9 批量修改原始预算 10批量修改原始默认竞价值 11 批量查询实时竞价 12 批量暂停 13 批量开启 14 全部暂停 15 全部开启
                        switch (task.getTaskAction()) {
                            case 0:
                                autoRuleProcessTaskMessageService.updateTask(task, recordList, false);
                                break;
                            case 1:
                                autoRuleProcessTaskMessageService.updateTask(task, recordList, false);
                                break;
                            case 2:
                                autoRuleProcessTaskMessageService.deleteTask(task, recordList, false);
                                break;
                            case 3:
                                autoRuleProcessTaskMessageService.deleteTask(task, recordList, false);
                                break;
                            case 4:

                                break;
                            case 5:

                                break;
                            case 6:
                                autoRuleProcessTaskMessageService.updateTask(task, recordList, false);
                                break;
                            case 7:
                                autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, false);
                                break;
                            case 8:
                                autoRuleProcessTaskMessageService.updateTask(task, recordList, false);
                                break;
                            case 9:
                                autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, false);
                                break;
                            case 10:
                                autoRuleProcessTaskMessageService.updateAutoRuleOriginalValue(task, recordList, false);
                                break;
                            case 12:
                                autoRuleProcessTaskMessageService.updateState(task, recordList, false, AutoRuleEnableStatusEnum.DISABLED.getCode());
                                break;
                            case 13:
                                autoRuleProcessTaskMessageService.updateState(task, recordList, false, AutoRuleEnableStatusEnum.ENABLED.getCode());
                                break;
                            case 14:
                                autoRuleProcessTaskMessageService.updateState(task, recordList, false, AutoRuleEnableStatusEnum.DISABLED.getCode());
                                break;
                            case 15:
                                autoRuleProcessTaskMessageService.updateState(task, recordList, false, AutoRuleEnableStatusEnum.ENABLED.getCode());
                                break;
                            default:
                                break;
                        }
                    }
                    int errorCount = autoRuleTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), -1);
                    int sucessCount = autoRuleTaskRecordDao.queryCount(task.getPuid(), task.getShopId(), task.getId(), 1);
                    if (errorCount > 0) {
                        task.setState(-1);
                    } else if (sucessCount >= task.getCount()) {
                        task.setState(1);
                    }
                    autoRuleTaskDao.updateStateByPrimaryKey(message.getPuid(), task);
                }
                //手动提交
            } catch (Exception e) {
                log.error("任务处理失败, puid:{}, shopId:{}, taskId:{}, templateId:{},", message.getPuid(), message.getShopId(), message.getTaskId(), message.getTemplateId(), e);
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("advertise auto rule process task kafka message consumer error:",e);
        }
    }

    private void processPlacementTopBidRatio(List<AutoRuleTaskRecord> recordList) {
        try {
            if (CollectionUtils.isEmpty(recordList)) {
                return;
            }
            log.info("processPlacementTopBidRatio recordList itemType:{} size:{}", recordList.get(0).getItemType(), recordList.size());
            if ("KEYWORD_TARGET".equals(TaskItemType.getDesc(recordList.get(0).getItemType()))) {
                List<AutoRuleTaskRecord> list = new ArrayList<>();
                for (AutoRuleTaskRecord ruleTaskRecord : recordList) {
                    if (ruleTaskRecord.getPlacementTopBidRatio() == null) {
                        list.add(ruleTaskRecord);
                    }
                }
                log.info("processPlacementTopBidRatio list:{}", list);
                if (CollectionUtils.isNotEmpty(list)) {
                    List<String> keywordIds = list.stream().map(AutoRuleTaskRecord::getItemId).collect(Collectors.toList());
                    List<AmazonAdKeyword> keywordList = amazonAdKeywordShardingDao.getByKeywordIds(list.get(0).getPuid(), keywordIds);
                    Map<String, AmazonAdKeyword> keywordMap = StreamUtil.toMap(keywordList, AmazonAdKeyword::getKeywordId);
                    List<String> campaignIds = keywordList.stream().map(AmazonAdKeyword::getCampaignId).collect(Collectors.toList());
                    List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByCampaignIds(list.get(0).getPuid(), list.get(0).getShopId(), campaignIds);
                    Map<String, AmazonAdCampaignAll> map = StreamUtil.toMap(campaignList, AmazonAdCampaignAll::getCampaignId);

                    for (AutoRuleTaskRecord taskRecord : list) {
                        String keywordId = taskRecord.getItemId();
                        AmazonAdKeyword amazonAdKeyword = keywordMap.get(keywordId);
                        AmazonAdCampaignAll campaignAll = map.get(amazonAdKeyword.getCampaignId());
                        String adjustments = campaignAll.getAdjustments();
                        List<Adjustment> arrays = JSONUtil.jsonToArray(adjustments, Adjustment.class);
                        if (CollectionUtils.isNotEmpty(arrays)) {
                            for (Adjustment array : arrays) {
                                if ("placementTop".equals(array.getPredicate())) {
                                    Double percentage = array.getPercentage();
                                    if (Objects.nonNull(percentage)) {
                                        taskRecord.setPlacementTopBidRatio(percentage.intValue());
                                    }
                                }
                            }
                        }
                        if (Objects.isNull(taskRecord.getPlacementTopBidRatio())) {
                            taskRecord.setPlacementTopBidRatio(0);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("processPlacementTopBidRatio error:", e);
        }
    }
}
