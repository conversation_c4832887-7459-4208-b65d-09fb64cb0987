package com.meiyunji.sponsored.service.enums;

/**
 * 产品可用状态枚举
 * @Author: hejh
 * @Date: 2024/8/2 11:22
 */
public enum ProductEligibilityStatusEnum {

    ELIGIBLE("ELIGIBLE", "符合条件"),
    INELIG<PERSON>LE("IN<PERSON>IG<PERSON><PERSON>", "不符合条件"),
    ELIG<PERSON>LE_WITH_WARNING("ELIGIBLE_WITH_WARNING", "ELIGIBLE_WITH_WARNING"),
    ;
    private String value;
    private String desc;
    ProductEligibilityStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
