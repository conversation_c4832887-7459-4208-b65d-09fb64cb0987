package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignTargetingTypeBO;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdGroupTypeBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-13  10:21
 */

@Service
public class AmazonAdGroupServiceImpl implements IAmazonAdGroupService {

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;


    /**
     * 通过puid和key为shop_id，value为group_id集合的map，获取所有的shop_id下，groupid对应的grouptype的map集合
     *
     * @param puid
     * @param shopGroupMap key为shop_id,value为group_id集合
     * @return
     */
    @Override
    public Map<Integer, Map<String, String>> queryGroupTypeByShopGroupId(Integer puid, Map<Integer, Set<String>> shopGroupMap) {
        Map<Integer, Map<String, String>> map = new HashMap<>(shopGroupMap.size());
        shopGroupMap.forEach((k, v) -> {
            List<List<String>> partitions = Lists.partition(Lists.newArrayList(v), 1000);
            partitions.forEach(x -> {
                List<AmazonAdGroupTypeBO> list = amazonAdGroupDao.getTAdGroupTypeByAdGroupIdList(puid, k, x);
                if (CollectionUtils.isNotEmpty(list)) {
                    Map<String, String> shopMap = map.get(k);
                    if (org.springframework.util.CollectionUtils.isEmpty(shopMap)) {
                        shopMap = new HashMap<>(list.size());
                        for (AmazonAdGroupTypeBO bo : list) {
                            shopMap.put(bo.getAdGroupId(), bo.getAdGroupType());
                        }
                        map.put(k, shopMap);
                    } else {
                        for (AmazonAdGroupTypeBO bo : list) {
                            shopMap.put(bo.getAdGroupId(), bo.getAdGroupType());
                        }
                    }
                }
            });
        });

        return map;
    }
}
