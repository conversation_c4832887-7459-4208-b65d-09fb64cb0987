package com.meiyunji.sponsored.service.export.convert.impl.sd;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportVoExportConvert;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.vo.AdSdAsinReportVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlin<PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.SD_PURCHASED)
public class PurchasedSdReportExportConvert implements ReportVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.SD_PURCHASED;
    }

    public void processExcelDataList(ReportVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdSdAsinReportVo vo = new AdSdAsinReportVo();
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            Date dailyDate = DateUtil.strToDate(reportVo.getCountDate(), "yyyyMMdd");
            vo.setDailyDate(DateUtil.dateToStrWithFormat(dailyDate, "yyyy-MM-dd"));
        }

        vo.setCampaignStartDate(reportVo.getCampaignStartDate());
        vo.setCampaignEndDate(reportVo.getCampaignEndDate());

        vo.setSku(reportVo.getSku());
        vo.setAsin(reportVo.getAsin());
        vo.setOtherAsin(reportVo.getOtherAsin());
        vo.setAdGroupName(reportVo.getAdGroupName());
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setAttributedSales14dOtherSKU(reportVo.getAttributedSales14dOtherSKU());
        vo.setAttributedUnitsOrdered14dOtherSKU(reportVo.getAttributedUnitsOrdered14dOtherSKU());
        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shop.getMarketplaceId());
            if (null != m) {
                vo.setCurrency(m.getCurrencyCode());
            }
        }
        list.add(vo);
    }
}
