package com.meiyunji.sponsored.service.kafka;

import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleConsistencyDto;
import org.springframework.kafka.core.KafkaTemplate;

public class AdStrategyTemplateEnableKafkaProducer {
    private final String topic;
    private final KafkaTemplate<String, String> kafkaTemplate;

    public AdStrategyTemplateEnableKafkaProducer(String topic, KafkaTemplate<String, String> kafkaTemplate) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(Object message) {
        kafkaTemplate.send(topic, JSONUtil.objectToJson(message));
    }

    public void send(Integer puid,Object message) {
        kafkaTemplate.send(topic, puid.toString(), JSONUtil.objectToJson(message));
    }
}
