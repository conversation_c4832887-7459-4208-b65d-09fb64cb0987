package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.bo.KeywordLibMarkupAsinBo;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibMarkupAsin;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibAsinPageListQo;

import java.util.List;
import java.util.Map;


/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-10-24  16:06
 */
public interface IAdKeywordLibMarkupAsinDao extends IBaseShardingDao<AdKeywordLibMarkupAsin> {
    /**
     * asin标签下拉列表
     */
    Page<KeywordLibMarkupAsinBo> getAsinPageList(Integer puid, List<Integer> uidList, List<String> marketplaceIdList, KeywordLibAsinPageListQo qo);

    /**
     * 批量更新插入
     * @param list
     * @return
     */
    int batchInsert(List<AdKeywordLibMarkupAsin> list);

    /**
     * 根据关键词库id获取每个关键词标记ASIN的数量
     * @param puid
     * @param uid
     * @param keywordsLibIdList
     * @return
     */
    List<AdKeywordLibMarkupAsin> getListByKeywordsLibId(Integer puid, List<Integer> uid, List<Long> keywordsLibIdList);

    /**
     * 取消标记ASIN
     */
    int deleteByKeywordsLibIdAndAsin(Integer puid, List<Integer> uid, List<Long> keywordsLibId, String marketplaceId, String asin);

    /**
     * 根据关键词库keywordsLibId列表获取ASIN标签
     */
    List<AdKeywordLibMarkupAsin> listByKeywordsLibIdsList(Integer puid, List<Integer> uid, List<Long> keywordsLibIdList);

    /**
     * 根据ASIN查出关联关键词库id
     */
    List<Long> keywordsLibIdListByAsin(Integer puid, List<Integer> uid, String marketplaceId, String asin);

    int deleteByKeywordsLibId(Integer puid, Integer uid, List<Long> keywordsLibId);
}
