package com.meiyunji.sponsored.service.autoRule.vo;

import lombok.Data;

import java.util.List;

@Data
public class AdKeywordCardAutoRuleParam {
    //puid
    private Integer puid;
    //店铺id
    private Integer shopId;
    //投放有效状态
    private String state;
    //广告组
    private List<String> groupIdList;
    //广告活动
    private List<String> campaignIdList;
    //投放名称
    private String keywordText;
    //当前页数
    private Integer pageNo;
    //每页查询条数
    private Integer pageSize;
    //站点
    private String marketplaceId;
    //匹配类型
    private String matchType;
    //模板id
    private Long templateId;
    //asin
    private List<String> skuList;
    //服务状态
    private List<String> servingStatusList;
    //服务状态
    private List<String> portfolioIdList;
    //traceId
    private String traceId;
    //竞价策略
    private String strategyType;
}
