package com.meiyunji.sponsored.service.syncTask.helper;

import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sellfox.aadas.types.exception.AmazonDuplicateAdItemIdException;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import com.meiyunji.sponsored.service.strategy.vo.TargetRuleVo;
import com.meiyunji.sponsored.service.strategyTask.enums.ItemType;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-03-11  18:03
 */
@Component
@Slf4j
public class AdvertiseStrategyManagementTargetHelper {
    @Autowired
    private AdvertiseStrategyStatusSequenceDao advertiseStrategyStatusSequenceDao;
    @Autowired
    private AdvertiseStrategyScheduleSequenceDao advertiseStrategyScheduleSequenceDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyStatusDeleteDao advertiseStrategyStatusDeleteDao;
    @Autowired
    private AdvertiseStrategyScheduleDao advertiseStrategyScheduleDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AadasApiFactory aadasApiFactory;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    public void spKeywordSubmitStrategy (AdvertiseStrategyAdGroup advertiseStrategyAdGroup, List<AmazonAdKeyword> amazonAdKeywordList) {
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), "TARGET", amazonAdKeywordList.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList()));
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1, e2)->e1));
        }
        List<Long> ids = advertiseStrategyStatusSequenceDao.batchGenId(amazonAdKeywordList.size());
        for (int s = 0; s<amazonAdKeywordList.size(); s++) {
            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordList.get(s);
            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
            AdvertiseStrategyStatus strategyStatus = new AdvertiseStrategyStatus();
            Long taskId = ids.get(s);
            strategyStatus.setId(taskId);
            strategyStatus.setTaskId(taskId);
            strategyStatus.setPuid(amazonAdKeyword.getPuid());
            strategyStatus.setShopId(amazonAdKeyword.getShopId());
            strategyStatus.setMarketplaceId(amazonAdKeyword.getMarketplaceId());
            strategyStatus.setAdType(AdType.SP.name());
            strategyStatus.setVersion(advertiseStrategyAdGroup.getVersion());
            strategyStatus.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
            strategyStatus.setType(advertiseStrategyAdGroup.getType());
            strategyStatus.setCampaignId(amazonAdKeyword.getCampaignId());
            strategyStatus.setAdGroupId(amazonAdKeyword.getAdGroupId());
            strategyStatus.setTargetType("keywordTarget");
            strategyStatus.setTargetName(amazonAdKeyword.getKeywordText());
            strategyStatus.setItemId(amazonAdKeyword.getKeywordId());
            strategyStatus.setItemType(ItemType.TARGET.name());
            strategyStatus.setRule(advertiseStrategyAdGroup.getRule());
            strategyStatus.setStatus("ENABLED");
            strategyStatus.setAddWayType("AD_GROUP_TARGET");
            strategyStatus.setStrategyAdGroupId(advertiseStrategyAdGroup.getId());
            strategyStatus.setIsDisengage(0);
            OriginValueVo originValueVo = new OriginValueVo();
            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(advertiseStrategyAdGroup.getRule(), TargetRuleVo.class);
            if (amazonAdKeyword.getBid() != null) {
                originValueVo.setBiddingValue(BigDecimal.valueOf(amazonAdKeyword.getBid()));
            } else {
                originValueVo.setBiddingValue(advertiseStrategyAdGroup.getDefaultBid());
            }
            String originValueJson = JSONUtil.objectToJson(originValueVo);
            strategyStatus.setOriginValue(originValueJson);
            for (int i = 0; i < ruleVoList.size(); i++) {
                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                advertiseStrategySchedule.setPuid(amazonAdKeyword.getPuid());
                advertiseStrategySchedule.setShopId(amazonAdKeyword.getShopId());
                advertiseStrategySchedule.setMarketplaceId(amazonAdKeyword.getMarketplaceId());
                advertiseStrategySchedule.setTaskId(taskId);
                advertiseStrategySchedule.setItemType(ItemType.TARGET.name());
                advertiseStrategySchedule.setType(advertiseStrategyAdGroup.getType());
                advertiseStrategySchedule.setAdType(AdType.SP.name());
                advertiseStrategySchedule.setCampaignId(amazonAdKeyword.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonAdKeyword.getAdGroupId());
                advertiseStrategySchedule.setTargetType("keywordTarget");
                advertiseStrategySchedule.setItemId(amazonAdKeyword.getKeywordId());
                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                advertiseStrategySchedule.setTargetName(amazonAdKeyword.getKeywordText());
                advertiseStrategySchedule.setCampaignId(amazonAdKeyword.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonAdKeyword.getAdGroupId());
                BigDecimal biddingValue = null;
                if (amazonAdKeyword.getBid() != null) {
                    biddingValue = BigDecimal.valueOf(amazonAdKeyword.getBid());
                } else {
                    biddingValue = advertiseStrategyAdGroup.getDefaultBid();
                }
                if (targetRuleVo.getBiddingType() == 0) {
                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 1) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 2) {
                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 3) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 4) {
                    biddingValue = targetRuleVo.getBiddingValue();
                }
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                            biddingValue = targetRuleVo.getBiddingMaxValue();
                        }
                    }
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                            biddingValue = targetRuleVo.getBiddingMinValue();
                        }
                    }
                }
                OriginValueVo newValueVo = new OriginValueVo();
                newValueVo.setBiddingValue(biddingValue);
                String newValueJson = JSONUtil.objectToJson(newValueVo);
                advertiseStrategySchedule.setNewValue(newValueJson);
                advertiseStrategySchedule.setOriginValue(originValueJson);
                list.add(advertiseStrategySchedule);
            }

            if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(amazonAdKeyword.getKeywordId())) {
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(amazonAdKeyword.getKeywordId());
                TaskTimeType timeType = null;
                if ("keywordTarget".equals(advertiseStrategyStatus.getTargetType())) {
                    timeType = TaskTimeType.keywordBid;
                } else {
                    timeType = TaskTimeType.targetBid;
                }
                //任务调度服务删除策略
                try {
                    aadasApiFactory.getStrategyApi(timeType).removeSchedule(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), false);
                } catch (Exception exception) {
                    //记录错误信息
                    log.error("puid={} shopId={} 分时调价删除组受控对象异常", advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), exception);
                    continue;
                }
                //删除组受控投放数据
                advertiseStrategyStatusDao.deleteStrategyStatus(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getId(), advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(advertiseStrategyStatus.getPuid(), scheduleIds);
                }
                amazonAdKeywordDaoRoutingService.updatePricing(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(),
                        advertiseStrategyStatus.getItemId(), 0, 0, amazonAdKeyword.getUpdateId());
            }

            advertiseStrategyStatusDao.insetStrategyStatus(amazonAdKeyword.getPuid(), strategyStatus);
            List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
            for (int i = 0; i<longIdList.size(); i++) {
                list.get(i).setId(longIdList.get(i));
            }
            advertiseStrategyScheduleDao.batchInsert(amazonAdKeyword.getPuid(), list);
            try {
                //推送数据到aadas调度服务
                aadasApiFactory.getStrategyApi(TaskTimeType.adGroupKeyword).setSchedule(taskId, strategyStatus.getTemplateId(), list, false);
            } catch (Exception e) {
                if (e instanceof AmazonDuplicateAdItemIdException) {
                    log.info("puid:{} shopId:{} 分时调价提交异常:", amazonAdKeyword.getPuid(), strategyStatus.getShopId(), e);
                } else {
                    log.error("puid:{} shopId:{} 分时调价提交异常:", amazonAdKeyword.getPuid(), strategyStatus.getShopId(), e);
                }
                //aadas服务异常删除对应的数据
                removeArchiveRecord(amazonAdKeyword.getPuid(),strategyStatus.getShopId(),strategyStatus.getTaskId(),strategyStatus.getItemType());
            }
        }
    }

    public void spTargetSubmitStrategy (AdvertiseStrategyAdGroup advertiseStrategyAdGroup, List<AmazonAdTargeting> amazonAdTargetingList) {
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), "TARGET", amazonAdTargetingList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()));
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1,e2)->e1));
        }
        List<Long> ids = advertiseStrategyStatusSequenceDao.batchGenId(amazonAdTargetingList.size());
        for (int s = 0; s<amazonAdTargetingList.size(); s++) {
            AmazonAdTargeting amazonAdTargeting = amazonAdTargetingList.get(s);
            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
            AdvertiseStrategyStatus strategyStatus = new AdvertiseStrategyStatus();
            Long taskId = ids.get(s);
            strategyStatus.setId(taskId);
            strategyStatus.setTaskId(taskId);
            strategyStatus.setPuid(amazonAdTargeting.getPuid());
            strategyStatus.setShopId(amazonAdTargeting.getShopId());
            strategyStatus.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
            strategyStatus.setAdType(AdType.SP.name());
            strategyStatus.setVersion(advertiseStrategyAdGroup.getVersion());
            strategyStatus.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
            strategyStatus.setType(advertiseStrategyAdGroup.getType());
            strategyStatus.setCampaignId(amazonAdTargeting.getCampaignId());
            strategyStatus.setAdGroupId(amazonAdTargeting.getAdGroupId());
            strategyStatus.setAddWayType("AD_GROUP_TARGET");
            if ("auto".equals(amazonAdTargeting.getType())) {
                strategyStatus.setTargetType("autoTarget");
            } else {
                strategyStatus.setTargetType("productTarget");
            }
            strategyStatus.setTargetName(amazonAdTargeting.getTargetingValue());
            strategyStatus.setItemId(amazonAdTargeting.getTargetId());
            strategyStatus.setItemType(ItemType.TARGET.name());
            strategyStatus.setRule(advertiseStrategyAdGroup.getRule());
            strategyStatus.setStatus("ENABLED");
            strategyStatus.setStrategyAdGroupId(advertiseStrategyAdGroup.getId());
            strategyStatus.setIsDisengage(0);
            OriginValueVo originValueVo = new OriginValueVo();
            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(advertiseStrategyAdGroup.getRule(), TargetRuleVo.class);
            if (amazonAdTargeting.getBid() != null) {
                originValueVo.setBiddingValue(BigDecimal.valueOf(amazonAdTargeting.getBid()));
            } else {
                originValueVo.setBiddingValue(advertiseStrategyAdGroup.getDefaultBid());
            }
            String originValueJson = JSONUtil.objectToJson(originValueVo);
            strategyStatus.setOriginValue(originValueJson);
            for (int i = 0; i < ruleVoList.size(); i++) {
                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                advertiseStrategySchedule.setPuid(amazonAdTargeting.getPuid());
                advertiseStrategySchedule.setShopId(amazonAdTargeting.getShopId());
                advertiseStrategySchedule.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
                advertiseStrategySchedule.setTaskId(taskId);
                advertiseStrategySchedule.setItemType(ItemType.TARGET.name());
                advertiseStrategySchedule.setType(advertiseStrategyAdGroup.getType());
                advertiseStrategySchedule.setAdType(AdType.SP.name());
                advertiseStrategySchedule.setCampaignId(amazonAdTargeting.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonAdTargeting.getAdGroupId());
                if ("auto".equals(amazonAdTargeting.getType())) {
                    advertiseStrategySchedule.setTargetType("autoTarget");
                } else {
                    advertiseStrategySchedule.setTargetType("productTarget");
                }
                advertiseStrategySchedule.setItemId(amazonAdTargeting.getTargetId());
                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                advertiseStrategySchedule.setTargetName(amazonAdTargeting.getTargetId());
                advertiseStrategySchedule.setCampaignId(amazonAdTargeting.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonAdTargeting.getAdGroupId());
                BigDecimal biddingValue = null;
                if (amazonAdTargeting.getBid() != null) {
                    biddingValue = BigDecimal.valueOf(amazonAdTargeting.getBid());
                } else {
                    biddingValue = advertiseStrategyAdGroup.getDefaultBid();
                }
                if (targetRuleVo.getBiddingType() == 0) {
                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 1) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 2) {
                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 3) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 4) {
                    biddingValue = targetRuleVo.getBiddingValue();
                }
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                            biddingValue = targetRuleVo.getBiddingMaxValue();
                        }
                    }
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                            biddingValue = targetRuleVo.getBiddingMinValue();
                        }
                    }
                }
                OriginValueVo newValueVo = new OriginValueVo();
                newValueVo.setBiddingValue(biddingValue);
                String newValueJson = JSONUtil.objectToJson(newValueVo);
                advertiseStrategySchedule.setNewValue(newValueJson);
                advertiseStrategySchedule.setOriginValue(originValueJson);
                list.add(advertiseStrategySchedule);
            }

            if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(amazonAdTargeting.getTargetId())) {
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(amazonAdTargeting.getTargetId());
                TaskTimeType timeType = null;
                if ("keywordTarget".equals(advertiseStrategyStatus.getTargetType())) {
                    timeType = TaskTimeType.keywordBid;
                } else {
                    timeType = TaskTimeType.targetBid;
                }
                //任务调度服务删除策略
                try {
                    aadasApiFactory.getStrategyApi(timeType).removeSchedule(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), false);
                } catch (Exception exception) {
                    //记录错误信息
                    log.error("puid={} shopId={} 分时调价删除组受控对象异常", advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), exception);
                    continue;
                }
                //删除组受控投放数据
                advertiseStrategyStatusDao.deleteStrategyStatus(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getId(), advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(advertiseStrategyStatus.getPuid(), scheduleIds);
                }
                amazonAdTargetDaoRoutingService.updatePricing(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(),
                        advertiseStrategyStatus.getItemId(), 0, 0, amazonAdTargeting.getUpdateId());
            }
            advertiseStrategyStatusDao.insetStrategyStatus(amazonAdTargeting.getPuid(), strategyStatus);
            List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
            for (int i = 0; i<longIdList.size(); i++) {
                list.get(i).setId(longIdList.get(i));
            }
            advertiseStrategyScheduleDao.batchInsert(amazonAdTargeting.getPuid(), list);
            try {
                //推送数据到aadas调度服务
                aadasApiFactory.getStrategyApi(TaskTimeType.adGroupTarget).setSchedule(taskId, strategyStatus.getTemplateId(), list, false);
            } catch (Exception e) {
                if (e instanceof AmazonDuplicateAdItemIdException) {
                    log.info("puid:{} shopId:{} 分时调价提交异常:", amazonAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                } else {
                    log.error("puid:{} shopId:{} 分时调价提交异常:", amazonAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                }
                //aadas服务异常删除对应的数据
                removeArchiveRecord(amazonAdTargeting.getPuid(),strategyStatus.getShopId(),strategyStatus.getTaskId(),strategyStatus.getItemType());
            }
        }
    }

    public void sbKeywordSubmitStrategy (AdvertiseStrategyAdGroup advertiseStrategyAdGroup, List<AmazonSbAdKeyword> amazonSbAdKeywordList) {
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), "TARGET", amazonSbAdKeywordList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1,e2)->e1));
        }
        List<Long> ids = advertiseStrategyStatusSequenceDao.batchGenId(amazonSbAdKeywordList.size());
        for (int s = 0; s<amazonSbAdKeywordList.size(); s++) {
            AmazonSbAdKeyword amazonSbAdKeyword = amazonSbAdKeywordList.get(s);
            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
            AdvertiseStrategyStatus strategyStatus = new AdvertiseStrategyStatus();
            Long taskId = ids.get(s);
            strategyStatus.setId(taskId);
            strategyStatus.setTaskId(taskId);
            strategyStatus.setPuid(amazonSbAdKeyword.getPuid());
            strategyStatus.setShopId(amazonSbAdKeyword.getShopId());
            strategyStatus.setMarketplaceId(amazonSbAdKeyword.getMarketplaceId());
            strategyStatus.setAdType(AdType.SB.name());
            strategyStatus.setVersion(advertiseStrategyAdGroup.getVersion());
            strategyStatus.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
            strategyStatus.setType(advertiseStrategyAdGroup.getType());
            strategyStatus.setCampaignId(amazonSbAdKeyword.getCampaignId());
            strategyStatus.setAdGroupId(amazonSbAdKeyword.getAdGroupId());
            strategyStatus.setTargetType("keywordTarget");
            strategyStatus.setTargetName(amazonSbAdKeyword.getKeywordText());
            strategyStatus.setItemId(amazonSbAdKeyword.getKeywordId());
            strategyStatus.setItemType(ItemType.TARGET.name());
            strategyStatus.setRule(advertiseStrategyAdGroup.getRule());
            strategyStatus.setStatus("ENABLED");
            strategyStatus.setAddWayType("AD_GROUP_TARGET");
            strategyStatus.setStrategyAdGroupId(advertiseStrategyAdGroup.getId());
            strategyStatus.setIsDisengage(0);
            OriginValueVo originValueVo = new OriginValueVo();
            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(advertiseStrategyAdGroup.getRule(), TargetRuleVo.class);
            if (amazonSbAdKeyword.getBid() != null) {
                originValueVo.setBiddingValue(amazonSbAdKeyword.getBid());
            }
            String originValueJson = JSONUtil.objectToJson(originValueVo);
            strategyStatus.setOriginValue(originValueJson);
            for (int i = 0; i < ruleVoList.size(); i++) {
                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                advertiseStrategySchedule.setPuid(amazonSbAdKeyword.getPuid());
                advertiseStrategySchedule.setShopId(amazonSbAdKeyword.getShopId());
                advertiseStrategySchedule.setMarketplaceId(amazonSbAdKeyword.getMarketplaceId());
                advertiseStrategySchedule.setTaskId(taskId);
                advertiseStrategySchedule.setItemType(ItemType.TARGET.name());
                advertiseStrategySchedule.setType(advertiseStrategyAdGroup.getType());
                advertiseStrategySchedule.setAdType(AdType.SB.name());
                advertiseStrategySchedule.setCampaignId(amazonSbAdKeyword.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonSbAdKeyword.getAdGroupId());
                advertiseStrategySchedule.setTargetType("keywordTarget");
                advertiseStrategySchedule.setItemId(amazonSbAdKeyword.getKeywordId());
                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                advertiseStrategySchedule.setTargetName(amazonSbAdKeyword.getKeywordText());
                advertiseStrategySchedule.setCampaignId(amazonSbAdKeyword.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonSbAdKeyword.getAdGroupId());
                BigDecimal biddingValue = amazonSbAdKeyword.getBid();
                if (targetRuleVo.getBiddingType() == 0) {
                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 1) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 2) {
                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 3) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 4) {
                    biddingValue = targetRuleVo.getBiddingValue();
                }
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                            biddingValue = targetRuleVo.getBiddingMaxValue();
                        }
                    }
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                            biddingValue = targetRuleVo.getBiddingMinValue();
                        }
                    }
                }
                OriginValueVo newValueVo = new OriginValueVo();
                newValueVo.setBiddingValue(biddingValue);
                String newValueJson = JSONUtil.objectToJson(newValueVo);
                advertiseStrategySchedule.setNewValue(newValueJson);
                advertiseStrategySchedule.setOriginValue(originValueJson);
                list.add(advertiseStrategySchedule);
            }
            if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(amazonSbAdKeyword.getKeywordId())) {
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(amazonSbAdKeyword.getKeywordId());
                TaskTimeType timeType = null;
                if ("keywordTarget".equals(advertiseStrategyStatus.getTargetType())) {
                    timeType = TaskTimeType.keywordBid;
                } else {
                    timeType = TaskTimeType.targetBid;
                }
                //任务调度服务删除策略
                try {
                    aadasApiFactory.getStrategyApi(timeType).removeSchedule(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), false);
                } catch (Exception exception) {
                    //记录错误信息
                    log.error("puid={} shopId={} 分时调价删除组受控对象异常", advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), exception);
                    continue;
                }
                //删除组受控投放数据
                advertiseStrategyStatusDao.deleteStrategyStatus(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getId(), advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(advertiseStrategyStatus.getPuid(), scheduleIds);
                }
                amazonSbAdKeywordDao.updatePricing(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(),
                        advertiseStrategyStatus.getItemId(), 0, 0, amazonSbAdKeyword.getUpdateId());
            }
            advertiseStrategyStatusDao.insetStrategyStatus(amazonSbAdKeyword.getPuid(), strategyStatus);
            List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
            for (int i = 0; i<longIdList.size(); i++) {
                list.get(i).setId(longIdList.get(i));
            }
            advertiseStrategyScheduleDao.batchInsert(amazonSbAdKeyword.getPuid(), list);
            try {
                //推送数据到aadas调度服务
                aadasApiFactory.getStrategyApi(TaskTimeType.adGroupKeyword).setSchedule(taskId, strategyStatus.getTemplateId(), list, false);
            } catch (Exception e) {
                if (e instanceof AmazonDuplicateAdItemIdException) {
                    log.info("puid:{} shopId:{} 分时调价提交异常:", amazonSbAdKeyword.getPuid(), strategyStatus.getShopId(), e);
                } else {
                    log.error("puid:{} shopId:{} 分时调价提交异常:", amazonSbAdKeyword.getPuid(), strategyStatus.getShopId(), e);
                }
                //aadas服务异常删除对应的数据
                removeArchiveRecord(amazonSbAdKeyword.getPuid(),strategyStatus.getShopId(),strategyStatus.getTaskId(),strategyStatus.getItemType());
            }
        }
    }

    public void sbTargetSubmitStrategy(AdvertiseStrategyAdGroup advertiseStrategyAdGroup, List<AmazonSbAdTargeting> amazonSbAdTargetingList) {
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), "TARGET", amazonSbAdTargetingList.stream().map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList()));
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1,e2)->e1));
        }
        List<Long> ids = advertiseStrategyStatusSequenceDao.batchGenId(amazonSbAdTargetingList.size());
        for (int s = 0; s<amazonSbAdTargetingList.size(); s++) {
            AmazonSbAdTargeting amazonSbAdTargeting = amazonSbAdTargetingList.get(s);
            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
            AdvertiseStrategyStatus strategyStatus = new AdvertiseStrategyStatus();
            Long taskId = ids.get(s);
            strategyStatus.setId(taskId);
            strategyStatus.setTaskId(taskId);
            strategyStatus.setPuid(amazonSbAdTargeting.getPuid());
            strategyStatus.setShopId(amazonSbAdTargeting.getShopId());
            strategyStatus.setMarketplaceId(amazonSbAdTargeting.getMarketplaceId());
            strategyStatus.setAdType(AdType.SB.name());
            strategyStatus.setVersion(advertiseStrategyAdGroup.getVersion());
            strategyStatus.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
            strategyStatus.setType(advertiseStrategyAdGroup.getType());
            strategyStatus.setCampaignId(amazonSbAdTargeting.getCampaignId());
            strategyStatus.setAdGroupId(amazonSbAdTargeting.getAdGroupId());
            strategyStatus.setTargetType("productTarget");
            strategyStatus.setTargetName(amazonSbAdTargeting.getTargetText());
            strategyStatus.setItemId(amazonSbAdTargeting.getTargetId());
            strategyStatus.setItemType(ItemType.TARGET.name());
            strategyStatus.setRule(advertiseStrategyAdGroup.getRule());
            strategyStatus.setStatus("ENABLED");
            strategyStatus.setAddWayType("AD_GROUP_TARGET");
            strategyStatus.setStrategyAdGroupId(advertiseStrategyAdGroup.getId());
            strategyStatus.setIsDisengage(0);
            OriginValueVo originValueVo = new OriginValueVo();
            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(advertiseStrategyAdGroup.getRule(), TargetRuleVo.class);
            if (amazonSbAdTargeting.getBid() != null) {
                originValueVo.setBiddingValue(amazonSbAdTargeting.getBid());
            }
            String originValueJson = JSONUtil.objectToJson(originValueVo);
            strategyStatus.setOriginValue(originValueJson);
            for (int i = 0; i < ruleVoList.size(); i++) {
                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                advertiseStrategySchedule.setPuid(amazonSbAdTargeting.getPuid());
                advertiseStrategySchedule.setShopId(amazonSbAdTargeting.getShopId());
                advertiseStrategySchedule.setMarketplaceId(amazonSbAdTargeting.getMarketplaceId());
                advertiseStrategySchedule.setTaskId(taskId);
                advertiseStrategySchedule.setItemType(ItemType.TARGET.name());
                advertiseStrategySchedule.setType(advertiseStrategyAdGroup.getType());
                advertiseStrategySchedule.setAdType(AdType.SB.name());
                advertiseStrategySchedule.setCampaignId(amazonSbAdTargeting.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonSbAdTargeting.getAdGroupId());
                advertiseStrategySchedule.setTargetType("keywordTarget");
                advertiseStrategySchedule.setItemId(amazonSbAdTargeting.getTargetId());
                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                advertiseStrategySchedule.setTargetName(amazonSbAdTargeting.getTargetText());
                advertiseStrategySchedule.setCampaignId(amazonSbAdTargeting.getCampaignId());
                advertiseStrategySchedule.setAdGroupId(amazonSbAdTargeting.getAdGroupId());
                BigDecimal biddingValue = amazonSbAdTargeting.getBid();
                if (targetRuleVo.getBiddingType() == 0) {
                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 1) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 2) {
                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 3) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 4) {
                    biddingValue = targetRuleVo.getBiddingValue();
                }
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                            biddingValue = targetRuleVo.getBiddingMaxValue();
                        }
                    }
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                            biddingValue = targetRuleVo.getBiddingMinValue();
                        }
                    }
                }
                OriginValueVo newValueVo = new OriginValueVo();
                newValueVo.setBiddingValue(biddingValue);
                String newValueJson = JSONUtil.objectToJson(newValueVo);
                advertiseStrategySchedule.setNewValue(newValueJson);
                advertiseStrategySchedule.setOriginValue(originValueJson);
                list.add(advertiseStrategySchedule);
            }
            if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(amazonSbAdTargeting.getTargetId())) {
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(amazonSbAdTargeting.getTargetId());
                TaskTimeType timeType = null;
                if ("keywordTarget".equals(advertiseStrategyStatus.getTargetType())) {
                    timeType = TaskTimeType.keywordBid;
                } else {
                    timeType = TaskTimeType.targetBid;
                }
                //任务调度服务删除策略
                try {
                    aadasApiFactory.getStrategyApi(timeType).removeSchedule(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), false);
                } catch (Exception exception) {
                    //记录错误信息
                    log.error("puid={} shopId={} 分时调价删除组受控对象异常", advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), exception);
                    continue;
                }
                //删除组受控投放数据
                advertiseStrategyStatusDao.deleteStrategyStatus(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getId(), advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(advertiseStrategyStatus.getPuid(), scheduleIds);
                }
                amazonSbAdTargetingDao.updatePricing(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(),
                        advertiseStrategyStatus.getItemId(), 0, 0, amazonSbAdTargeting.getUpdateId());
            }
            advertiseStrategyStatusDao.insetStrategyStatus(amazonSbAdTargeting.getPuid(), strategyStatus);
            List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
            for (int i = 0; i<longIdList.size(); i++) {
                list.get(i).setId(longIdList.get(i));
            }
            advertiseStrategyScheduleDao.batchInsert(amazonSbAdTargeting.getPuid(), list);
            try {
                //推送数据到aadas调度服务
                aadasApiFactory.getStrategyApi(TaskTimeType.adGroupTarget).setSchedule(taskId, strategyStatus.getTemplateId(), list, false);
            } catch (Exception e) {
                if (e instanceof AmazonDuplicateAdItemIdException) {
                    log.info("puid:{} shopId:{} 分时调价提交异常:", amazonSbAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                } else {
                    log.error("puid:{} shopId:{} 分时调价提交异常:", amazonSbAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                }
                //aadas服务异常删除对应的数据
                removeArchiveRecord(amazonSbAdTargeting.getPuid(),strategyStatus.getShopId(),strategyStatus.getTaskId(),strategyStatus.getItemType());
            }
        }
    }

    public void sdTargetSubmitStrategy (AdvertiseStrategyAdGroup advertiseStrategyAdGroup,List<AmazonSdAdTargeting> amazonSdAdTargetingList) {
        List<AdvertiseStrategyStatus> advertiseStrategyStatusList = advertiseStrategyStatusDao.getByItemIds(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), "TARGET", amazonSdAdTargetingList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
        Map<String, AdvertiseStrategyStatus> advertiseStrategyStatusMap = null;
        if (CollectionUtils.isNotEmpty(advertiseStrategyStatusList)) {
            advertiseStrategyStatusMap = advertiseStrategyStatusList.stream().collect(Collectors.toMap(AdvertiseStrategyStatus::getItemId, Function.identity(), (e1,e2)->e1));
        }
        List<Long> ids = advertiseStrategyStatusSequenceDao.batchGenId(amazonSdAdTargetingList.size());
        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(advertiseStrategyAdGroup.getPuid(), advertiseStrategyAdGroup.getShopId(), advertiseStrategyAdGroup.getAdGroupId());
        for (int s = 0; s<amazonSdAdTargetingList.size(); s++) {
            AmazonSdAdTargeting amazonSdAdTargeting = amazonSdAdTargetingList.get(s);
            List<AdvertiseStrategySchedule> list = Lists.newArrayList();
            AdvertiseStrategyStatus strategyStatus = new AdvertiseStrategyStatus();
            Long taskId = ids.get(s);
            strategyStatus.setId(taskId);
            strategyStatus.setTaskId(taskId);
            strategyStatus.setPuid(amazonSdAdTargeting.getPuid());
            strategyStatus.setShopId(amazonSdAdTargeting.getShopId());
            strategyStatus.setMarketplaceId(amazonSdAdTargeting.getMarketplaceId());
            strategyStatus.setAdType(AdType.SD.name());
            strategyStatus.setVersion(advertiseStrategyAdGroup.getVersion());
            strategyStatus.setTemplateId(advertiseStrategyAdGroup.getTemplateId());
            strategyStatus.setType(advertiseStrategyAdGroup.getType());
            strategyStatus.setAddWayType("AD_GROUP_TARGET");
            strategyStatus.setStrategyAdGroupId(advertiseStrategyAdGroup.getId());
            strategyStatus.setIsDisengage(0);
            if (amazonSdAdGroup !=null && amazonSdAdGroup.getAdGroupId().equals(amazonSdAdTargeting.getAdGroupId())) {
                strategyStatus.setCampaignId(amazonSdAdGroup.getCampaignId());
                if ("T00020".equals(amazonSdAdGroup.getTactic())) {
                    strategyStatus.setTargetType("productTarget");
                } else if ("T00030".equals(amazonSdAdGroup.getTactic())) {
                    strategyStatus.setTargetType("audienceTarget");
                }
            }
            strategyStatus.setAdGroupId(amazonSdAdTargeting.getAdGroupId());
            strategyStatus.setTargetName(amazonSdAdTargeting.getTargetText());
            strategyStatus.setItemId(amazonSdAdTargeting.getTargetId());
            strategyStatus.setItemType(ItemType.TARGET.name());
            strategyStatus.setRule(advertiseStrategyAdGroup.getRule());
            strategyStatus.setStatus("ENABLED");
            OriginValueVo originValueVo = new OriginValueVo();
            List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(advertiseStrategyAdGroup.getRule(), TargetRuleVo.class);
            if (amazonSdAdTargeting.getBid() != null) {
                originValueVo.setBiddingValue(amazonSdAdTargeting.getBid());
            } else {
                originValueVo.setBiddingValue(advertiseStrategyAdGroup.getDefaultBid());
            }
            String originValueJson = JSONUtil.objectToJson(originValueVo);
            strategyStatus.setOriginValue(originValueJson);
            for (int i = 0; i < ruleVoList.size(); i++) {
                AdvertiseStrategySchedule advertiseStrategySchedule = new AdvertiseStrategySchedule();
                TargetRuleVo targetRuleVo = ruleVoList.get(i);
                advertiseStrategySchedule.setPuid(amazonSdAdTargeting.getPuid());
                advertiseStrategySchedule.setShopId(amazonSdAdTargeting.getShopId());
                advertiseStrategySchedule.setMarketplaceId(amazonSdAdTargeting.getMarketplaceId());
                advertiseStrategySchedule.setTaskId(taskId);
                advertiseStrategySchedule.setItemType(ItemType.TARGET.name());
                advertiseStrategySchedule.setType(advertiseStrategyAdGroup.getType());
                advertiseStrategySchedule.setAdType(AdType.SD.name());
                if (amazonSdAdGroup !=null && amazonSdAdGroup.getAdGroupId().equals(amazonSdAdTargeting.getAdGroupId())) {
                    advertiseStrategySchedule.setCampaignId(amazonSdAdGroup.getCampaignId());
                    if ("T00020".equals(amazonSdAdGroup.getTactic())) {
                        strategyStatus.setTargetType("productTarget");
                    } else if ("T00030".equals(amazonSdAdGroup.getTactic())) {
                        strategyStatus.setTargetType("audienceTarget");
                    }
                }
                advertiseStrategySchedule.setAdGroupId(amazonSdAdTargeting.getAdGroupId());
                advertiseStrategySchedule.setItemId(amazonSdAdTargeting.getTargetId());
                advertiseStrategySchedule.setDay(targetRuleVo.getSiteDate());
                advertiseStrategySchedule.setStart(targetRuleVo.getStartTimeSite() * 60);
                advertiseStrategySchedule.setEnd(targetRuleVo.getEndTimeSite() * 60);
                advertiseStrategySchedule.setTargetName(amazonSdAdTargeting.getTargetText());
                advertiseStrategySchedule.setAdGroupId(amazonSdAdTargeting.getAdGroupId());
                BigDecimal biddingValue = null;
                if (amazonSdAdTargeting.getBid() != null) {
                    biddingValue = amazonSdAdTargeting.getBid();
                } else {
                    biddingValue = advertiseStrategyAdGroup.getDefaultBid();
                }
                if (targetRuleVo.getBiddingType() == 0) {
                    biddingValue = biddingValue.add(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 1) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).add(targetRuleVo.getBiddingValue().
                            multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 2) {
                    biddingValue = biddingValue.subtract(targetRuleVo.getBiddingValue()).
                            setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 3) {
                    biddingValue = biddingValue.multiply(BigDecimal.valueOf(1).subtract(targetRuleVo.getBiddingValue()
                            .multiply(BigDecimal.valueOf(0.01)))).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else if (targetRuleVo.getBiddingType() == 4) {
                    biddingValue = targetRuleVo.getBiddingValue();
                }
                if (targetRuleVo.getBiddingType() == 0 || targetRuleVo.getBiddingType() == 1) {
                    if (targetRuleVo.getBiddingMaxValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMaxValue()) > 0) {
                            biddingValue = targetRuleVo.getBiddingMaxValue();
                        }
                    }
                }
                if (targetRuleVo.getBiddingType() == 2 || targetRuleVo.getBiddingType() == 3) {
                    if (targetRuleVo.getBiddingMinValue() != null) {
                        if (biddingValue.compareTo(targetRuleVo.getBiddingMinValue()) < 0) {
                            biddingValue = targetRuleVo.getBiddingMinValue();
                        }
                    }
                }
                OriginValueVo newValueVo = new OriginValueVo();
                newValueVo.setBiddingValue(biddingValue);
                String newValueJson = JSONUtil.objectToJson(newValueVo);
                advertiseStrategySchedule.setNewValue(newValueJson);
                advertiseStrategySchedule.setOriginValue(originValueJson);
                list.add(advertiseStrategySchedule);
            }
            if (MapUtils.isNotEmpty(advertiseStrategyStatusMap) && advertiseStrategyStatusMap.containsKey(amazonSdAdTargeting.getTargetId())) {
                AdvertiseStrategyStatus advertiseStrategyStatus = advertiseStrategyStatusMap.get(amazonSdAdTargeting.getTargetId());
                TaskTimeType timeType = null;
                if ("keywordTarget".equals(advertiseStrategyStatus.getTargetType())) {
                    timeType = TaskTimeType.keywordBid;
                } else {
                    timeType = TaskTimeType.targetBid;
                }
                //任务调度服务删除策略
                try {
                    aadasApiFactory.getStrategyApi(timeType).removeSchedule(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), false);
                } catch (Exception exception) {
                    //记录错误信息
                    log.error("puid={} shopId={} 分时调价删除组受控对象异常", advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), exception);
                    continue;
                }
                //删除组受控投放数据
                advertiseStrategyStatusDao.deleteStrategyStatus(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getId(), advertiseStrategyStatus.getShopId());
                List<Long> scheduleIds = advertiseStrategyScheduleDao.queryIdByTaskId(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(), advertiseStrategyStatus.getTaskId(), advertiseStrategyStatus.getItemType());
                if (CollectionUtils.isNotEmpty(scheduleIds)) {
                    advertiseStrategyScheduleDao.deleteStrategyByIds(advertiseStrategyStatus.getPuid(), scheduleIds);
                }
                amazonSdAdTargetingDao.updatePricing(advertiseStrategyStatus.getPuid(), advertiseStrategyStatus.getShopId(),
                        advertiseStrategyStatus.getItemId(), 0, 0, amazonSdAdTargeting.getUpdateId());
            }
            advertiseStrategyStatusDao.insetStrategyStatus(amazonSdAdTargeting.getPuid(), strategyStatus);
            List<Long> longIdList = advertiseStrategyScheduleSequenceDao.batchGenId(list.size());
            for (int i = 0; i<longIdList.size(); i++) {
                list.get(i).setId(longIdList.get(i));
            }
            advertiseStrategyScheduleDao.batchInsert(amazonSdAdTargeting.getPuid(), list);
            try {
                //推送数据到aadas调度服务
                aadasApiFactory.getStrategyApi(TaskTimeType.adGroupTarget).setSchedule(taskId, strategyStatus.getTemplateId(), list, false);
            } catch (Exception e) {
                if (e instanceof AmazonDuplicateAdItemIdException) {
                    log.info("puid:{} shopId:{} 分时调价提交异常:", amazonSdAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                } else {
                    log.error("puid:{} shopId:{} 分时调价提交异常:", amazonSdAdTargeting.getPuid(), strategyStatus.getShopId(), e);
                }
                //aadas服务异常删除对应的数据
                removeArchiveRecord(amazonSdAdTargeting.getPuid(),strategyStatus.getShopId(),strategyStatus.getTaskId(),strategyStatus.getItemType());
            }
        }
    }

    public void processTargetStatus(Integer puid, String status, List<AdvertiseStrategyStatus> strategyStatuses) throws Exception {
        for (AdvertiseStrategyStatus strategyStatus : strategyStatuses) {
            TaskTimeType taskTimeType = null;
            if (strategyStatus.getTargetType().equals(Constants.KEYWORD_TARGET)) {
                taskTimeType = TaskTimeType.adGroupKeyword;
            } else {
                taskTimeType = TaskTimeType.adGroupTarget;
            }
            List<AdvertiseStrategySchedule> list = advertiseStrategyScheduleDao.listByTaskId(puid, strategyStatus.getShopId(),strategyStatus.getTaskId(),
                    strategyStatus.getItemType());

//            addControlledObjectsLog(puid, updateId, loginIp, strategyStatus.getItemType(), null, adManageOperationLog, false);
//            filterBaseMessage(strategyStatus.getAdType(), strategyStatus.getMarketplaceId(),
//                    strategyStatus.getCampaignId(), strategyStatus.getAdGroupId(), adManageOperationLog, strategyStatus.getItemId());
//            adManageOperationLog.setShopId(strategyStatus.getShopId());

            // 回写受控对象表数据
            if ("ENABLED".equals(status)) {
                // 修改状态
                advertiseStrategyStatusDao.updateStrategyStatusById(puid, strategyStatus.getId(), status);
                // 推送数据到aads调度服务
                aadasApiFactory.getStrategyApi(taskTimeType).setSchedule(strategyStatus.getTaskId(),
                        strategyStatus.getTemplateId(), list, false);
            } else {
                // 将aads调度服务数据删除
                aadasApiFactory.getStrategyApi(taskTimeType).removeSchedule(puid, strategyStatus.getShopId(),
                        strategyStatus.getTaskId(), true);
                //修改状态
                advertiseStrategyStatusDao.updateStrategyStatusById(puid, strategyStatus.getId(), status);
            }
        }
    }

    public void removeArchiveRecord(Integer puid, Integer shopId, Long taskId, String itemType) {
        // 删除任务调度表
        advertiseStrategyScheduleDao.deleteStrategySchedule(puid, shopId, taskId, itemType);
        // 删除受控对象表
        advertiseStrategyStatusDao.deleteStrategyByTaskId(puid, shopId, taskId);
    }
}
