package com.meiyunji.sponsored.service.util;

import com.luciad.imageio.webp.WebPReadParam;
import com.meiyunji.sponsored.common.util.RegexUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.AuthSchemes;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.MemoryCacheImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by DXM_0013 on 2016/12/27.
 */
public class ImageUtils {
    private static final Logger logger = LoggerFactory.getLogger(ImageUtils.class);
    private static HttpClientConnectionManager sharedCM;
    private boolean frozen;
    private CloseableHttpClient httpClient;
    private HttpContext httpContext;

    //导出的亚马逊图片链接标识
    public static final String AMAZON_IMAGE_LINK = "https://m.media-amazon.com/";

    /**
     * 读取图片
     *
     * @param src 图片路径
     */
    public ByteArrayOutputStream getImageFromSrc(String src) {
        InputStream in = null;
        CloseableHttpResponse response = null;
        ByteArrayOutputStream baos;
        try {
            HttpGet httpget = new HttpGet(src);
            freeze();
            response = httpClient.execute(httpget, httpContext);

            if (response == null || response.getStatusLine().getStatusCode() != HttpStatus.SC_OK)
                return null;

            HttpEntity entity = response.getEntity();
            if (entity == null)
                return null;

            in = entity.getContent();
            if (in == null) {
                throw new IOException("获取图片异常3");
            }

            baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
        } catch (Exception e) {
            logger.error("Load image from src: {} error.", src, e);
            return null;
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
        return baos;
    }

    private synchronized void freeze() {
        if (frozen) {
            return;
        }
        httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(RequestConfig.copy(RequestConfig.DEFAULT)
                        .setCookieSpec(CookieSpecs.DEFAULT)
                        .setExpectContinueEnabled(true)
                        .setTargetPreferredAuthSchemes(Arrays.asList(AuthSchemes.NTLM, AuthSchemes.DIGEST))
                        .setProxyPreferredAuthSchemes(Collections.singletonList(AuthSchemes.BASIC))
                        .setConnectTimeout(3000)
                        .setSocketTimeout(5000)
                        .setConnectionRequestTimeout(3000)
                        .build())
                .setConnectionManager(getConnectionManager())
                .build();
        httpContext = new BasicHttpContext();
        frozen = true;
    }

    private HttpClientConnectionManager getConnectionManager() {
        synchronized (this.getClass()) {
            if (sharedCM == null) {
                PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
                cm.setValidateAfterInactivity(10000);
                cm.setMaxTotal(20);
                cm.setDefaultMaxPerRoute(5);
                sharedCM = cm;
            }
            return sharedCM;
        }
    }

    //处理png变黑的问题
    public BufferedImage graphics2D(String srcPath, int width, int height) {
        BufferedImage newImage = null;
        if (StringUtils.isNotEmpty(srcPath) && srcPath.trim().toLowerCase().endsWith("png") || srcPath.trim().toLowerCase().endsWith("gif")) {
            newImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = newImage.createGraphics();
            newImage = g2d.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
            g2d.dispose();
        }
        return newImage;
    }

    //重新绘制压缩图
    public void IMG(BufferedImage bufferedImage, int width, int height) {
        Long start2 = System.currentTimeMillis();
        Image image = bufferedImage.getScaledInstance(width, height, Image.SCALE_SMOOTH);
        BufferedImage newImage = new BufferedImage(width, height, bufferedImage.getType());
        Graphics graphics = newImage.getGraphics();
        graphics.drawImage(image, 0, 0, null);
        graphics.dispose();
        logger.info("压缩后图宽：" + newImage.getWidth() + "压缩后原图高：" + newImage.getHeight());
        Long end2 = System.currentTimeMillis();
        logger.info("重新绘制压缩图1：" + (end2 - start2));
    }

    public static void getNotNullImg(List<String> targetList, List<String> sourceList) {
        if (sourceList != null && sourceList.size() > 0) {
            for (String imgUrl : sourceList) {
                if (StringUtils.isNotEmpty(imgUrl)) {
                    targetList.add(imgUrl);
                }
            }
        }
    }

    /**
     *
     * @param url      文件的访问链接
     * @param type     缩放类型：1：限定缩略图的宽高最小值。该操作会将图像等比缩放直至某一边达到设定最小值，之后将另一边居中裁剪至设定值
     *                 缩放类型：2：限定缩略图的宽高最大值。该操作会将图像等比缩放至宽高都小于设定最大值
     *                 缩放类型：3：限定缩略图的宽高最小值。该操作会将图像等比缩放至宽高都大于设定最小值
     * @param quality  图片质量，取值范围0 - 100，默认值为原图质量；取原图质量和指定质量的最小值；<Quality> 后面加!（注意为英文字符），表示强制使用指定值
     * @param width    图片宽度，单位像素
     * @param heigth   图片高度，单位像素
     * @param format   目标缩略图的图片格式，Format 可为：jpg，bmp，gif，png，webp，缺省为原图格式
     * @return
     */
    public static String getCompressImageUrlByCOS(String url ,String type, String quality, String width, String heigth, String format){
        if (StringUtils.isNotBlank(url) && url.contains(AMAZON_IMAGE_LINK)) {
            String newUrl = RegexUtils.getReplaceFirst(url, "\\._SL(\\d+)_", "").replaceFirst("\\.jpg$", "._SL300_.jpg");
            return newUrl;
        }
        if(StringUtils.isEmpty(url) || !url.contains("myqcloud.com")){
            return url;
        }
        //如果原来有imageView2就截取掉
        String imageView2 = "?imageView2";
        if(url.contains(imageView2)){
            url = url.substring(0, url.indexOf(imageView2));
        }
        StringBuilder builder = new StringBuilder(url);
        builder.append(imageView2);
        builder.append("/").append(type).append("/w/").append(width).append("/h/").append(heigth);
        if(StringUtils.isNotEmpty(quality)){
            builder.append("/q/").append(quality);
        }
        if(StringUtils.isNotEmpty(format)){
            builder.append("/format/").append(format);
        }
        return builder.toString();
    }

    public static void main(String[] args) {
        String a1 = ImageUtils.getCompressImageUrlByCOS("https://sellfox-test-1251220924.picgz.myqcloud.com/sellfox-test/commodityPicture/100/20210825170513/7c41842bff56aa75c377ac11140987fc.jpg");
        System.out.println(a1);
        String a2 = ImageUtils.getCompressImageUrlByCOS("https://sellfox-test-1251220924.picgz.myqcloud.com/sellfox-test/commodityPicture/100/20210825170513/7c41842bff56aa75c377ac11140987fc.jpg?imageView2/2/w/150/h/150");
        System.out.println(a2);
    }

    /**
     * 默认缩放类型，等比缩放，按照【高】or【宽】最大上限是【100】像素，图片格式使用原来格式，图片质量留空不做要求
     * @param url   图片地址
     * @return
     */
    public static String getCompressImageUrlByCOS(String url){
        return getCompressImageUrlByCOS(url, "2", null, "300", "300", null);
    }

    /**
     * webp格式图片转JPG格式图片
     *
     * @param imageUrl 图片链接
     * @return
     * @throws IOException
     */
    public static InputStream getImgInputStreamWebpAuto2JPG(String imageUrl) {
        try {
            byte[] jpgTypes = readImageFromUrlAuto2JPG(imageUrl);
            if (jpgTypes != null && jpgTypes.length > 0) {
                return new ByteArrayInputStream(jpgTypes);
            }
        } catch (IOException e) {
            logger.error("read webp image input stream error", e);
//            throw new IOException(imageUrl + ", read webp image input stream error");
        }
        return null;
    }

    /**
     * 读取到webp 则将 webp 格式图片转为 jpg 格式
     * @param imageUrl webp 图片链接
     * @return 返回转换后的jpg字节数组，如果无需转换或转换失败返回null
     */
    public static byte[] readImageFromUrlAuto2JPG(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection conn = url.openConnection();
        conn.setConnectTimeout(30 * 1000);
        conn.setReadTimeout(60 * 1000);
        // 检测图片格式是否是webp
        byte[] imgBytes = null;
        try (
                InputStream inputStream = conn.getInputStream();
                ByteArrayInputStream is = new ByteArrayInputStream(StreamUtils.copyToByteArray(inputStream));
        ) {
            String imageFormat = getImageFormat(is);
            // 重置流
            is.reset();
            // 获取图片文件字节数组
            imgBytes = StreamUtils.copyToByteArray(is);
            if ("JPEG".equals(imageFormat)) {
                logger.info("readImageFromUrlAuto2JPG - 检测到jpg图片,无需转换 imageUrl: {} ", imageUrl);
                return imgBytes;
            }
            if (!"WEBP".equals(imageFormat)) {
                return null;
            }
            logger.info("readImageFromUrlAuto2JPG - 检测到webp图片, imageUrl: {} ", imageUrl);
        } catch (Exception e) {
            logger.error("readImageFromUrlAuto2JPG - check webp error, 检测webp图片格式失败！ imageUrl: {} ", imageUrl, e);
        }
        if (imgBytes == null || imgBytes.length == 0) {
            return null;
        }
        try (ByteArrayInputStream is = new ByteArrayInputStream(imgBytes);
             MemoryCacheImageInputStream memoryCacheImageInputStream = new MemoryCacheImageInputStream(is);
             ByteArrayOutputStream output = new ByteArrayOutputStream()) {

            // Obtain a WebP ImageReader instance, Configure the input on the ImageReader

            ImageReader reader = ImageIO.getImageReadersByMIMEType("image/webp").next();
            reader.setInput(memoryCacheImageInputStream);

            // Configure decoding parameters
            WebPReadParam readParam = new WebPReadParam();
            readParam.setBypassFiltering(true);

            // Decode the image
            BufferedImage image = reader.read(0, readParam);

            // Make sure BufferedImage does not have alpha transparency
            image = ensureOpaque(image);

            ImageIO.write(image, "jpg", output);

            return output.toByteArray();
        } catch (Exception e) {
            logger.error(" readImageFromUrlAuto2JPG - convert webp fail, 转换webp图片格式失败！ imageUrl: {} ", imageUrl, e);
        }
        return null;
    }

    /**
     * 明确图片不是半透明的
     */
    private static BufferedImage ensureOpaque(BufferedImage bi) {
        if (bi.getTransparency() == BufferedImage.OPAQUE) {
            return bi;
        }
        int w = bi.getWidth();
        int h = bi.getHeight();
        int[] pixels = new int[w * h];
        bi.getRGB(0, 0, w, h, pixels, 0, w);
        BufferedImage bi2 = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
        bi2.setRGB(0, 0, w, h, pixels, 0, w);
        return bi2;
    }

    /**
     * 通过输入流获取图片格式
     *
     * @param inputStream 输入流
     * @return
     * @throws IOException
     */
    public static String getImageFormat(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[8];
        inputStream.read(buffer);

        if (buffer[0] == (byte) 0xFF && buffer[1] == (byte) 0xD8) {
            return "JPEG";
        } else if (buffer[0] == (byte) 0x89 && buffer[1] == (byte) 0x50 && buffer[2] == (byte) 0x4E && buffer[3] == (byte) 0x47
                && buffer[4] == (byte) 0x0D && buffer[5] == (byte) 0x0A && buffer[6] == (byte) 0x1A && buffer[7] == (byte) 0x0A) {
            return "PNG";
        } else if (buffer[0] == (byte) 0x47 && buffer[1] == (byte) 0x49 && buffer[2] == (byte) 0x46 && buffer[3] == (byte) 0x38) {
            return "GIF";
        } else if (buffer[0] == (byte) 0x42 && buffer[1] == (byte) 0x4D) {
            return "BMP";
        } else if (buffer[0] == (byte) 0x49 && buffer[1] == (byte) 0x49 && buffer[2] == (byte) 0x2A && buffer[3] == (byte) 0x00) {
            return "TIFF";
        } else if (buffer[0] == (byte) 0x52 && buffer[1] == (byte) 0x49 && buffer[2] == (byte) 0x46 && buffer[3] == (byte) 0x46) {
            // WebP文件头的标识字节序列{ 0x52, 0x49, 0x46, 0x46 }
            return "WEBP";
        } else {
            return "Unknown";
        }
    }

    /**
     * 读取图片文件内容
     *
     * @param imageUrl  图片链接
     * @param timeoutMs 获取超时时间（毫秒），例如30秒超时传值为30000
     * @return 返回图片字节数组，如果获取失败返回null
     */
    public static byte[] readImageFromUrl(String imageUrl, int timeoutMs) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection conn = url.openConnection();
        conn.setConnectTimeout(5 * 1000);
        conn.setReadTimeout(timeoutMs);
        try (
                InputStream inputStream = conn.getInputStream();
        ) {
            return StreamUtils.copyToByteArray(inputStream);
        } catch (Exception e) {
            logger.info("readImageFromUrl - 获取图片内容失败！ imageUrl: {}, Exception:{}", imageUrl, e);
        }
        return null;
    }
}
