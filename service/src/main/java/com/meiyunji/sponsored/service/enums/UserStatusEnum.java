package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 用户状态
 *
 * <AUTHOR>
 * @date 2023/2/10
 */
@Getter
public enum UserStatusEnum {
    /**
     * 禁用状态-已禁用
     */
    DISABLE(0, "已禁用"),
    /**
     * 正常状态-已启用
     */
    NORMAL(1, "已启用"),
    /**
     * 删除状态-已归档
     */
    DELETE(2, "已归档"),
    ;

    private int code;
    private String desc;

    UserStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 比较code值是否相等
     */
    public boolean equalsCode(final Integer code) {
        return Objects.equals(this.code, code);
    }

    /**
     * 根据code获取枚举
     */
    public static UserStatusEnum getByCode(final Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(UserStatusEnum.values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(null);
    }
}
