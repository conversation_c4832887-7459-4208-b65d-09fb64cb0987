package com.meiyunji.sponsored.service.export.handler;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.CategoryTargetViewAggregateRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.KeywordViewAggregateExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.TargetViewAggregateExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.ICategoryTargetViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IKeywordViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregateVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewAggregateVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_TARGET_VIEW_AGGREGATE)
public class PerspectiveTargetViewAggregateExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICategoryTargetViewService categoryTargetViewService;

    /**
     * 透视接口
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getCategoryTargetViewAggregate(CategoryTargetViewAggregateRequest, StreamObserver)
     * @see ViewManageServiceImpl#getAllCategoryTargetViewAggregate(Integer, TargetViewParam)
     * 广告管理下载
     * @see TargetPageExportTaskHandler#export(AdManagePageExportTask)
     */

    @Override
    public void export(AdManagePageExportTask task) {
        TargetViewParam param = JSONUtil.jsonToObject(task.getParam(), TargetViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 商品投放视图列表页汇总 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        CategoryTargetViewAggregatePageVo pageVO = categoryTargetViewService.getAllCategoryTargetViewAggregatePageVo(puid, param);
        if (Objects.isNull(pageVO)) {
            log.error(String.format("产品广告透视 商品投放视图列表页汇总 export error, pageVO is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        Page<CategoryTargetViewAggregateVo> page = pageVO.getPage();
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRows())) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();

        List<TargetViewAggregateExcelVO> dataList = page.getRows().stream().map(i -> new TargetViewAggregateExcelVO(currency, i)).collect(Collectors.toList());

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), TargetViewAggregateExcelVO.class,
                build.currencyNew(TargetViewAggregateExcelVO.class), Collections.emptyList()));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(uuid, new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
}
