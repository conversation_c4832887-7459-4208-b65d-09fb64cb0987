package com.meiyunji.sponsored.service.enums;

import com.meiyunji.sponsored.common.enums.BaseEnum;

public enum AdOrderByFieldEnum implements BaseEnum {
    STATE("state", "运行状态"),
    VCPM("vcpm", "每千次展现费用"),
    ;

    private String code;
    private String desc;

    AdOrderByFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
