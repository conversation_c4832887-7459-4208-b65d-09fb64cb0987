package com.meiyunji.sponsored.service.autoRule.po;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_ad_auto_rule_template")
public class AdvertiseAutoRuleTemplate implements Serializable {

    /**
     * id
     */
    @DbColumn(value = "id")
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 配置id
     */
    @DbColumn(value = "profile_id")
    private String profileId;

    /**
     * 模板名称
     */
    @DbColumn(value = "template_name")
    private String templateName;

    /**
     * 规则类型
     */
    @DbColumn(value = "rule_type")
    private String ruleType;

    /**
     * 对象类型
     */
    @DbColumn(value = "item_type")
    private String itemType;

    /**
     * 规则类型
     */
    @DbColumn(value = "ad_type")
    private String adType;

    /**
     * 执行方式
     */
    @DbColumn(value = "execute_type")
    private String executeType;

    /**
     * 规则json
     */
    @DbColumn(value = "rule")
    private String rule;

    /**
     * 消息推送的类型
     */
    @DbColumn(value = "push_message_type")
    private String pushMessageType;

    /**
     * 达成条件后执行的操作
     */
    @DbColumn(value = "perform_operation")
    private String performOperation;

    /**
     * 模板版本号
     */
    @DbColumn(value = "version")
    private Integer version;

    /**
     * 创建人ID
     */
    @DbColumn(value = "create_uid")
    private Integer createUid;

    /**
     * 更新人ID
     */
    @DbColumn(value = "update_uid")
    private Integer updateUid;

    /**
     * 创建人
     */
    @DbColumn(value = "create_name")
    private String createName;

    /**
     * 更新人
     */
    @DbColumn(value = "update_name")
    private String updateName;

    @DbColumn(value = "asin")
    private String asin;

    @DbColumn(value = "sku")
    private String sku;

    @DbColumn(value = "time_type")
    private String TimeType;

    @DbColumn(value = "start_date")
    private LocalDate startDate;

    @DbColumn(value = "end_date")
    private LocalDate endDate;

    @DbColumn(value = "time_rule")
    private String timeRule;

    @DbColumn(value = "desired_position")
    private String desiredPosition;

    @DbColumn(value = "ad_data_rule")
    private String adDataRule;

    @DbColumn(value = "ad_data_operate")
    private String adDataOperate;

    @DbColumn(value = "auto_price_rule")
    private String autoPriceRule;

    @DbColumn(value = "auto_price_operate")
    private String autoPriceOperate;

    @DbColumn(value = "bidding_callback_operate")
    private String biddingCallbackOperate;

    @DbColumn(value = "check_frequency")
    private String checkFrequency;

    @DbColumn(value = "postal_code_settings")
    private String postalCodeSettings;

    @DbColumn(value = "choose_time_type")
    private String chooseTimeType;

    @DbColumn(value = "set_relation")
    private String setRelation;

    @DbColumn(value = "callback_operate")
    private String callbackOperate;

    @DbColumn(value = "callback_state")
    private String callbackState;

    @DbColumn(value = "execute_time_space_value")
    private String executeTimeSpaceValue;

    @DbColumn(value = "execute_time_space_unit")
    private String executeTimeSpaceUnit;

    @DbColumn(value = "message_reminder_type")
    private String messageReminderType;

    private Integer optimizedTimes;

    private Integer itemCount;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @DbColumn(value = "update_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime updateTime;

    @DbColumn(value = "report_complete_push")
    private Integer reportCompletePush;


    private Integer state;

    @DbColumn(value = "status")
    private String status;

    public String getMarketplaceName(){
        if (this.marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplaceCN();
        }
        return null;
    }
}
