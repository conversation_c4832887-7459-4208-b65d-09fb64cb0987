package com.meiyunji.sponsored.service.kafka;

import org.springframework.kafka.core.KafkaTemplate;

import java.nio.charset.StandardCharsets;

/**
 * @author: wade
 * @date: 2022/1/13 10:02
 * @describe:
 */
public class AdOperationNotesHandleProducer {
    private final String topic;
    private final KafkaTemplate<String, byte[]> kafkaTemplate;

    public AdOperationNotesHandleProducer(String topic, KafkaTemplate<String, byte[]> kafkaTemplate) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(Integer key, String message) {
        kafkaTemplate.send(topic, String.valueOf(key), message.getBytes(StandardCharsets.UTF_8));
    }
}
