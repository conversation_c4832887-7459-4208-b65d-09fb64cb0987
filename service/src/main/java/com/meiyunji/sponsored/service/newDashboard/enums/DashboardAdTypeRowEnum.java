package com.meiyunji.sponsored.service.newDashboard.enums;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 广告看板枚举类
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024-03-26  14:30
 */

public enum DashboardAdTypeRowEnum {

    //数据类型
    // SP   SP自动  SP手动(关键词投放、商品投放)
    // SB   SB商品集(关键词投放、商品投放)、SB视频(关键词投放、商品投放)、SB旗舰店(关键词投放、商品投放)
    // SD   商品投放(CPC、VCPM)    受众投放(CPC、VCPM)

    //注意因为需要同时表示父子的双向关系，追溯父节点用于累加值，追溯子节点用于组装前端数据
    //所以会存在前置引用异常，所以我们把子节点定义在前面方便父节点引用集合，但是此时子节点直接引用父枚举会报错，所以引用父枚举只能用字符串表示了

    //第四层,投放类型
    SP_AUTO("SP",  "auto", "SP自动", "SP", null, 210, "SP自动"),
    SP_MANUAL_KEYWORD("SP", "keyword", "关键词投放", "SPmanual", null, 221, "SP手动-关键词投放"),
    SP_MANUAL_TARGETING("SP", "targeting", "商品投放", "SPmanual", null, 222, "SP手动-商品投放"),
    SB_PRODUCT_COLLECTION_KEYWORD("productCollection", "keyword", "关键词投放", "productCollection", null, 311, "SB商品集-关键词投放"),
    SB_PRODUCT_COLLECTION_PRODUCT("productCollection", "product", "商品投放", "productCollection", null, 312, "SB商品集-商品投放"),
    SB_VIDEO_KEYWORD("video", "keyword", "关键词投放", "video", null, 321, "SB视频-关键词投放"),
    SB_VIDEO_PRODUCT("video", "product", "商品投放", "video", null, 322, "SB视频-商品投放"),
    SB_STORE_SPOTLIGHT_KEYWORD("storeSpotlight", "keyword", "关键词投放", "storeSpotlight", null, 331, "SB旗舰店-关键词投放"),
    SB_STORE_SPOTLIGHT_PRODUCT("storeSpotlight", "product", "商品投放", "storeSpotlight", null, 332, "SB旗舰店-商品投放"),
    SD_PRODUCT_CPC("T00020", "cpc", "CPC", "T00020", null, 411, "SD商品投放-CPC"),
    SD_PRODUCT_VCPM("T00020", "vcpm", "VCPM", "T00020", null, 412, "SD商品投放-VCPM"),
    SD_AUDIENCE_CPC("T00030", "cpc", "CPC", "T00030", null, 421, "SD受众投放-CPC"),
    SD_AUDIENCE_VCPM("T00030", "vcpm", "VCPM", "T00030", null, 422, "SD受众投放-VCPM"),


    //第三层，广告组类型层
    SP_MANUAL("SP", "manual", "SP手动", "SP", Arrays.asList(SP_MANUAL_KEYWORD, SP_MANUAL_TARGETING), 220, "SP手动"),
    SB_PRODUCT_COLLECTION("productCollection", "", "SB商品集", "SB", Arrays.asList(SB_PRODUCT_COLLECTION_KEYWORD, SB_PRODUCT_COLLECTION_PRODUCT), 310, "SB商品集"),
    SB_VIDEO("video", "", "SB视频", "SB", Arrays.asList(SB_VIDEO_KEYWORD, SB_VIDEO_PRODUCT), 320, "SB视频"),
    SB_STORE_SPOTLIGHT("storeSpotlight", "", "SB旗舰店", "SB", Arrays.asList(SB_STORE_SPOTLIGHT_KEYWORD, SB_STORE_SPOTLIGHT_PRODUCT), 330, "SB旗舰店"),
    SD_PRODUCT("T00020", "", "商品投放", "SD", Arrays.asList(SD_PRODUCT_CPC, SD_PRODUCT_VCPM), 410, "SD商品投放"),
    SD_AUDIENCE("T00030", "", "受众投放", "SD", Arrays.asList(SD_AUDIENCE_CPC, SD_AUDIENCE_VCPM), 420, "SD受众投放"),

    //第二层-广告类型层
    SP("SP", "", "SP", "ALL", Arrays.asList(SP_AUTO, SP_MANUAL), 200, "SP"),
    SB("SB", "", "SB", "ALL", Arrays.asList(SB_PRODUCT_COLLECTION, SB_VIDEO, SB_STORE_SPOTLIGHT), 300, "SB"),
    SD("SD", "", "SD", "ALL", Arrays.asList(SD_PRODUCT, SD_AUDIENCE), 400, "SD"),

    //第一层-汇总层
    ALL("ALL", "", "汇总", null, Arrays.asList(SP, SB, SD), 100, "汇总"),

    ;

    private String groupType;

    private String targetType;

    private String desc;

    //为解决前置引用异常，使用字符串表示，字符串为groupType+targetType
    private String parent;

    private List<DashboardAdTypeRowEnum> childList;

    private Integer orderBy;

    private String exportName;


    //所有行
    public static Set<String> set = Arrays.stream(DashboardAdTypeRowEnum.values()).map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toSet());

    //sp底层行
    public static Set<String> spSet = Stream.of(SP_AUTO, SP_MANUAL_KEYWORD, SP_MANUAL_TARGETING).map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toSet());

    //sb底层行
    public static Set<String> sbSet = Stream.of(SB_PRODUCT_COLLECTION_KEYWORD,
            SB_PRODUCT_COLLECTION_PRODUCT,
            SB_VIDEO_KEYWORD,
            SB_VIDEO_PRODUCT,
            SB_STORE_SPOTLIGHT_KEYWORD,
            SB_STORE_SPOTLIGHT_PRODUCT).map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toSet());

    //sd底层行
    public static Set<String> sdSet = Stream.of(SD_PRODUCT_CPC,SD_PRODUCT_VCPM, SD_AUDIENCE_CPC, SD_AUDIENCE_VCPM)
            .map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toSet());

    //sp、sb、sd底层行
    public static Set<String> allTargetSet = Stream.of(spSet, sbSet, sdSet).flatMap(Set::stream).collect(Collectors.toSet());

    //所有计算汇总对象行
    public static List<DashboardAdTypeRowEnum> allComputeSet = Arrays.asList(ALL,
            SP, SB, SD,
            SP_MANUAL,
            SB_PRODUCT_COLLECTION, SB_VIDEO, SB_STORE_SPOTLIGHT,
            SD_PRODUCT, SD_AUDIENCE);

    //所有行对象map
    public static Map<String, DashboardAdTypeRowEnum> map = Arrays.stream(DashboardAdTypeRowEnum.values())
            .collect((Collectors.groupingBy(x -> x.getGroupType() + x.getTargetType(),
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    //按照行顺序排序好的key集合
    public static List<String> rowList = Arrays.stream(DashboardAdTypeRowEnum.values())
            .sorted(Comparator.comparingInt(DashboardAdTypeRowEnum::getOrderBy))
            .map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toList());

    DashboardAdTypeRowEnum(String groupType,
                           String targetType,
                           String desc,
                           String parent,
                           List<DashboardAdTypeRowEnum> childList,
                           Integer orderBy,
                           String exportName) {
        this.groupType = groupType;
        this.targetType = targetType;
        this.desc = desc;
        this.parent = parent;
        this.childList = childList;
        this.orderBy = orderBy;
        this.exportName = exportName;
    }

    public String getGroupType() {
        return groupType;
    }

    public String getTargetType() {
        return targetType;
    }

    public String getDesc() {
        return desc;
    }

    public String getParent() {
        return parent;
    }

    public List<DashboardAdTypeRowEnum> getChildList() {
        return childList;
    }

    public Integer getOrderBy() {
        return orderBy;
    }

    public String getExportName() {
        return exportName;
    }
}
