package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dto.AdProductReportSearchTermsViewDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbAdsReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbAdsReport;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * sb ads 报告(OdsAmazonAdSbAdsReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
@Repository
public class OdsAmazonAdSbAdsReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSbAdsReport> implements IOdsAmazonAdSbAdsReportDao {

    @Override
    public List<AdProductReportSearchTermsViewDto> listAmazonSbAdProduct(Integer puid, SearchTermsViewParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder sql = new StringBuilder(" select p.shop_id shop_id, p.asins asin, p.ad_group_id ad_group_id, ");
        sql.append(" IFNULL(sum(cost),0) `cost`, IFNULL(sum(sales14d),0) sales14d, IFNULL(sum(sales14d_same_sku),0) sales14d_same_sku, ")
                .append(" IFNULL(sum(`impressions`),0) impressions, IFNULL(sum(conversions14d),0) conversions14d,  IFNULL(sum(clicks),0) clicks, ")
                .append(" IFNULL(sum(units_sold14d),0) units_sold14d, IFNULL(sum(conversions14d_same_sku),0) conversions14d_same_sku, ")
                .append(" IFNULL(sum(viewable_impressions),0) viewable_impressions, IFNULL(sum(orders_new_to_brand14d),0) orders_new_to_brand14d, ")
                .append(" IFNULL(sum(sales_new_to_brand14d),0) sales_new_to_brand14d ");
        sql.append(" from ods_t_amazon_sb_ads p join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.ad_id = r.ad_id ");

        sql.append(" and r.puid= ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(SqlStringUtil.dealDorisInList("r.shop_id", param.getShopIdList(), argsList));
        sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", param.getSbAdGroupIdList(), argsList));

        sql.append(" where p.puid= ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("p.shop_id", param.getShopIdList(), argsList));
        sql.append(SqlStringUtil.dealBitMapDorisInList("p.ad_group_id", param.getSbAdGroupIdList(), argsList));

        sql.append(" group by p.shop_id, p.asins, p.ad_group_id");
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdProductReportSearchTermsViewDto.builder()
                .type("sb")
                .shopId(re.getInt("shop_id"))
                .adGroupId(re.getString("ad_group_id"))
                .asin(re.getString("asin"))
                .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getLong("clicks")).orElse(0L))
                .impressions(Optional.of(re.getLong("impressions")).orElse(0L))
                //本广告产品订单量
                .orderNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                .adOrderNum(0)  //销量字段订单
                .totalSales(re.getBigDecimal("sales14d") != null ? re.getBigDecimal("sales14d") : BigDecimal.ZERO)
                .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                .adSales(Optional.ofNullable(re.getBigDecimal("sales14d_same_sku")).orElse(BigDecimal.ZERO))
                .saleNum(Optional.ofNullable(re.getInt("conversions14d")).orElse(0))
                .viewImpressions(Optional.of(re.getLong("viewable_impressions")).orElse(0L))
                .ordersNewToBrandFTD(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                .salesNewToBrandFTD(re.getBigDecimal("sales_new_to_brand14d") != null ? re.getBigDecimal("sales_new_to_brand14d") : BigDecimal.ZERO)
                .build(), argsList.toArray());
    }
}

