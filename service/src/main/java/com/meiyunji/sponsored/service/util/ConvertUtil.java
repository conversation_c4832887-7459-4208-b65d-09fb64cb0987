package com.meiyunji.sponsored.service.util;

import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.util.ClassUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.dataWarehouse.po.StatPerformanceProduct;
import com.meiyunji.sponsored.service.system.po.CurrencyExchangeRate;
import com.meiyunji.sponsored.service.system.po.UserRate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.BeanInfo;
import java.beans.FeatureDescriptor;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

@Slf4j
public class ConvertUtil {

    private static final long rateExpirationInMs = 2 * 60 * 1000;

    static class ExpiredRate {
        public UserRate value;
        public long ms;

        public ExpiredRate(UserRate value, long ms) {
            this.value = value;
            this.ms = ms;
        }
    }

    static class ExpiredCurrRate {
        public BigDecimal value;
        public long ms;

        public ExpiredCurrRate(BigDecimal value, long ms) {
            this.value = value;
            this.ms = ms;
        }
    }

    /**
     * 缓存汇率
     */
    private static final ConcurrentMap<String, ExpiredRate> map = Maps.newConcurrentMap();
    /**
     * 统一缓存汇率
     */
    private static final ConcurrentMap<String, ExpiredCurrRate> currMap = Maps.newConcurrentMap();


    /**
     * 根据 CurrencyConversion 注解换算字段
     *
     * @param p
     * @param rate 除法运算
     * @param <P>
     */
    public static <P> void convert(P p, BigDecimal rate) {
        if (p == null || rate == null) return;
        try {

            List<Field> fields = ClassUtils.getFieldWithClass(p.getClass());
            if (CollectionUtils.isNotEmpty(fields)) {
                BeanInfo beanInfo = Introspector.getBeanInfo(p.getClass());
                PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
                Map<String, PropertyDescriptor> descriptorMap = Arrays.stream(propertyDescriptors).collect(Collectors.toMap(FeatureDescriptor::getName, f -> f));
                fields = fields.stream().filter(
                        f -> f.isAnnotationPresent(CurrencyConversion.class)
                ).collect(Collectors.toList());

                for (Field field : fields) {
                    if (descriptorMap.containsKey(field.getName())) {
                        PropertyDescriptor descriptor = descriptorMap.get(field.getName());
                        Object value = descriptor.getReadMethod().invoke(p);
                        if (value == null) continue;
                        BigDecimal decimal = new BigDecimal(value + "");
                        CurrencyConversion annotation = field.getAnnotation(CurrencyConversion.class);
                        if (!annotation.needConversion()) continue;
                        BigDecimal divide = MathUtil.divide(decimal, rate, annotation.conversionScale());
                        Method writeMethod = descriptor.getWriteMethod();
                        Class<?> type = descriptor.getPropertyType();
                        if (BigDecimal.class == type) {
                            writeMethod.invoke(p, divide.setScale(annotation.saveScale(), BigDecimal.ROUND_HALF_UP));
                        } else if (Double.class == type || double.class == type) {
                            writeMethod.invoke(p, divide.setScale(annotation.saveScale(), BigDecimal.ROUND_HALF_UP).doubleValue());
                        } else if (String.class == type) {
                            writeMethod.invoke(p, divide.setScale(annotation.saveScale(), BigDecimal.ROUND_HALF_UP).toString());
                        } else {
                            log.info("convert err 暂不支持的类型");
                        }

                    }
                }

            }


        } catch (Exception e) {
            log.error("convert err", e);
        }
    }

    public static void setRate(int puid, List<UserRate> rates) {
        if (rates != null) {
            for (UserRate rate : rates) {
                map.put(puid + rate.getName() + rate.getDate(), new ExpiredRate(rate, System.currentTimeMillis()));
            }
        }
    }


    /**
     * 获取汇率
     *
     * @param key
     * @param month
     * @return
     */
    public static BigDecimal getRate(int puid, String key, String month) {
        if (StringUtils.isNotBlank(key)) {
            if (StringUtils.isBlank(month)) {
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            }
            ExpiredRate rate = map == null ? null : map.getOrDefault(puid + key + month, null);
            if (rate == null) {
                return null;
            } else {
                if ((System.currentTimeMillis() - rate.ms) >= rateExpirationInMs) {
                    return null;
                }
                return rate.value.getUserRate() == null ? rate.value.getRate() : rate.value.getUserRate();
            }
        }
        return null;
    }

    /**
     * 统一汇率
     *
     * @param key
     * @param month
     * @return
     */
    public static BigDecimal getRate(String key, String month) {
        if (StringUtils.isNotBlank(key)) {
            if (StringUtils.isBlank(month)) {
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            }
            ExpiredCurrRate rate = currMap == null ? null : currMap.getOrDefault(key + month, null);
            if (rate == null) {
                return null;
            } else {
                if ((System.currentTimeMillis() - rate.ms) >= rateExpirationInMs) {
                    return null;
                }
                return rate.value;
            }
        }
        return null;
    }

    /**
     * 统一汇率
     *
     * @param key
     * @param month
     * @return
     */
    public static BigDecimal setRate(String key, String month, BigDecimal rate) {
        if (StringUtils.isNotBlank(key)) {
            if (StringUtils.isBlank(month)) {
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            }
            currMap.put(key + month, new ExpiredCurrRate(rate, System.currentTimeMillis()));
        }
        return null;
    }

    /**
     * 统一汇率
     *
     * @param rates
     */
    public static void setRate(List<CurrencyExchangeRate> rates) {
        if (rates != null) {
            for (CurrencyExchangeRate rate : rates) {
                currMap.put(rate.getName() + rate.getDate(), new ExpiredCurrRate(rate.getRate(), System.currentTimeMillis()));
            }
        }
    }

    /**
     * 获取汇率
     *
     * @param key
     * @param month
     * @return
     */
    public static UserRate getRateObj(int puid, String key, String month) {
        if (StringUtils.isNotBlank(key)) {
            if (StringUtils.isBlank(month)) {
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            }
            ExpiredRate rate = map == null ? null : map.getOrDefault(puid + key + month, null);
            if (rate == null) {
                return null;
            } else {
                if ((System.currentTimeMillis() - rate.ms) >= rateExpirationInMs) {
                    return null;
                }
                return rate.value;
            }
        }
        return null;
    }


    /**
     * @param key
     * @param rate
     * @param month
     */
    public static void setRate(int puid, String key, UserRate rate, String month) {
        if (StringUtils.isNotBlank(key) && rate != null) {
            if (StringUtils.isBlank(month)) {
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
            }

            map.put(puid + month + key, new ExpiredRate(rate, System.currentTimeMillis()));
        }
    }

    /**
     * @param key
     * @param rate
     * @param month
     */
    public static void setRate(int puid, String key, BigDecimal rate, String month) {
        UserRate userRate = new UserRate();
        userRate.setRate(rate);
        userRate.setUserRate(rate);
        userRate.setDate(month);
        userRate.setPuid(puid);
        userRate.setName(key);

        setRate(puid, key, userRate, month);
    }

    public static void remove() {
    }


    public static void main(String[] args) {
        StatPerformanceProduct statPerformanceProduct = new StatPerformanceProduct(true);
        statPerformanceProduct.setStandardPrice(new BigDecimal(100));

        convert(statPerformanceProduct, new BigDecimal("1.11"));

        System.out.println(JSONUtil.objectToJson(statPerformanceProduct));
    }

}
