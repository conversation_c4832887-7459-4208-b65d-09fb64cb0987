package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.KeywordViewRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.KeywordViewAggregateExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.KeywordViewExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IKeywordViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.KeywordViewServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_KEYWORD_VIEW)
public class PerspectiveKeywordViewExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IKeywordViewService keywordViewService;

    /**
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getKeywordView(KeywordViewRequest, StreamObserver)
     * @see ViewManageServiceImpl#getAllKeywordView(Integer, KeywordViewParam)
     * @see KeywordViewServiceImpl#getAllKeywordViewPageVoList(Integer, KeywordViewParam)
     * @param task
     */

    @Override
    public void export(AdManagePageExportTask task) {
        KeywordViewParam param = JSONUtil.jsonToObject(task.getParam(), KeywordViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 关键词视图列表页 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        List<String> excludeFields = Lists.newArrayList();
        if (StringUtils.equalsIgnoreCase(param.getType(), Constants.SB)) {
            excludeFields.add("servingStatusName");
        }
        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();

        Page<KeywordViewVo> page = keywordViewService.getAllKeywordViewPageVoList(puid, param);
        if (Objects.isNull(page)) {
            log.error(String.format("产品广告透视 关键词视图列表页 export error, page is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        List<KeywordViewVo> rows = page.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        List<KeywordViewExcelVO> dataList = rows.stream().map(i -> KeywordViewExcelVO.getInstance(currency, i)).collect(Collectors.toList());

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), KeywordViewExcelVO.class,
                build.currencyNew(KeywordViewExcelVO.class), excludeFields));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(uuid, new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
}
