package com.meiyunji.sponsored.service.autoRule.vo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataDetailJson implements Serializable {

    @JsonProperty("ruleIndexList")
    private List<RuleIndexJson> ruleIndexList;
    @JsonProperty("originalValue")
    private String originalValue;
    @JsonProperty("executeValue")
    private String executeValue;
    @JsonProperty("negativeTargetType")
    private String negativeTargetType;
    @JsonProperty("originalAdPlaceTopValue")
    private String originalAdPlaceTopValue;
    @JsonProperty("originalAdPlaceProductValue")
    private String originalAdPlaceProductValue;
    @JsonProperty("originalAdOtherValue")
    private String originalAdOtherValue;
    @JsonProperty("executeAdPlaceTopValue")
    private String executeAdPlaceTopValue;
    @JsonProperty("executeAdPlaceProductValue")
    private String executeAdPlaceProductValue;
    @JsonProperty("executeAdOtherValue")
    private String executeAdOtherValue;
    @JsonProperty("adGroupId")
    private String adGroupId;
    @JsonProperty("adGroupName")
    private String adGroupName;
    @JsonProperty("matchType")
    private String matchType;
    @JsonProperty("bidType")
    private String bidType;
    @JsonProperty("bidValue")
    private String bidValue;

}
