package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbCampaignOldReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbCampaignReport;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
public class AmazonAdSbCampaignOldReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSbCampaignReport> implements IAmazonAdSbCampaignOldReportDao {



    @Override
    public AmazonAdSbCampaignReport getSumReportByDate(int puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
             StringBuilder sql = new StringBuilder("SELECT sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d ")

                    .append(" FROM `t_amazon_ad_sb_campaign_report` where`puid`=? and`shop_id`=? and`marketplace_id`=? and count_date>=? and count_date<=?  ");
            return getJdbcTemplate(puid).queryForObject(sql.toString(), new Object[]{puid, shopId, marketplaceId, startDate, endDate}, getMapper());

    }

    @Override
    public AmazonAdSbCampaignReport getSumReportByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {

            StringBuilder sql = new StringBuilder("SELECT sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                    .append(" FROM `t_amazon_ad_sb_campaign_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=?  ");
            return getJdbcTemplate(puid).queryForObject(sql.toString(), new Object[]{puid, shopId, type, startDate, endDate}, getMapper());

    }

    @Override
    public List<AmazonAdSbCampaignReport> getSumCountDateReportByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {

        StringBuilder sql = new StringBuilder("SELECT count_date, sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                .append(" FROM `t_amazon_ad_sb_campaign_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=? group by count_date ");
        return getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, type, startDate, endDate}, getMapper());

    }

    @Override
    public List<AmazonAdSbCampaignReport> getSbVideoReportSumByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT campaign_id, sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                .append(" FROM `t_amazon_ad_sb_campaign_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=?  group by campaign_id ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(type);
        args.add(startDate);
        args.add(endDate);
        return getJdbcTemplate(puid).query(sql.toString(),args.toArray(),getMapper());
    }


}
