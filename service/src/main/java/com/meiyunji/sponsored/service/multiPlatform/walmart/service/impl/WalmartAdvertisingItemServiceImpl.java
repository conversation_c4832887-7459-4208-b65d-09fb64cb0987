package com.meiyunji.sponsored.service.multiPlatform.walmart.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.shop.service.IMultiPlatformShopService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingCampaignDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingGroupDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingItemDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingItemRecommendationsDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdCreateCommonErrorVo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdItemUpdateDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingItemAddToGroupDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingItemPageDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.enums.WalmartAdCampaignTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.walmart.enums.WalmartAdTargetTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.walmart.enums.WalmartAdUpdateTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.walmart.log.po.WalmartAdvertisingOperationLog;
import com.meiyunji.sponsored.service.multiPlatform.walmart.log.service.IWalmartOperationLogService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdItemListCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdItemsCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartItemBidRecommend;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingItemClientService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingItemService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.Constants;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.WalmartAdvetisingUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdItemsVo;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.dto.ItemToAdGroupDTO;
import com.walmart.oms.advertiser.base.vo.ItemToAdGroupResponseVO;
import com.walmart.oms.advertiser.base.vo.ItemsCampaignDataResponseVO;
import com.walmart.oms.advertiser.base.vo.SearchItemsDataResponseVO;
import com.walmart.oms.advertiser.model.ListAllTheItemsCampaignsResponse;
import com.walmart.oms.advertiser.model.SearchItemsResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动产品ervice
 */
@Service
@Slf4j
public class WalmartAdvertisingItemServiceImpl implements IWalmartAdvertisingItemService {

    @Autowired
    private IMultiPlatformShopService multiPlatformShopService;

    @Autowired
    private IWalmartAdvertisingCampaignDao walmartAdvertisingCampaignDao;

    @Autowired
    private IWalmartAdvertisingGroupDao walmartAdvertisingGroupDao;
    @Autowired
    private IWalmartAdvertisingItemDao walmartAdvertisingItemDao;
    @Autowired
    private WalmartAdvertiserAttributesServiceImplService walmartAdvertiserAttributesService;

    @Autowired
    private IWalmartAdvertisingItemRecommendationsDao walmartAdvertisingItemRecommendationsDao;
    @Autowired
    private IWalmartAdvertisingItemClientService walmartAdvertisingItemClientService;
    @Autowired
    private IWalmartOperationLogService walmartSellfoxOperationLogService;

    @Override
    public int delete(Integer puid, Long id) {
        return walmartAdvertisingItemDao.delete(puid, id);
    }

    @Override
    public int update(Integer puid, WalmartAdvertisingItem item) {
        return walmartAdvertisingItemDao.update(puid, item);
    }

    @Override
    public Page<WalmartAdvertisingItemPageDTO> getPageList(WalmartAdItemsVo reqVo) {
        if (Objects.isNull(reqVo.getPuid()) || CollectionUtils.isEmpty(reqVo.getShopIdList())) {
            log.error("walmart puid or shopId is null");
            throw new ServiceException("puid或店铺不能为空");
        }
        //如果入参包含了指定店铺，过滤其中未授权的广告的店铺，如果未指定，则获取所有亚马逊已授权的店铺
        List<MultiPlatformShopAuth> queryShopList = multiPlatformShopService.getWalmartAdShopList(reqVo.getPuid());
        Map<Integer, MultiPlatformShopAuth> shopMap = queryShopList.stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, v -> v, (old, current) -> current));
        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            reqVo.getShopIdList().removeIf(id -> !shopMap.containsKey(id));
        }
        if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
            log.error("walmart ad auth shop id list is empty");
            return new Page<>();
        }
        if (Objects.isNull(reqVo = this.checkItemPageQueryParam(reqVo))) {
            return new Page<>();
        };
        Page<WalmartAdvertisingItemPageDTO> itemsPage = walmartAdvertisingItemDao.getPageListWithReport(reqVo.getPuid(), reqVo);
        if (Objects.isNull(itemsPage) || CollectionUtils.isEmpty(itemsPage.getRows())) {
            return itemsPage;
        }
        Set<String> campaignIdSet = itemsPage.getRows().parallelStream().map(WalmartAdvertisingItemPageDTO::getCampaignId).collect(Collectors.toSet());
        Set<String> groupIdSet = itemsPage.getRows().parallelStream().map(WalmartAdvertisingItemPageDTO::getAdGroupId).collect(Collectors.toSet());
        Map<String, WalmartAdvertisingCampaign> campaignInfoMap = new HashMap<>();
        Map<String, WalmartAdvertisingGroup> groupInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignIdSet)) {
            //批量查询广告产品对应的广告活动
            List<WalmartAdvertisingCampaign> campaignList = walmartAdvertisingCampaignDao.getByCampaignIdsAndShopIds(reqVo.getPuid(), reqVo.getShopIdList(), new ArrayList<>(campaignIdSet));
            campaignInfoMap = campaignList.parallelStream().collect(Collectors.toMap(WalmartAdvertisingCampaign::getCampaignId, v1 -> v1, (old, current) -> current));
        }
        if (CollectionUtils.isNotEmpty(groupIdSet)) {
            List<WalmartAdvertisingGroup> groupList = walmartAdvertisingGroupDao.getGroupNameByIds(reqVo.getPuid(), reqVo.getShopIdList(), new ArrayList<>(groupIdSet));
            groupInfoMap = groupList.parallelStream().collect(Collectors.toMap(WalmartAdvertisingGroup::getAdGroupId, v1 -> v1, (old, current) -> current));
        }
        // 报告数据处理
        for (WalmartAdvertisingItemPageDTO item : itemsPage.getRows()) {
            WalmartAdvetisingUtil.convertReportVal(item);
            fillDefaultVal(item, shopMap.get(item.getShopId()), campaignInfoMap, groupInfoMap, false);
        }
        return itemsPage;
    }

    @Override
    public WalmartAdvertisingItemPageDTO getAggregateData(WalmartAdItemsVo reqVo) {
        if (Objects.isNull(reqVo.getPuid()) || CollectionUtils.isEmpty(reqVo.getShopIdList())) {
            log.error("walmart puid or shopId is null");
            throw new ServiceException("puid或店铺不能为空");
        }
        //如果入参包含了指定店铺，过滤其中未授权的广告的店铺，如果未指定，则获取所有亚马逊已授权的店铺
        List<MultiPlatformShopAuth> queryShopList = multiPlatformShopService.getWalmartAdShopList(reqVo.getPuid());
        Map<Integer, MultiPlatformShopAuth> shopMap = queryShopList.stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, v -> v, (old, current) -> current));
        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            reqVo.getShopIdList().removeIf(id -> !shopMap.containsKey(id));
        }
        if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
            log.error("walmart ad auth shop id list is empty");
            return new WalmartAdvertisingItemPageDTO();
        }
        if (Objects.isNull(reqVo = this.checkItemPageQueryParam(reqVo))) {
            return new WalmartAdvertisingItemPageDTO();
        };
        WalmartAdvertisingItemPageDTO aggregate = walmartAdvertisingItemDao.getAggregate(reqVo.getPuid(), reqVo);
        WalmartAdvetisingUtil.convertReportVal(aggregate);
        return aggregate;
    }

    @Override
    public WalmartAdItemListCreateResp itemAddToGroup(int puid, WalmartAdvertisingItemAddToGroupDTO dto) throws ServiceException {
        if (Objects.isNull(dto) || StringUtils.isEmpty(dto.getAdGroupId())) {
            log.error("ad group id is empty, puid:{}, shopId:{}", puid, dto.getShopId());
            throw new ServiceException("广告组Id不能为空");
        }
        if (CollectionUtils.isEmpty(dto.getItems())) {
            log.error("item list is empty, puid:{}, shopId:{}", puid, dto.getShopId());
            throw new SponsoredBizException("广告产品不能为空");
        }
        if (dto.getItems().size() > 2000) {
            log.error("item list size over 2000, puid:{}, shopId:{}", puid, dto.getShopId());
            throw new SponsoredBizException("添加产品不能超过2000个");
        }
        //如果入参包含了指定店铺，过滤其中未授权的广告的店铺，如果未指定，则获取所有亚马逊已授权的店铺
        List<MultiPlatformShopAuth> queryShopList = multiPlatformShopService.getWalmartAdShopList(puid);
        Map<Integer, MultiPlatformShopAuth> shopMap = queryShopList.stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, v -> v, (old, current) -> current));
        if (!shopMap.containsKey(dto.getShopId())) {
            log.error("walmart ad auth shop id list is empty");
            throw new SponsoredBizException("店铺未授权");
        }
        WalmartAdvertisingGroup group = walmartAdvertisingGroupDao.getByAdGroupId(puid, dto.getShopId(), dto.getAdGroupId());
        if (group == null) {
            log.error("ad group not exist, puid:{}, shopId:{}, adGroupId:{}", puid, dto.getShopId(), dto.getAdGroupId());
            throw new SponsoredBizException("未找到广告组！");
        }
        String campaignId = group.getCampaignId();
        WalmartAdvertisingCampaign campaignInfo = walmartAdvertisingCampaignDao.getByCampaignId(puid, dto.getShopId(), campaignId);
        if (Objects.isNull(campaignInfo)) {
            log.error("ad group campaign not exist, puid:{}, shopId:{}, groupId:{}, campaignId:{}", puid, dto.getShopId(), dto.getAdGroupId(), campaignId);
            throw new ServiceException("广告组对应的广告活动不存在");
        }
        //校验用户是否授权Walmart写权限
        walmartAdvertiserAttributesService.checkAttributesAndWrite(puid, group.getShopId());
        List<ItemToAdGroupDTO> itemToAdGroupDTOS = new ArrayList<>();
        ItemToAdGroupDTO adGroupDTO;
        for (WalmartAdvertisingItemAddToGroupDTO.Item item : dto.getItems()) {
            adGroupDTO = new ItemToAdGroupDTO();
            adGroupDTO.setCampaignId(group.getCampaignId());
            adGroupDTO.setAdGroupId(group.getAdGroupId());
            adGroupDTO.setItemId(item.getItemId());
            adGroupDTO.setBid(item.getBid());
            adGroupDTO.setStatus(item.getState());
            itemToAdGroupDTOS.add(adGroupDTO);
        }
        List<List<ItemToAdGroupDTO>> allList;
        if (itemToAdGroupDTOS.size() > 50) {
            allList = Lists.partition(itemToAdGroupDTOS, 50);
        } else {
            allList = new ArrayList<>();
            allList.add(itemToAdGroupDTOS);
        }
        WalmartAdItemListCreateResp itemResult = new WalmartAdItemListCreateResp();
        List<WalmartAdCreateCommonErrorVo> errorList = new ArrayList<>();
        List<WalmartAdItemsCreateResp> successList = new ArrayList<>();
        for (List<ItemToAdGroupDTO> dtos : allList) {
            try {
                List<ItemToAdGroupResponseVO> response = null;
                try {
                    response = walmartAdvertisingItemClientService.addNewItemToAdGroup(dtos);
                } catch (ServiceException e) {
                    throw new SponsoredBizException(e.getMsg());
                }
                //walmart返回结果是按条件list的顺序返回的
                for (int i = 0; i < response.size(); i++) {
                    if (StringUtils.equals("failure", response.get(i).getCode()) || StringUtils.isNotBlank(response.get(i).getDetails())) {
                        log.error("remote api response failure, puid:{}, shopId:{}, adGroupId:{}, item:{}, error:{}", puid,
                                dto.getShopId(), dto.getAdGroupId(), JSONObject.toJSONString(dtos), WalmartAdvetisingUtil.getErrorStr(response.get(i).getDetails()));
                        String msg = "产品添加到广告组失败！平台响应:" + WalmartAdvetisingUtil.getErrorStr(response.get(i).getDetails());
                        errorList.add(WalmartAdCreateCommonErrorVo.builder()
                                .field(dtos.get(i).getItemId())
                                .msg(msg)
                                .build());
                    } else {
                        WalmartAdItemsCreateResp item = WalmartAdItemsCreateResp.builder()
                                .itemId(dtos.get(i).getItemId())
                                .build();
                        Optional.ofNullable(response.get(i).getAdItemId()).map(String::valueOf).ifPresent(item::setAdItemId);
                        successList.add(item);
                    }
                }
            } catch (ServiceException e) {
                errorList.addAll(dtos.stream().map(i -> WalmartAdCreateCommonErrorVo.builder()
                        .field(i.getItemId())
                        .msg(e.getMsg())
                        .build()).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            itemResult.setItemListErrMsg(JSONObject.toJSONString(errorList));
            return itemResult;
        }
        //从Walmart同步广告产品
        ListAllTheItemsCampaignsResponse listAllItemsResp;
        try {
            listAllItemsResp = walmartAdvertisingItemClientService.getListAllTheItemsCampaign(group.getCampaignId());
        } catch (ServiceException e) {
            throw new SponsoredBizException(e.getMsg());
        }
        List<WalmartAdvertisingItem> itemInsertList = new ArrayList<>();
        for (ItemsCampaignDataResponseVO responseVO : listAllItemsResp.getData()) {
            WalmartAdvertisingItem item = new WalmartAdvertisingItem();
            item.setPuid(puid);
            item.setShopId(dto.getShopId());
            itemsCampaignDataToItemPo(responseVO, item);
            itemInsertList.add(item);
        }

        //收集日志
        WalmartAdvertisingOperationLog operationLog =  walmartSellfoxOperationLogService.getItemLog(puid, dto.getShopId(), campaignId,
                shopMap.get(dto.getShopId()).getMarketplaceCode(), campaignInfo.getCampaignType(), dto.getOperatorId(), null, itemInsertList);

        walmartAdvertisingItemDao.addBatch(puid, itemInsertList);
        if (CollectionUtils.isNotEmpty(successList)) {
            itemResult.setItemList(successList);
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            itemResult.setItemListErrMsg(JSONObject.toJSONString(errorList));
            operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            operationLog.setResultInfo(itemResult.getItemListErrMsg());
        } else {
            operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        walmartSellfoxOperationLogService.printAdOperationLog(Collections.singletonList(operationLog));
        return itemResult;
    }

    private void itemsCampaignDataToItemPo(ItemsCampaignDataResponseVO responseVO, WalmartAdvertisingItem item) {
        item.setCampaignId(responseVO.getCampaignId());
        item.setAdGroupId(responseVO.getAdGroupId());
        item.setItemId(responseVO.getItemId());
        item.setAdItemId(responseVO.getAdItemId());
        item.setBid(responseVO.getBid());
        item.setStatus(responseVO.getStatus());
        item.setItemImageUrl(responseVO.getItemImageUrl());
        item.setItemPageUrl(responseVO.getItemPageUrl());
        item.setName(responseVO.getName());
        item.setReviewStatus(responseVO.getReviewStatus());
        item.setReviewReason(responseVO.getReviewReason());
    }

    @Override
    public Boolean updateBidOrStatus(int puid, Integer shopId, WalmartAdItemUpdateDTO req) {
        if (puid == 0 || Objects.isNull(shopId)) {
            log.error("walmart puid or shopId is null");
            throw new ServiceException("puid或店铺Id为空");
        }
        if (StringUtils.isEmpty(req.getAdGroupId())) {
            log.error("walmart group id is null");
            throw new ServiceException("广告组Id为空");
        }
        if (StringUtils.isEmpty(req.getAdItemId())) {
            log.error("walmart item id is null");
            throw new ServiceException("广告产品Id为空");
        }
        //如果入参包含了指定店铺，过滤其中未授权的广告的店铺，如果未指定，则获取所有亚马逊已授权的店铺
        List<MultiPlatformShopAuth> queryShopList = multiPlatformShopService.getWalmartAdShopList(puid);
        Map<Integer, MultiPlatformShopAuth> shopMap = queryShopList.stream().collect(Collectors.toMap(MultiPlatformShopAuth::getId, v -> v, (old, current) -> current));
        if (!shopMap.containsKey(shopId)) {
            log.error("walmart ad auth shop id list is empty");
            throw new SponsoredBizException("店铺未授权");
        }
        //校验是否授权广告写权限
        walmartAdvertiserAttributesService.checkAttributesAndWrite(puid, shopId);

        WalmartAdvertisingGroup group = walmartAdvertisingGroupDao.getByAdGroupId(puid, shopId, req.getAdGroupId());
        if (group == null) {
            log.error("ad group not exist, puid:{}, shopId:{}, adGroupId:{}", puid, shopId, req.getAdGroupId());
            throw new SponsoredBizException("未找到广告组！");
        }
        String campaignId = group.getCampaignId();
        WalmartAdvertisingCampaign campaignInfo = walmartAdvertisingCampaignDao.getByCampaignId(puid, shopId, campaignId);
        if (Objects.isNull(campaignInfo)) {
            log.error("ad group campaign not exist, puid:{}, shopId:{}, groupId:{}, campaignId:{}", puid, shopId, req.getAdGroupId(), campaignId);
            throw new ServiceException("广告组对应的广告活动不存在");
        }
        WalmartAdUpdateTypeEnum updateType = WalmartAdUpdateTypeEnum.getUpdateTypeByCode(req.getUpdateType());
        if (Objects.isNull(updateType)) {
            log.error("walmart update type is error, puid:{}, shopId:{}, updateType:{}", puid, shopId, req.getUpdateType());
            throw new SponsoredBizException("操作类型错误");
        }

        WalmartAdvertisingItem oldItem;
        oldItem = walmartAdvertisingItemDao.getByAdItemId(puid, shopId, req.getAdGroupId(), req.getAdItemId());
        if (oldItem == null) {
            log.error("walmart item not exist, puid:{}, shopId:{}, groupId:{}, itemId:{}", puid, shopId, req.getAdGroupId(), req.getAdItemId());
            throw new SponsoredBizException("未找到该产品！");
        }
        //拷贝一份当前产品信息，便于修改
        WalmartAdvertisingItem currentItem = new WalmartAdvertisingItem();
        BeanUtils.copyProperties(oldItem, currentItem);
        if (WalmartAdUpdateTypeEnum.UPDATE_STATE == updateType) {
            if (!StringUtils.equalsIgnoreCase(Constants.UPDATE_STATUS_ENABLED, req.getState())
                    && !StringUtils.equalsIgnoreCase(Constants.UPDATE_STATUS_PAUSED, req.getState())
                    && !StringUtils.equalsIgnoreCase(Constants.UPDATE_STATUS_DELETE, req.getState())) {
                log.error("walmart state error, puid:{}, shopId:{}, groupId:{}, state:{}", puid, shopId, req.getAdGroupId(), req.getState());
                throw new SponsoredBizException("更新状态错误");
            } else {
                if (StringUtils.equalsIgnoreCase(Constants.UPDATE_STATUS_PAUSED, req.getState())) {
                    req.setState(Constants.UPDATE_STATUS_DISABLED);
                }
                currentItem.setStatus(req.getState());
            }
        }
        if (WalmartAdUpdateTypeEnum.UPDATE_BID == updateType) {
            if (Objects.isNull(req.getBid()) || req.getBid() == 0) {
                log.error("walmart bid is null, puid:{}, shopId:{}, groupId:{}", puid, shopId, req.getAdGroupId());
                throw new SponsoredBizException("竞价不能为空");
            } else {
                currentItem.setBid(req.getBid());
            }
        }

        //收集日志
        Map<String, WalmartAdvertisingItem> existMap = new HashMap<>();
        existMap.put(oldItem.getItemId(), oldItem);
        WalmartAdvertisingOperationLog operationLog = walmartSellfoxOperationLogService.getItemLog(puid, shopId, campaignId,
                shopMap.get(shopId).getMarketplaceCode(), campaignInfo.getCampaignType(), req.getOperatorId(), existMap, Collections.singletonList(currentItem));
        ItemToAdGroupDTO itemToAdGroupDTO;
        itemToAdGroupDTO = new ItemToAdGroupDTO();
        itemToAdGroupDTO.setItemId(currentItem.getItemId());
        itemToAdGroupDTO.setStatus(currentItem.getStatus());
        itemToAdGroupDTO.setBid(currentItem.getBid());
        itemToAdGroupDTO.setAdGroupId(currentItem.getAdGroupId());
        itemToAdGroupDTO.setCampaignId(currentItem.getCampaignId());
        try {
            walmartAdvertisingItemClientService.updateItem(itemToAdGroupDTO);
        } catch (ServiceException e) {
            //写日志
            operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            operationLog.setResultInfo(e.getMsg());
            walmartSellfoxOperationLogService.printAdOperationLog(Collections.singletonList(operationLog));
            throw new SponsoredBizException(e.getMsg());
        }
        int result = update(puid, currentItem);
        if (result > 0) {
            //写日志
            operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            walmartSellfoxOperationLogService.printAdOperationLog(Collections.singletonList(operationLog));
            return true;
        } else {
            //写日志
            operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            operationLog.setResultInfo("更新数据库失败");
            walmartSellfoxOperationLogService.printAdOperationLog(Collections.singletonList(operationLog));
            return false;
        }
    }

    @Override
    public Page<WalmartItemBidRecommend> getBidRecommend(int puid, List<Integer> shopIdList,
                                                         List<String> itemIdList, Integer pageNo,
                                                         Integer pageSize) {
        if (CollectionUtils.isEmpty(itemIdList)) {
            return new Page<>();
        }
        if (puid == 0) {
            log.error("walmart puid is null");
            throw new ServiceException("puid不能为空");
        }
        //为分页参数设置默认值
        pageNo = Math.max(pageNo, 0);
        pageSize = pageSize <= 0 ? 10 : Math.min(pageSize, 200);
        WalmartAdvertisingItemRecommendations lastItemRecommendations = walmartAdvertisingItemRecommendationsDao.getLastItemRecommendations(puid);
        if(lastItemRecommendations == null || lastItemRecommendations.getReportDate() == null){
            return new Page<>();
        }
        Page<WalmartAdvertisingItemRecommendations> page = walmartAdvertisingItemRecommendationsDao.getPageList(puid, shopIdList,
                itemIdList, pageNo, pageSize);
        if (page == null || CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>();
        }
        //组装返回数据
        Page<WalmartItemBidRecommend> resultPage = new Page<>();
        resultPage.setRows(page.getRows().stream().map(row -> WalmartItemBidRecommend.builder()
                .itemId(row.getItemId())
                .shopId(row.getShopId())
                .bid(Optional.ofNullable(row.getSuggestedBid()).map(String::valueOf).orElse("0.0D"))
                .marketplaceCode(Optional.ofNullable(row.getMarketplaceCode()).orElse(""))
                .build()).collect(Collectors.toList()));
        resultPage.setPageNo(page.getPageNo());
        resultPage.setPageSize(page.getPageSize());
        resultPage.setTotalSize(page.getTotalSize());
        resultPage.setTotalPage(page.getTotalPage());
        return resultPage;
    }

    @Override
    public List<SearchItemsDataResponseVO> getItemSuggest(int puid, Integer shopId, List<String> itemIdList) {
        if (CollectionUtils.isEmpty(itemIdList)) {
            return new ArrayList<>();
        }
        if (puid == 0) {
            log.error("walmart puid is null");
            throw new ServiceException("puid不能为空");
        }
        //校验是否含有写权限
        WalmartAdvertiserAttributes advertiserAttributes = walmartAdvertiserAttributesService.checkAttributesAndWrite(puid, shopId);
        itemIdList = itemIdList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        //为分页参数设置默认值
        int listSize = itemIdList.size();
        listSize = Math.min(listSize, 200);
        itemIdList = itemIdList.subList(0, listSize);
        WalmartAdvertiserClient advertisingClient = new WalmartAdvertiserClient();
        SearchItemsResponse response = advertisingClient.searchItems(advertiserAttributes.getAdvertiserId(), null, itemIdList);
        if (response == null) {
            throw new ServiceException("搜索广告产品失败！平台响应response is null");
        }
        if (StringUtils.isNotBlank(response.getError())) {
            throw new ServiceException("搜索广告产品失败！平台响应：" + response.getError());
        }
        if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
            throw new ServiceException("搜索广告产品失败！平台响应:" + response.getDetailsObj().getDescription());
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            throw new ServiceException("搜索广告产品失败，请稍后重试！");
        }
        List<SearchItemsDataResponseVO> responseVO = response.getData();
        if (CollectionUtils.isEmpty(responseVO)) {
            return Collections.emptyList();
        }
        return responseVO;
    }

    @Override
    public int deleteByCampaignId(Integer puid, Integer shopId, String campaignId) {
        return walmartAdvertisingItemDao.deleteByCampaignId(puid, shopId, campaignId);
    }

    @Override
    public List<WalmartAdvertisingItem> getByCampaignId(Integer puid, Integer shopId, String campaignId) {
        return walmartAdvertisingItemDao.getByCampaignId(puid, shopId, campaignId);
    }

    @Override
    public int syncItemByCampaigns(Integer puid, List<Integer> shopIdList,
                                   WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException {
        int count = 0;
        ListAllTheItemsCampaignsResponse response;
        for (WalmartAdvertisingCampaign campaign : campaigns) {
            response = advertiserClient.getListAllTheItemsCampaign(campaign.getCampaignId());
            if (response == null) {
                throw new ServiceException("同步产品失败！平台响应response is null");
            }
            if (StringUtils.isNotBlank(response.getError())) {
                throw new ServiceException("同步产品失败！平台响应：" + WalmartAdvetisingUtil.getErrorStr(response.getError()));
            }
            if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
                throw new ServiceException("同步产品失败！平台响应:" + WalmartAdvetisingUtil.getErrorStr(response.getDetailsObj().getDescription()));
            }
            if (CollectionUtils.isEmpty(response.getData())) {
                continue;
            }
            List<WalmartAdvertisingItem> oldItem = getByCampaignId(puid, campaign.getShopId(), campaign.getCampaignId());
            if (CollectionUtils.isEmpty(oldItem)) {
                for (ItemsCampaignDataResponseVO responseVO : response.getData()) {
                    WalmartAdvertisingItem item = new WalmartAdvertisingItem();
                    item.setPuid(puid);
                    item.setShopId(campaign.getShopId());
                    item.setMarketplaceCode(campaign.getMarketplaceCode());
                    itemsCampaignDataToSfItem(responseVO, item);
                    add(item);
                    count++;
                }
            } else {
                Map<String, ItemsCampaignDataResponseVO> updateData = new HashMap<>();
                for (ItemsCampaignDataResponseVO responseVO : response.getData()) {
                    updateData.put(responseVO.getAdGroupId() + "_" + responseVO.getItemId(), responseVO);
                }
                for (WalmartAdvertisingItem item : oldItem) {
                    if (updateData.containsKey(item.getAdGroupId() + "_" + item.getItemId())) {
                        ItemsCampaignDataResponseVO responseVO = updateData.get(item.getAdGroupId() + "_" + item.getItemId());
                        itemsCampaignDataToSfItem(responseVO, item);
                        update(puid, item);
                        updateData.remove(item.getAdGroupId() + "_" + item.getItemId());
                        count++;
                    } else {
                        delete(puid, item.getId());
                    }
                }
                if (MapUtils.isNotEmpty(updateData)) {
                    WalmartAdvertisingItem item;
                    for (String key : updateData.keySet()) {
                        ItemsCampaignDataResponseVO responseVO = updateData.get(key);
                        item = new WalmartAdvertisingItem();
                        item.setPuid(puid);
                        item.setShopId(campaign.getShopId());
                        item.setMarketplaceCode(campaign.getMarketplaceCode());
                        itemsCampaignDataToSfItem(responseVO, item);
                        add(item);
                        count++;
                    }
                }
            }
        }
        return count;
    }

    private WalmartAdItemsVo checkItemPageQueryParam (WalmartAdItemsVo reqVo) {
        //处理广告活动类型
        if (StringUtils.isNotEmpty(reqVo.getAdType())) {
            WalmartAdCampaignTypeEnum campaignType = WalmartAdCampaignTypeEnum.campaignTypeFrontMap.get(reqVo.getAdType());
            if (Objects.isNull(campaignType)) {
                throw new ServiceException("广告类型错误");
            }
            reqVo.setAdType(campaignType.getCode());
        } else {
            reqVo.setAdType(WalmartAdCampaignTypeEnum.SP_CAMPAIGN.getCode());
        }
        //如果入参中包含广告活动，校验广告活动id
        //如果没有直接指定campaignId而是指定了活动类型和投放类型，先过滤出对应的活动，再查询广告组数据
        if (CollectionUtils.isNotEmpty(reqVo.getCampaignIdList()) || StringUtils.isNotEmpty(reqVo.getAdType())
                || StringUtils.isNotEmpty(reqVo.getTargetingType())) {
            List<WalmartAdTargetTypeEnum> targetEnumList = new ArrayList<>();
            if (StringUtils.isNotEmpty(reqVo.getTargetingType())) {
                List<String> targetTypeList = Optional.ofNullable(reqVo.getTargetingType()).map(t -> t.split(",")).map(Arrays::asList).orElse(new ArrayList<>());
                for (String target : targetTypeList) {
                    WalmartAdTargetTypeEnum targetEnum = WalmartAdTargetTypeEnum.getWalmartAdTargetTypeEnumByCode(target);
                    Optional.ofNullable(targetEnum).ifPresent(targetEnumList::add);
                }
            }
            List<WalmartAdvertisingCampaign> campaigns = walmartAdvertisingCampaignDao
                    .getByCampaignIdsAndType(reqVo.getPuid(), reqVo.getShopIdList(),
                            reqVo.getCampaignIdList(), reqVo.getAdType(), targetEnumList.parallelStream().map(WalmartAdTargetTypeEnum::getCode).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(campaigns)) {
                return null;
            }
            List<String> campaignIdList = campaigns.stream().map(WalmartAdvertisingCampaign::getCampaignId).collect(Collectors.toList());
            reqVo.setCampaignIdList(campaignIdList);
        }
        //校验广告组id
        if (CollectionUtils.isNotEmpty(reqVo.getGroupIdList())) {
            List<WalmartAdvertisingGroup> queryGroupIds = walmartAdvertisingGroupDao.getGroupIdsByShopIdAndCampaignIdsAndGroupIds(reqVo.getPuid(),
                    reqVo.getShopIdList(), reqVo.getCampaignIdList(), reqVo.getGroupIdList());
            if (CollectionUtils.isNotEmpty(reqVo.getGroupIdList())) {
                reqVo.setGroupIdList(queryGroupIds.stream().map(WalmartAdvertisingGroup::getAdGroupId).collect(Collectors.toList()));
            }
            else {
                log.error("campaign not exist, puid:{}, shopIds:{}, campaignIds:{}, adGroupIds:{}", reqVo.getPuid(),
                        reqVo.getShopIdList(), reqVo.getCampaignIdList(), reqVo.getGroupIdList());
                throw new ServiceException("广告组不存在");
            }
        }
        return reqVo;
    }

    private void itemsCampaignDataToSfItem(ItemsCampaignDataResponseVO responseVO, WalmartAdvertisingItem item) {
        item.setCampaignId(responseVO.getCampaignId());
        item.setAdGroupId(responseVO.getAdGroupId());
        item.setItemId(responseVO.getItemId());
        item.setAdItemId(responseVO.getAdItemId());
        item.setBid(responseVO.getBid());
        item.setStatus(responseVO.getStatus());
        item.setItemImageUrl(responseVO.getItemImageUrl());
        item.setItemPageUrl(responseVO.getItemPageUrl());
        item.setName(responseVO.getName());
        item.setReviewStatus(responseVO.getReviewStatus());
        item.setReviewReason(responseVO.getReviewReason());
    }

    @Override
    public void add(WalmartAdvertisingItem group) {
         walmartAdvertisingItemDao.add(group);
    }

    private void fillDefaultVal(WalmartAdvertisingItemPageDTO report, MultiPlatformShopAuth shopInfo,
                                Map<String, WalmartAdvertisingCampaign> campaignInfoMap, Map<String, WalmartAdvertisingGroup> groupInfoMap, boolean aggregate) {
        if (report == null) {
            return;
        }
        report.setState(Optional.ofNullable(report.getState()).orElse(""));
        if (!aggregate) {
            report.setBid(Optional.ofNullable(report.getBid()).orElse(0.0D));
        }
        report.setItemImageUrl(Optional.ofNullable(report.getItemImageUrl()).orElse(""));
        report.setItemPageUrl(Optional.ofNullable(report.getItemPageUrl()).orElse(""));
        Optional.ofNullable(shopInfo).map(MultiPlatformShopAuth::getName).ifPresent(report::setShopName);
        Optional.ofNullable(campaignInfoMap).map(cMap -> cMap.get(report.getCampaignId())).map(WalmartAdvertisingCampaign::getName).ifPresent(report::setCampaignName);
        Optional.ofNullable(campaignInfoMap).map(cMap -> cMap.get(report.getCampaignId())).map(WalmartAdvertisingCampaign::getTargetingType).ifPresent(report::setTargetingType);
        report.setDailyBudget(Optional.ofNullable(campaignInfoMap)
                .map(cMap -> cMap.get(report.getCampaignId())).map(WalmartAdvertisingCampaign::getDailyBudget)
                .map(BigDecimal::new).map(d -> d.setScale(2, RoundingMode.HALF_UP)).map(BigDecimal::doubleValue).orElse(0.0D));
        Optional.ofNullable(groupInfoMap).map(gMap -> gMap.get(report.getAdGroupId())).map(WalmartAdvertisingGroup::getName).ifPresent(report::setGroupName);
        report.setMarketplaceId(Optional.ofNullable(report.getMarketplaceId())
                .orElse(Optional.ofNullable(shopInfo).map(MultiPlatformShopAuth::getMarketplaceCode).orElse("")));
    }
}
