package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.enums.SbAdGroupTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2024-01-23  21:08
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SbAdGroupTypeDto {

    private String campaignId;

    private String groupId;

    private SbAdGroupTypeEnum sbAdGroupTypeEnum;

}
