package com.meiyunji.sponsored.service.attribution.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 站外流量归因标签表
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_amazon_ad_attribution_tag")
public class AmazonAdAttributionTag implements Serializable {
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    /**
    * 商户uid
    */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
    * 店铺ID
    */
    private Integer shopId;

    /**
    * 站点
    */
    private String marketplaceId;

    /**
    * 广告主ID
    */
    private String advertiserId;

    /**
    * 渠道id
    */
    private String publisherId;

    /**
    * 是否宏标签 0:否,1:是
    */
    private Integer macroEnable;

    /**
    * 标签
    */
    private String tag;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}