package com.meiyunji.sponsored.service.syncAd.task.init.sd;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncConstant;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncTaskTypeEnum;
import com.meiyunji.sponsored.service.syncAd.po.AmazonAdShopDataInitTask;
import com.meiyunji.sponsored.service.syncAd.task.init.AdShopDataSyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-01-31  17:14
 */

@Component(ShopDataSyncConstant.sd + ShopDataSyncConstant.targeting)
public class SdTargetingTask extends AdShopDataSyncTask {

    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;

    @PostConstruct
    public void init() {
        setAdType(ShopDataSyncAdTypeEnum.sd);
        setTaskType(ShopDataSyncTaskTypeEnum.TARGETING);
    }

    @Override
    protected final void doSync(ShopAuth shop, AmazonAdProfile profile, AmazonAdShopDataInitTask task) {
        //同步商品投放
        cpcSdTargetingApiService.syncTargetings(shop, null, task.getAdGroupId(), null, null, true);
    }
}
