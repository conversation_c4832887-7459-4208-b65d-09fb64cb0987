package com.meiyunji.sponsored.service.product.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.product.dao.IAsinInfoDao;
import com.meiyunji.sponsored.service.product.po.AsinInfo;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * AsinInfo
 * <AUTHOR>
 */
@Repository
public class AsinInfoDaoImpl extends AdBaseDaoImpl<AsinInfo> implements IAsinInfoDao {

    private static final String tableName = "t_amazon_asin_info_";

    @Override
    public List<AsinInfo> getRatingByAsinList(HashSet<String> list, String marketplace) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select asin, title, star_rating, price, rating_count, image from ").append(tableName + marketplace).append(" where ");
        sql.append(SqlStringUtil.dealInListNotAnd("asin", Arrays.asList(list.toArray()), argsList));
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinInfo.class), argsList.toArray());
    }




    @Override
    public int  insertOrUpdate(List<AsinInfo> list, String marketplaceId) {
        StringBuilder sql = new StringBuilder("INSERT INTO "+ getTableName(marketplaceId) )
                .append("  (marketplace_id, asin, image, title, star_rating, price, rating_count, is_need_sync , update_time, is_valid ) ")
                .append(" VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AsinInfo asinInfo : list) {
            sql.append(" (?,?,?,?,?,?,?,0,now(),1),");
            argsList.add(asinInfo.getMarketplaceId());
            argsList.add(asinInfo.getAsin());
            argsList.add(asinInfo.getImage());
            argsList.add(asinInfo.getTitle());
            argsList.add(asinInfo.getStarRating());
            argsList.add(asinInfo.getPrice());
            argsList.add(asinInfo.getRatingCount());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `image`=values(image),`title`=values(title),")
                .append("`star_rating`=values(star_rating),`price`=values(price),`rating_count`=values(rating_count),")
                .append("`is_need_sync`=values(is_need_sync),`update_time`= values(update_time) ,`is_valid`= values(is_valid)");

        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }



    @Override
    public int  insertOrUpdateIsNeedSync(List<String> asins, String marketplaceId) {
        StringBuilder sql = new StringBuilder("INSERT INTO "+ getTableName(marketplaceId) )
                .append("  (marketplace_id, asin, is_need_sync, update_time) ")
                .append(" VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (String asin : asins) {
            sql.append(" (?,?,1,now()),");
            argsList.add(marketplaceId);
            argsList.add(asin);
        }
        sql.deleteCharAt(sql.length() - 1);
        //大于 30 小时才更新updateTime 才会拿出数据进行同步，否则将不会同步
        sql.append(" on duplicate key update `marketplace_id`=values(marketplace_id),`asin`=values(asin),")
                .append("`is_need_sync`= IF(TIMESTAMPDIFF(HOUR, update_time, now())  > 30, VALUES(is_need_sync), is_need_sync),  update_time = IF(TIMESTAMPDIFF(HOUR, update_time, now())  > 30, VALUES(update_time), update_time) ");

        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }



    @Override
    public List<String> getNeedSyncAsin(String marketplaceId, Date updateTime, Integer limit) {
        StringBuilder sql = new StringBuilder("select asin from "+ getTableName(marketplaceId) + " where is_need_sync = 1  ");

        if (updateTime != null) {
            sql.append(" and update_time > ? ");
            if (limit == null  || limit  < 1) {
                sql.append(" limit 1000 ");
            } else {
                sql.append(" limit  " + limit);
            }
            return getJdbcTemplate().queryForList(sql.toString(), String.class, updateTime);
        } else {
            if (limit == null  || limit  < 1) {
                sql.append(" limit 1000 ");
            } else {
                sql.append(" limit  " + limit);
            }
            return getJdbcTemplate().queryForList(sql.toString(), String.class);
        }


    }

    @Override
    public int updateIsNeedSync(String marketplaceId, List<String> asins) {
        StringBuilder sql = new StringBuilder(" update "+ getTableName(marketplaceId) )
                .append("  set is_need_sync = 0 where 1=1  ");
        List<Object> argsList = new ArrayList<>();
        sql.append(SqlStringUtil.dealInList("asin", asins, argsList));
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());

    }

    @Override
    public int updateValid(String marketplaceId, List<String> asins) {
        StringBuilder sql = new StringBuilder(" update "+ getTableName(marketplaceId) )
                .append("  set is_valid = 0, is_need_sync = 0, update_time = now() where 1=1  ");
        List<Object> argsList = new ArrayList<>();
        sql.append(SqlStringUtil.dealInList("asin", asins, argsList));
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());

    }

    private String getTableName (String marketplaceId) {
        return tableName + marketplaceId;
    }
}
