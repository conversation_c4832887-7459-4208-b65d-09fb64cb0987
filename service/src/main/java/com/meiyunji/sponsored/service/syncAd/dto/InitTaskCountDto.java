package com.meiyunji.sponsored.service.syncAd.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-02-21  13:45
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class InitTaskCountDto {

    private Byte state;

    private Integer count;

    private Date updateTime;

}
