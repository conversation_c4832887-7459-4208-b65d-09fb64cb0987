package com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meiyunji.sponsored.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-30  15:15
 */
@Data
public class ViewBaseParam {
    @ApiModelProperty("pageSign")
    private String pageSign;

    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;

    @ApiModelProperty(value = "站点",required = true)
    private String marketplaceId;

    @ApiModelProperty(value = "店铺",required = true)
    private List<Integer> shopIdList;

    @Deprecated
    @ApiModelProperty(value = "asin(已弃用，改用searchType和searchValue)",required = true)
    private String asin;

    @ApiModelProperty(value = "搜索类型（asin、msku、parent_asin）",required = true)
    private String searchType;

    @ApiModelProperty(value = "搜索值",required = true)
    private String searchValue;

    @ApiModelProperty(value = "广告活动类型",required = true)
    private String type;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("排序字段")
    private String orderField;

    @ApiModelProperty("排序类型")
    private String orderType;

    //环比相关参数
    @ApiModelProperty(value = "是否对比")
    private Boolean isCompare;
    @ApiModelProperty(value = "对比开始时间")
    private String compareStartDate;
    @ApiModelProperty(value = "对比结束时间")
    private String compareEndDate;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;
    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;
    @ApiModelProperty(value = "vcpm最小值")
    private BigDecimal vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private BigDecimal vcpmMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private BigDecimal acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private BigDecimal acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private BigDecimal asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private BigDecimal asotsMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;
    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;
    @ApiModelProperty(value = "高级搜索广告销售额最小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索广告销售额最大值")
    private BigDecimal salesMax;
    @ApiModelProperty(value = "本广告产品销售额最小值")
    private BigDecimal adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private BigDecimal adSalesMax;
    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private BigDecimal adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private BigDecimal adOtherSalesMax;
    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;
    @ApiModelProperty(value = "本广告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本广告产品销量最大值")
    private Integer adSelfSaleNumMax;
    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;
    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private BigDecimal orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private BigDecimal orderRateNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private BigDecimal salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private BigDecimal salesNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private BigDecimal salesRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private BigDecimal salesRateNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;
    @ApiModelProperty(value = "“品牌新买家”订单转化率最小值")
    private BigDecimal brandNewBuyerOrderConversionRateMin;
    @ApiModelProperty(value = "“品牌新买家”订单转化率最大值")
    private BigDecimal brandNewBuyerOrderConversionRateMax;
    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    private Integer puid;
    private Integer uid;
    private List<String> adIdList;
    private List<String> spAdIdList;
    private List<String> sbAdIdList;
    private List<String> sdAdIdList;
    private List<String> sellerIdList;
    private BigDecimal shopSales;


    private List<String> spAdTargetIdList;
    private List<String> sbAdTargetIdList;
    private List<String> sdAdTargetIdList;

    private String exportFileName;

    public List<String> searchValueList() {
        if (StringUtils.isBlank(searchValue)) {
            return Lists.newArrayList();
        }
        return StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
    }

    public Set<String> searchValueSet() {
        List<String> list = searchValueList();
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        return Sets.newHashSet(list);
    }

    public enum SearchTypeEnum {
        ASIN("asin", "ASIN"),
        MSKU("msku", "MSKU"),
        PARENT_ASIN("parentAsin", "父ASIN");

        private String value;
        private String desc;

        SearchTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public String getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }

        public static SearchTypeEnum getEnum(String value) {
            for (SearchTypeEnum searchTypeEnum : SearchTypeEnum.values()) {
                if (searchTypeEnum.getValue().equals(value)) {
                    return searchTypeEnum;
                }
            }
            return null;
        }
    }
}
