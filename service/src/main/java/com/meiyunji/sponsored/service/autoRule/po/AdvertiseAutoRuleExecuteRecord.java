package com.meiyunji.sponsored.service.autoRule.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_ad_auto_rule_execute_record")
public class AdvertiseAutoRuleExecuteRecord implements Serializable {

    /**
     * 主键id
     */
    @DbColumn(value = "id")
    private Long id;

    /**
     * 任务id
     */
    @DbColumn(value = "task_id")
    private Long taskId;

    /**
     * 执行记录id
     */
    @DbColumn(value = "record_id")
    private Long recordId;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 配置id
     */
    @DbColumn(value = "profile_id")
    private String profileId;

    /**
     * 规则类型
     */
    @DbColumn(value = "rule_type")
    private String ruleType;

    /**
     * 受控对象类型
     */
    @DbColumn(value = "item_type")
    private String itemType;

    /**
     * 操作对象id
     */
    @DbColumn(value = "item_operate_id")
    private String itemOperateId;

    /**
     * 操作对象名称
     */
    @DbColumn(value = "item_operate_name")
    private String itemOperateName;

    /**
     * 操作对象类型
     */
    @DbColumn(value = "item_operate_type")
    private String itemOperateType;


    /**
     * 受控对象名称
     */
    @DbColumn(value = "item_name")
    private String itemName;

    /**
     * 规则类型
     */
    @DbColumn(value = "ad_type")
    private String adType;

    /**
     * 模板id
     */
    @DbColumn(value = "template_id")
    private Long templateId;

    /**
     * 模板名称
     */
    @DbColumn(value = "template_name")
    private String templateName;

    /**
     * 数据详情json
     */
    @DbColumn(value = "data_detail")
    private String dataDetail;

    /**
     * 受控对象id
     */
    @DbColumn(value = "item_id")
    private String itemId;

    /**
     * 微信推送状态
     */
    @DbColumn(value = "wx_push_state")
    private String wxPushState;

    /**
     * 站内消息推送状态
     */
    @DbColumn(value = "instation_push_state")
    private String instationPushState;

    /**
     * 操作状态
     */
    @DbColumn(value = "code")
    private String code;


    /**
     * 操作状态
     */
    @DbColumn(value = "state_err_msg")
    private String stateErrMsg;

    /**
     * 规则json
     */
    @DbColumn(value = "rule")
    private String rule;

    /**
     * 达成后的操作
     */
    @DbColumn(value = "perform_operation")
    private String performOperation;

    /**
     * 执行方式
     */
    @DbColumn(value = "execute_type")
    private String executeType;

    /**
     * 运行时间
     */
    @DbColumn(value = "execute_at",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime executeAt;

    /**
     * 运行时间
     */
    @DbColumn(value = "trigger_at",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime triggerAt;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @DbColumn(value = "update_time",type = DbColumn.ColumnType.QUERY_ONLY)
    private LocalDateTime updateTime;

    @DbColumn(value = "sku")
    private String sku;

    @DbColumn(value = "asin")
    private String asin;

    @DbColumn(value = "time_type")
    private String TimeType;

    @DbColumn(value = "start_date")
    private LocalDate startDate;

    @DbColumn(value = "end_date")
    private LocalDate endDate;

    @DbColumn(value = "time_rule")
    private String timeRule;

    @DbColumn(value = "desired_position")
    private String desiredPosition;

    @DbColumn(value = "ad_data_rule")
    private String adDataRule;

    @DbColumn(value = "ad_data_operate")
    private String adDataOperate;

    @DbColumn(value = "auto_price_rule")
    private String autoPriceRule;

    @DbColumn(value = "auto_price_operate")
    private String autoPriceOperate;

    @DbColumn(value = "bidding_callback_operate")
    private String biddingCallbackOperate;

    @DbColumn(value = "check_frequency")
    private String checkFrequency;

    @DbColumn(value = "postal_code_settings")
    private String postalCodeSettings;

    @DbColumn(value = "ad_rank_data_detail")
    private String adRankDataDetail;

    @DbColumn(value = "ad_data_detail")
    private String adDataDetail;

    @DbColumn(value = "count_result")
    private Integer countResult;

    @DbColumn(value = "overtop_count")
    private Integer overtopCount;

    @DbColumn(value = "operation")
    private String operation;

    @DbColumn(value = "after_operation_rank")
    private String afterOperationRank;

    @DbColumn(value = "unadjusted_reason")
    private String unadjustedReason;

    @DbColumn(value = "continuous_count")
    private Integer continuousCount;

    @DbColumn(value = "item_status")
    private String itemStatus;

    @DbColumn(value = "target_type")
    private String targetType;

    @DbColumn(value = "set_relation")
    private String setRelation;

    @DbColumn(value = "callback_operate")
    private String callbackOperate;

    @DbColumn(value = "execute_time_space_value")
    private String executeTimeSpaceValue;

    @DbColumn(value = "execute_time_space_unit")
    private String executeTimeSpaceUnit;

    @DbColumn(value = "message_reminder_type")
    private String messageReminderType;

    @DbColumn(value = "operation_type")
    private Integer operationType;

    @DbColumn(value = "query_type")
    private String queryType;

    @DbColumn(value = "campaign_id")
    private String campaignId;

    @DbColumn(value = "ad_group_id")
    private String adGroupId;

    @DbColumn(value = "execute_batch_id")
    private String executeBatchId = "";

    private String originalValue;
    private String executeValue;
    private String ruleActionType;
    private String campaignName;
    private String adGroupName;
    private LocalDateTime executeSiteDate;
    private String negativeTargetType;
    private String originalAdPlaceTopValue;
    private String originalAdPlaceProductValue;
    private String originalAdOtherValue;
    private String executeAdPlaceTopValue;
    private String executeAdPlaceProductValue;
    private String executeAdOtherValue;
    private String addTargetType;
    private Integer hasSimilarRule;
    private String portfolioId;
    private String portfolioName;
    private String recordExecuteType;

    private String keywordCardKeywordText;


    public String getMarketplaceName(){
        if (this.marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplaceCN();
        }
        return null;
    }
}
