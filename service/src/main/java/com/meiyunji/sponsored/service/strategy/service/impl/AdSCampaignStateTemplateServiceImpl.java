package com.meiyunji.sponsored.service.strategy.service.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyScheduleDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateSequenceDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.AdvertiseStrategyTemplateService;
import com.meiyunji.sponsored.service.strategy.vo.AdvertiseStrategyTemplateRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdSCampaignStateTemplateServiceImpl implements AdvertiseStrategyTemplateService {

    @Autowired
    private AdvertiseStrategyTemplateDao strategyTemplateDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao;

    @Override
    public boolean checkValid(String itemType) {
        return "START_STOP".equals(itemType);
    }

    @Override
    public Result<Page<AdvertiseStrategyTemplate>> pageList(int puid, AdvertiseStrategyTemplateRequest request) {
        Result<Page<AdvertiseStrategyTemplate>> result = new Result<>();
        try {
            Page<AdvertiseStrategyTemplate> voPage = new Page<>();
            Page<AdvertiseStrategyTemplate> page = strategyTemplateDao.pageList(puid,request);
            voPage.setPageNo(page.getPageNo());
            voPage.setPageSize(page.getPageSize());
            voPage.setTotalSize(page.getTotalSize());
            voPage.setTotalPage(page.getTotalPage());
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                List<AdvertiseStrategyTemplate> list = page.getRows();
                List<AdvertiseStrategyTemplate> advertiseStrategyTemplateList = Lists.newArrayListWithExpectedSize(list.size());
                List<Long> templateIds = list.stream().map(AdvertiseStrategyTemplate::getId).collect(Collectors.toList());
                List<Long> ids = advertiseStrategyStatusDao.getTemplateUsageAmount(puid,templateIds,request.getItemType());
                Map<Long,List<Long>> map = ids.stream().collect(Collectors.groupingBy(e->e));
                for (AdvertiseStrategyTemplate advertiseStrategyTemplate : list) {
                    if (MapUtils.isNotEmpty(map) && map.containsKey(advertiseStrategyTemplate.getId())) {
                        advertiseStrategyTemplate.setUsageAmount(map.get(advertiseStrategyTemplate.getId()).size());
                    }
                    advertiseStrategyTemplateList.add(advertiseStrategyTemplate);
                }
                voPage.setRows(advertiseStrategyTemplateList);
            }
            result.setCode(Result.SUCCESS);
            result.setData(voPage);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setData(null);
            log.error("puid={} shopIdList={} 分页查询模板数据异常:",puid,request.getShopIdList(),e);
        }
        return result;
    }

    @Override
    public Result<Long> insertTemplate(Integer puid, AdvertiseStrategyTemplate template) {
        Result<Long> result = new Result<>();

        if (StringUtils.isBlank(template.getTemplateName())) {
            return ResultUtil.error("模板名称为空");
        }

        if (template.getTemplateName().length() > 30) {
            return ResultUtil.error("模板名称不能超过30个字符");
        }

        if (strategyTemplateDao.existByName(puid, template.getTemplateName().trim(),template.getItemType())) {
            return ResultUtil.error("模板名称已存在");
        }
        try {
            Long id = advertiseStrategyTemplateSequenceDao.genId();;
            template.setId(id);
            strategyTemplateDao.insertTemplate(puid,template);
            result.setCode(Result.SUCCESS);
            result.setData(id);
        } catch (Exception e) {
            log.error("puid={}  shopId={} 模板插入异常{}",puid,template.getShopId(),e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
        }
        return result;
    }

    @Override
    public Result<Long> updateTemplate(Integer puid, AdvertiseStrategyTemplate template) {
        Result<Long> result = new Result<>();
        try {
            if (template.getTemplateName().length() > 30) {
                return ResultUtil.error("模板名称不能超过30个字符");
            }
            //版本号处理
            Integer versionId = strategyTemplateDao.selectByPrimaryKey(puid,template.getId()).getVersion();
            template.setVersion(versionId + 1);
            strategyTemplateDao.updateTemplate(puid,template);
            result.setCode(Result.SUCCESS);
            result.setData(template.getId());
            result.setMsg("修改成功");
        } catch (Exception e) {
            log.error("puid={} id={},模板修改异常{}",puid,template.getId(),e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
        }
        return result;
    }

    @Override
    public Result<String> deleteTemplate(Integer puid, Long id) {
        Result<String> result = new Result<>();
        try {
            if (advertiseStrategyStatusDao.existListByTemplateId(puid,id) > 0) {
                return ResultUtil.error("当前模板存在受控对象不能删除");
            }
            strategyTemplateDao.deleteTemplateId(puid, id);  //删除模板
            result.setCode(Result.SUCCESS);
            result.setMsg("删除成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("当前模板删除异常");
            log.error("templateId={} 查询当前模板数据异常:{}",id,e);
        }
        return result;
    }

    @Override
    public Result<AdvertiseStrategyTemplate> copyTemplate(Integer puid, Long id) {
        Result<AdvertiseStrategyTemplate> result = new Result<>();
        try {
            AdvertiseStrategyTemplate advertiseStrategyTemplate = strategyTemplateDao.selectByPrimaryKey(puid,id);
            result.setCode(Result.SUCCESS);
            result.setData(advertiseStrategyTemplate);
        }catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("复制模板异常");
            log.error("templateId={}  复制模板异常:{}",id,e);
        }
        return result;
    }

}
