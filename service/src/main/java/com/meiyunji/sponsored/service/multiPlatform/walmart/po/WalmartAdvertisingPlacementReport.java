package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告位报告
 */
@Data
@DbTable(value = "t_walmart_advertising_placement_report")
public class WalmartAdvertisingPlacementReport extends WalmartAdReportInStoreBase implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @DbColumn(value = "id",autoIncrement=true,key = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点code
     */
    @DbColumn(value = "marketplace_code")
    private String marketplaceCode;

    /**
     * 报告日期
     */
    @DbColumn(value = "report_date")
    private Date reportDate;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 广告位
     */
    @DbColumn(value = "placement")
    private String placement;



    /**
     * 销售额
     */
    private Double sales;

    /**
     * 订单量
     */
    private Integer orderQuantity;

    /**
     * 销量
     */
    private Integer saleQuantity;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @DbColumn(value = "update_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    //非数据库字段
    private Double cpc; //平均点击费用（adSpend/numAdsClicks）
    private Double acos; //花费销售比（adSpend/sales）
    private Double clickRate; //点击率（numAdsClicks/numAdsShown）
    private Double salesConversionRate; //下单转化率（orderQuantity/numAdsClicks）


    public String getIdStr() {
        return id == null ? null : id.toString();
    }

}
