package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sellfox.ams.api.entry.ProductPerspectiveAnalysisAdvancedFilterDataPb;
import com.meiyunji.sellfox.ams.api.service.AmsApiGrpc;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.helper.ViewServiceHelper;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.ISearchTermsViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 搜索词
 * <AUTHOR>
 * @date 2024/05/16
 */
@Service
@Slf4j
public class SearchTermsViewServiceImpl implements ISearchTermsViewService {
    @Qualifier("adFeedBlockingStub")
    @Autowired
    private AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdOperationLogService amazonAdOperationLogService;
    @Autowired
    private ICpcTargetingReportDao cpcTargetingReportDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonSpAdProductDorisDao amazonAdProductReportDao;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    @Autowired
    private ICpcQueryKeywordDorisReportDao cpcQueryKeywordReportDao;
    @Autowired
    private IOdsAmazonSbAdsDao odsAmazonSbAdsDao;
    @Autowired
    private IOdsAmazonAdSbAdsReportDao odsAmazonAdSbAdsReportDao;
    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;
    @Autowired
    private IWordTranslateService wordTranslateService;



    @Override
    public SearchTermsViewPageVo getSearchTermsViewPageVoList(Integer puid, SearchTermsViewParam param) {
        String title = "搜索词视图接口调用";
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析 {} --{}-参数 {}", uuid, title, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        SearchTermsViewPageVo searchTermsViewPageVo = new SearchTermsViewPageVo();
        Page<SearchTermsViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        searchTermsViewPageVo.setPage(voPage);
        List<String> campaignIds = new ArrayList<>();

        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> collect = Arrays.stream(StringUtils.split(param.getCampaignId(), ",")).collect(Collectors.toList());
            campaignIds.addAll(collect);
        }

        //第一步：查询广告产品报告数据

        List<String> groupIds = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> collect = Arrays.stream(StringUtils.split(param.getGroupId(), ",")).collect(Collectors.toList());
            groupIds.addAll(collect);
        }
        List<GroupCampaignDto> dtoList = odsAmazonAdProductReportDao.getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), campaignIds,
                groupIds, param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());


        log.info("产品透视分析 {}--{}-过滤广告组- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(dtoList)) {
            return searchTermsViewPageVo;
        }

        Map<String, List<GroupCampaignDto>> stringListMap = dtoList.stream().collect(Collectors.groupingBy(GroupCampaignDto::getCampaignId));

        //第第二步：如果前端带有活动状态，服务状态需要过滤一次活动表
        t = Instant.now().toEpochMilli();
        if (StringUtils.isNotBlank(param.getStatus()) || StringUtils.isNotBlank(param.getServingStatus())) {
            //筛选活动id为空直接推出
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, param.getShopIdList(), null, param.getStatus(), param.getServingStatus(), new ArrayList<>(stringListMap.keySet()), Constants.SP);
            log.info("产品透视分析 {}--{}-筛选活动状态运行状态- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
            if (CollectionUtils.isEmpty(campaignIds)) {
                return searchTermsViewPageVo;
            }
            
            //如果之存在部分活动id，剔除不存在的活动id
            groupIds.clear();
            for (String campaignId : campaignIds) {
                //严谨一点一定不会没有，但是以免出现错误
                if (stringListMap.containsKey(campaignId)) {
                    groupIds.addAll(stringListMap.get(campaignId).stream().map(GroupCampaignDto::getAdGroupId).collect(Collectors.toList()));
                }
            }
        } else {
            groupIds = stringListMap.values().stream().flatMap(Collection::stream).map(GroupCampaignDto::getAdGroupId).collect(Collectors.toList());
        }
        param.setAdGroupIdList(groupIds);

        t = Instant.now().toEpochMilli();
        //第三步用广告组id 查询广告组报告数据
        List<AdProductReportSearchTermsViewDto> productViewList = amazonAdProductReportDao.listAmazonSpAdProduct(puid, param);
        log.info("产品透视分析 {}--{}-获取广告产品报告数据- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(productViewList)) {
            return searchTermsViewPageVo;
        }
        //若为msku或父asin，需要获取多个asin
        Set<String> asinList = new HashSet<>();
        if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
            asinList.addAll(odsAmazonAdProductReportDao.getAsinByParentAsinAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                    param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
            asinList.addAll(odsAmazonAdProductReportDao.getAsinByMskuAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                    param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        }

        t = Instant.now().toEpochMilli();
        Map<String, List<AdProductReportSearchTermsViewDto>> groupMap = productViewList.stream().collect(Collectors.groupingBy(AdProductReportSearchTermsViewDto::getAdGroupId));

        //开始计算占比


        //asin占比
        Map<String, AsinRatio> rationMap = Maps.newConcurrentMap();

        for (Map.Entry<String, List<AdProductReportSearchTermsViewDto>> entry : groupMap.entrySet()) {
            String adGroupId = entry.getKey();
            List<AdProductReportSearchTermsViewDto> list = entry.getValue();
            Long adImpressionTotal = 0L;
            Long adClickTotal = 0L;
            Integer adOrderNumTotal = 0;
            Integer saleNumTotal = 0;
            BigDecimal totalSalesTotal = BigDecimal.ZERO;
            Integer orderNumTotal = 0;
            BigDecimal adSalesTotal = BigDecimal.ZERO;
            BigDecimal costTotal = BigDecimal.ZERO;
            Integer adSaleNumTotal = 0;
            Integer salesNumTotal = 0;

            for (AdProductReportSearchTermsViewDto item : list) {
                adImpressionTotal = MathUtil.add(adImpressionTotal, item.getImpressions());
                adClickTotal = MathUtil.add(adClickTotal, item.getClicks());
                adOrderNumTotal = MathUtil.add(adOrderNumTotal, item.getAdOrderNum());
                saleNumTotal = MathUtil.add(saleNumTotal, item.getSaleNum());
                totalSalesTotal = MathUtil.add(totalSalesTotal, item.getTotalSales());
                orderNumTotal = MathUtil.add(orderNumTotal, item.getOrderNum());
                adSalesTotal = MathUtil.add(adSalesTotal, item.getAdSales());
                costTotal = MathUtil.add(costTotal, item.getCost());
                adSaleNumTotal = MathUtil.add(adSaleNumTotal, item.getAdSaleNum());
                salesNumTotal = MathUtil.add(salesNumTotal, item.getSalesNum());
            }


            AsinRatio asinRatio = new AsinRatio();
            AdProductReportSearchTermsViewDto adSearchTermsDto = new AdProductReportSearchTermsViewDto();
            //根据搜索条件获取单个广告组下对应产品数据，asin时只获取asin数据，sku和parentAsin时需要获取多个asin的数据合起来
            if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(param.getSearchType())) {
                Optional<AdProductReportSearchTermsViewDto> reportDto = list.stream().filter(e -> e.getAsin().equals(param.getSearchValue())).findFirst();
                if (reportDto.isPresent()) {
                    adSearchTermsDto = reportDto.get();
                }
            } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(item -> item.getAsin() + "#" + item.getMsku(), item -> item, (c1, c2) -> c1));
                String msku = param.getSearchValue();
                for (String asin : asinList) {
                    if (map.containsKey(asin + "#" + msku)) {
                        adSearchTermsDto.merge(map.get(asin + "#" + msku));
                    }
                }
            } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getAsin, item -> item, (c1, c2) -> c1));
                for (String asin : asinList) {
                    if (map.containsKey(asin)) {
                        adSearchTermsDto.merge(map.get(asin));
                    }
                }
            }
            //曝光占比
            Long impressions = adSearchTermsDto.getImpressions();
            BigDecimal adImpressionRatio = MathUtil.divideIfZero(impressions, adImpressionTotal, 8);
            asinRatio.setImpressionsRatio(adImpressionRatio);
            //点击占比
            Long adClick = adSearchTermsDto.getClicks();
            BigDecimal adClickRatio = MathUtil.divideIfZero(adClick, adClickTotal, 8);
            asinRatio.setClicksRatio(adClickRatio);
            //销量占比
            Integer adSaleNum = adSearchTermsDto.getAdSaleNum();
            BigDecimal adSaleNumRatio = MathUtil.divideIfZero(adSaleNum, adSaleNumTotal, 8);
            asinRatio.setAdSaleNumRatio(adSaleNumRatio);
            //订单量占比
            Integer adOrderNum = adSearchTermsDto.getAdOrderNum();
            BigDecimal adOrderNumRatio = MathUtil.divideIfZero(adOrderNum, adOrderNumTotal, 8);
            asinRatio.setAdOrderNumRatio(adOrderNumRatio);
            //销售额占比

            Integer saleNum = adSearchTermsDto.getSaleNum();
            BigDecimal saleNumRatio = MathUtil.divideIfZero(saleNum, saleNumTotal, 8);
            asinRatio.setSaleNumRatio(saleNumRatio);

            BigDecimal totalSales = adSearchTermsDto.getTotalSales();
            BigDecimal totalSalesRatio = MathUtil.divideByZero(totalSales, totalSalesTotal, 8);
            asinRatio.setTotalSalesRatio(totalSalesRatio);

            Integer orderNum = adSearchTermsDto.getOrderNum();
            BigDecimal orderNumRatio = MathUtil.divideIfZero(orderNum, orderNumTotal, 8);
            asinRatio.setOrderNumRatio(orderNumRatio);

            BigDecimal adSales = adSearchTermsDto.getAdSales();
            BigDecimal adSalesRatio = MathUtil.divideByZero(adSales, adSalesTotal, 8);
            asinRatio.setAdSalesRatio(adSalesRatio);


            //花费占比
            BigDecimal adCost = adSearchTermsDto.getCost();
            BigDecimal adCostRatio = MathUtil.divideByZero(adCost, costTotal, 8);
            asinRatio.setCostRatio(adCostRatio);


            Integer salesNum = adSearchTermsDto.getSalesNum();
            BigDecimal salesNumRatio = MathUtil.divideIfZero(salesNum, salesNumTotal, 8);
            asinRatio.setSalesNumRatio(salesNumRatio);
            rationMap.put(adGroupId,asinRatio);
        }

        log.info("产品透视分析 {}--{}-计算占比- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);

        //支持模糊查询
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> termsList = cpcQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, param);
        if (CollectionUtils.isEmpty(termsList)) {
            return searchTermsViewPageVo;
        }
        log.info("产品透视分析 {}--{}-查询搜索词耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        t = Instant.now().toEpochMilli();
        //根据占比设置指标
        termsList.forEach(item ->{
            String adGroupId = item.getAdGroupId();
            AsinRatio asinRatio = rationMap.get(adGroupId);
            if(asinRatio != null){
                //曝光
                Long totalImpressions = item.getImpressions();
                Long impressions = MathUtil.multiply(asinRatio.getImpressionsRatio(),new BigDecimal(totalImpressions)).longValue();
                item.setImpressions(impressions);
                //点击
                Long totalClick = item.getClicks();
                Long click = MathUtil.multiply(asinRatio.getClicksRatio(),new BigDecimal(totalClick)).longValue();
                item.setClicks(click);
                //销量
                Integer totalAdSaleNum = item.getAdSaleNum();
                Integer adSaleNum = MathUtil.multiply(asinRatio.getAdSaleNumRatio(),new BigDecimal(totalAdSaleNum)).intValue();
                item.setAdSaleNum(adSaleNum);
                //订单量
                Integer totalOrderNum = item.getOrderNum();
                Integer orderNum = MathUtil.multiply(asinRatio.getOrderNumRatio(),new BigDecimal(totalOrderNum)).intValue();
                item.setOrderNum(orderNum);
                //花费
                BigDecimal totalCost = item.getCost();
                BigDecimal adCost = MathUtil.multiply(asinRatio.getCostRatio(),totalCost);
                item.setCost(adCost);

                Integer adOrderNumTotal = item.getAdOrderNum();
                Integer adOrderNum = MathUtil.multiply(asinRatio.getAdOrderNumRatio(), new BigDecimal(adOrderNumTotal)).intValue();
                item.setAdOrderNum(adOrderNum);

                BigDecimal totalSalesTotal = item.getTotalSales();
                BigDecimal totalSales = MathUtil.multiply(asinRatio.getTotalSalesRatio(), totalSalesTotal);
                item.setTotalSales(totalSales);

                BigDecimal adSalesTotal = item.getAdSales();
                BigDecimal adSales = MathUtil.multiply(asinRatio.getAdSalesRatio(), adSalesTotal);
                item.setAdSales(adSales);

                Integer salesNumTotal = item.getSalesNum() == null ? 0 : item.getSalesNum();
                Integer salesNum = MathUtil.multiply(asinRatio.getSalesNumRatio(), new BigDecimal(salesNumTotal)).intValue();
                item.setSalesNum(salesNum);

                Integer totalSaleNum = item.getSaleNum();
                Integer saleNum = MathUtil.multiply(asinRatio.getSaleNumRatio(),new BigDecimal(totalSaleNum)).intValue();
                item.setSaleNum(saleNum);

            }
        });
        log.info("产品透视分析 {}--{}-计算实际值耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        //合并数据
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> resultList =
                new ArrayList<>(termsList.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery,
                        Function.identity(), (s1, s2) -> {
                            merge(s1, s2);
                            return s1;
                        }
                )).values());
        log.info("产品透视分析 {}--{}-聚合数据耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);


        //高级筛选


        AdProductReportSearchTermsViewDto sum = new AdProductReportSearchTermsViewDto();


        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = termsList.stream().map(AdProductReportSearchTermsViewDto::getQuery).distinct()
                .map(e -> new WordTranslateQo(param.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);
        //列表页数据
        Map<String, String> queryMainImageMap = termsList.stream().filter(e -> StringUtils.isNotBlank(e.getMainImage()))
                .collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery, AdProductReportSearchTermsViewDto::getMainImage, (q1, q2) -> q2));
        List<SearchTermsViewVo> voList = new ArrayList<>();
        SearchTermsViewVo vo;
        //根据指标排序，需算出所有投放指标
        for (AdProductReportSearchTermsViewDto item : resultList) {
            vo = new SearchTermsViewVo();
            vo.setQuery(item.getQuery());
            vo.setQueryCn(wordTranslateMap.getOrDefault(wordTranslateService.getWordTranslateKey(param.getMarketplaceId(), item.getQuery()), ""));
            vo.setMainImage(queryMainImageMap.get(item.getQuery()));
            vo.setIsAsin(item.getQuery().matches(Constants.ASIN_REGEX));
            vo.setType("SP");
            ViewServiceHelper.fillStreamDataIntoViewVo(vo, item, param.getShopSales());
            if (param.getUseAdvanced()) {
                if (!isFilterAdVanceData(vo, param)) {
                    continue;
                }
            }
            this.merge(sum, item);
            voList.add(vo);
        }
        if (CollectionUtils.isEmpty(voList)) {
            return searchTermsViewPageVo;
        }
        StreamDataViewVo aggregateVo = new StreamDataViewVo();
        ViewServiceHelper.fillStreamDataIntoViewVo(aggregateVo, sum, param.getShopSales());
        aggregateVo.setAdCostPercentage(sum.getCost().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdSalePercentage(sum.getTotalSales().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdOrderNumPercentage(sum.getAdOrderNum() > 0 ? "100" : "0");
        aggregateVo.setOrderNumPercentage(sum.getOrderNum() > 0 ? "100" : "0");
        searchTermsViewPageVo.setAggregateViewVo(aggregateVo);
        //获取占比指标汇总数据
        AdMetricDto adMetricDto = this.getSumAdMetricDto(sum);
        voList.forEach(e->ViewServiceHelper.filterAdMetricData(e, adMetricDto));
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            //根据选择做排序（内存）和分页（内存）
            String orderField = SearchTermsViewVo.orderFieldTransferMap.getOrDefault(param.getOrderField(), param.getOrderField());
            boolean isSorted = StringUtils.isNotBlank(orderField) && Constants.isADOrderField(orderField, SearchTermsViewVo.class);
            if (isSorted) {
                voList = PageUtil.sort(voList, orderField, param.getOrderType());
            }
            PageUtil.getPage(voPage, voList);
            log.info("产品透视分析 {} --自动投放视图汇总接口调用-排序分页-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        } else {
            PageUtil.getPage(voPage, voList);
        }
        //将搜索词为asin的转为大写
        voPage.getRows().forEach(e -> {
            if (RegexUtils.isMatch(Constants.ASIN_REGEX, e.getQuery())) {
                e.setQuery(e.getQuery().toUpperCase());
            }
        });
        searchTermsViewPageVo.setPage(voPage);
        return searchTermsViewPageVo;
    }

    @Override
    public SearchTermsViewPageVo getAllSearchTermsViewPageVoList(Integer puid, SearchTermsViewParam param) {
        String title = "搜索词视图接口调用";
        String uuid = UUID.randomUUID().toString();
        log.info("产品透视分析 {} --{}-参数 {}", uuid, title, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        SearchTermsViewPageVo searchTermsViewPageVo = new SearchTermsViewPageVo();
        Page<SearchTermsViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        searchTermsViewPageVo.setPage(voPage);
        List<String> types = StringUtil.splitStr(param.getType());
        List<String> campaignIds = new ArrayList<>();

        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> collect = Arrays.stream(StringUtils.split(param.getCampaignId(), ",")).collect(Collectors.toList());
            campaignIds.addAll(collect);
        }

        //第一步：查询广告产品报告数据

        List<String> groupIds = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> collect = Arrays.stream(StringUtils.split(param.getGroupId(), ",")).collect(Collectors.toList());
            groupIds.addAll(collect);
        }
        List<GroupCampaignDto> dtoList = new ArrayList<>();
        if (types.contains(Constants.SP)) {
            dtoList.addAll(odsAmazonAdProductReportDao.getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), campaignIds,
                    groupIds, param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        }
        if (types.contains(Constants.SB)) {
            dtoList.addAll(odsAmazonSbAdsDao.getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), campaignIds,
                    groupIds, param.getSearchType(), param.getSearchValue()));
        }


        log.info("产品透视分析 {}--{}-过滤广告组- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(dtoList)) {
            return searchTermsViewPageVo;
        }

        Map<String, List<GroupCampaignDto>> stringListMap = dtoList.stream().collect(Collectors.groupingBy(GroupCampaignDto::getCampaignId));

        //第第二步：如果前端带有活动状态，服务状态需要过滤一次活动表
        t = Instant.now().toEpochMilli();
        if (StringUtils.isNotBlank(param.getStatus()) || StringUtils.isNotBlank(param.getServingStatus())) {
            //筛选活动id为空直接推出
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, param.getShopIdList(), null, param.getStatus(), param.getServingStatus(), new ArrayList<>(stringListMap.keySet()), param.getType());
            log.info("产品透视分析 {}--{}-筛选活动状态运行状态- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
            if (CollectionUtils.isEmpty(campaignIds)) {
                return searchTermsViewPageVo;
            }

            //如果之存在部分活动id，剔除不存在的活动id
            groupIds.clear();
            for (String campaignId : campaignIds) {
                //严谨一点一定不会没有，但是以免出现错误
                if (stringListMap.containsKey(campaignId)) {
                    groupIds.addAll(stringListMap.get(campaignId).stream().map(GroupCampaignDto::getAdGroupId).collect(Collectors.toList()));
                }
            }
        } else {
            groupIds = stringListMap.values().stream().flatMap(Collection::stream).map(GroupCampaignDto::getAdGroupId).collect(Collectors.toList());
        }
        List<String> spGroupIdList = new ArrayList<>();
        List<String> sbGroupIdList = new ArrayList<>();
        dtoList.forEach(e -> {
            if (Constants.SP.equalsIgnoreCase(e.getType())) {
                spGroupIdList.add(e.getAdGroupId());
            } else {
                sbGroupIdList.add(e.getAdGroupId());
            }
        });
        param.setAdGroupIdList(groupIds.stream().filter(spGroupIdList::contains).collect(Collectors.toList()));
        param.setSbAdGroupIdList(groupIds.stream().filter(sbGroupIdList::contains).collect(Collectors.toList()));

        t = Instant.now().toEpochMilli();
        //第三步用广告组id 查询广告组报告数据
        List<AdProductReportSearchTermsViewDto> productViewList = new ArrayList<>();
        if (types.contains(Constants.SP) && CollectionUtils.isNotEmpty(spGroupIdList)) {
            List<AdProductReportSearchTermsViewDto> productReport = amazonAdProductReportDao.listAmazonSpAdProduct(puid, param);
            productViewList.addAll(productReport);
            param.setAdGroupIdList(productReport.stream().map(AdProductReportSearchTermsViewDto::getAdGroupId).collect(Collectors.toList()));
        }
        if (types.contains(Constants.SB) && CollectionUtils.isNotEmpty(sbGroupIdList)) {
            List<AdProductReportSearchTermsViewDto> productReport = odsAmazonAdSbAdsReportDao.listAmazonSbAdProduct(puid, param);
            productViewList.addAll(productReport);
            param.setSbAdGroupIdList(productReport.stream().map(AdProductReportSearchTermsViewDto::getAdGroupId).collect(Collectors.toList()));
        }
        log.info("产品透视分析 {}--{}-获取广告产品报告数据- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(productViewList)) {
            return searchTermsViewPageVo;
        }
        //若为msku或父asin，需要获取多个asin
//        Set<String> asinList = this.getAsinListBySkuOrParentAsin(puid, param, types);
        List<AsinListDto> asinDtoList = Lists.newArrayList();
        if (StringUtils.equalsAnyIgnoreCase(param.getSearchType(), ViewBaseParam.SearchTypeEnum.MSKU.getValue(), ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue())) {
            asinDtoList.addAll(getAsinBySkuOrParentAsin(puid, param, types));
        }
        Set<String> shopAsinSkuSet = asinDtoList.stream().map(i -> i.getShopId() + "#" + i.getAsin() + "#" + i.getMsku()).collect(Collectors.toSet());
        Set<String> shopAsinSet = asinDtoList.stream().map(i -> i.getShopId() + "#" + i.getAsin()).collect(Collectors.toSet());

        t = Instant.now().toEpochMilli();
        Map<String, List<AdProductReportSearchTermsViewDto>> groupMap = productViewList.stream().collect(Collectors.groupingBy(AdProductReportSearchTermsViewDto::getAdGroupId));

        //开始计算占比
        //asin占比
        Map<String, AsinRatio> rationMap = Maps.newConcurrentMap();

        for (Map.Entry<String, List<AdProductReportSearchTermsViewDto>> entry : groupMap.entrySet()) {
            String adGroupId = entry.getKey();
            List<AdProductReportSearchTermsViewDto> list = entry.getValue();
            Long adImpressionTotal = 0L;
            Long adClickTotal = 0L;
            Integer adOrderNumTotal = 0;
            Integer saleNumTotal = 0;
            BigDecimal totalSalesTotal = BigDecimal.ZERO;
            Integer orderNumTotal = 0;
            BigDecimal adSalesTotal = BigDecimal.ZERO;
            BigDecimal costTotal = BigDecimal.ZERO;
            Integer adSaleNumTotal = 0;
            Integer salesNumTotal = 0;
            Long viewImpressionsTotal = 0L;
            Integer ordersNewToBrandFTDTotal = 0;
            BigDecimal salesNewToBrandFTDTotal = BigDecimal.ZERO;

            for (AdProductReportSearchTermsViewDto item : list) {
                adImpressionTotal = MathUtil.add(adImpressionTotal, item.getImpressions());
                adClickTotal = MathUtil.add(adClickTotal, item.getClicks());
                adOrderNumTotal = MathUtil.add(adOrderNumTotal, item.getAdOrderNum());
                saleNumTotal = MathUtil.add(saleNumTotal, item.getSaleNum());
                totalSalesTotal = MathUtil.add(totalSalesTotal, item.getTotalSales());
                orderNumTotal = MathUtil.add(orderNumTotal, item.getOrderNum());
                adSalesTotal = MathUtil.add(adSalesTotal, item.getAdSales());
                costTotal = MathUtil.add(costTotal, item.getCost());
                adSaleNumTotal = MathUtil.add(adSaleNumTotal, item.getAdSaleNum());
                salesNumTotal = MathUtil.add(salesNumTotal, item.getSalesNum());
                viewImpressionsTotal = MathUtil.add(viewImpressionsTotal, item.getViewImpressions());
                ordersNewToBrandFTDTotal = MathUtil.add(ordersNewToBrandFTDTotal, item.getOrdersNewToBrandFTD());
                salesNewToBrandFTDTotal = MathUtil.add(salesNewToBrandFTDTotal, item.getSalesNewToBrandFTD());
            }


            AsinRatio asinRatio = new AsinRatio();
            AdProductReportSearchTermsViewDto adSearchTermsDto = new AdProductReportSearchTermsViewDto();
            //根据搜索条件获取单个广告组下对应产品数据，asin时只获取asin数据，sku和parentAsin时需要获取多个asin的数据合起来
            if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(param.getSearchType())) {
//                Optional<AdProductReportSearchTermsViewDto> reportDto = list.stream().filter(e -> e.getAsin().equals(param.getSearchValue())).findFirst();
//                if (reportDto.isPresent()) {
//                    adSearchTermsDto = reportDto.get();
//                }
                list.stream().filter(e -> param.searchValueSet().contains(e.getAsin())).forEach(adSearchTermsDto::merge);
//            } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
//                String type = list.get(0).getType();
//                if (Constants.SP.equalsIgnoreCase(type)) {
//                    Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(item -> item.getAsin() + "#" + item.getMsku(), item -> item, (c1, c2) -> c1));
//                    String msku = param.getSearchValue();
//                    for (String asin : asinList) {
//                        if (map.containsKey(asin + "#" + msku)) {
//                            adSearchTermsDto.merge(map.get(asin + "#" + msku));
//                        }
//                    }
//                } else if (Constants.SB.equalsIgnoreCase(type)) {
//                    Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getAsin, item -> item, (c1, c2) -> c1));
//                    for (String asin : asinList) {
//                        if (map.containsKey(asin)) {
//                            adSearchTermsDto.merge(map.get(asin));
//                        }
//                    }
//                }
//            } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
//                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getAsin, item -> item, (c1, c2) -> c1));
//                for (String asin : asinList) {
//                    if (map.containsKey(asin)) {
//                        adSearchTermsDto.merge(map.get(asin));
//                    }
//                }
//            }
            } else {
                for (AdProductReportSearchTermsViewDto item : list) {
                    String type = item.getType();
                    if (StringUtils.equalsIgnoreCase(type, Constants.SP)) {
                        if (shopAsinSkuSet.contains(item.getShopId() + "#" + item.getAsin() + "#" + item.getMsku())) {
                            adSearchTermsDto.merge(item);
                        }
                    }
                    if (StringUtils.equalsIgnoreCase(type, Constants.SB)) {
                        if (shopAsinSet.contains(item.getShopId() + "#" + item.getAsin())) {
                            adSearchTermsDto.merge(item);
                        }
                    }
                }
            }
            //曝光占比
            Long impressions = adSearchTermsDto.getImpressions();
            BigDecimal adImpressionRatio = MathUtil.divideIfZero(impressions, adImpressionTotal, 8);
            asinRatio.setImpressionsRatio(adImpressionRatio);
            //点击占比
            Long adClick = adSearchTermsDto.getClicks();
            BigDecimal adClickRatio = MathUtil.divideIfZero(adClick, adClickTotal, 8);
            asinRatio.setClicksRatio(adClickRatio);
            //销量占比
            Integer adSaleNum = adSearchTermsDto.getAdSaleNum();
            BigDecimal adSaleNumRatio = MathUtil.divideIfZero(adSaleNum, adSaleNumTotal, 8);
            asinRatio.setAdSaleNumRatio(adSaleNumRatio);
            //订单量占比
            Integer adOrderNum = adSearchTermsDto.getAdOrderNum();
            BigDecimal adOrderNumRatio = MathUtil.divideIfZero(adOrderNum, adOrderNumTotal, 8);
            asinRatio.setAdOrderNumRatio(adOrderNumRatio);
            //销售额占比

            Integer saleNum = adSearchTermsDto.getSaleNum();
            BigDecimal saleNumRatio = MathUtil.divideIfZero(saleNum, saleNumTotal, 8);
            asinRatio.setSaleNumRatio(saleNumRatio);

            BigDecimal totalSales = adSearchTermsDto.getTotalSales();
            BigDecimal totalSalesRatio = MathUtil.divideByZero(totalSales, totalSalesTotal, 8);
            asinRatio.setTotalSalesRatio(totalSalesRatio);

            Integer orderNum = adSearchTermsDto.getOrderNum();
            BigDecimal orderNumRatio = MathUtil.divideIfZero(orderNum, orderNumTotal, 8);
            asinRatio.setOrderNumRatio(orderNumRatio);

            BigDecimal adSales = adSearchTermsDto.getAdSales();
            BigDecimal adSalesRatio = MathUtil.divideByZero(adSales, adSalesTotal, 8);
            asinRatio.setAdSalesRatio(adSalesRatio);


            //花费占比
            BigDecimal adCost = adSearchTermsDto.getCost();
            BigDecimal adCostRatio = MathUtil.divideByZero(adCost, costTotal, 8);
            asinRatio.setCostRatio(adCostRatio);


            Integer salesNum = adSearchTermsDto.getSalesNum();
            BigDecimal salesNumRatio = MathUtil.divideIfZero(salesNum, salesNumTotal, 8);
            asinRatio.setSalesNumRatio(salesNumRatio);

            //可见展示次数占比
            Long viewImpressions = adSearchTermsDto.getViewImpressions();
            BigDecimal viewImpressionRatio = MathUtil.divideIfZero(viewImpressions, viewImpressionsTotal, 8);
            asinRatio.setViewImpressionsRatio(viewImpressionRatio);
            //品牌新买家订单量占比
            Integer ordersNewToBrandFTD = adSearchTermsDto.getOrdersNewToBrandFTD();
            BigDecimal ordersNewToBrandFTDTotalRatio = MathUtil.divideIfZero(ordersNewToBrandFTD, ordersNewToBrandFTDTotal, 8);
            asinRatio.setOrdersNewToBrandFTDRatio(ordersNewToBrandFTDTotalRatio);
            //品牌新买家销售额占比
            BigDecimal salesNewToBrandFTD = adSearchTermsDto.getSalesNewToBrandFTD();
            BigDecimal salesNewToBrandFTDTotalRatio = MathUtil.divideByZero(salesNewToBrandFTD, salesNewToBrandFTDTotal, 8);
            asinRatio.setSalesNewToBrandFTDRatio(salesNewToBrandFTDTotalRatio);
            rationMap.put(adGroupId,asinRatio);
        }

        log.info("产品透视分析 {}--{}-计算占比- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);

        //支持模糊查询
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> termsList = new ArrayList<>();
        if (types.contains(Constants.SP) && CollectionUtils.isNotEmpty(spGroupIdList)) {
            termsList.addAll(cpcQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, param));
        }
        if (types.contains(Constants.SB) && CollectionUtils.isNotEmpty(sbGroupIdList)) {
            termsList.addAll(odsCpcSbQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, param));
        }
        if (CollectionUtils.isEmpty(termsList)) {
            return searchTermsViewPageVo;
        }
        log.info("产品透视分析 {}--{}-查询搜索词耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        t = Instant.now().toEpochMilli();
        //根据占比设置指标
        termsList.forEach(item ->{
            String adGroupId = item.getAdGroupId();
            AsinRatio asinRatio = rationMap.get(adGroupId);
            if(asinRatio != null){
                //曝光
                Long totalImpressions = item.getImpressions();
                Long impressions = MathUtil.multiply(asinRatio.getImpressionsRatio(),new BigDecimal(totalImpressions)).longValue();
                item.setImpressions(impressions);
                //点击
                Long totalClick = item.getClicks();
                Long click = MathUtil.multiply(asinRatio.getClicksRatio(),new BigDecimal(totalClick)).longValue();
                item.setClicks(click);
                //销量
                Integer totalAdSaleNum = item.getAdSaleNum();
                Integer adSaleNum = MathUtil.multiply(asinRatio.getAdSaleNumRatio(),new BigDecimal(totalAdSaleNum)).intValue();
                item.setAdSaleNum(adSaleNum);
                //订单量
                Integer totalOrderNum = item.getOrderNum();
                Integer orderNum = MathUtil.multiply(asinRatio.getOrderNumRatio(),new BigDecimal(totalOrderNum)).intValue();
                item.setOrderNum(orderNum);
                //花费
                BigDecimal totalCost = item.getCost();
                BigDecimal adCost = MathUtil.multiply(asinRatio.getCostRatio(),totalCost);
                item.setCost(adCost);

                Integer adOrderNumTotal = item.getAdOrderNum();
                Integer adOrderNum = MathUtil.multiply(asinRatio.getAdOrderNumRatio(), new BigDecimal(adOrderNumTotal)).intValue();
                item.setAdOrderNum(adOrderNum);

                BigDecimal totalSalesTotal = item.getTotalSales();
                BigDecimal totalSales = MathUtil.multiply(asinRatio.getTotalSalesRatio(), totalSalesTotal);
                item.setTotalSales(totalSales);

                BigDecimal adSalesTotal = item.getAdSales();
                BigDecimal adSales = MathUtil.multiply(asinRatio.getAdSalesRatio(), adSalesTotal);
                item.setAdSales(adSales);

                Integer salesNumTotal = item.getSalesNum() == null ? 0 : item.getSalesNum();
                Integer salesNum = MathUtil.multiply(asinRatio.getSalesNumRatio(), new BigDecimal(salesNumTotal)).intValue();
                item.setSalesNum(salesNum);

                Integer totalSaleNum = item.getSaleNum();
                Integer saleNum = MathUtil.multiply(asinRatio.getSaleNumRatio(),new BigDecimal(totalSaleNum)).intValue();
                item.setSaleNum(saleNum);

                //可见展示次数
                if (item.getViewImpressions() != null) {
                    Long totalViewImpressions = item.getViewImpressions();
                    Long viewImpressions = MathUtil.multiply(asinRatio.getViewImpressionsRatio(),new BigDecimal(totalViewImpressions)).longValue();
                    item.setViewImpressions(viewImpressions);
                }
                //品牌新买家订单量
                if (item.getOrdersNewToBrandFTD() != null) {
                    Integer ordersNewToBrandFTDTotal = item.getOrdersNewToBrandFTD();
                    Integer ordersNewToBrandFTD = MathUtil.multiply(asinRatio.getOrdersNewToBrandFTDRatio(), new BigDecimal(ordersNewToBrandFTDTotal)).intValue();
                    item.setOrdersNewToBrandFTD(ordersNewToBrandFTD);
                }
                //品牌新买家销售额
                if (item.getSalesNewToBrandFTD() != null) {
                    BigDecimal salesNewToBrandFTDTotal = item.getSalesNewToBrandFTD();
                    BigDecimal salesNewToBrandFTD = MathUtil.multiply(asinRatio.getSalesNewToBrandFTDRatio(), salesNewToBrandFTDTotal);
                    item.setSalesNewToBrandFTD(salesNewToBrandFTD);
                }
            }
        });
        log.info("产品透视分析 {}--{}-计算实际值耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        //合并数据
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> resultList =
                new ArrayList<>(termsList.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery,
                        Function.identity(), (s1, s2) -> {
                            merge(s1, s2);
                            return s1;
                        }
                )).values());
        log.info("产品透视分析 {}--{}-聚合数据耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);


        //高级筛选


        AdProductReportSearchTermsViewDto sum = new AdProductReportSearchTermsViewDto();


        //列表页数据
        List<SearchTermsViewVo> voList = new ArrayList<>();
        SearchTermsViewVo vo;
        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = termsList.stream().map(AdProductReportSearchTermsViewDto::getQuery).distinct()
                .map(e -> new WordTranslateQo(param.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);
        Map<String, String> queryMainImageMap = termsList.stream().filter(e -> StringUtils.isNotBlank(e.getMainImage()))
                .collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery, AdProductReportSearchTermsViewDto::getMainImage, (q1, q2) -> q2));
        //根据指标排序，需算出所有投放指标
        for (AdProductReportSearchTermsViewDto item : resultList) {
            vo = new SearchTermsViewVo();
            vo.setQuery(item.getQuery());
            vo.setQueryCn(wordTranslateMap.getOrDefault(wordTranslateService.getWordTranslateKey(param.getMarketplaceId(), item.getQuery()), ""));
            vo.setMainImage(queryMainImageMap.get(item.getQuery()));
            vo.setIsAsin(item.getQuery().matches(Constants.ASIN_REGEX));
            vo.setType(item.getType().toUpperCase());
            ViewServiceHelper.fillStreamDataIntoViewVo(vo, item, param.getShopSales());
            if (param.getUseAdvanced()) {
                if (!isFilterAdVanceData(vo, param)) {
                    continue;
                }
            }
            this.merge(sum, item);
            voList.add(vo);
        }
        if (CollectionUtils.isEmpty(voList)) {
            return searchTermsViewPageVo;
        }
        StreamDataViewVo aggregateVo = new StreamDataViewVo();
        ViewServiceHelper.fillStreamDataIntoViewVo(aggregateVo, sum, param.getShopSales());
        aggregateVo.setAdCostPercentage(sum.getCost().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdSalePercentage(sum.getTotalSales().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdOrderNumPercentage(sum.getSaleNum() > 0 ? "100" : "0");
        aggregateVo.setOrderNumPercentage(sum.getOrderNum() > 0 ? "100" : "0");
        searchTermsViewPageVo.setAggregateViewVo(aggregateVo);
        //获取占比指标汇总数据
        AdMetricDto adMetricDto = this.getSumAdMetricDto(sum);
        voList.forEach(e->ViewServiceHelper.filterAdMetricData(e, adMetricDto));
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            //根据选择做排序（内存）和分页（内存）
            String orderField = SearchTermsViewVo.orderFieldTransferMap.getOrDefault(param.getOrderField(), param.getOrderField());
            boolean isSorted = StringUtils.isNotBlank(orderField) && Constants.isADOrderField(orderField, SearchTermsViewVo.class);
            if (isSorted) {
                voList = PageUtil.sort(voList, orderField, param.getOrderType());
            }
            PageUtil.getPage(voPage, voList);
            log.info("产品透视分析 {} --自动投放视图汇总接口调用-排序分页-花费时间 {}", uuid, Instant.now().toEpochMilli() - t);
        } else {
            PageUtil.getPage(voPage, voList);
        }
        //将搜索词为asin的转为大写
        voPage.getRows().forEach(e -> {
            if (RegexUtils.isMatch(Constants.ASIN_REGEX, e.getQuery())) {
                e.setQuery(e.getQuery().toUpperCase());
            }
        });
        searchTermsViewPageVo.setPage(voPage);
        return searchTermsViewPageVo;
    }

    /**
     * 根据sku或者父asin获取asin列表
     */
    private Set<String> getAsinListBySkuOrParentAsin(Integer puid, SearchTermsViewParam param, List<String> types) {
        Set<String> asinList = new HashSet<>();
        if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
            if (types.contains(Constants.SP)) {
                asinList.addAll(odsAmazonAdProductReportDao.getAsinByParentAsinAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                        param.getSearchValue(), param.getStartDate(), param.getEndDate()));
            }
            if (types.contains(Constants.SB)) {
                asinList.addAll(odsAmazonSbAdsDao.getAsinByParentAsinAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getSbAdGroupIdList(), param.getSearchValue()));
            }
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
            if (types.contains(Constants.SP)) {
                asinList.addAll(odsAmazonAdProductReportDao.getAsinByMskuAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                        param.getSearchValue(), param.getStartDate(), param.getEndDate()));
            }
            if (types.contains(Constants.SB)) {
                asinList.addAll(odsAmazonSbAdsDao.getAsinByMskuAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getSbAdGroupIdList(), param.getSearchValue()));
            }
        }
        return asinList;
    }

    private List<AsinListDto> getAsinBySkuOrParentAsin(Integer puid, SearchTermsViewParam param, List<String> types) {
        List<AsinListDto> asinList = Lists.newArrayList();
        if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
            if (types.contains(Constants.SP)) {
                asinList.addAll(odsAmazonAdProductReportDao.getAsinByParentAsinList(puid, param.getShopIdList(), param.getAdGroupIdList(),
                        param.searchValueList(), param.getStartDate(), param.getEndDate()));
            }
            if (types.contains(Constants.SB)) {
                asinList.addAll(odsAmazonSbAdsDao.getAsinByParentAsinList(puid, param.getShopIdList(), param.getSbAdGroupIdList(), param.searchValueList()));
            }
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
            if (types.contains(Constants.SP)) {
                asinList.addAll(odsAmazonAdProductReportDao.getAsinByMskuList(puid, param.getShopIdList(), param.getAdGroupIdList(),
                        param.searchValueList(), param.getStartDate(), param.getEndDate()));
            }
            if (types.contains(Constants.SB)) {
                asinList.addAll(odsAmazonSbAdsDao.getAsinByMskuList(puid, param.getShopIdList(), param.getSbAdGroupIdList(), param.searchValueList()));
            }
        }
        return asinList;
    }

    private void merge(AdProductReportSearchTermsViewDto s1,AdProductReportSearchTermsViewDto s2){


        s1.setImpressions(MathUtil.add(s1.getImpressions(), s2.getImpressions()));
        s1.setAdSaleNum(MathUtil.add(s1.getAdSaleNum(), s2.getAdSaleNum()));
        s1.setClicks(MathUtil.add(s1.getClicks(), s2.getClicks()));
        s1.setAdOrderNum(MathUtil.add(s1.getAdOrderNum(), s2.getAdOrderNum()));
        s1.setSaleNum(MathUtil.add(s1.getSaleNum(), s2.getSaleNum()));
        s1.setTotalSales(MathUtil.add(s1.getTotalSales(), s2.getTotalSales()));
        s1.setCost(MathUtil.add(s1.getCost(), s2.getCost()));
        s1.setOrderNum(MathUtil.add(s1.getOrderNum(), s2.getOrderNum()));
        s1.setAdSales(MathUtil.add(s1.getAdSales(), s2.getAdSales()));
        s1.setSalesNum(MathUtil.add(s1.getSalesNum(), s2.getSalesNum()));
        s1.setViewImpressions(MathUtil.add(s1.getViewImpressions(), s2.getViewImpressions()));
        s1.setOrdersNewToBrandFTD(MathUtil.add(s1.getOrdersNewToBrandFTD(), s2.getOrdersNewToBrandFTD()));
        s1.setSalesNewToBrandFTD(MathUtil.add(s1.getSalesNewToBrandFTD(), s2.getSalesNewToBrandFTD()));

    }
    
    
    
    private ProductPerspectiveDiagnoseSelectDto buildBaseRequest(TargetViewParam param, List<String> targetIds) {
        //填充高级筛选数据
        ProductPerspectiveAnalysisFilterDto advance = null;
        if (param.getUseAdvanced()) {
            ProductPerspectiveAnalysisAdvancedFilterDataPb.ProductPerspectiveAnalysisAdvancedFilterData advancedFilter =
                    ViewServiceHelper.campaignViewParamToAdvancedFilter(param);
            advance = new ProductPerspectiveAnalysisFilterDto(advancedFilter);
        }
        return ProductPerspectiveDiagnoseSelectDto.builder()
                .sellerIds(param.getSellerIdList())
                .marketplaceId(param.getMarketplaceId())
                .adIds(param.getAdIdList())
                .startDate(amazonAdFeedReportService.getSellerIdsDataStartTime(param.getSellerIdList(), param.getMarketplaceId(), param.getStartDate()))
                .endDate(param.getEndDate())
                .shopSales(Optional.ofNullable(param.getShopSales()).orElse(BigDecimal.ZERO))
                .advance(advance)
                .keywordIds(targetIds)
                .build();
    }


    /**
     * 获取占比指标汇总数据
     */
    private AdMetricDto getSumAdMetricDto(AdProductReportSearchTermsViewDto sum) {
        AdMetricDto adMetricDto = new AdMetricDto();
        adMetricDto.setSumCost(sum.getCost());
        //广告订单量
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(sum.getSaleNum()));
        //广告销售额
        adMetricDto.setSumAdSale(sum.getTotalSales());
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(sum.getOrderNum()));
        return adMetricDto;
    }

    /**
     * 过滤广告产品高级筛选数据
     */
    public void filterAdVanceData(List<SearchTermsViewVo> voList, TargetViewParam param) {
        if (CollectionUtils.isNotEmpty(voList)) {
            Iterator<SearchTermsViewVo> it = voList.iterator();
            SearchTermsViewVo vo;
            while (it.hasNext()) {
                vo = it.next();
                if (!isFilterAdVanceData(vo, param)) {
                    it.remove();  //不符合,过滤掉
                    continue;
                }
            }
        }
    }


    private boolean isFilterAdVanceData(SearchTermsViewVo vo, ViewBaseParam param){
        Integer orderNum = vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0;
        vo.setAdOrderNum(orderNum);
        Long impressions = vo.getImpressions() != null ? vo.getImpressions() : 0;
        vo.setImpressions(impressions);
        Long clicks = vo.getClicks() != null ? vo.getClicks() : 0;
        vo.setClicks(clicks);

        //cpa
        if(param.getCpaMin() != null){
            double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
            double b = param.getCpaMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getCpaMax() != null){
            double a = vo.getCpa() != null ? Double.parseDouble(vo.getCpa()) : 0;
            double b = param.getCpaMax().doubleValue();
            if (!(a <= b)) {  //a大于等于b
                return false;
            }
        }


        //展示量
        if(param.getAdSaleNumMin() != null){
            if (!(vo.getAdSaleNum() >= param.getAdSaleNumMin())) {
                return false;
            }
        }
        if(param.getAdSaleNumMax() != null){
            if (!(vo.getAdSaleNum() <= param.getAdSaleNumMax())) {
                return false;
            }
        }

        // vcpm
        if(param.getVcpmMin() != null){
            if("SP".equals(vo.getType())){
                return false;
            }
            double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
            double b = param.getVcpmMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }

        if(param.getVcpmMax() != null){
            if("SP".equals(vo.getType())){
                return false;
            }
            double a = vo.getVcpm() != null ? Double.parseDouble(vo.getVcpm()) : 0;
            double b = param.getVcpmMax().doubleValue();
            if (!(a <= b)) {
                return false;
            }
        }

        //展示量
        if(param.getImpressionsMin() != null){
            if (!(vo.getImpressions() >= param.getImpressionsMin())) {
                return false;
            }
        }
        if(param.getImpressionsMax() != null){
            if (!(vo.getImpressions() <= param.getImpressionsMax())) {
                return false;
            }
        }
        //点击量
        if(param.getClicksMin() != null){
            if (!(vo.getClicks() >= param.getClicksMin())) {
                return false;
            }
        }
        if(param.getClicksMax() != null){
            if (!(vo.getClicks() <= param.getClicksMax())) {
                return false;
            }
        }
        //点击率（clicks/impressions）
        if(param.getClickRateMin() != null){
            double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
            double b = param.getClickRateMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getClickRateMax() != null){
            double a = vo.getCtr() != null ? Double.parseDouble(vo.getCtr()) : 0;
            double b = param.getClickRateMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        //花费
        if(param.getCostMin() != null){
            double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
            double b = param.getCostMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getCostMax() != null){
            double a = vo.getAdCost() != null ? Double.parseDouble(vo.getAdCost()) : 0;
            double b = param.getCostMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        //cpc  平均点击费用
        if(param.getCpcMin() != null){
            double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
            double b = param.getCpcMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getCpcMax() != null){
            double a = vo.getAdCostPerClick() != null ? Double.parseDouble(vo.getAdCostPerClick()) : 0;
            double b = param.getCpcMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        //广告订单量
        if(param.getOrderNumMin() != null){
            if (!(vo.getAdOrderNum() >= param.getOrderNumMin())) {
                return false;
            }
        }
        if(param.getOrderNumMax() != null){
            if (!(vo.getAdOrderNum() <= param.getOrderNumMax())) {
                return false;
            }
        }
        //广告销售额
        if(param.getSalesMin() != null){
            double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
            double b = param.getSalesMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getSalesMax() != null){
            double a = vo.getAdSale() != null ? Double.parseDouble(vo.getAdSale()) : 0;
            double b = param.getSalesMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        //订单转化率
        if(param.getSalesConversionRateMin() != null){
            double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
            double b = param.getSalesConversionRateMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getSalesConversionRateMax() != null){
            double a = vo.getCvr() != null ? Double.parseDouble(vo.getCvr()) : 0;
            double b = param.getSalesConversionRateMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        //acos
        if(param.getAcosMin() != null){
            double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
            double b = param.getAcosMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        if(param.getAcosMax() != null){
            double a = vo.getAcos() != null ? Double.parseDouble(vo.getAcos()) : 0;
            double b = param.getAcosMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        // roas
        if (param.getRoasMin() != null) {
            double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
            double b = param.getRoasMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        // roas
        if (param.getRoasMax() != null) {
            double a = vo.getRoas() != null ? Double.parseDouble(vo.getRoas()) : 0;
            double b = param.getRoasMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
            double b = param.getAcotsMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            double a = vo.getAcots() != null ? Double.parseDouble(vo.getAcots()) : 0;
            double b = param.getAcotsMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
            double b = param.getAsotsMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            double a = vo.getAsots() != null ? Double.parseDouble(vo.getAsots()) : 0;
            double b = param.getAsotsMax().doubleValue();
            if (!(a <= b)) {  //a小于等于b
                return false;
            }
        }


        //本广告产品销售额过滤
        if (param.getAdSalesMin() != null) {
            double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
            double b = param.getAdSalesMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }

        if (param.getAdSalesMax() != null) {
            double a = vo.getAdSales() != null ? Double.parseDouble(vo.getAdSales()) : 0;
            double b = param.getAdSalesMax().doubleValue();
            if (!(a <= b)) {  //a大于等于b
                return false;
            }
        }

        //其他产品广告销售额 过滤
        if (param.getAdOtherSalesMin() != null) {
            double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
            double b = param.getAdOtherSalesMin().doubleValue();
            if (!(a >= b)) {  //a大于等于b
                return false;
            }
        }

        if (param.getAdOtherSalesMax() != null) {
            double a = vo.getAdOtherSales() != null ? Double.parseDouble(vo.getAdOtherSales()) : 0;
            double b = param.getAdOtherSalesMax().doubleValue();
            if (!(a <= b)) {  //a大于等于b
                return false;
            }
        }


        //本廣告产品销量过滤
        Integer adSelfSaleNum = vo.getAdSelfSaleNum() != null ? vo.getAdSelfSaleNum().intValue() : 0;
        if(param.getAdSelfSaleNumMin() != null){
            if (!(adSelfSaleNum >= param.getAdSelfSaleNumMin())) {
                return false;
            }
        }
        if(param.getAdSelfSaleNumMax() != null){
            if (!(adSelfSaleNum <= param.getAdSelfSaleNumMax())) {
                return false;
            }
        }

        //其他产品广告销量 过滤
        Integer adOtherSaleNum = vo.getAdOtherSaleNum() != null ? vo.getAdOtherSaleNum().intValue() : 0;
        if(param.getAdOtherSaleNumMin() != null){
            if (!(adOtherSaleNum >= param.getAdOtherSaleNumMin())) {
                return false;
            }
        }
        if(param.getAdOtherSaleNumMax() != null){
            if (!(adOtherSaleNum <= param.getAdOtherSaleNumMax())) {
                return false;
            }
        }

        //其他产品广告订单量过滤
        Integer adOtherOrderNum = vo.getAdOtherOrderNum() != null ? vo.getAdOtherOrderNum().intValue() : 0;
        if(param.getAdOtherOrderNumMin() != null){
            if (!(adOtherOrderNum >= param.getAdOtherOrderNumMin())) {
                return false;
            }
        }
        if(param.getAdOtherOrderNumMax() != null){
            if (!(adOtherOrderNum <= param.getAdOtherOrderNumMax())) {
                return false;
            }
        }


        return true;
    }


    @Override
    public  Map<Long, Integer> getDiagnoseCount4SearchTerms(Integer puid, List<DiagnoseCountParam> diagnoseCountParams) {
        String title = "自动投放视图接口调用";
        String uuid = UUID.randomUUID().toString();
        DiagnoseCountParam param = diagnoseCountParams.get(0);
        log.info("产品透视分析 {} --{}-参数 {}", uuid, title, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        t = Instant.now().toEpochMilli();
        //第二步：查询广告产品报告数据
        List<String> adGroupIds = odsAmazonAdProductReportDao.getGroupIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), null,
                param.getAdGroupIdList(), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());

        log.info("产品透视分析 {}--{}-过滤广告组- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(adGroupIds)) {
            return new HashMap<>();
        }
        param.setAdGroupIdList(adGroupIds);
        t = Instant.now().toEpochMilli();
        //第三步用广告组id 查询广告组报告数据

        SearchTermsViewParam paramView = new SearchTermsViewParam();
        BeanUtils.copyProperties(param, paramView);
        List<AdProductReportSearchTermsViewDto> productViewList = amazonAdProductReportDao.listAmazonSpAdProduct(puid, paramView);
        log.info("产品透视分析 {}--{}-获取广告产品报告数据- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(productViewList)) {
            return new HashMap<>();
        }
        //若为msku或父asin，需要获取多个asin
        Set<String> asinList = new HashSet<>();
        if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
            asinList.addAll(odsAmazonAdProductReportDao.getAsinByParentAsinAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                    param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
            asinList.addAll(odsAmazonAdProductReportDao.getAsinByMskuAndGroupIdsAndCampaignIds(puid, param.getShopIdList(), param.getAdGroupIdList(),
                    param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        }

        t = Instant.now().toEpochMilli();
        Map<String, List<AdProductReportSearchTermsViewDto>> groupMap = productViewList.stream().collect(Collectors.groupingBy(AdProductReportSearchTermsViewDto::getAdGroupId));

        //开始计算占比


        //asin占比
        Map<String, AsinRatio> rationMap = Maps.newConcurrentMap();

        for (Map.Entry<String, List<AdProductReportSearchTermsViewDto>> entry : groupMap.entrySet()) {
            String adGroupId = entry.getKey();
            List<AdProductReportSearchTermsViewDto> list = entry.getValue();
            Long adImpressionTotal = 0L;
            Long adClickTotal = 0L;
            Integer adOrderNumTotal = 0;
            Integer saleNumTotal = 0;
            BigDecimal totalSalesTotal = BigDecimal.ZERO;
            Integer orderNumTotal = 0;
            BigDecimal adSalesTotal = BigDecimal.ZERO;
            BigDecimal costTotal = BigDecimal.ZERO;
            Integer adSaleNumTotal = 0;
            Integer salesNumTotal = 0;

            for (AdProductReportSearchTermsViewDto item : list) {
                adImpressionTotal = MathUtil.add(adImpressionTotal, item.getImpressions());
                adClickTotal = MathUtil.add(adClickTotal, item.getClicks());
                adOrderNumTotal = MathUtil.add(adOrderNumTotal, item.getAdOrderNum());
                saleNumTotal = MathUtil.add(saleNumTotal, item.getSaleNum());
                totalSalesTotal = MathUtil.add(totalSalesTotal, item.getTotalSales());
                orderNumTotal = MathUtil.add(orderNumTotal, item.getOrderNum());
                adSalesTotal = MathUtil.add(adSalesTotal, item.getAdSales());
                costTotal = MathUtil.add(costTotal, item.getCost());
                adSaleNumTotal = MathUtil.add(adSaleNumTotal, item.getAdSaleNum());
                salesNumTotal = MathUtil.add(salesNumTotal, item.getSalesNum());
            }

            AsinRatio asinRatio = new AsinRatio();
            AdProductReportSearchTermsViewDto adSearchTermsDto = new AdProductReportSearchTermsViewDto();
            //根据搜索条件获取单个广告组下对应产品数据，asin时只获取asin数据，sku和parentAsin时需要获取多个asin的数据合起来
            if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(param.getSearchType())) {
                Optional<AdProductReportSearchTermsViewDto> reportDto = list.stream().filter(e -> e.getAsin().equals(param.getSearchValue())).findFirst();
                if (reportDto.isPresent()) {
                    adSearchTermsDto = reportDto.get();
                }
            } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(item -> item.getAsin() + "#" + item.getMsku(), item -> item, (c1, c2) -> c1));
                String msku = param.getSearchValue();
                for (String asin : asinList) {
                    if (map.containsKey(asin + "#" + msku)) {
                        adSearchTermsDto.merge(map.get(asin + "#" + msku));
                    }
                }
            } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getAsin, item -> item, (c1, c2) -> c1));
                for (String asin : asinList) {
                    if (map.containsKey(asin)) {
                        adSearchTermsDto.merge(map.get(asin));
                    }
                }
            }
            //曝光占比
            Long impressions = adSearchTermsDto.getImpressions();
            BigDecimal adImpressionRatio = MathUtil.divideIfZero(impressions, adImpressionTotal, 8);
            asinRatio.setImpressionsRatio(adImpressionRatio);
            //点击占比
            Long adClick = adSearchTermsDto.getClicks();
            BigDecimal adClickRatio = MathUtil.divideIfZero(adClick, adClickTotal, 8);
            asinRatio.setClicksRatio(adClickRatio);
            //销量占比
            Integer adSaleNum = adSearchTermsDto.getAdSaleNum();
            BigDecimal adSaleNumRatio = MathUtil.divideIfZero(adSaleNum, adSaleNumTotal, 8);
            asinRatio.setAdSaleNumRatio(adSaleNumRatio);
            //订单量占比
            Integer adOrderNum = adSearchTermsDto.getAdOrderNum();
            BigDecimal adOrderNumRatio = MathUtil.divideIfZero(adOrderNum, adOrderNumTotal, 8);
            asinRatio.setAdOrderNumRatio(adOrderNumRatio);
            //销售额占比

            Integer saleNum = adSearchTermsDto.getSaleNum();
            BigDecimal saleNumRatio = MathUtil.divideIfZero(saleNum, saleNumTotal, 8);
            asinRatio.setSaleNumRatio(saleNumRatio);

            BigDecimal totalSales = adSearchTermsDto.getTotalSales();
            BigDecimal totalSalesRatio = MathUtil.divideByZero(totalSales, totalSalesTotal, 8);
            asinRatio.setTotalSalesRatio(totalSalesRatio);

            Integer orderNum = adSearchTermsDto.getOrderNum();
            BigDecimal orderNumRatio = MathUtil.divideIfZero(orderNum, orderNumTotal, 8);
            asinRatio.setOrderNumRatio(orderNumRatio);

            BigDecimal adSales = adSearchTermsDto.getAdSales();
            BigDecimal adSalesRatio = MathUtil.divideByZero(adSales, adSalesTotal, 8);
            asinRatio.setAdSalesRatio(adSalesRatio);

            //花费占比
            BigDecimal adCost = adSearchTermsDto.getCost();
            BigDecimal adCostRatio = MathUtil.divideByZero(adCost, costTotal, 8);
            asinRatio.setCostRatio(adCostRatio);

            Integer salesNum = adSearchTermsDto.getSalesNum();
            BigDecimal salesNumRatio = MathUtil.divideIfZero(salesNum, salesNumTotal, 8);
            asinRatio.setSalesNumRatio(salesNumRatio);
            rationMap.put(adGroupId,asinRatio);
        }

        log.info("产品透视分析 {}--{}-计算占比- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);

        //支持模糊查询
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> termsList = cpcQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, paramView);
        log.info("产品透视分析 {}--{}-查询搜索词耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(termsList)) {
            return new HashMap<>();
        }
        t = Instant.now().toEpochMilli();
        //根据占比设置指标
        termsList.forEach(item ->{
            String adGroupId = item.getAdGroupId();
            AsinRatio asinRatio = rationMap.get(adGroupId);
            if(asinRatio != null){
                //曝光
                Long totalImpressions = item.getImpressions();
                Long impressions = MathUtil.multiply(asinRatio.getImpressionsRatio(),new BigDecimal(totalImpressions)).longValue();
                item.setImpressions(impressions);
                //点击
                Long totalClick = item.getClicks();
                Long click = MathUtil.multiply(asinRatio.getClicksRatio(),new BigDecimal(totalClick)).longValue();
                item.setClicks(click);
                //销量
                Integer totalAdSaleNum = item.getAdSaleNum();
                Integer adSaleNum = MathUtil.multiply(asinRatio.getAdSaleNumRatio(),new BigDecimal(totalAdSaleNum)).intValue();
                item.setAdSaleNum(adSaleNum);
                //订单量
                Integer totalOrderNum = item.getOrderNum();
                Integer orderNum = MathUtil.multiply(asinRatio.getOrderNumRatio(),new BigDecimal(totalOrderNum)).intValue();
                item.setOrderNum(orderNum);
                //花费
                BigDecimal totalCost = item.getCost();
                BigDecimal adCost = MathUtil.multiply(asinRatio.getCostRatio(),totalCost);
                item.setCost(adCost);

                Integer adOrderNumTotal = item.getAdOrderNum();
                Integer adOrderNum = MathUtil.multiply(asinRatio.getAdOrderNumRatio(), new BigDecimal(adOrderNumTotal)).intValue();
                item.setAdOrderNum(adOrderNum);

                BigDecimal totalSalesTotal = item.getTotalSales();
                BigDecimal totalSales = MathUtil.multiply(asinRatio.getTotalSalesRatio(), totalSalesTotal);
                item.setTotalSales(totalSales);

                BigDecimal adSalesTotal = item.getAdSales();
                BigDecimal adSales = MathUtil.multiply(asinRatio.getAdSalesRatio(), adSalesTotal);
                item.setAdSales(adSales);

                Integer salesNumTotal =  item.getSalesNum() == null ? 0 : item.getSalesNum();
                Integer salesNum = MathUtil.multiply(asinRatio.getSalesNumRatio(), new BigDecimal(salesNumTotal)).intValue();
                item.setSalesNum(salesNum);


                Integer totalSaleNum = item.getSaleNum();
                Integer saleNum = MathUtil.multiply(asinRatio.getSaleNumRatio(),new BigDecimal(totalSaleNum)).intValue();
                item.setSaleNum(saleNum);

            }
        });
        log.info("产品透视分析 {}--{}-计算实际值耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        //合并数据
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> resultList =
                new ArrayList<>(termsList.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery,
                        Function.identity(), (s1, s2) -> {
                            merge(s1, s2);
                            return s1;
                        }
                )).values());
        log.info("产品透视分析 {}--{}-聚合数据耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);


        //高级筛选


        //汇总
        AdProductReportSearchTermsViewDto sum = new AdProductReportSearchTermsViewDto();
        termsList.forEach(sum::merge);

        StreamDataViewVo aggregateVo = new StreamDataViewVo();
        ViewServiceHelper.fillStreamDataIntoViewVo(aggregateVo, sum, param.getShopSales());
        aggregateVo.setAdCostPercentage(sum.getCost().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdSalePercentage(sum.getTotalSales().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdOrderNumPercentage(sum.getAdOrderNum() > 0 ? "100" : "0");
        aggregateVo.setOrderNumPercentage(sum.getOrderNum() > 0 ? "100" : "0");

        //获取占比指标汇总数据
        AdMetricDto adMetricDto = this.getSumAdMetricDto(sum);

        //列表页数据
        SearchTermsViewVo vo;
        Map<Long, Integer> result = new HashMap<>();
        for (AdProductReportSearchTermsViewDto item : resultList) {
            vo = new SearchTermsViewVo();
            vo.setQuery(item.getQuery());
            vo.setType("SP");
            ViewServiceHelper.fillStreamDataIntoViewVo(vo, item, param.getShopSales());
            ViewServiceHelper.filterAdMetricData(vo, adMetricDto);

            for (DiagnoseCountParam diagnoseCountParam : diagnoseCountParams) {
                Integer count = result.getOrDefault(diagnoseCountParam.getId(), 0);
                if (isFilterAdVanceData(vo, diagnoseCountParam)) {
                    count = count + 1;
                }
                result.put(diagnoseCountParam.getId(), count);
            }

        }
        return result;

    }

    @Override
    public Map<Long, Integer> getAllTypeDiagnoseCount4SearchTerms(Integer puid, List<DiagnoseCountParam> diagnoseCountParams) {
        String title = "搜索词视图接口调用";
        String uuid = UUID.randomUUID().toString();
        DiagnoseCountParam param = diagnoseCountParams.get(0);
        log.info("产品透视分析 {} --{}-参数 {}", uuid, title, JSONUtil.objectToJson(param));
        long t = Instant.now().toEpochMilli();

        List<String> types = StringUtil.splitStr(param.getType());

        //第二步：查询广告产品报告数据
        List<String> groupIds = new ArrayList<>();
        List<String> spGroupIdList = new ArrayList<>();
        List<String> sbGroupIdList = new ArrayList<>();
        if (types.contains(Constants.SP)) {
            spGroupIdList.addAll(odsAmazonAdProductReportDao.getGroupIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), null,
                    param.getAdGroupIdList(), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate()));
            groupIds.addAll(spGroupIdList);
        }
        if (types.contains(Constants.SB)) {
            sbGroupIdList.addAll(odsAmazonSbAdsDao.getGroupIdsByCampaignIdsAndAsin(puid, param.getShopIdList(), null,
                    param.getAdGroupIdList(), param.getSearchType(), param.getSearchValue()));
            groupIds.addAll(sbGroupIdList);
        }
        log.info("产品透视分析 {}--{}-过滤广告组- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        t = Instant.now().toEpochMilli();
        //第三步用广告组id 查询广告组报告数据
        List<AdProductReportSearchTermsViewDto> productViewList = new ArrayList<>();
        SearchTermsViewParam paramView = new SearchTermsViewParam();
        BeanUtils.copyProperties(param, paramView);
        paramView.setAdGroupIdList(spGroupIdList);
        paramView.setSbAdGroupIdList(sbGroupIdList);
        if (types.contains(Constants.SP) && CollectionUtils.isNotEmpty(spGroupIdList)) {
            productViewList.addAll(amazonAdProductReportDao.listAmazonSpAdProduct(puid, paramView));
        }
        if (types.contains(Constants.SB) && CollectionUtils.isNotEmpty(sbGroupIdList)) {
            productViewList.addAll(odsAmazonAdSbAdsReportDao.listAmazonSbAdProduct(puid, paramView));
        }
        log.info("产品透视分析 {}--{}-获取广告产品报告数据- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        if (CollectionUtils.isEmpty(productViewList)) {
            return new HashMap<>();
        }
        //若为msku或父asin，需要获取多个asin
        List<AsinListDto> asinDtoList = Lists.newArrayList();
        if (StringUtils.equalsAnyIgnoreCase(paramView.getSearchType(), ViewBaseParam.SearchTypeEnum.MSKU.getValue(), ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue())) {
            asinDtoList.addAll(getAsinBySkuOrParentAsin(puid, paramView, types));
        }
        Set<String> shopAsinSkuSet = asinDtoList.stream().map(i -> i.getShopId() + "#" + i.getAsin() + "#" + i.getMsku()).collect(Collectors.toSet());
        Set<String> shopAsinSet = asinDtoList.stream().map(i -> i.getShopId() + "#" + i.getAsin()).collect(Collectors.toSet());

        t = Instant.now().toEpochMilli();
        Map<String, List<AdProductReportSearchTermsViewDto>> groupMap = productViewList.stream().collect(Collectors.groupingBy(AdProductReportSearchTermsViewDto::getAdGroupId));

        //开始计算占比
        //asin占比
        Map<String, AsinRatio> rationMap = Maps.newConcurrentMap();

        for (Map.Entry<String, List<AdProductReportSearchTermsViewDto>> entry : groupMap.entrySet()) {
            String adGroupId = entry.getKey();
            List<AdProductReportSearchTermsViewDto> list = entry.getValue();
            Long adImpressionTotal = 0L;
            Long adClickTotal = 0L;
            Integer adOrderNumTotal = 0;
            Integer saleNumTotal = 0;
            BigDecimal totalSalesTotal = BigDecimal.ZERO;
            Integer orderNumTotal = 0;
            BigDecimal adSalesTotal = BigDecimal.ZERO;
            BigDecimal costTotal = BigDecimal.ZERO;
            Integer adSaleNumTotal = 0;
            Integer salesNumTotal = 0;
            Long viewImpressionsTotal = 0L;
            Integer ordersNewToBrandFTDTotal = 0;
            BigDecimal salesNewToBrandFTDTotal = BigDecimal.ZERO;

            for (AdProductReportSearchTermsViewDto item : list) {
                adImpressionTotal = MathUtil.add(adImpressionTotal, item.getImpressions());
                adClickTotal = MathUtil.add(adClickTotal, item.getClicks());
                adOrderNumTotal = MathUtil.add(adOrderNumTotal, item.getAdOrderNum());
                saleNumTotal = MathUtil.add(saleNumTotal, item.getSaleNum());
                totalSalesTotal = MathUtil.add(totalSalesTotal, item.getTotalSales());
                orderNumTotal = MathUtil.add(orderNumTotal, item.getOrderNum());
                adSalesTotal = MathUtil.add(adSalesTotal, item.getAdSales());
                costTotal = MathUtil.add(costTotal, item.getCost());
                adSaleNumTotal = MathUtil.add(adSaleNumTotal, item.getAdSaleNum());
                salesNumTotal = MathUtil.add(salesNumTotal, item.getSalesNum());
                viewImpressionsTotal = MathUtil.add(viewImpressionsTotal, item.getViewImpressions());
                ordersNewToBrandFTDTotal = MathUtil.add(ordersNewToBrandFTDTotal, item.getOrdersNewToBrandFTD());
                salesNewToBrandFTDTotal = MathUtil.add(salesNewToBrandFTDTotal, item.getSalesNewToBrandFTD());
            }


            AsinRatio asinRatio = new AsinRatio();
            AdProductReportSearchTermsViewDto adSearchTermsDto = new AdProductReportSearchTermsViewDto();
            //根据搜索条件获取单个广告组下对应产品数据，asin时只获取asin数据，sku和parentAsin时需要获取多个asin的数据合起来
            if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(param.getSearchType())) {
                list.stream().filter(e -> param.searchValueSet().contains(e.getAsin())).forEach(adSearchTermsDto::merge);
//            } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(param.getSearchType())) {
//                String type = list.get(0).getType();
//                if (Constants.SP.equalsIgnoreCase(type)) {
//                    Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(i -> i.getShopId() + "#" + i.getAsin() + "#" + i.getMsku(), v -> v, (v1, v2) -> v1));
//                    for (String shopAsinSku : shopAsinSkuSet) {
//                        adSearchTermsDto.merge(map.get(shopAsinSku));
//                    }
//                } else if (Constants.SB.equalsIgnoreCase(type)) {
//                    Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(i -> i.getShopId() + "#" + i.getAsin(), v -> v, (v1, v2) -> v1));
//                    for (String shopAsin : shopAsinSet) {
//                        adSearchTermsDto.merge(map.get(shopAsin));
//                    }
//                }
//            } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(param.getSearchType())) {
//                Map<String, AdProductReportSearchTermsViewDto> map = list.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getAsin, item -> item, (c1, c2) -> c1));
//                for (String asin : asinList) {
//                    if (map.containsKey(asin)) {
//                        adSearchTermsDto.merge(map.get(asin));
//                    }
//                }
            } else {
                for (AdProductReportSearchTermsViewDto item : list) {
                    String type = item.getType();
                    if (StringUtils.equalsIgnoreCase(type, Constants.SP)) {
                        if (shopAsinSkuSet.contains(item.getShopId() + "#" + item.getAsin() + "#" + item.getMsku())) {
                            adSearchTermsDto.merge(item);
                        }
                    }
                    if (StringUtils.equalsIgnoreCase(type, Constants.SB)) {
                        if (shopAsinSet.contains(item.getShopId() + "#" + item.getAsin())) {
                            adSearchTermsDto.merge(item);
                        }
                    }
                }
            }

            //曝光占比
            Long impressions = adSearchTermsDto.getImpressions();
            BigDecimal adImpressionRatio = MathUtil.divideIfZero(impressions, adImpressionTotal, 8);
            asinRatio.setImpressionsRatio(adImpressionRatio);
            //点击占比
            Long adClick = adSearchTermsDto.getClicks();
            BigDecimal adClickRatio = MathUtil.divideIfZero(adClick, adClickTotal, 8);
            asinRatio.setClicksRatio(adClickRatio);
            //销量占比
            Integer adSaleNum = adSearchTermsDto.getAdSaleNum();
            BigDecimal adSaleNumRatio = MathUtil.divideIfZero(adSaleNum, adSaleNumTotal, 8);
            asinRatio.setAdSaleNumRatio(adSaleNumRatio);
            //订单量占比
            Integer adOrderNum = adSearchTermsDto.getAdOrderNum();
            BigDecimal adOrderNumRatio = MathUtil.divideIfZero(adOrderNum, adOrderNumTotal, 8);
            asinRatio.setAdOrderNumRatio(adOrderNumRatio);
            //销售额占比

            Integer saleNum = adSearchTermsDto.getSaleNum();
            BigDecimal saleNumRatio = MathUtil.divideIfZero(saleNum, saleNumTotal, 8);
            asinRatio.setSaleNumRatio(saleNumRatio);

            BigDecimal totalSales = adSearchTermsDto.getTotalSales();
            BigDecimal totalSalesRatio = MathUtil.divideByZero(totalSales, totalSalesTotal, 8);
            asinRatio.setTotalSalesRatio(totalSalesRatio);

            Integer orderNum = adSearchTermsDto.getOrderNum();
            BigDecimal orderNumRatio = MathUtil.divideIfZero(orderNum, orderNumTotal, 8);
            asinRatio.setOrderNumRatio(orderNumRatio);

            BigDecimal adSales = adSearchTermsDto.getAdSales();
            BigDecimal adSalesRatio = MathUtil.divideByZero(adSales, adSalesTotal, 8);
            asinRatio.setAdSalesRatio(adSalesRatio);


            //花费占比
            BigDecimal adCost = adSearchTermsDto.getCost();
            BigDecimal adCostRatio = MathUtil.divideByZero(adCost, costTotal, 8);
            asinRatio.setCostRatio(adCostRatio);


            Integer salesNum = adSearchTermsDto.getSalesNum();
            BigDecimal salesNumRatio = MathUtil.divideIfZero(salesNum, salesNumTotal, 8);
            asinRatio.setSalesNumRatio(salesNumRatio);

            //可见展示次数占比
            Long viewImpressions = adSearchTermsDto.getViewImpressions();
            BigDecimal viewImpressionRatio = MathUtil.divideIfZero(viewImpressions, viewImpressionsTotal, 8);
            asinRatio.setViewImpressionsRatio(viewImpressionRatio);
            //品牌新买家订单量占比
            Integer ordersNewToBrandFTD = adSearchTermsDto.getOrdersNewToBrandFTD();
            BigDecimal ordersNewToBrandFTDTotalRatio = MathUtil.divideIfZero(ordersNewToBrandFTD, ordersNewToBrandFTDTotal, 8);
            asinRatio.setOrdersNewToBrandFTDRatio(ordersNewToBrandFTDTotalRatio);
            //品牌新买家销售额占比
            BigDecimal salesNewToBrandFTD = adSearchTermsDto.getSalesNewToBrandFTD();
            BigDecimal salesNewToBrandFTDTotalRatio = MathUtil.divideByZero(salesNewToBrandFTD, salesNewToBrandFTDTotal, 8);
            asinRatio.setSalesNewToBrandFTDRatio(salesNewToBrandFTDTotalRatio);
            rationMap.put(adGroupId,asinRatio);
        }

        log.info("产品透视分析 {}--{}-计算占比- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);

        //支持模糊查询
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> termsList = new ArrayList<>();
        if (types.contains(Constants.SP) && CollectionUtils.isNotEmpty(spGroupIdList)) {
            termsList.addAll(cpcQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, paramView));
        }
        if (types.contains(Constants.SB) && CollectionUtils.isNotEmpty(sbGroupIdList)) {
            termsList.addAll(odsCpcSbQueryKeywordReportDao.selectProductSearchTermsViewInfo(puid, paramView));
        }
        if (CollectionUtils.isEmpty(termsList)) {
            return new HashMap<>();
        }
        log.info("产品透视分析 {}--{}-查询搜索词耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        t = Instant.now().toEpochMilli();
        //根据占比设置指标
        termsList.forEach(item ->{
            String adGroupId = item.getAdGroupId();
            AsinRatio asinRatio = rationMap.get(adGroupId);
            if(asinRatio != null){
                //曝光
                Long totalImpressions = item.getImpressions();
                Long impressions = MathUtil.multiply(asinRatio.getImpressionsRatio(),new BigDecimal(totalImpressions)).longValue();
                item.setImpressions(impressions);
                //点击
                Long totalClick = item.getClicks();
                Long click = MathUtil.multiply(asinRatio.getClicksRatio(),new BigDecimal(totalClick)).longValue();
                item.setClicks(click);
                //销量
                Integer totalAdSaleNum = item.getAdSaleNum();
                Integer adSaleNum = MathUtil.multiply(asinRatio.getAdSaleNumRatio(),new BigDecimal(totalAdSaleNum)).intValue();
                item.setAdSaleNum(adSaleNum);
                //订单量
                Integer totalOrderNum = item.getOrderNum();
                Integer orderNum = MathUtil.multiply(asinRatio.getOrderNumRatio(),new BigDecimal(totalOrderNum)).intValue();
                item.setOrderNum(orderNum);
                //花费
                BigDecimal totalCost = item.getCost();
                BigDecimal adCost = MathUtil.multiply(asinRatio.getCostRatio(),totalCost);
                item.setCost(adCost);

                Integer adOrderNumTotal = item.getAdOrderNum();
                Integer adOrderNum = MathUtil.multiply(asinRatio.getAdOrderNumRatio(), new BigDecimal(adOrderNumTotal)).intValue();
                item.setAdOrderNum(adOrderNum);

                BigDecimal totalSalesTotal = item.getTotalSales();
                BigDecimal totalSales = MathUtil.multiply(asinRatio.getTotalSalesRatio(), totalSalesTotal);
                item.setTotalSales(totalSales);

                BigDecimal adSalesTotal = item.getAdSales();
                BigDecimal adSales = MathUtil.multiply(asinRatio.getAdSalesRatio(), adSalesTotal);
                item.setAdSales(adSales);

                Integer salesNumTotal = item.getSalesNum() == null ? 0 : item.getSalesNum();
                Integer salesNum = MathUtil.multiply(asinRatio.getSalesNumRatio(), new BigDecimal(salesNumTotal)).intValue();
                item.setSalesNum(salesNum);

                Integer totalSaleNum = item.getSaleNum();
                Integer saleNum = MathUtil.multiply(asinRatio.getSaleNumRatio(),new BigDecimal(totalSaleNum)).intValue();
                item.setSaleNum(saleNum);

                //可见展示次数
                if (item.getViewImpressions() != null) {
                    Long totalViewImpressions = item.getViewImpressions();
                    Long viewImpressions = MathUtil.multiply(asinRatio.getViewImpressionsRatio(),new BigDecimal(totalViewImpressions)).longValue();
                    item.setViewImpressions(viewImpressions);
                }
                //品牌新买家订单量
                if (item.getOrdersNewToBrandFTD() != null) {
                    Integer ordersNewToBrandFTDTotal = item.getOrdersNewToBrandFTD();
                    Integer ordersNewToBrandFTD = MathUtil.multiply(asinRatio.getOrdersNewToBrandFTDRatio(), new BigDecimal(ordersNewToBrandFTDTotal)).intValue();
                    item.setOrdersNewToBrandFTD(ordersNewToBrandFTD);
                }
                //品牌新买家销售额
                if (item.getSalesNewToBrandFTD() != null) {
                    BigDecimal salesNewToBrandFTDTotal = item.getSalesNewToBrandFTD();
                    BigDecimal salesNewToBrandFTD = MathUtil.multiply(asinRatio.getSalesNewToBrandFTDRatio(), salesNewToBrandFTDTotal);
                    item.setSalesNewToBrandFTD(salesNewToBrandFTD);
                }
            }
        });
        log.info("产品透视分析 {}--{}-计算实际值耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        //合并数据
        t = Instant.now().toEpochMilli();
        List<AdProductReportSearchTermsViewDto> resultList =
                new ArrayList<>(termsList.stream().collect(Collectors.toMap(AdProductReportSearchTermsViewDto::getQuery,
                        Function.identity(), (s1, s2) -> {
                            merge(s1, s2);
                            return s1;
                        }
                )).values());
        log.info("产品透视分析 {}--{}-聚合数据耗时- 花费时间 {}", uuid, title , Instant.now().toEpochMilli() - t);
        //汇总
        AdProductReportSearchTermsViewDto sum = new AdProductReportSearchTermsViewDto();
        termsList.forEach(sum::merge);

        StreamDataViewVo aggregateVo = new StreamDataViewVo();
        ViewServiceHelper.fillStreamDataIntoViewVo(aggregateVo, sum, param.getShopSales());
        aggregateVo.setAdCostPercentage(sum.getCost().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdSalePercentage(sum.getTotalSales().compareTo(BigDecimal.ZERO) > 0 ? "100" : "0");
        aggregateVo.setAdOrderNumPercentage(sum.getAdOrderNum() > 0 ? "100" : "0");
        aggregateVo.setOrderNumPercentage(sum.getOrderNum() > 0 ? "100" : "0");

        //获取占比指标汇总数据
        AdMetricDto adMetricDto = this.getSumAdMetricDto(sum);

        //列表页数据
        SearchTermsViewVo vo;
        Map<Long, Integer> result = new HashMap<>();
        for (AdProductReportSearchTermsViewDto item : resultList) {
            vo = new SearchTermsViewVo();
            vo.setQuery(item.getQuery());
            vo.setType(item.getType().toUpperCase());
            ViewServiceHelper.fillStreamDataIntoViewVo(vo, item, param.getShopSales());
            ViewServiceHelper.filterAdMetricData(vo, adMetricDto);

            for (DiagnoseCountParam diagnoseCountParam : diagnoseCountParams) {
                Integer count = result.getOrDefault(diagnoseCountParam.getId(), 0);
                if (isFilterAdVanceData(vo, diagnoseCountParam)) {
                    count = count + 1;
                }
                result.put(diagnoseCountParam.getId(), count);
            }

        }
        return result;

    }

}
