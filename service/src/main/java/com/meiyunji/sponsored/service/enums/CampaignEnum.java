package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

@Getter
public enum CampaignEnum {
    /**
     * 广告活动 推广类型
     * */
    sponsoredProducts("sponsoredProducts", "SP", "产品推广"),

    sponsoredBrands("sponsoredBrands", "SB", "品牌推广"),

    sponsoredDisplay("sponsoredDisplay", "SD", "展示型推广");

    private String campaignType;

    private String code;

    private String campaignValue;

    public static String getCampaignValue(String campaignType){
        CampaignEnum[] values = values();
        for (CampaignEnum value : values) {
            if(value.getCampaignType().equals(campaignType)){
                return value.getCampaignValue();
            }
        }
        return "";
    }

    CampaignEnum(String campaignType, String code, String campaignValue) {
        this.campaignType = campaignType;
        this.code = code;
        this.campaignValue = campaignValue;
    }
}
