package com.meiyunji.sponsored.service.autoRule.enums;

public enum OperationEnum {
    AUTO_ADD_BID("autoAddBid", "自动加价"),
    AUTO_REDUCE_BID("autoReduceBid", "自动降价"),
    RESTORE_BID("restoreBid", "还原竞价"),
    UNADJUSTED("unadjusted", "不调整");

    private String value;
    private String desc;

    OperationEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
