package com.meiyunji.sponsored.service.stream.strgtegy;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: ys
 * @date: 2024/1/17 20:25
 * @describe:
 */
@Service
public class ManageStreamSbKeywordStreamProcess extends AbstractManageStreamRetryProcessStrategy {

    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageStreamSbKeywordStreamProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration);
    }

    @Override
    public int getMaxCount() {
        return StreamConstants.SB_MAX_KEYWORD_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcSbKeywordApiService.syncKeywords(shopAuth, null, null, ids, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
    }
}
