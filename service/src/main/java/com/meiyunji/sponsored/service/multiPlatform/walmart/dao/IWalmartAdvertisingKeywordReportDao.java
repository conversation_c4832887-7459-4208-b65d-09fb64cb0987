package com.meiyunji.sponsored.service.multiPlatform.walmart.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroupReportPage;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordRecommendations;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordReport;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordReportPage;

import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingKeywordReportDao extends IBaseShardingDao<WalmartAdvertisingKeywordReport> {

    Long add(WalmartAdvertisingKeywordReport keywordReport);

    int update(WalmartAdvertisingKeywordReport keywordReport);

    int delete(Integer puid, Long id);

    WalmartAdvertisingKeywordReport getByKeywordId(int puid, Long shopId, String reportDate, Long campaignId, Long gruopId, Long keywordId);

    WalmartAdvertisingKeywordReport getLastKeywordReport(int puid, Long shopId);

    Page getPageListKeywordReport(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    List<WalmartAdvertisingKeywordReportPage> getListKeywordReport(int puid, Map<String, Object> queryParams);

    List<WalmartAdvertisingGroupReportPage> getSumReportOrderByReportDate(int puid, Map<String, Object> queryParams);

    Page getSumReportOrderByReportDatePage(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    WalmartAdvertisingGroupReportPage getSumReportDate(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    WalmartAdvertisingGroupReportPage getSumReport(int puid, Map<String, Object> queryParams);


    WalmartAdvertisingKeywordReport getLastKeywordReport(int puid, Integer shopId);

    WalmartAdvertisingKeywordReport getByKeywordId(int puid, Integer shopId, String reportDate, String campaignId, String gruopId, String keywordId);

    int insertOrUpdate(int puid, List<WalmartAdvertisingKeywordReport> list);
}
