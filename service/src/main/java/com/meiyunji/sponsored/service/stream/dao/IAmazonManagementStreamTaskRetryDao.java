package com.meiyunji.sponsored.service.stream.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskRetryStatusEnum;
import com.meiyunji.sponsored.service.stream.po.AmazonManagementStreamTaskRetry;

import java.util.List;

/**
 * @author: sunlinfeng
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-16  09:30
 */
public interface IAmazonManagementStreamTaskRetryDao extends IAdBaseDao<AmazonManagementStreamTaskRetry> {

    void insertList(List<AmazonManagementStreamTaskRetry> groupList);

    List<Integer> getNeedRetryPuid(Integer limit);

    List<AmazonManagementStreamTaskRetry> getNeedRetryByPuidAndShopId(Integer puid, Integer shopId, int index, int limit, String type);

    void updateList(List<AmazonManagementStreamTaskRetry> amazonManagementStreamTaskRetries, String errorInfo);


    List<Integer> getNeedRetryShopId(Integer limit, Integer puid, Integer shopId, String type);
}
