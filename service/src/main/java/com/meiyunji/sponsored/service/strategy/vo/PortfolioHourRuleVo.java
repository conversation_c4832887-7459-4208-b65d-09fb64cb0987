package com.meiyunji.sponsored.service.strategy.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PortfolioHourRuleVo implements Serializable {
    @JsonProperty("siteDate")
    private Integer siteDate;   //站点日期 1,2,3,4,5,6,7,0   0表示每日
    @JsonProperty("startTimeSite")
    private Integer startTimeSite;  //站点开始时间 小时
    @JsonProperty("endTimeSite")
    private Integer endTimeSite; //站点结束时间
    @JsonProperty("amount")
    private BigDecimal amount;  //数值
    @JsonProperty("policy")
    private String policy;  //数值
}
