package com.meiyunji.sponsored.service.reportImport.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LxCampaignPlacementReport extends BaseLxReport {

    /**
     * 广告位
     */
    @ExcelProperty("广告位")
    private String placement;

    /**
     *  花费-本币
     */
    @ExcelProperty(value = "花费-本币", converter = CustomStringNumberConverter.class)
    private String adCost;

    /**
     * 曝光量
     */
    @ExcelProperty(value = "曝光量", converter = CustomStringNumberConverter.class)
    private String impressions;

    /**
     * 点击量
     */
    @ExcelProperty(value = "点击", converter = CustomStringNumberConverter.class)
    private String clicks;

    /**
     * 广告订单
     */
    @ExcelProperty(value = "广告订单", converter = CustomStringNumberConverter.class)
    private String adOrder;

    /**
     * 直接成交订单
     */
    @ExcelProperty(value = "直接成交订单", converter = CustomStringNumberConverter.class)
    private String adSelfOrder;

    /**
     * 间接成交订单
     */
    @ExcelProperty(value = "间接成交订单", converter = CustomStringNumberConverter.class)
    private String adOtherOrder;

    /**
     * 销售额-本币
     */
    @ExcelProperty(value = "销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSales;

    /**
     * 直接成交销售额-本币
     */
    @ExcelProperty(value = "直接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSelfSales;

    /**
     * 间接成交销售额-本币
     */
    @ExcelProperty(value = "间接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adOtherSales;

    /**
     * 广告销量
     */
    @ExcelProperty(value = "广告销量", converter = CustomStringNumberConverter.class)
    private String adSaleNum;

    /**
     * 直接成交销量
     */
    @ExcelProperty(value = "直接成交销量", converter = CustomStringNumberConverter.class)
    private String adSelfSaleNum;

    /**
     * 间接成交销量
     */
    @ExcelProperty(value = "间接成交销量", converter = CustomStringNumberConverter.class)
    private String adOtherSaleNum;

}
