package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2024/8/16 14:40
 * @describe:
 */
@Getter
public enum SDVideoCheckApprovedProgramsEnum {
    SPONSORED_DISPLAY_VIDEO(1, "SPONSORED_DISPLAY_VIDEO", "SD视频"),
    SPONSORED_DISPLAY_LANDSCAPE_VIDEO(1, "SPONSORED_DISPLAY_LANDSCAPE_VIDEO", "SD景观视频"),
    SPONSORED_DISPLAY_PORTRAIT_VIDEO(3, "SPONSORED_DISPLAY_PORTRAIT_VIDEO", "SD纵向视频"),
    SPONSORED_DISPLAY_SQUARE_VIDEO(4, "SPONSORED_DISPLAY_SQUARE_VIDEO", "SD正方形视频")
    ;
    private Integer code;
    private String approved;
    private String msg;

    SDVideoCheckApprovedProgramsEnum(Integer code, String approved, String msg) {
        this.code = code;
        this.approved = approved;
        this.msg = msg;
    }
}
