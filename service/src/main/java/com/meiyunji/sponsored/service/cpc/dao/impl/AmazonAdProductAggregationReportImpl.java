package com.meiyunji.sponsored.service.cpc.dao.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductAggregationReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductAggregationReport;
import com.meiyunji.sponsored.service.cpc.vo.AdProductSearchVo;
import com.meiyunji.sponsored.service.cpc.vo.ProductReportDetailsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public class AmazonAdProductAggregationReportImpl extends BaseShardingSphereDaoImpl<AmazonAdProductAggregationReport> implements IAmazonAdProductAggregationReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdProductAggregationReport> list) {
        //插入原表
        insertOrUpdateListOriginAndHotTable(puid, list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //插入热表
            insertOrUpdateListOriginAndHotTable(puid, list, getHotTableName());
        }
    }

    private void insertOrUpdateListOriginAndHotTable(Integer puid, List<AmazonAdProductAggregationReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`type`,`count_date`,`campaign_id`,`ad_group_id`,")
                .append("`ad_id`,`sku`,`asin`,`ad_group_name`,`campaign_name`,")
                .append("`impressions`,`clicks`,`sale_num`,`cost`,`ad_sales`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProductAggregationReport report : list) {
            sql.append(" (?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getType());
            argsList.add(report.getCountDate());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getAdId());
            argsList.add(report.getSku());
            argsList.add(report.getAsin());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getSaleNum());
            argsList.add(report.getCost());
            argsList.add(report.getAdSales());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`ad_group_name`=values(ad_group_name),`asin`=values(asin),`sku`=values(sku),`impressions`=values(impressions),`clicks`=values(clicks),`cost`=values(cost),");
        sql.append("`sale_num`=values(sale_num),`ad_sales`=values(ad_sales)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public Page<AmazonAdProductAggregationReport> getPageList(Integer puid, AdProductSearchVo searchVo) {
        String tableName = getTableNameByStartDate(searchVo.getStart());
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,marketplace_id,`asin`,`sku`,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`ad_sales`) ad_sales  FROM ")
                .append(tableName)
                 .append(" force index(idx_pid_sid_cdate_asin_impressions_mid_sku_cls_sn_cost_as) ");

        StringBuilder countSql = new StringBuilder("select count(*) from ( select `asin`,shop_id FROM ");
        countSql.append(tableName);
        countSql.append(" force index(idx_pid_sid_cdate_asin_impressions_mid_sku_cls_sn_cost_as) ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(searchVo.getShopIdList())) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", searchVo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(searchVo.getType())) {
            whereSql.append(" and type=? ");
            argsList.add(searchVo.getType());
        }
        if (StringUtils.isNotBlank(searchVo.getSearchValue())) {
            whereSql.append(" and asin=? ");
            argsList.add(searchVo.getSearchValue().trim());
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        whereSql.append(" group by `shop_id`,asin ");
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");

        if (StringUtils.isNotBlank(searchVo.getOrderField()) && StringUtils.isNotBlank(searchVo.getOrderValue())) {
            String orderField = getProductReportField(searchVo.getOrderField());
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(searchVo.getOrderValue())){
                    selectSql.append(" desc");
                } else if ("asc".equals(searchVo.getOrderValue())) {
                    selectSql.append(" asc");
                } else {
                    selectSql.append(" desc");
                }
                selectSql.append(" ,shop_id desc ");
            }
        }

        Object[] args = argsList.toArray();
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        return this.getPageResult(puid, searchVo.getPageNo(), searchVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdProductAggregationReport.class);
    }

    @Override
    public List<AmazonAdProductAggregationReport> getListBySearch(Integer puid, AdProductSearchVo searchVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost, sum(`ad_sales`) ad_sales FROM ");
        String tableName = getTableNameByStartDate(searchVo.getStart());
        selectSql.append(tableName);

        selectSql.append(" force index(idx_pid_sid_cdate_asin_impressions_mid_sku_cls_sn_cost_as)");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(searchVo.getShopIdList())) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", searchVo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(searchVo.getType())) {
            whereSql.append(" and type=? ");
            argsList.add(searchVo.getType());
        }
        if (StringUtils.isNotBlank(searchVo.getSearchValue())) {
            whereSql.append(" and asin=? ");
            argsList.add(searchVo.getSearchValue().trim());
        }
        whereSql.append("and count_date>=? and count_date<=? group by shop_id");
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdProductAggregationReport getDetailsSumVo(Integer puid, Integer shopId, String type, String asin, String startStr, String endStr) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,`asin`,`sku`,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`ad_sales`) ad_sales  FROM ");
        String tableName = getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(tableName);

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and asin = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(asin);

        if (StringUtils.isNotBlank(type)) {
            whereSql.append(" and type=? ");
            argsList.add(type);
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(startStr);
        argsList.add(endStr);
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdProductAggregationReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductAggregationReport> getReportListByDay(Integer puid, ProductReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,marketplace_id,`asin`,`sku`,`count_date`,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`ad_sales`) ad_sales  FROM ");
        String tableName = getTableNameByStartDate(detailsVo.getStart());
        selectSql.append(tableName);

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and asin = ? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getAsin());

        if (StringUtils.isNotBlank(detailsVo.getType())) {
            whereSql.append(" and type=? ");
            argsList.add(detailsVo.getType());
        }
        whereSql.append("and count_date>=? and count_date<=? group by count_date ");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductAggregationReport> getListByAsin(Integer puid, Integer shopId, Date start, Date end, String type, String asin) {
        StringBuilder selectSql = new StringBuilder("SELECT * FROM ");
        String tableName = getTableNameByStartDate(start);
        selectSql.append(tableName);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        whereSql.append("and shop_id=? and asin= ? ");
        argsList.add(shopId);
        argsList.add(asin);
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(start,"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
        if (StringUtils.isNotBlank(type)) {
            whereSql.append(" and type=? ");
            argsList.add(type);
        }

        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdProductAggregationReport> getListByParentAsin(Integer puid, Integer shopId, Date start, Date end, String type, String parentAsin) {
        StringBuilder selectSql = new StringBuilder("SELECT * FROM ");
        String tableName = getTableNameByStartDate(start);
        selectSql.append(tableName);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        whereSql.append("and shop_id=? and parent_asin= ? ");
        argsList.add(shopId);
        argsList.add(parentAsin);
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(start,"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
        if (StringUtils.isNotBlank(type)) {
            whereSql.append(" and type=? ");
            argsList.add(type);
        }

        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdProductAggregationReport> getNeedsSyncParentAsin(Integer puid, Integer shopId, Integer limit){
        String selectSql = "SELECT * FROM t_amazon_ad_product_aggregation_report" +
                " where  puid = ? and shop_id = ? and parent_asin = '' limit ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(limit);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql, argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdProductAggregationReport> getNeedsSyncNoneParentAsin(Integer puid, Integer shopId, Integer limit, Integer syncDay){
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.equalToWithoutCheck("parent_asin", "None");
        builder.greaterThanOrEqualTo("create_time", LocalDateTime.now().minusDays(syncDay == null? 3: syncDay));
        builder.lessThan("sync_next_date", LocalDateTime.now());
        builder.orderByAsc("sync_next_date");
        builder.limit(limit);
        return listByCondition(puid, builder.build());
    }

    @Override
    public int[] batchUpdateParentAsin(Integer puid, List<AmazonAdProductAggregationReport> reports) {
        //更新总表
        int[] ints = batchUpdateParentAsinOrigin(puid, reports);
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //更新hot表
            batchUpdateParentAsinHot(puid, reports);
        }
        return ints;
    }

    private int[] batchUpdateParentAsinOrigin(Integer puid, List<AmazonAdProductAggregationReport> reports) {
        HintManager hintManager = HintManager.getInstance();
        StringBuilder sql = new StringBuilder("update " + getJdbcHelper().getTable() + " set parent_asin = ? where id = ? and puid = ? and shop_id = ?");
        Object[] arg ;
        List<Object[]> args = Lists.newArrayList();
        for (AmazonAdProductAggregationReport report : reports) {
            arg = new Object[]{report.getParentAsin(),report.getId(),
                    report.getPuid(), report.getShopId()};
            args.add(arg);
        }
        try {
            return getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), args);
        } finally {
            hintManager.close();
        }
    }

    private void batchUpdateParentAsinHot(Integer puid, List<AmazonAdProductAggregationReport> reports) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`type`,`count_date`,`ad_id`,`parent_asin`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProductAggregationReport report : reports) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getType());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdId());
            argsList.add(report.getParentAsin());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `parent_asin`=values(parent_asin) ");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int[] batchUpdateSyncNextDate(Integer puid, List<AmazonAdProductAggregationReport> reports) {
        HintManager hintManager = HintManager.getInstance();
        //sync_next_date非业务字段，hot表无需处理
        StringBuilder sql = new StringBuilder("update t_amazon_ad_product_aggregation_report " +
                "set sync_next_date = ? where id = ? and puid = ? and shop_id = ?");
        Object[] arg;
        List<Object[]> args = Lists.newArrayList();
        for (AmazonAdProductAggregationReport report : reports) {
            arg = new Object[]{report.getSyncNextDate(), report.getId(),
                    report.getPuid(), report.getShopId()};
            args.add(arg);
        }
        try {
            return getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), args);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<AmazonAdProductAggregationReport> getPageListGroupByParentAsin(Integer puid, AdProductSearchVo searchVo) {
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,marketplace_id, `parent_asin` as `asin`,`sku`,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`ad_sales`) ad_sales  FROM ");
        String tableName = getTableNameByStartDate(searchVo.getStart());
        selectSql.append(tableName);
        selectSql.append(" force index(idx_pid_sid_cdate_parent_asin_impressions_mid_sku_cls_sn_cost_as) ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select `parent_asin`,`shop_id` FROM ");
        countSql.append(tableName);
        countSql.append(" force index(idx_pid_sid_cdate_parent_asin_impressions_mid_sku_cls_sn_cost_as) ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(searchVo.getShopIdList())) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", searchVo.getShopIdList(), argsList));
        }
        whereSql.append(" and parent_asin <> 'None' and parent_asin <> '' ");
        if (StringUtils.isNotBlank(searchVo.getType())) {
            whereSql.append(" and type=? ");
            argsList.add(searchVo.getType());
        }
        if (StringUtils.isNotBlank(searchVo.getSearchValue())) {
            if (searchVo.getListSearchValue().size() > 1) {
                whereSql.append(SqlStringUtil.dealInList("parent_asin", searchVo.getListSearchValue(), argsList));
            } else {
                whereSql.append(" and parent_asin=? ");
                argsList.add(searchVo.getSearchValue().trim());
            }
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        whereSql.append(" group by `shop_id`,parent_asin ");
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");

        if (StringUtils.isNotBlank(searchVo.getOrderField()) && StringUtils.isNotBlank(searchVo.getOrderValue())) {
            String orderField = getProductReportField(searchVo.getOrderField());
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(searchVo.getOrderValue())){
                    selectSql.append(" desc");
                } else if ("asc".equals(searchVo.getOrderValue())) {
                    selectSql.append(" asc");
                } else {
                    selectSql.append(" desc");
                }
                selectSql.append(" ,shop_id desc ");
            }
        }

        Object[] args = argsList.toArray();
        return this.getPageResult(puid, searchVo.getPageNo(), searchVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdProductAggregationReport.class);
    }

    @Override
    public List<AmazonAdProductAggregationReport> getListGroupByParentAsin(Integer puid, AdProductSearchVo searchVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost, sum(`ad_sales`) ad_sales FROM ");
        String tableName = getTableNameByStartDate(searchVo.getStart());
        selectSql.append(tableName);
        selectSql.append(" force index(idx_pid_sid_cdate_parent_asin_impressions_mid_sku_cls_sn_cost_as)");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(searchVo.getShopIdList())) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", searchVo.getShopIdList(), argsList));
        }
        whereSql.append(" and parent_asin <> 'None' ");
        if (StringUtils.isNotBlank(searchVo.getType())) {
            whereSql.append(" and type=? ");
            argsList.add(searchVo.getType());
        }
        if (StringUtils.isNotBlank(searchVo.getSearchValue())) {
            whereSql.append(" and parent_asin=? ");
            argsList.add(searchVo.getSearchValue().trim());
        }
        whereSql.append("and count_date>=? and count_date<=? group by shop_id");
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    /**
     * 产品聚合报告排序
     * @param field
     * @return
     */
    private String getProductReportField(String field) {
        switch (field){
            case "impressions":
                return "sum(impressions)";
            case "clicks" :
                return "sum(clicks)";
            case "ctr" :
                return "ifnull(sum(`clicks`)/sum(`impressions`),0)";
            case "cvr" :
                return "ifnull(sum(`sale_num`)/sum(`clicks`),0)";
            case "acos" :
                return "ifnull(sum(`cost`)/sum(`ad_sales`),0)";
            case "saleNum" :
                return "sum(sale_num)";
            case "cost" :
                return "sum(cost)";
            case "adCostPerClick" :
                return "ifnull(sum(`cost`)/sum(`clicks`),0)";
            case "adSales" :
                return "sum(ad_sales)";
            case "roas" :
                return "ifnull(sum(`ad_sales`)/sum(`cost`),0)";
            default:
                return " sum(impressions) ";
        }
    }
}
