package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisQueryRequest;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdBudgetUsageDayDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsageDay;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Repository
public class OdsAmazonAdBudgetUsageDayDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdBudgetUsageDay> implements IOdsAmazonAdBudgetUsageDayDao {


    @Override
    public Page<OdsAmazonAdBudgetUsageDay> listPage(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        List<Object> argsList = new ArrayList<>();
        // 站点今日
        StringBuilder selectSql = new StringBuilder(" select bsa.budget_scope_id budget_scope_id, bsa.usage_updated_site_date usage_updated_site_date ");
        boolean bool = StringUtils.isNotBlank(budgetAnalysisQueryRequest.getOrderType()) && !"null".equalsIgnoreCase(budgetAnalysisQueryRequest.getOrderType()) && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getOrderField());
        // 判断是否排序 然后增加字段
        selectSql.append(getSqlField(bool ? budgetAnalysisQueryRequest.getOrderField() : null));
        StringBuilder countSql = new StringBuilder("select count(*) from (select bsa.budget_scope_id budget_scope_id, bsa.usage_updated_site_date usage_updated_site_date ");
        selectSql.append(" from ods_t_amazon_ad_budget_usage_day bsa ");
        countSql.append(" from ods_t_amazon_ad_budget_usage_day bsa ");
        String joinSql = leftJoinSql(budgetAnalysisQueryRequest, argsList, bool);
        selectSql.append(joinSql);
        countSql.append(joinSql);
        StringBuilder whereSql = buildWhere(budgetAnalysisQueryRequest, argsList);
        selectSql.append(whereSql);
        countSql.append(whereSql);
        countSql.append(" ) r");
        selectSql.append(" order by ").append(getOrderField(bool ? budgetAnalysisQueryRequest.getOrderField() : null, budgetAnalysisQueryRequest.getOrderType())).append(" ");
        Object[] args = argsList.toArray();
        return getPageResult(budgetAnalysisQueryRequest.getPageNo(), budgetAnalysisQueryRequest.getPageSize(), countSql.toString(), args, selectSql.toString(), args, OdsAmazonAdBudgetUsageDay.class);
    }

    @Override
    public int onlyCount(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        List<Object> argsList = new ArrayList<>();
        // 站点今日
        boolean bool = StringUtils.isNotBlank(budgetAnalysisQueryRequest.getOrderType()) && !"null".equalsIgnoreCase(budgetAnalysisQueryRequest.getOrderType()) && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getOrderField());
        // 判断是否排序 然后增加字段
        StringBuilder countSql = new StringBuilder("select count(*) from (select bsa.budget_scope_id budget_scope_id, bsa.usage_updated_site_date usage_updated_site_date ");
        countSql.append(" from ods_t_amazon_ad_budget_usage_day bsa ");
        String joinSql = leftJoinSql(budgetAnalysisQueryRequest, argsList, bool);
        countSql.append(joinSql);
        StringBuilder whereSql = buildWhere(budgetAnalysisQueryRequest, argsList);
        countSql.append(whereSql);
        countSql.append(" ) r");
        Object[] args = argsList.toArray();
        return countPageResult(budgetAnalysisQueryRequest.getPuid(), countSql.toString(), args);
    }

    @Override
    public List<OdsAmazonAdBudgetUsageDay> listByBudgetScopeIdAndDate(Integer puid, List<Integer> shopId, List<String> ids, List<String> date) {
        StringBuilder sql = new StringBuilder("select *  from ods_t_amazon_ad_budget_usage_day where puid=? ");
        List<Object> args = Lists.newArrayList(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopId, args));
        sql.append(SqlStringUtil.dealBitMapDorisInList("budget_scope_id", ids, args));
        sql.append(SqlStringUtil.dealInList("usage_updated_site_date", date, args));
        return getJdbcTemplate().query(sql.toString(), BeanPropertyRowMapper.newInstance(OdsAmazonAdBudgetUsageDay.class),  args.toArray());
    }

    /**
     * 根据条件决定连表操作
     */
    private String leftJoinSql(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest, List<Object> argsList, boolean bool) {
        Set<String> key = new HashSet<>();
        if (bool) {
            getLeftSqlTable(key, budgetAnalysisQueryRequest.getOrderField());
        } else {
            key.add("acr");
            key.add("dcr");
        }
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getStatus()) || StringUtils.isNotBlank(budgetAnalysisQueryRequest.getSeverStatus())) {
            key.add("ac");
        }
        // 开启了高级筛选
        if (budgetAnalysisQueryRequest.getUseAdvanced() != null && budgetAnalysisQueryRequest.getUseAdvanced()) {
            key.add("acr");
        }
        if (CollectionUtils.isNotEmpty(key)) {
            StringBuilder stringBuilder = new StringBuilder();
            String month = DateUtil.dateToStrWithFormat(new Date(), DateUtil.PATTERN_YYYYMM);
            if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getMarketplaceIdList())) {
                month = CalculateAdDataUtil.getMinSiteMonth(budgetAnalysisQueryRequest.getMarketplaceIdList());
            }
            final String rateMonth = month;
            key.forEach(sql -> {
                // 汇率表
                if ("dcr".equals(sql)) {
                    stringBuilder.append(" left join (select * from dim_currency_rate where puid = ? and `to` = 'USD' and `month` =  ").append(rateMonth).append(" ) dcr on bsa.puid = dcr.puid and bsa.currency = dcr.`from`  ");
                    argsList.add(budgetAnalysisQueryRequest.getPuid());
                    return;
                }
                // 广告活动报告表
                if ("acr".equals(sql)) {
                    stringBuilder.append(" left join ods_t_amazon_ad_campaign_all_report acr on bsa.puid = acr.puid and bsa.shop_id = acr.shop_id and bsa.budget_scope_id = acr.campaign_id and bsa.usage_updated_site_date = acr.count_day and acr.puid = ?  and acr.count_day >= ? and acr.count_day <= ? and acr.is_summary = 1 ");
                    argsList.add(budgetAnalysisQueryRequest.getPuid());
                    argsList.add(budgetAnalysisQueryRequest.getStartDate());
                    argsList.add(budgetAnalysisQueryRequest.getEndDate());
                    if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getShopIdList())) {
                        // 过滤店铺Id
                        stringBuilder.append(SqlStringUtil.dealInList("acr.shop_id", budgetAnalysisQueryRequest.getShopIdList(), argsList));
                    }
                    // 站点今日
                    if (budgetAnalysisQueryRequest.getSiteToday() && CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getSiteTodayDate())) {
                        stringBuilder.append(SqlStringUtil.dealDorisInList("concat_ws('|', acr.marketplace_id, acr.count_day)", budgetAnalysisQueryRequest.getSiteTodayDate(), argsList));
                    }
                    return;
                }
                // 广告活动表
                if ("ac".equals(sql)) {
                    stringBuilder.append(" left join ods_t_amazon_ad_campaign_all ac on bsa.puid = ac.puid and bsa.shop_id = ac.shop_id and bsa.budget_scope_id = ac.campaign_id and ac.puid = ?  ");
                    argsList.add(budgetAnalysisQueryRequest.getPuid());
                    if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getShopIdList())) {
                        // 过滤店铺Id
                        stringBuilder.append(SqlStringUtil.dealInList("ac.shop_id", budgetAnalysisQueryRequest.getShopIdList(), argsList));
                    }
                }
            });
            return stringBuilder.toString();
        }
        return " ";
    }


    private StringBuilder buildWhere(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where bsa.puid = ? ");
        argsList.add(budgetAnalysisQueryRequest.getPuid());
        if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getShopIdList())) {
            // 过滤店铺Id
            whereSql.append(SqlStringUtil.dealInList("bsa.shop_id", budgetAnalysisQueryRequest.getShopIdList(), argsList));
        }
        // marketplace 先忽略
        // 运行状态
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getStatus())) {
            List<String> statusList = StringUtil.splitStr(budgetAnalysisQueryRequest.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("ac.state", statusList, argsList));
        }
        // 服务状态
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getSeverStatus())) {
            List<String> list = StringUtil.splitStr(budgetAnalysisQueryRequest.getSeverStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("ac.serving_status", servings, argsList));
            }
        }
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getStartDate())) {
            whereSql.append(" and bsa.usage_updated_site_date >= ? ");
            argsList.add(budgetAnalysisQueryRequest.getStartDate());
        }
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getEndDate())) {
            whereSql.append(" and bsa.usage_updated_site_date <= ? ");
            argsList.add(budgetAnalysisQueryRequest.getEndDate());
        }

        // 站点今日
        if (budgetAnalysisQueryRequest.getSiteToday() && CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getSiteTodayDate())) {
            whereSql.append(SqlStringUtil.dealDorisInList("concat_ws('|', bsa.marketplace_id, bsa.usage_updated_site_date)", budgetAnalysisQueryRequest.getSiteTodayDate(), argsList));
        }

        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getType())) {
            whereSql.append(SqlStringUtil.dealInList("bsa.advertising_product_type", StringUtil.splitStr(budgetAnalysisQueryRequest.getType()), argsList));
        }
        if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("bsa.budget_scope_id", budgetAnalysisQueryRequest.getCampaignIdList(), argsList));
        }
        if (budgetAnalysisQueryRequest.getBudgetActiveTime() != null && budgetAnalysisQueryRequest.getBudgetActiveTime() > 0) {
            whereSql.append(" and bsa.budget_time <= ? ");
            argsList.add(budgetAnalysisQueryRequest.getBudgetActiveTime());
        }

        if (budgetAnalysisQueryRequest.getOutBudget() != null && budgetAnalysisQueryRequest.getOutBudget()) {
            whereSql.append(" and bsa.over_budget_time > 0 ");
        }

        if (budgetAnalysisQueryRequest.getUseAdvanced() != null && budgetAnalysisQueryRequest.getUseAdvanced()) {
            //展示量
            if (budgetAnalysisQueryRequest.getImpressionsMin() != null) {
                whereSql.append(" and ifnull(acr.impressions,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getImpressionsMin());
            }
            if (budgetAnalysisQueryRequest.getImpressionsMax() != null) {
                whereSql.append(" and ifnull(acr.impressions,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getImpressionsMax());
            }
            //点击量
            if (budgetAnalysisQueryRequest.getClicksMin() != null) {
                whereSql.append(" and ifnull(acr.clicks,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getClicksMin());
            }
            if (budgetAnalysisQueryRequest.getClicksMax() != null) {
                whereSql.append(" and ifnull(acr.clicks,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (budgetAnalysisQueryRequest.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(acr.clicks/acr.impressions,0),4) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getClickRateMin());
            }
            if (budgetAnalysisQueryRequest.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(acr.clicks/acr.impressions,0),4) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getClickRateMax());
            }
            //花费
            if (budgetAnalysisQueryRequest.getCostMin() != null) {
                whereSql.append(" and ifnull(acr.cost,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCostMin());
            }
            if (budgetAnalysisQueryRequest.getCostMax() != null) {
                whereSql.append(" and ifnull(acr.cost,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCostMax());
            }
            //cpc  平均点击费用
            if (budgetAnalysisQueryRequest.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/acr.clicks,0),2) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCpcMin());
            }
            if (budgetAnalysisQueryRequest.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/acr.clicks,0),2) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCpcMax());
            }
            //广告订单量
            if (budgetAnalysisQueryRequest.getOrderNumMin() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d),0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getOrderNumMin());
            }
            if (budgetAnalysisQueryRequest.getOrderNumMax() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d),0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getOrderNumMax());
            }
            //广告销售额
            if (budgetAnalysisQueryRequest.getSalesMin() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp', acr.sales7d, acr.sales14d),0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSalesMin());
            }
            if (budgetAnalysisQueryRequest.getSalesMax() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp', acr.sales7d, acr.sales14d),0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSalesMax());
            }
            //订单转化率
            if (budgetAnalysisQueryRequest.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)/acr.clicks,0),4) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSalesConversionRateMin());
            }
            if (budgetAnalysisQueryRequest.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)/acr.clicks,0),4) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSalesConversionRateMax());
            }
            //acos
            if (budgetAnalysisQueryRequest.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/if (acr.type = 'sp', acr.sales7d, acr.sales14d),0),4) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAcosMin());
            }
            if (budgetAnalysisQueryRequest.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/if (acr.type = 'sp', acr.sales7d, acr.sales14d),0),4) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAcosMax());
            }
            // roas
            if (budgetAnalysisQueryRequest.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d)/acr.cost,0),4) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getRoasMin());
            }
            // roas
            if (budgetAnalysisQueryRequest.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d)/acr.cost,0),4) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getRoasMax());
            }

            // 每日预算
            if (budgetAnalysisQueryRequest.getBudgetMin() != null) {
                whereSql.append(" and ifnull(bsa.budget,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getBudgetMin());
            }
            // 每日预算
            if (budgetAnalysisQueryRequest.getBudgetMax() != null) {
                whereSql.append(" and ifnull(bsa.budget,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getBudgetMax());
            }

            //广告销量
            if (budgetAnalysisQueryRequest.getAdSalesTotalMin() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp' , acr.`units_ordered7d`,if (acr.type = 'sb' , acr.`units_sold14d`, acr.`units_ordered14d`)),0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdSalesTotalMin());
            }
            if (budgetAnalysisQueryRequest.getAdSalesTotalMax() != null) {
                whereSql.append(" and IFNULL(if (acr.type = 'sp' , acr.`units_ordered7d`,if (acr.type = 'sb' , acr.`units_sold14d`, acr.`units_ordered14d`)),0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdSalesTotalMax());
            }

            //CPA
            if (budgetAnalysisQueryRequest.getCpaMin() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0), 2) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCpaMin());
            }
            if (budgetAnalysisQueryRequest.getCpaMax() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost/if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0), 2) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getCpaMax());
            }

            // 广告笔单价 筛选
            if (budgetAnalysisQueryRequest.getAdvertisingUnitPriceMin() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d)/if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0), 2) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdvertisingUnitPriceMin());
            }
            if (budgetAnalysisQueryRequest.getAdvertisingUnitPriceMax() != null) {
                whereSql.append(" and ROUND(ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d)/if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0), 2) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdvertisingUnitPriceMax());
            }

            if (budgetAnalysisQueryRequest.getAdCostPerClickMin() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost /acr.clicks , 0), 2) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdCostPerClickMin());
            }
            if (budgetAnalysisQueryRequest.getAdCostPerClickMax() != null) {
                whereSql.append(" and ROUND(ifnull(acr.cost /acr.clicks , 0), 2) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getAdCostPerClickMax());
            }

            if (budgetAnalysisQueryRequest.getBudgetAdjustmentNumMin() != null) {
                whereSql.append(" and ifnull(bsa.budget_adjustment_num,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getBudgetAdjustmentNumMin());
            }
            if (budgetAnalysisQueryRequest.getBudgetAdjustmentNumMax() != null) {
                whereSql.append(" and ifnull(bsa.budget_adjustment_num,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getBudgetAdjustmentNumMax());
            }

            if (budgetAnalysisQueryRequest.getSpentMin() != null) {
                whereSql.append(" and ifnull(bsa.budget_usage,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSpentMin());
            }
            if (budgetAnalysisQueryRequest.getSpentMax() != null) {
                whereSql.append(" and ifnull(bsa.budget_usage,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getSpentMax());
            }

            if (budgetAnalysisQueryRequest.getEstimateClicksMin() != null) {
                whereSql.append(" and ROUND(IFNULL(bsa.budget_usage / (acr.cost/acr.clicks) , 0), 4) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getEstimateClicksMin());
            }
            if (budgetAnalysisQueryRequest.getEstimateClicksMax() != null) {
                whereSql.append(" and ROUND(IFNULL(bsa.budget_usage / (acr.cost/acr.clicks) , 0), 4) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getEstimateClicksMax());
            }
            //预算剩余
            if (budgetAnalysisQueryRequest.getBudgetSurplusMin() != null) {
                whereSql.append(" and ifnull(bsa.budget_usage_percentage,0) <= ? ");
                argsList.add(new BigDecimal(100).subtract(budgetAnalysisQueryRequest.getBudgetSurplusMin()));
            }
            if (budgetAnalysisQueryRequest.getBudgetSurplusMax() != null) {
                whereSql.append(" and ifnull(bsa.budget_usage_percentage,0) >= ? ");
                argsList.add(new BigDecimal(100).subtract(budgetAnalysisQueryRequest.getBudgetSurplusMax()));
            }
            //剩余预算
            if (budgetAnalysisQueryRequest.getResidualBudgetMin() != null) {
                whereSql.append(" and ifnull(bsa.budget_remaining,0) >= ? ");
                argsList.add(budgetAnalysisQueryRequest.getResidualBudgetMin());
            }
            if (budgetAnalysisQueryRequest.getResidualBudgetMax() != null) {
                whereSql.append(" and ifnull(bsa.budget_remaining,0) <= ? ");
                argsList.add(budgetAnalysisQueryRequest.getResidualBudgetMax());
            }
        }
        return whereSql;
    }

    /**
     * 根据排序确定连表的数量
     */
    private void getLeftSqlTable(Set<String> key, String field) {
        if (StringUtils.isBlank(field)) {
            key.add("acr");
            key.add("dcr");
            return;
        }
        switch (field) {
            case "dailyBudget":
                key.add("ac");
                key.add("dcr");
                return;
            case "name":
                key.add("ac");
                return;
            case "budgetTime":
            case "budgetAdjustmentNum":
                return;
            case "impressions":
            case "clicks":
            case "adOrderNum":
            case "ctr":
            case "cvr":
            case "orderNum":
                key.add("acr");
                return;
            case "spent":
            case "budgetRemaining":
            case "budget":
                key.add("dcr");
                return;
            default:
                key.add("dcr");
                key.add("acr");
        }
    }


    private String getSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " , IFNULL(acr.cost * dcr.rate, 0) adCostDoris ";
        }
        // 广告活动 ac
        // 主表 bsa
        // 报告表 acr
        // 汇率表 dcr
        switch (field) {
            case "name":
                return " , ac.name nameDoris ";
            case "date":
                return "  ";
            case "budget":
                return " , IFNULL(bsa.budget * dcr.rate,0) budgetDoris  ";
            case "dailyBudget":
                return " , IFNULL(ac.budget * dcr.rate,0) dailyBudgetDoris  ";
            case "budgetTime":
                return " , IFNULL(bsa.budget_time, 0) budgetTimeDoris  ";
            case "budgetRemaining":
                return " , IFNULL(bsa.budget_remaining * dcr.rate, 0) budgetRemainingDoris  ";
            case "budgetAdjustmentNum":
                return " , IFNULL(bsa.budget_adjustment_num, 0) budgetAdjustmentNumDoris  ";
            case "impressions":
                return " , IFNULL(acr.impressions,0) impressionsDoris  ";
            case "clicks":
                return " , IFNULL(acr.clicks,0) clicksDoris  ";
            case "adOrderNum":
                return " , IFNULL(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0) adOrderNumDoris ";
            case "ctr":
                return " , IFNULL(acr.clicks/acr.impressions, 0)  ctrDoris ";
            case "cvr":
                return " , IFNULL(if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d)/acr.clicks, 0) cvrDoris ";
            case "orderNum":
                return " , IFNULL(if (acr.type = 'sp' ,acr.`units_ordered7d`,if (acr.type = 'sb' ,acr.`units_sold14d`,acr.`units_ordered14d`)),0) orderNumDoris ";
//            case "adCost":
//                return " , IFNULL(acr.cost * dcr.rate, 0) adCostDoris ";
            case "adSale":
                return " , IFNULL(if (acr.type = 'sp', acr.sales7d * dcr.rate , acr.sales14d * dcr.rate),0) adSaleDoris ";
            case "acos":
                return " , ifnull(acr.cost /if (acr.type = 'sp', acr.sales7d, acr.sales14d),0) acosDoris ";
            case "roas":
                return " , ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d) /acr.cost,0) roasDoris ";
            case "adCostPerClick":
                return " , ifnull(acr.cost * dcr.rate/acr.clicks, 0)  adCostPerClickDoris ";
            case "spent":
                return " , IFNULL(bsa.budget_usage * dcr.rate, 0) spentDoris ";
            case "estimateClicks":
                return " , IFNULL(bsa.budget_usage / (acr.cost /acr.clicks) , 0) estimateClicksDoris ";
            case "cpa":
                return " , IFNULL(acr.cost * dcr.rate /if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d),0)  cpaDoris ";
            case "advertisingUnitPrice":
                return " , ifnull(if (acr.type = 'sp', acr.sales7d, acr.sales14d) * dcr.rate / if (acr.type = 'sp' , acr.conversions7d, acr.conversions14d), 0)  advertisingUnitPriceDoris ";
            default:
                return " , IFNULL(acr.cost * dcr.rate, 0) adCostDoris ";
        }
    }

    private String getOrderField(String field, String orderType) {
        if (StringUtils.isBlank(field)) {
            return " usage_updated_site_date desc , adCostDoris desc, budget_scope_id desc ";
        }
        switch (field) {
            case "adCost":
            case "name":
            case "budget":
            case "budgetTime":
            case "dailyBudget":
            case "budgetRemaining":
            case "budgetAdjustmentNum":
            case "impressions":
            case "clicks":
            case "adOrderNum":
            case "ctr":
            case "cvr":
            case "orderNum":
            case "adSale":
            case "acos":
            case "roas":
            case "adCostPerClick":
            case "spent":
            case "estimateClicks":
            case "cpa":
            case "advertisingUnitPrice":
                return field + "Doris " + getOrderTypeSql(orderType) + ", usage_updated_site_date , budget_scope_id desc ";
            case "date":
                return " usage_updated_site_date " + getOrderTypeSql(orderType) + ", budget_scope_id desc ";
            default:
                return " usage_updated_site_date desc ,adCostDoris desc, budget_scope_id desc ";
        }
    }

    /**
     * 获取排序的sql
     */
    private String getOrderTypeSql(String orderType) {
        if (StringUtils.isBlank(orderType) || OrderTypeEnum.desc.name().equalsIgnoreCase(orderType)) {
            return " desc ";
        }
        return "";
    }

}
