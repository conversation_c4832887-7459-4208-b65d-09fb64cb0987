package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredBrand;

import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.sb.mode.report.SbReportAds;
import com.amazon.advertising.sb.mode.report.SbReportKeyword;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportType;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbAdsReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbShopReport;
import com.meiyunji.sponsored.service.syncTask.entity.sb.SbReportV3Ads;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sb 关键词报告处理类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SbAdsReportV3Strategy extends AbstractReportProcessStrategy {


    private final RedisService redisService;
    private final Producer<ReportReadyNotification> reportReadyProducer;
    private final IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao;
    private final IAmazonSbAdsDao amazonSbAdsDao;
    private final IAmazonAdSbShopReportDao amazonAdSbShopReportDao;

    public SbAdsReportV3Strategy(
            CosBucketClient dataBucketClient,
            IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao,
            IAmazonSbAdsDao amazonSbAdsDao,
            RedisService redisService, Producer<ReportReadyNotification> reportReadyProducer,IAmazonAdSbShopReportDao amazonAdSbShopReportDao) {
        super(dataBucketClient);
        this.amazonAdSbAdsReportDao = amazonAdSbAdsReportDao;
        this.redisService = redisService;
        this.reportReadyProducer = reportReadyProducer;
        this.amazonSbAdsDao = amazonSbAdsDao;
        this.amazonAdSbShopReportDao = amazonAdSbShopReportDao;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && AmazonReportV3Type.sb_ads == notification.getV3Type();
    }


    @Override
    public void processReport(ReportReadyNotification notification) throws IOException {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(
                dataBucketClient.getObjectToBytes(notification.getPath()))))) {
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<SbReportV3Ads> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SbReportV3Ads report = new SbReportV3Ads();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= 500) {
                    dealReport(notification, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(notification, reports);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getV3Type(), notification.getV3StartDate(), e);
            throw e;
        }
    }


    private void dealReport(ReportReadyNotification notification, List<SbReportV3Ads> reports) {
        List<SbReportV3Ads> validReports = reports.stream()
                .filter(item -> BigDecimal.valueOf(item.getImpressions())
                        .compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }
        List<AmazonAdSbAdsReport> poList = getPoBySbReportAds(notification, validReports);
        List<List<AmazonAdSbAdsReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSbAdsReport> amazonAdSbAdsReports : partition) {
            amazonAdSbAdsReportDao.insertOrUpdateList(notification.getSellerIdentifier(), amazonAdSbAdsReports);
        }
    }


    private List<AmazonAdSbAdsReport> getPoBySbReportAds(ReportReadyNotification notification, List<SbReportV3Ads> reports) {
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum
                .getByMarketplaceId(notification.getMarketplace().getId()).getCurrencyCode();
        List<AmazonAdSbAdsReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSbAdsReport sbAdsReport;
        boolean needRetry = false;
        for (SbReportV3Ads report : reports) {
            //查询sb_format
            String adFormat = amazonSbAdsDao.getSbFormatByGroupId(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier(), report.getAdGroupId().toString());
            if (StringUtils.isBlank(adFormat)) {
                log.info("SB广告ADS管理数据未成功匹配成功,30分钟后再解析报告. puid:{} shopId : {} adGroupId: {}",
                        notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), report.getAdGroupId());
                needRetry = true;
                continue;
            }
            sbAdsReport = new AmazonAdSbAdsReport();
            sbAdsReport.setPuid(notification.getSellerIdentifier());
            sbAdsReport.setShopId(notification.getMarketplaceIdentifier());
            sbAdsReport.setMarketplaceId(notification.getMarketplace().getId());
            sbAdsReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sbAdsReport.setCurrency(currencyCode);
            sbAdsReport.setAdFormat(adFormat);
            sbAdsReport.setCampaignName(report.getCampaignName());
            sbAdsReport.setCampaignId(report.getCampaignId() != null ? report.getCampaignId().toString() : "");
            sbAdsReport.setCampaignStatus(report.getCampaignStatus());
            sbAdsReport.setCampaignBudget(report.getCampaignBudgetAmount() != null ? report.getCampaignBudgetAmount().doubleValue(): null);
            sbAdsReport.setCampaignBudgetType(report.getCampaignBudgetType());
//            sbAdsReport.setCampaignRuleBasedBudget(report.getCampaignRuleBasedBudget());
//            sbAdsReport.setApplicableBudgetRuleId(report.getApplicableBudgetRuleId());
//            sbAdsReport.setApplicableBudgetRuleName(report.getApplicableBudgetRuleName());
            sbAdsReport.setAdGroupName(report.getAdGroupName());
            sbAdsReport.setAdGroupId(report.getAdGroupId().toString());
            sbAdsReport.setAdId(report.getAdId() == null ? "" :report.getAdId().toString());
            sbAdsReport.setImpressions(report.getImpressions());
            sbAdsReport.setClicks(report.getClicks());
            sbAdsReport.setCost(report.getCost() != null ? report.getCost() : null);
            sbAdsReport.setSales14d(report.getSalesClicks() != null ? report.getSalesClicks() : null);
            sbAdsReport.setSales14dSameSKU(report.getSalesPromoted() != null ? report.getSalesPromoted() : null);
            sbAdsReport.setConversions14d(report.getPurchasesClicks());
            sbAdsReport.setConversions14dSameSKU(report.getPurchasesPromoted());
            sbAdsReport.setDetailPageViewsClicks14d(report.getDetailPageViewsClicks());
            sbAdsReport.setOrdersNewToBrand14d(report.getNewToBrandPurchasesClicks());
            sbAdsReport.setOrdersNewToBrandPercentage14d(report.getNewToBrandPurchasesPercentage() != null ? report.getNewToBrandPurchasesPercentage().doubleValue() : null);
            sbAdsReport.setOrderRateNewToBrand14d(report.getNewToBrandPurchasesRate() != null ? report.getNewToBrandPurchasesRate().doubleValue() : null);
            sbAdsReport.setSalesNewToBrand14d(report.getNewToBrandSalesClicks() != null ? report.getNewToBrandSalesClicks() : null);
            sbAdsReport.setSalesNewToBrandPercentage14d(report.getNewToBrandSalesPercentage() != null ? report.getNewToBrandSalesPercentage().doubleValue() : null);
            sbAdsReport.setUnitsOrderedNewToBrand14d(report.getNewToBrandUnitsSoldClicks());
            sbAdsReport.setUnitsOrderedNewToBrandPercentage14d(report.getNewToBrandUnitsSoldPercentage() != null ? report.getNewToBrandUnitsSoldPercentage().doubleValue() : null);
            sbAdsReport.setUnitsSold14d(report.getUnitsSold());
//            sbAdsReport.setDpv14d(report.getDpv14d());
//            sbAdsReport.setVctr(report.getViewClickThroughRate());
            sbAdsReport.setVideo5SecondViewRate(report.getVideo5SecondViewRate() != null ? report.getVideo5SecondViewRate().doubleValue() : null);
            sbAdsReport.setVideo5SecondViews(report.getVideo5SecondViews());
            sbAdsReport.setVideoFirstQuartileViews(report.getVideoFirstQuartileViews());
            sbAdsReport.setVideoMidpointViews(report.getVideoMidpointViews());
            sbAdsReport.setVideoThirdQuartileViews(report.getVideoThirdQuartileViews());
            sbAdsReport.setVideoUnmutes(report.getVideoUnmutes());
            sbAdsReport.setViewableImpressions(report.getViewableImpressions());
            sbAdsReport.setVideoCompleteViews(report.getVideoCompleteViews());
            sbAdsReport.setVtr(report.getViewabilityRate() != null ? report.getViewabilityRate().doubleValue() : null);
            sbAdsReport.setBrandedSearches14d(report.getBrandedSearchesClicks());
            if(report.getAdId() == null){
                sbAdsReport.setQueryId(report.getAdGroupId().toString());
            } else {
                sbAdsReport.setQueryId(report.getAdId().toString());
            }
            list.add(sbAdsReport);
        }
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_DAILY_REPORT,notification.getSellerIdentifier(),
                notification.getMarketplaceIdentifier(), notification.getV3Type().name(),
                notification.getV3StartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (needRetry) {
            Long value = redisService.incr(cacheKey, 1L);
            if (value <= 1) {
                redisService.expire(cacheKey, 3600);
                try {
                    reportReadyProducer.newMessage().value(notification)
                            .deliverAfter(30, TimeUnit.MINUTES).send();
                } catch (PulsarClientException e) {
                    log.error("Pulsar send message with an error.", e);
                }
            }
        }
        return list;
    }

    private void sumSbShopReport(ReportReadyNotification notification) {
        List<String> creativeTypes = Arrays.asList("video", "productCollection");
        for (String creativeType : creativeTypes) {
            AmazonAdSbAdsReport sumReport = amazonAdSbAdsReportDao
                    .getSbSumReport(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(),
                            notification.getMarketplace().getId(),
                            notification.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), creativeType);
            if (sumReport != null) {
                AmazonAdSbShopReport shopReport = new AmazonAdSbShopReport();
                shopReport.setPuid(notification.getSellerIdentifier());
                shopReport.setShopId(notification.getMarketplaceIdentifier());
                shopReport.setMarketplaceId(notification.getMarketplace().getId());
                shopReport.setCountDate(notification.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                shopReport.setAdFormat(creativeType);
                shopReport.setCost(sumReport.getCost());
                shopReport.setTotalSales(sumReport.getSales14d());
                shopReport.setImpressions(sumReport.getImpressions());
                shopReport.setClicks(sumReport.getClicks());
                shopReport.setOrderNum(sumReport.getConversions14d());
                amazonAdSbShopReportDao.insertOrUpdate(shopReport);
            }
        }
    }
}
