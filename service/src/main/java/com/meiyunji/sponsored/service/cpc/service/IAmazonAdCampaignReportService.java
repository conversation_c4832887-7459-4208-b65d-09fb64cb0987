package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignReport;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName IAmazonAdCampaignReportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/18 11:35
 **/
public interface IAmazonAdCampaignReportService {
    /**
     * 获取列表页数据
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page pageList(int puid, SearchVo search, Page page);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param start
     * @param end
     * @param lastMonth
     * @param campaignId
     * @return
     */
    SumReportVo getSumReport(int puid, Integer shopId, String marketplaceId, Date start, Date end, Integer lastMonth, String campaignId);

    /**
     * 获取图表信息
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param start
     * @param end
     * @param campaignId
     * @return
     */
    List<ReportVo> getChartList(int puid, Integer shopId, String marketplaceId, Date start, Date end, String campaignId);

    /**
     * 活动报告详情页
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @param page
     * @return
     */
    Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    /**
     * 获取详情信息
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param campaignId
     * @return
     */
    ReportDetailVo getDetailInfo(int puid, Integer shopId, String marketplaceId, String campaignId);

    /**
     *
     * @param puid
     * @param campaignId
     * @param searchVo
     * @return
     */
    CampaignReportVo getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo);


    /**
     * 获取活动弹层数据
     */
    void getDetailsSumVo(CampaignReportDetails detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale);

    /**
     * 获取活动弹层每天数据
     * @param adReportDetailsVo
     * @param detailsVo
     */
    void getCampaignDetailsDay(AdReportDetailsVo adReportDetailsVo, CampaignReportDetails detailsVo);


}
