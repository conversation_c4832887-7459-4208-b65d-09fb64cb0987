package com.meiyunji.sponsored.service.taskGrpcApi;


import com.meiyunji.sponsored.common.enums.EnvironmentType;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class TaskApiConfig {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${services.schedule-task.prefix}")
    private String scheduleTaskUrl;

    @Value("${services.schedule-task.port}")
    private Integer scheduleTaskPort;

    @Value("${services.schedule-task.encrypted}")
    private Boolean encrypted;

    @Value(value = "${spring.profiles.active}")
    private String active;

    @Bean("taskManagedChannel")
    public ManagedChannel taskManagedChannel() {
        logger.info("===================start task grpc connection===================");
        ManagedChannelBuilder<?> managedChannelBuilder =
                ManagedChannelBuilder.forAddress(scheduleTaskUrl, scheduleTaskPort);
        //未使用https证书
        if (encrypted == null || !encrypted) {
            managedChannelBuilder.usePlaintext();
        }
        if (EnvironmentType.dev.name().equalsIgnoreCase(active) || EnvironmentType.test.name().equalsIgnoreCase(active)) {
            managedChannelBuilder.keepAliveTime(1, TimeUnit.MINUTES);
            managedChannelBuilder.keepAliveWithoutCalls(true);
            managedChannelBuilder.keepAliveTimeout(5, TimeUnit.MINUTES);
        }
        return managedChannelBuilder.build();
    }
}
