package com.meiyunji.sponsored.service.newDashboard.service.globalView;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.doris.dao.globalView.IGlobalViewDao;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.GlobalViewAdTypeDto;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.base.GlobalViewBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewProcessorEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.globalView.GlobalViewBaseReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 全局概览-店铺、广告类型列表
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class GlobalViewAdTypeProcessor extends AbstractGlobalViewProcessor<GlobalViewBaseReqVo, GlobalViewAdTypeDto> {

    @Resource
    private IGlobalViewDao globalViewDao;


    @Override
    public Page<GlobalViewAdTypeDto> getPageData(GlobalViewBaseReqVo req) {
        // 分页获取原始指标数据
        Page<GlobalViewBaseDataDto> page = globalViewDao.getPageData(req, GlobalViewProcessorEnum.AD_TYPE);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>(req.getPageNo(), req.getPageSize());
        }
        // key值 shopId_type
        List<Integer> shopIdList = StreamUtil.toList(page.getRows(), it -> Integer.parseInt(it.getKey().split("_")[0]));
        req.setShopIdList(shopIdList);
        // 获取店铺原始指标数据
        List<GlobalViewBaseDataDto> shopSaleList = globalViewDao.getShopSaleList(req, GlobalViewProcessorEnum.AD_TYPE, req.getStartDate(), req.getEndDate());
        Map<String, GlobalViewBaseDataDto> shopSaleMap = StreamUtil.toMap(shopSaleList, GlobalViewBaseDataDto::getKey);
        // 构建列表返回参数
        List<GlobalViewAdTypeDto> adTypeList = buildAdType(page, shopSaleMap);
        return new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), adTypeList);
    }

    @Override
    public GlobalViewBaseDataDto getSumData(GlobalViewBaseReqVo req, List<GlobalViewAdTypeDto> dataList) {
        GlobalViewBaseDataDto sumData = globalViewDao.getSumData(req, GlobalViewProcessorEnum.AD_TYPE);
        GlobalViewBaseDataDto shopSumData = globalViewDao.getShopSumData(req);
        sumData.setShopSaleNum(shopSumData.getShopSaleNum());
        sumData.setShopSales(shopSumData.getShopSales());
        return sumData;
    }

    @Override
    public List<GlobalViewAdTypeDto> getMomData(GlobalViewBaseReqVo req, List<GlobalViewAdTypeDto> dataList) {
        return getMomYoyData(req, req.getMomStartDate(), req.getMomEndDate(), dataList);
    }

    @Override
    public List<GlobalViewAdTypeDto> getYoyData(GlobalViewBaseReqVo req, List<GlobalViewAdTypeDto> dataList) {
        return getMomYoyData(req, req.getYoyStartDate(), req.getYoyEndDate(), dataList);
    }

    @Override
    public List<String> getExportHeader() {
        return Collections.emptyList();
    }

    @Override
    public List<GlobalViewAdTypeDto> getExportData(List<GlobalViewAdTypeDto> dataList) {
        return Collections.emptyList();
    }

    /**
     * 构建列表返回参数
     */
    private static List<GlobalViewAdTypeDto> buildAdType(Page<GlobalViewBaseDataDto> page, Map<String, GlobalViewBaseDataDto> shopSaleMap) {
        List<GlobalViewAdTypeDto> adTypeList = new ArrayList<>();
        for (GlobalViewBaseDataDto row : page.getRows()) {
            GlobalViewAdTypeDto adType = new GlobalViewAdTypeDto();
            BeanUtils.copyProperties(row, adType);
            // key格式 shopId_type  取shop_id
            String shopId = row.getKey().split("_")[0];
            String type = row.getKey().split("_")[1];
            GlobalViewBaseDataDto shopSale = shopSaleMap.get(shopId);
            if (shopSale != null) {
                adType.setShopSales(shopSale.getShopSales());
                adType.setShopSaleNum(shopSale.getShopSaleNum());
            }
            // 获取店铺、广告类型
            adType.setCampaignType(type.toUpperCase());
            adType.setShopId(shopId);
            adTypeList.add(adType);
        }
        return adTypeList;
    }

    /**
     * 根据时间获取同环比原始指标数据
     */
    private List<GlobalViewAdTypeDto> getMomYoyData(GlobalViewBaseReqVo req, String startDate, String endDate, List<GlobalViewAdTypeDto> dataList) {
        List<GlobalViewAdTypeDto> list = new ArrayList<>();
        List<GlobalViewBaseDataDto> listData = globalViewDao.getListData(req, GlobalViewProcessorEnum.AD_TYPE, startDate, endDate);
        List<GlobalViewBaseDataDto> shopSaleList = globalViewDao.getShopSaleList(req, GlobalViewProcessorEnum.AD_TYPE, startDate, endDate);
        Map<String, GlobalViewBaseDataDto> dataMap = StreamUtil.toMap(listData, GlobalViewBaseDataDto::getKey);
        Map<String, GlobalViewBaseDataDto> shopMap = StreamUtil.toMap(shopSaleList, GlobalViewBaseDataDto::getKey);
        for (GlobalViewAdTypeDto dto : dataList) {
            GlobalViewAdTypeDto typeDto = new GlobalViewAdTypeDto();
            GlobalViewBaseDataDto data = dataMap.get(dto.getKey());
            GlobalViewBaseDataDto shop = shopMap.get(dto.getKey().split("_")[0]);
            if(data != null) {
                BeanUtils.copyProperties(data, typeDto);
            }
            if(shop != null) {
                typeDto.setShopSales(shop.getShopSales());
                typeDto.setShopSaleNum(shop.getShopSaleNum());
            }
            typeDto.setKey(dto.getKey());
            list.add(typeDto);
        }
        return list;
    }
}
