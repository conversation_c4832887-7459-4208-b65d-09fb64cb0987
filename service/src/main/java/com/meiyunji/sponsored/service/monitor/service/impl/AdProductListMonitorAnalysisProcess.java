package com.meiyunji.sponsored.service.monitor.service.impl;

import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllProductAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllProductDataResponse;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdListMonitor;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.monitor.dao.IAmazonAdListMonitorDao;
import com.meiyunji.sponsored.service.monitor.enums.MonitorCompareStateEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.monitor.service.AbstractMonitorAnalysis;
import com.meiyunji.sponsored.service.monitor.vo.MonitorCompare;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/7/24 20:45
 */
@Service
public class AdProductListMonitorAnalysisProcess extends AbstractMonitorAnalysis {

    protected AdProductListMonitorAnalysisProcess(IAmazonAdListMonitorDao amazonAdListMonitorDao) {
        super(amazonAdListMonitorDao);
    }

    @Override
    protected AmazonAdListMonitor analysisData(Object[] args, Object result, MonitorPageFunctionEnum monitorPageFunctionEnum, MonitorTypeEnum monitorTypeEnum) {
        AmazonAdListMonitor amazonAdListMonitor;
        if (MonitorTypeEnum.LIST == monitorTypeEnum) {
            AdProductPageParam param = (AdProductPageParam) args[1];
            if (Boolean.TRUE.equals(param.getOnlyCount())) {
                amazonAdListMonitor = new AmazonAdListMonitor();
                amazonAdListMonitor.setPageSign(null);
                return amazonAdListMonitor;
            }
            amazonAdListMonitor = handleList(param, monitorPageFunctionEnum, (AllProductDataResponse.AdProductHomeVo) result);
        } else {
            AdProductPageParam param = (AdProductPageParam) args[1];
            amazonAdListMonitor = handleSum(param, monitorPageFunctionEnum, (AllProductAggregateDataResponse.AdProductHomeVo) result);
        }
        return amazonAdListMonitor;
    }

    private AmazonAdListMonitor handleList(AdProductPageParam param, MonitorPageFunctionEnum monitorPageFunctionEnum, AllProductDataResponse.AdProductHomeVo vo) {
        List<AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo> rowsList = vo.getPage().getRowsList();
        MonitorCompare monitorCompare = initCompare();
        if (CollectionUtils.isNotEmpty(rowsList)) {
            rowsList.forEach(e -> monitorCompare.sum(handleListCompareData(e)));
        }
        AmazonAdListMonitor amazonAdListMonitor = new AmazonAdListMonitor();
        amazonAdListMonitor.setPuid(param.getPuid());
        amazonAdListMonitor.setShopId(param.getShopId());
        amazonAdListMonitor.setMonitorDataTypeEnum(1);
        amazonAdListMonitor.setCompareState(MonitorCompareStateEnum.WAITING.getType());
        amazonAdListMonitor.setListRequestParam(JSONUtil.objectToJson(param));
        amazonAdListMonitor.setListCompareData(JSONUtil.objectToJson(monitorCompare));
        amazonAdListMonitor.setPageSign(param.getPageSign());
        amazonAdListMonitor.setPageFunction(monitorPageFunctionEnum.getType());
        amazonAdListMonitor.setTotalPage(vo.getPage().getTotalPage().getValue());
        return amazonAdListMonitor;

    }

    private MonitorCompare handleListCompareData(AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo vo) {
        MonitorCompare build = MonitorCompare.builder()
                .cost(getStringBigDecimal(vo.getAdCost()))
                .adSales(getStringBigDecimal(vo.getAdSales()))
                .clicks(vo.hasClicks() ? vo.getClicks().getValue() : 0L)
                .impressions(vo.hasImpressions() ? vo.getImpressions().getValue() : 0L)
                .adSale(getStringBigDecimal(vo.getAdSale()))
                .adOrderNum(vo.hasAdOrderNum() ? vo.getAdOrderNum().getValue() : 0)
                .orderNum(vo.hasOrderNum() ? vo.getOrderNum().getValue() : 0)
                .saleNum(vo.hasAdSaleNum() ? vo.getAdSaleNum().getValue() : 0)
                .build();
        return build;
    }


    private AmazonAdListMonitor handleSum(AdProductPageParam param, MonitorPageFunctionEnum monitorPageFunctionEnum, AllProductAggregateDataResponse.AdProductHomeVo vo) {
        AdHomeAggregateDataRpcVo aggregateDataVo = vo.getAggregateDataVo();
        MonitorCompare monitorCompare = MonitorCompare.builder()
                .orderNum(aggregateDataVo.hasOrderNum() ? aggregateDataVo.getOrderNum().getValue() : 0)
                .adOrderNum(aggregateDataVo.hasAdOrderNum() ? aggregateDataVo.getAdOrderNum().getValue() : 0)
                .adSales(aggregateDataVo.hasAdSales() ? getStringBigDecimal(aggregateDataVo.getAdSales()) : BigDecimal.ZERO)
                .adSale(aggregateDataVo.hasAdSale() ? getStringBigDecimal(aggregateDataVo.getAdSale()) : BigDecimal.ZERO)
                .impressions(aggregateDataVo.hasImpressions() ? aggregateDataVo.getImpressions().getValue() : 0L)
                .clicks(aggregateDataVo.hasClicks() ? aggregateDataVo.getClicks().getValue() : 0L)
                .cost(aggregateDataVo.hasAdCost() ? getStringBigDecimal(aggregateDataVo.getAdCost()) : BigDecimal.ZERO)
                .saleNum(aggregateDataVo.hasAdSaleNum() ? aggregateDataVo.getAdSaleNum().getValue() : 0).build();

        AmazonAdListMonitor amazonAdListMonitor = new AmazonAdListMonitor();
        amazonAdListMonitor.setPuid(param.getPuid());
        amazonAdListMonitor.setShopId(param.getShopId());
        amazonAdListMonitor.setMonitorDataTypeEnum(1);
        amazonAdListMonitor.setCompareState(MonitorCompareStateEnum.WAITING.getType());
        amazonAdListMonitor.setSumRequestParam(JSONUtil.objectToJson(param));
        amazonAdListMonitor.setSumCompareData(JSONUtil.objectToJson(monitorCompare));
        amazonAdListMonitor.setPageSign(param.getPageSign());
        amazonAdListMonitor.setPageFunction(monitorPageFunctionEnum.getType());
        return amazonAdListMonitor;

    }


}
