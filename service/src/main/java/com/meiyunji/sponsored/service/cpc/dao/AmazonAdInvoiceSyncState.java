package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 广告发票同步状态
 *
 * <AUTHOR>
 * @date 2023/07/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@DbTable(value = "t_amazon_ad_invoice_sync_state")
public class AmazonAdInvoiceSyncState extends BasePo {

    /**
     * id
     */
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;
    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;


    /**
     * 店铺id
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;


    /**
     * 店铺id
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;


    /**
     * 翻页标签
     */
    @DbColumn(value = "next_token")
    private String nextToken;

    /**
     * 下次同步时间
     */
    @DbColumn(value = "next_sync_time")
    private LocalDateTime nextSyncTime;


    /**
     * 是否初始化完成
     */
    @DbColumn(value = "is_initialize")
    private Boolean isInitialize;

    /**
     * 初始化时间
     */
    @DbColumn(value = "init_date")
    private LocalDate initDate;


}