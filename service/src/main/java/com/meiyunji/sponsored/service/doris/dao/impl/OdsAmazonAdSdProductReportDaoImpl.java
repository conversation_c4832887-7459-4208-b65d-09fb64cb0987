package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dto.AdProductReportSearchTermsViewDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryFieldEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSdProductReport;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * sd产品广告报告(OdsAmazonAdSdProductReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Repository
public class OdsAmazonAdSdProductReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSdProductReport> implements IOdsAmazonAdSdProductReportDao {

    @Override
    public String adProductByAsinOrSkuQuerySql(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, List<String> ads, String currency, String startDate, String endDate, List<Object> argsList, boolean isAsin) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  ");
        if (isAsin) {
            sb.append(" report.asin label , ");
        } else {
            sb.append(" report.sku label, ");
        }
        sb.append(" any(report.asin) asin, ");
        sb.append(" group_concat(distinct CAST(shop_id AS CHAR)) shop_ids, ");
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(conversions14d), 0) orderNum, ");
        sb.append(" ifnull(sum(units_ordered14d), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 2), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 2), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 2) clickRate, ");//点击率
        sb.append(" ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 2) conversionRate, ");//转化率
        sb.append(" ROUND(ifnull(sum(cost * c.rate)/ sum(impressions), 0), 2) cpc, ");//cpc
        sb.append(" ROUND(ifnull(sum(cost * c.rate)/ sum(conversions14d), 0), 2) cpa ");//cpa
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        sb.append(" join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on report.puid = c.puid and DATE_FORMAT(report.count_date, '%Y%m') = c.month ");
        sb.append(" join dim_marketplace_info m on m.marketplace_id = report.marketplace_id and c.`from` = m.currency ");
        sb.append(" where report.puid = ? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(ads)) {
            if (isAsin) {
                sb.append(" and  report.asin  ");
            } else {
                sb.append(" and report.sku  ");
            }
            sb.append(" in ('").append(StringUtils.join(ads, "','")).append("') ");
        }
        sb.append(" and report.count_day >= ? and report.count_day <= ? ");
        argsList.add(startDate);
        argsList.add(endDate);
        if (isAsin) {
            sb.append(" group by  report.asin  ");
        } else {
            sb.append(" group by report.sku  ");
        }
        return sb.toString();
    }


    @Override
    public String adProductGroupByParentAsinQuerySql(Integer puid, List<Integer> shopIdList,
                                                     List<String> marketplaceIdList, List<String> adIds,
                                                     String currency, String startDate, String endDate,
                                                     List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT " );
        sb.append("GROUP_CONCAT(DISTINCT CAST(shop_ids AS CHAR)) shop_ids, " );
        sb.append("ifnull(sum(t.cost * c.rate), 0) cost,  " );
        sb.append("ifnull(sum(t.total_sales * c.rate), 0) totalSales, " );
        sb.append("ifnull(sum(t.impressions), 0) impressions, ");
        sb.append("ifnull(sum(t.clicks), 0) clicks," );
        sb.append("ifnull(sum(t.order_num), 0) orderNum, " );
        sb.append("ifnull(sum(t.sale_num), 0) saleNum, " );
        sb.append("ifnull(ROUND(ifnull(sum(t.cost * c.rate), 0)/ ifnull(sum(total_sales * c.rate), 0), 4), 0) acos, " );
        sb.append("ifnull(ROUND(ifnull(sum(t.total_sales * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) roas, " );
        sb.append("ROUND(ifnull(sum(t.clicks)/ sum(t.impressions), 0), 4) clickRate, " );
        sb.append("ROUND(ifnull(sum(t.order_num)/ sum(t.clicks), 0), 4) conversionRate, ");
        sb.append("ROUND(ifnull(sum(t.cost * c.rate)/ sum(t.clicks), 0), 4) cpc," );
        sb.append("ROUND(ifnull(sum(t.cost * c.rate)/ sum(t.order_num), 0), 4) cpa, " );
        sb.append("GROUP_CONCAT(DISTINCT t.asin) ASIN, " );
        sb.append("t.parent_asin label " );
        sb.append("FROM  (  SELECT " );
        sb.append(" ANY(t1.puid) puid, " );
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t1.shop_id AS CHAR)) shop_ids, " );
        sb.append(" t1.marketplace_id AS marketplace_id, " );
        sb.append(" ifnull(sum(t1.cost), 0) cost, ifnull(sum(sales14d), 0) total_sales, ");
        sb.append(" ifnull(sum(t1.impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t1.conversions14d), 0) order_num, ");
        sb.append(" ifnull(sum(t1.units_ordered14d), 0) sale_num, ");
        sb.append(" GROUP_CONCAT(DISTINCT t2.asin) ASIN, " );
        sb.append(" IF(t2.parent_asin IS NULL " );
        sb.append(" OR t2.parent_asin = '', " );
        sb.append(" t2.asin, " );
        sb.append(" t2.parent_asin) AS parent_asin, " );
        sb.append(" DATE_FORMAT(t1.count_date, '%Y%m') AS MONTH " );
        sb.append(" FROM  ").append(getJdbcHelper().getTable()).append(" t1 " );
        sb.append("JOIN ods_t_product t2 " );
        sb.append(" ON  t1.puid = t2.puid  AND t1.shop_id = t2.shop_id  AND t1.asin = t2.asin  AND t1.sku = t2.sku ");
        sb.append(" AND t2.puid = ? " );
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t2.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t2.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append("and t2.asin in ('").append(StringUtils.join(adIds, "','")).append("') ");
        }

        sb.append(" WHERE ");
        sb.append(" t1.puid = ? ");
        argsList.add(puid);
        sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
        argsList.add(startDate);
        argsList.add(endDate);
        sb.append("GROUP BY  parent_asin,marketplace_id,MONTH) t " );
        sb.append("JOIN (SELECT puid,`month`,`from`,rate FROM dim_currency_rate WHERE " );
        sb.append(" puid = ? ");
        argsList.add(puid);
        sb.append("AND `to` = ? ) c ");
        argsList.add(currency);
        sb.append("ON t.puid = c.puid AND t.month = c.month ");
        sb.append("JOIN dim_marketplace_info m ON m.marketplace_id = t.marketplace_id AND c.`from` = m.currency " );
        sb.append("GROUP BY label ");
        return sb.toString();
    }


    @Override
    public String adProductQuerySql(Integer puid, List<Integer> shopIdList,
                                    List<String> marketplaceIdList, List<String> adIds,
                                    String currency, String startDate, String endDate,
                                    List<Object> argsList, DashboardQueryFieldEnum dashboardQueryFieldEnum){
        if (DashboardQueryFieldEnum.ASIN_QUERY_TYPE == dashboardQueryFieldEnum) {
            return adProductByAsinOrSkuQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, true);
        } else if (DashboardQueryFieldEnum.SKU_QUERY_TYPE == dashboardQueryFieldEnum) {
            return adProductByAsinOrSkuQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, false);
        } else if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE == dashboardQueryFieldEnum) {
            return adProductGroupByParentAsinQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList);
        } else {
            return null;
        }

    }


    @Override
    public List<DashboardAdProductTopDataDto> queryAdProductYoyOrMomTop(String subSqlA, String subSqlB,
                                                                        List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                        DashboardOrderByRateEnum orderField, String orderBy,
                                                                        Integer limit) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.label as label, subSqlA.asin asin ,subSqlA.shop_ids as shopIds,ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.label = subSqlB.label ");
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdProductTopDataDto.class), argsList.toArray());
    }


    @Override
    public List<DashboardAdProductDto> queryAdProductCharts(Integer puid, List<Integer> shopIdList,
                                                            List<String> marketplaceIdList, List<String> ads,
                                                            String currency, String startDate, String endDate, DashboardQueryFieldEnum dashboardQueryFieldEnum) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = adProductQuerySql(puid, shopIdList, marketplaceIdList, ads, currency, startDate, endDate, argsList, dashboardQueryFieldEnum);
        return getJdbcTemplate().query(querySql, new BeanPropertyRowMapper<>(DashboardAdProductDto.class), argsList.toArray());
    }

    @Override
    public List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                                                                String searchType, String searchValue, String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select 'sd' as type, r.shop_id shopId, r.campaign_id campaignId, r.ad_group_id adGroupId, r.ad_id adId from ods_t_product p")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and r.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        if (StringUtils.isNotBlank(startDate)) {
            sb.append(" and r.count_day >= ? ");
            argsList.add(startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            sb.append(" and r.count_day <= ? ");
            argsList.add(endDate);
        }
        sb.append(" where p.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and p.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
//            sb.append(" and p.asin = ? ");
            sb.append(SqlStringUtil.dealInList("p.asin", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
//            sb.append(" and p.sku = ? ");
            sb.append(SqlStringUtil.dealInList("p.sku", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
//            sb.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?)) ");
//            argsList.add(searchValue);
            sb.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, argsList))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, argsList)).append(")")
                    .append(") ");
        }
//        argsList.add(searchValue);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdProductPerspectiveBO.class));
    }
}

