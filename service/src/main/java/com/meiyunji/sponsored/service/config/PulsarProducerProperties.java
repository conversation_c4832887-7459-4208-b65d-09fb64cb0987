package com.meiyunji.sponsored.service.config;

import lombok.Getter;
import lombok.Setter;
import org.apache.pulsar.client.api.CompressionType;
import org.apache.pulsar.client.api.HashingScheme;
import org.apache.pulsar.client.api.MessageRoutingMode;
import org.apache.pulsar.client.api.ProducerAccessMode;

@Setter
@Getter
public class PulsarProducerProperties {
    private static final String PERSISTENT_PREFIX = "persistent";
    private Boolean enabled = false;
    private String namespace;
    private String topic;
    private String messageClass;
    private String producerName;
    private ProducerAccessMode accessMode = ProducerAccessMode.Shared;
    private Integer sendTimeoutSec = 30;
    private Boolean enableChunking = false;
    private Boolean enableBatching = true;
    private Integer batchingMaxPublishDelaySec = 1;
    private Integer batchingMaxBytes = 131072;
    private Integer batchingMaxMessage = 1000;
    private MessageRoutingMode messageRoutingMode = MessageRoutingMode.RoundRobinPartition;
    private Integer roundRobinRouterBatchingPartitionSwitchFrequency = 10;
    private Integer maxPendingMessages = 1000;
    private Integer maxPendingMessagesAcrossPartitions = 50000;
    private HashingScheme hashingScheme = HashingScheme.JavaStringHash;
    private Boolean enableMultiSchema = true;
    private Boolean blockIfQueueFull = false;
    private CompressionType compressionType = CompressionType.NONE;
    private Boolean autoUpdatePartitions = true;
    private Integer autoUpdatePartitionsIntervalSec = 60;

    public String getTopicUrl(String tenant) {
        return PERSISTENT_PREFIX + "://" + tenant + "/" + namespace +
                "/" + topic;
    }
}