package com.meiyunji.sponsored.service.adTagSystem.enums;

public enum AdTagPermissionTypeEnum {

    ONLY_CREATOR(0, "仅创建人可见"),
    ASSIGN(1, "创建人和指定人可见"),
    ALL(2, "所有人可见"),
    ;

    private Integer code;
    private String desc;

    AdTagPermissionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
