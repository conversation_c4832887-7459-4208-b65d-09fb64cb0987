package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignReport;
import com.meiyunji.sponsored.service.cpc.vo.CampaignReportDetails;
import com.meiyunji.sponsored.service.cpc.vo.CampaignReportSearchVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;

import java.util.List;

/**
 * AmazonAdCampaignReport
 * <AUTHOR>
 */
public interface IAmazonAdCampaignReportDao extends IBaseShardingDao<AmazonAdCampaignAllReport> {

    /**
     * 汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param reportDate
     * @return
     */
    AmazonAdCampaignAllReport getSumReport(int puid, Integer shopId, String marketplaceId, String reportDate);

    /**
     * 汇总数据用于列表展示
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startDate
     * @param endDate
     * @return
     */
    List<AmazonAdCampaignAllReport> getSumByDate(int puid, Integer shopId, String marketplaceId, String startDate, String endDate);

    /**
     * 获取列表页数据
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page getPageList(int puid, SearchVo search, Page page);

    /**
     * 获取单个活动的汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param campaignId
     * @return
     */
    AmazonAdCampaignAllReport getSumReportByCampaignId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    /**
     * 获取图形化数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param startStr
     * @param endStr
     * @param campaignId
     * @return
     */
    List<AmazonAdCampaignAllReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String campaignId);

    /**
     * 活动报告详情页
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param param
     * @param page
     * @return
     */
    Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param, Page page);

    /**
     * 获取报告基础信息
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param campaignId
     * @return
     */
    AmazonAdCampaignAllReport getDetailInfo(int puid, Integer shopId, String marketplaceId, String campaignId);




    /**
     * 获取指定广告活动的汇总数据
     * @param puid
     * @param shopId
     * @param start
     * @param end
     * @param campaignId
     * @return
     */
    List<AmazonAdCampaignAllReport> listReports(int puid, Integer shopId, String start, String end, String campaignId);

    /**
     * 统计指定时间段的数据
     * @param puid
     * @param shopId
     * @param start
     * @param end
     * @return
     */
    AmazonAdCampaignAllReport statByDateRange(int puid, Integer shopId, String start, String end);


    AmazonAdCampaignAllReport getReportVoByCampaignId(Integer puid, String campaignId, CampaignReportSearchVo searchVo);

    AmazonAdCampaignAllReport getDetailsSumVo(Integer puid, CampaignReportDetails detailsVo);

    List<AmazonAdCampaignAllReport> getListCampaignDetailsDay(Integer puid, CampaignReportDetails detailsVo);
}