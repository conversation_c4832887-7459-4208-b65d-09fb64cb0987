package com.meiyunji.sponsored.service.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URI;

@Configuration
@EnableConfigurationProperties(CosProperties.class)
public class CosAutoConfiguration extends CachingConfigurerSupport {
    @Bean(name = "cosClient")
    @ConditionalOnMissingBean(name = "cosClient")
    @ConditionalOnProperty("cos.app-id")
    public S3Client cosClient(CosProperties cosProperties) {
        return S3Client.builder()
                .region(Region.of(cosProperties.getRegion()))
                .endpointOverride(URI.create(cosProperties.getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(cosProperties.getSecretId(), cosProperties.getSecretKey())))
                .build();
    }


    @Bean(name = "dataBucketClient")
    @ConditionalOnBean(name = "cosClient")
    @ConditionalOnProperty(value = {"cos.buckets.data.bucket","cos.app-id"})
    public CosBucketClient dataBucketClient(
            S3Client cosClient, CosProperties cosProperties) {
        CosProperties.CosBucketProperties bucketProperties = cosProperties.getBuckets().get("data");
        return new CosBucketClient(cosClient, cosProperties.getAppId(), bucketProperties.getBucket(),
                bucketProperties.getPrefix());
    }

    @Bean(name = "fileBucketClient")
    @ConditionalOnBean(name = "cosClient")
    @ConditionalOnProperty(value = {"cos.buckets.public.bucket","cos.app-id"})
    public CosBucketClient fileBucketClient(
            S3Client cosClient, CosProperties cosProperties) {
        CosProperties.CosBucketProperties bucketProperties = cosProperties.getBuckets().get("public");
        return new CosBucketClient(cosClient, cosProperties.getAppId(), bucketProperties.getBucket(),
                bucketProperties.getPrefix());
    }

    @Bean(name = "tempBucketClient")
    @ConditionalOnBean(name = "cosClient")
    @ConditionalOnProperty(value = {"cos.buckets.temp.bucket","cos.app-id"})
    public CosBucketClient tempBucketClient(
            S3Client cosClient, CosProperties cosProperties) {
        CosProperties.CosBucketProperties bucketProperties = cosProperties.getBuckets().get("temp");
        return new CosBucketClient(cosClient, cosProperties.getAppId(), bucketProperties.getBucket(),
                bucketProperties.getPrefix());
    }

    @Bean(name = "nativeCosClient")
    public COSClient nativeCosClient(CosProperties cosProperties) {
        COSCredentials cred = new BasicCOSCredentials(cosProperties.getSecretId(), cosProperties.getSecretKey());
        com.qcloud.cos.region.Region region = new com.qcloud.cos.region.Region(cosProperties.getRegion());
        ClientConfig clientConfig = new ClientConfig(region);
        clientConfig.setHttpProtocol(HttpProtocol.https);
        return new COSClient(cred, clientConfig);
    }
}
