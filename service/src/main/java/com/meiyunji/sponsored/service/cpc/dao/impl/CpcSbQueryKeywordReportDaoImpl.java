package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleIndexType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleOperatorType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleStatisticalModeType;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleObjectParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.ICpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.SbMatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;


/**
 * CpcSbQueryKeywordReport
 *
 */
@Repository
public class CpcSbQueryKeywordReportDaoImpl extends BaseShardingDaoImpl<CpcSbQueryKeywordReport> implements ICpcSbQueryKeywordReportDao {
    private final int LIMIT = 2000;

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertList(Integer puid, List<CpcSbQueryKeywordReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //插入热表
            insertListOriginAndHotTable(puid, list, getHotTableName());
        }
    }

    private void insertListOriginAndHotTable(Integer puid, List<CpcSbQueryKeywordReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_format`,")
                .append("`campaign_name`,`campaign_id`,`campaign_status`,`campaign_budget`,`campaign_budget_type`,`ad_group_name`,`ad_group_id`,`keyword_text`,`keyword_bid`,")
                .append("`keyword_status`,`keyword_id`,`match_type`,`impressions`,`clicks`,`currency`,`cost`, ")
                .append("`sales14d`,`conversions14d`,`query`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,")
                .append("`units_ordered_new_to_brand14d`,`units_ordered_new_to_brand_percentage14d`,")
                .append("`vctr`,`video5second_view_rate`,`video5second_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views` ,")
                .append("`video_unmutes`,`viewable_impressions`,`video_complete_views`,`vtr`,`query_id`,`create_time`,`update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (CpcSbQueryKeywordReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdFormat());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getKeywordText());
            argsList.add(report.getKeywordBid());
            argsList.add(report.getKeywordStatus());
            argsList.add(report.getKeywordId());
            argsList.add(report.getMatchType());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getSales14d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getQuery());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage14d());
            argsList.add(report.getOrderRateNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getSalesNewToBrandPercentage14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getVctr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVtr());
            argsList.add(report.getQueryId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),")
                .append("`match_type`=values(match_type),`ad_group_name`=values(ad_group_name),`ad_group_id`=values(ad_group_id),`keyword_text`=values(keyword_text),`keyword_bid`=values(keyword_bid),`keyword_status`=values(keyword_status),`keyword_id`=values(keyword_id),")
                .append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`sales14d`=values(sales14d),")
                .append("`conversions14d`=values(conversions14d),")
                .append("`vctr`=values(vctr),`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_first_quartile_views`=values(video_first_quartile_views),")
                .append("`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),`video_unmutes`=values(video_unmutes),")
                .append("`viewable_impressions`=values(viewable_impressions),`video_complete_views`=values(video_complete_views),`vtr`=values(vtr),`match_type`=values(match_type) ");

        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {

        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,`marketplace_id`,count_date , `query`, `query_cn`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append(" sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectSql.append(whereSql);

        StringBuilder sql = new StringBuilder("select t.* from ( ");
        sql.append(selectSql);
        sql.append(") t  ");


        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getSbReportField(dto.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();

        return this.getPageResult(puid,page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcSbQueryKeywordReport.class);
    }


    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id, `query`, `query_cn`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append(" sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectSql.append(whereSql);

        StringBuilder sql = new StringBuilder("select sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d from ( ");
        sql.append(selectSql);
        sql.append(") t ");



        List<AdMetricDto> adMetricDtoList = getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("sales14d")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("conversions14d")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());

        return adMetricDtoList != null && adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypeList,argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) ");
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                whereSql.append(" not in ");
            } else {
                whereSql.append(" in ");
            }
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,?),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }

            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and conversions14d >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and conversions14d <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and sales14d >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and sales14d <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(conversions14d/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(conversions14d/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/sales14d,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/sales14d,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(sales14d/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(sales14d/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(sales14d,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(sales14d,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /******************************高级搜索新增查询指标******************************/

            //CPA:广告花费除以广告订单量
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/conversions14d,0), 4) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/conversions14d,0), 4) <= ?");
                argsList.add(dto.getCpaMax());
            }
            //“品牌新买家”订单量 orders_new_to_brand14d
            if (dto.getOrdersNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMin());
            }
            if (dto.getOrdersNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMax());
            }
            //“品牌新买家”订单百分比
            if (dto.getOrderRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/conversions14d,0), 4) >= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMin());
            }
            if (dto.getOrderRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/conversions14d,0), 4) <= ?");
                argsList.add(dto.getOrderRateNewToBrandFTDMax());
            }
            //“品牌新买家”销售额
            if (dto.getSalesNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ? ");
                argsList.add(dto.getSalesNewToBrandFTDMin().doubleValue());
            }
            if (dto.getSalesNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ? ");
                argsList.add(dto.getSalesNewToBrandFTDMax().doubleValue());
            }
            //“品牌新买家”销售额百分比
            if (dto.getSalesRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d * 100 / sales14d), 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (dto.getSalesRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d * 100 / sales14d), 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //“品牌新买家”订单转化率 orders_new_to_brand14d/clicks
            if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks, 0), 4) >= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 4));
            }
            if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks, 0), 4) <= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 4));
            }


            if (dto.getVideo5SecondViewsMin() != null){
                whereSql.append(" and ROUND(ifnull(video5second_views, 0), 4) >= ?");
                argsList.add(dto.getVideo5SecondViewsMin());
            }
            if (dto.getVideo5SecondViewsMax() != null){
                whereSql.append(" and ROUND(ifnull(video5second_views, 0), 4) <= ?");
                argsList.add(dto.getVideo5SecondViewsMax());
            }

            if (dto.getVideoCompleteViewsMin() != null){
                whereSql.append(" and ROUND(ifnull(video_complete_views, 0), 4) >= ?");
                argsList.add(dto.getVideoCompleteViewsMin());
            }
            if (dto.getVideoCompleteViewsMax() != null){
                whereSql.append(" and ROUND(ifnull(video_complete_views, 0), 4) <= ?");
                argsList.add(dto.getVideoCompleteViewsMax());
            }

            //可见展示次数
            if (dto.getViewImpressionsMin() != null) {
                whereSql.append(" and sum(viewable_impressions) >= ?");
                argsList.add(dto.getViewImpressionsMin());
            }
            if (dto.getViewImpressionsMax() != null) {
                whereSql.append(" and sum(viewable_impressions) <= ?");
                argsList.add(dto.getViewImpressionsMax());
            }

            // 观看率 筛选
            if (dto.getViewabilityRateMin() != null) {
                whereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) >= ? ");
                argsList.add(dto.getViewabilityRateMin());
            }
            if (dto.getViewabilityRateMax() != null) {
                whereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) <= ? ");
                argsList.add(dto.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (dto.getViewClickThroughRateMin() != null) {
                whereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) >= ? ");
                argsList.add(dto.getViewClickThroughRateMin());
            }
            if (dto.getViewClickThroughRateMax() != null) {
                whereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) <= ? ");
                argsList.add(dto.getViewClickThroughRateMax());
            }

            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(sales14d/conversions14d, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(sales14d/conversions14d, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }

        }
        return whereSql.toString();
    }




    @Override
    public List<AdHomePerformancedto> getReportKeywordByDate(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("select keyword_id keyword_id, count_date,sum(cost) `cost`, sum(sales14d) total_sales, ")
                .append(" sum(impressions) `impressions`, sum(`conversions14d`) `conversions14d`, sum(clicks) `clicks`, sum(conversions14d) as sale_num, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(impression_rank) `impression_rank`, ")
                .append(" sum(impression_share) `impression_share`,")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid, dto, argsList);
        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                return AdHomePerformancedto.builder()
                        .keywordId(re.getString("keyword_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .impressionRank(Optional.ofNullable(re.getInt("impression_rank")).orElse(0))
                        .impressionShare(Optional.ofNullable(re.getDouble("impression_share")).orElse(0.0))
                        .video5SecondViews(Optional.ofNullable(re.getInt("video5second_views")).orElse(0))
                        .videoFirstQuartileViews(Optional.ofNullable(re.getInt("video_first_quartile_views")).orElse(0))
                        .videoMidpointViews(Optional.ofNullable(re.getInt("video_Midpoint_Views")).orElse(0))
                        .videoThirdQuartileViews(Optional.ofNullable(re.getInt("video_third_quartile_views")).orElse(0))
                        .videoCompleteViews(Optional.ofNullable(re.getInt("video_complete_views")).orElse(0))
                        .videoUnmutes(Optional.ofNullable(re.getInt("video_unmutes")).orElse(0))
                        .viewableImpressions(Optional.ofNullable(re.getInt("viewable_impressions")).orElse(0))
                        .build();
            }
        }, argsList.toArray());
    }

    @Override
    public Page pageManageExportList(Integer puid, SearchVo searchVo, Page page) {
        StringBuilder sql = new StringBuilder("SELECT `count_date` ,`puid`,`shop_id`,`marketplace_id`, `query`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append("sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append("`impression_rank`,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`orders_new_to_brand_percentage14d`) orders_new_to_brand_percentage14d,sum(`order_rate_new_to_brand14d`) order_rate_new_to_brand14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`sales_new_to_brand_percentage14d`) sales_new_to_brand_percentage14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`units_ordered_new_to_brand_percentage14d`) units_ordered_new_to_brand_percentage14d ,")
                .append("sum(impression_share) `impression_share`,")
                .append("sum(`vctr`) vctr,sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,")
                .append("sum(`video_complete_views`) video_complete_views,sum(`vtr`) vtr FROM ");
        sql.append(getTableNameByStartDate(searchVo.getStart())).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid,searchVo,argsList);
        sql.append(whereSql);

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();

        return this.getPageResult(puid,page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcSbQueryKeywordReport.class);
    }

    private String getWhereSqlCountByKeyword(int puid, SearchVo searchVo, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"));
        if (CollectionUtils.isNotEmpty(searchVo.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(searchVo.getShopIds(),"','")).append("') ");
        }
        whereSql.append(" group by shop_id,keyword_id,`query` ");
        if ("daily".equals(searchVo.getTabType())) {
            whereSql.append(", count_date");
        }
        return whereSql.toString();
    }


    private String getWhereSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'紧密匹配'，'宽泛匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(SbMatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)){
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        // end
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (campaign_id, ad_group_id, query) in ");
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,?,?),");
                argsList.add(param.getCampaignId());
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            whereSql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }
        whereSql.append(" group by keyword_id,`query` ");
        if(dto.getUseAdvanced()){

            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /******************高级筛选新增查询指标*****************************/

            //CPA:广告花费除以广告订单量
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/conversions14d,0), 4) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/conversions14d,0), 4) <= ?");
                argsList.add(dto.getCpaMax());
            }
            //“品牌新买家”订单量 orders_new_to_brand14d
            if (dto.getOrdersNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMin());
            }
            if (dto.getOrdersNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(orders_new_to_brand14d, 0) <= ?");
                argsList.add(dto.getOrdersNewToBrandFTDMax());
            }
            //“品牌新买家”订单百分比
            if (dto.getOrderRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ifnull((orders_new_to_brand14d*100)/sale_num, 0), 2) >= ? ");
                argsList.add(MathUtil.multiplyZero(dto.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (dto.getOrderRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ifnull((orders_new_to_brand14d*100)/sale_num, 0), 2) <= ? ");
                argsList.add(MathUtil.multiplyZero(dto.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }
            //“品牌新买家”销售额
            if (dto.getSalesNewToBrandFTDMin() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) >= ?");
                argsList.add(dto.getSalesNewToBrandFTDMin());
            }
            if (dto.getSalesNewToBrandFTDMax() != null) {
                whereSql.append(" and ifnull(sales_new_to_brand14d, 0) <= ? ");
                argsList.add(dto.getSalesNewToBrandFTDMax());
            }
            //“品牌新买家”销售额百分比
            if (dto.getSalesRateNewToBrandFTDMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d * 100 / total_sales), 0), 4), 2) >= ? ");
                argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100)));
            }
            if (dto.getSalesRateNewToBrandFTDMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull((sales_new_to_brand14d * 100 / total_sales), 0), 4), 2) <= ? ");
                argsList.add(MathUtil.multiply(dto.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100)));
            }

            //“品牌新买家”订单转化率 orders_new_to_brand14d/clicks
            if (dto.getBrandNewBuyerOrderConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 4) >= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 4));
            }
            if (dto.getBrandNewBuyerOrderConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(orders_new_to_brand14d/clicks,0), 4) <= ?");
                argsList.add(MathUtil.divide(dto.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 4));
            }


            if (dto.getVideo5SecondViewsMin() != null){
                whereSql.append(" and ROUND(ifnull(video5second_views, 0), 4) >= ?");
                argsList.add(dto.getVideo5SecondViewsMin());
            }
            if (dto.getVideo5SecondViewsMax() != null){
                whereSql.append(" and ROUND(ifnull(video5second_views, 0), 4) <= ?");
                argsList.add(dto.getVideo5SecondViewsMax());
            }

            if (dto.getVideoCompleteViewsMin() != null){
                whereSql.append(" and ROUND(ifnull(video_complete_views, 0), 4) >= ?");
                argsList.add(dto.getVideoCompleteViewsMin());
            }
            if (dto.getVideoCompleteViewsMax() != null){
                whereSql.append(" and ROUND(ifnull(video_complete_views, 0), 4) <= ?");
                argsList.add(dto.getVideoCompleteViewsMax());
            }

            //可见展示次数
            if (dto.getViewImpressionsMin() != null) {
                whereSql.append(" and viewable_impressions >= ?");
                argsList.add(dto.getViewImpressionsMin());
            }
            if (dto.getViewImpressionsMax() != null) {
                whereSql.append(" and viewable_impressions <= ?");
                argsList.add(dto.getViewImpressionsMax());
            }

            // 观看率 筛选
            if (dto.getViewabilityRateMin() != null) {
                whereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) >= ? ");
                argsList.add(dto.getViewabilityRateMin());
            }
            if (dto.getViewabilityRateMax() != null) {
                whereSql.append(" and ROUND(ifnull((viewable_impressions/impressions) * 100, 0), 2) <= ? ");
                argsList.add(dto.getViewabilityRateMax());
            }

            // 观看点击率 筛选
            if (dto.getViewClickThroughRateMin() != null) {
                whereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) >= ? ");
                argsList.add(dto.getViewClickThroughRateMin());
            }
            if (dto.getViewClickThroughRateMax() != null) {
                whereSql.append(" and ROUND(ifnull((clicks/viewable_impressions) * 100, 0), 2) <= ? ");
                argsList.add(dto.getViewClickThroughRateMax());
            }

            // 广告笔单价(广告销售额÷广告订单量×100%)
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }

        }
        return whereSql.toString();
    }

    @Override
    public List<AdHomePerformancedto> getReportKeywordByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> keywordIdList,
                                                                      CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return new ArrayList<>();
        }

        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(conversions14d) sale_num,SUM(sales14d) total_sales,")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share` FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD))).append(" ");
        sql.append(" where puid= ? and shop_id= ?  ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));

        //词根搜索
        if (CollectionUtils.isNotEmpty(dto.getQueryIds())) {
            sql.append(SqlStringUtil.dealInList("query_id", dto.getQueryIds(), argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        sql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        sql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .impressionRank(Optional.ofNullable(re.getInt("impression_rank")).orElse(0))
                        .impressionShare(Optional.ofNullable(re.getDouble("impression_share")).orElse(0.0))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if ("day".equals(dto.getDateType())) {
            selectSql.append(" count_date,");
        } else if ("week".equals(dto.getDateType())) {
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        } else if ("month".equals(dto.getDateType())) {
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        } else {
            return page;
        }

        if ("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append("impressions,clicks,cost,  sales14d ,conversions14d ");
        } else {
            selectSql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sales14d) sales14d,SUM(conversions14d) conversions14d ");
        }
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select 1 FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            whereSql.append(" and keyword_id=? and `query`=? ");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }
        if ("day".equals(dto.getDateType())) {
            whereSql.append(" group by count_date ");
        } else if ("week".equals(dto.getDateType())) {
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        } else if ("month".equals(dto.getDateType())) {
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getSbReportField(dto.getOrderField(), true);
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    selectSql.append(" desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, CpcSbQueryKeywordReport.class);
    }

    @Override
    public CpcSbQueryKeywordReport sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sales14d) sales14d,SUM(conversions14d) conversions14d FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append(" and keyword_id=? and `query`=?");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }
        return getJdbcTemplate(puid).queryForObject(selectSql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<CpcSbQueryKeywordReport> detailListChart(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if ("day".equals(dto.getDateType())) {
            selectSql.append(" count_date,");
        } else if ("week".equals(dto.getDateType())) {
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        } else if ("month".equals(dto.getDateType())) {
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        } else {
            return null;
        }

        if ("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            selectSql.append(" impressions,clicks,cost,  sales14d ,conversions14d ");
        } else {
            selectSql.append(" SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sales14d) sales14d,SUM(conversions14d) conversions14d ");
        }
        selectSql.append(" FROM ").append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotEmpty(dto.getKeywordId()) && StringUtils.isNotEmpty(dto.getQuery())) {
            whereSql.append(" and keyword_id=? and `query`=?");
            argsList.add(dto.getKeywordId());
            argsList.add(dto.getQuery());
        }
        if ("day".equals(dto.getDateType())) {
            whereSql.append(" group by count_date ");
        } else if ("week".equals(dto.getDateType())) {
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        } else if ("month".equals(dto.getDateType())) {
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql).append(" order by count_date");
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<CpcSbQueryKeywordReport> getNeedsLocalizationKeywords(Integer puid, Integer shopId, Integer limit) {
        return listByCondition(puid, new ConditionBuilder.Builder().equalTo("puid", puid)
                .equalTo("shop_id", shopId).equalToWithoutCheck("query_cn", "").limit(limit).build());
    }

    @Override
    public int updateQueryCnById(Integer puid, CpcSbQueryKeywordReport report) {
        int i = updateQueryCnByIdOrigin(puid, report);
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            updateQueryCnByIdHot(puid, report);
        }
        return i;
    }

    private int updateQueryCnByIdOrigin(Integer puid, CpcSbQueryKeywordReport report) {
        StringBuilder sql = new StringBuilder("update " + getJdbcHelper().getTable() +
                " set query_cn = ?,update_time = NOW(3)  where id = ? and puid = ? and shop_id = ?");
        List<Object> args = Lists.newArrayList(report.getQueryCn(), report.getId(),
                report.getPuid(), report.getShopId());
        return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    private void updateQueryCnByIdHot(Integer puid, CpcSbQueryKeywordReport report) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_format`,`keyword_id`,`query`,`query_cn`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, now(3)) ");
        argsList.add(puid);
        argsList.add(report.getShopId());
        argsList.add(report.getMarketplaceId());
        argsList.add(report.getCountDate());
        argsList.add(report.getAdFormat());
        argsList.add(report.getKeywordId());
        argsList.add(report.getQuery());
        argsList.add(report.getQueryCn());

        sql.append(" on duplicate key update `query_cn`=values(query_cn), `update_time`=now(3)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }


    @Override
    public List<AdQueryAutoRuleVo> getAutoRuleDataList(Integer puid, AutoRuleQueryWordDto dto) {

        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(conversions14d) conversions14d,")
                .append("SUM(sales14d) sales14d FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getAutoRuleWhereSqlCountByKeyword(puid, dto, argList);
        selectKeywordSql.append(whereSql);




        return getJdbcTemplate(puid).query( selectKeywordSql.toString(), argList.toArray(), new RowMapper<AdQueryAutoRuleVo>() {
            @Override
            public AdQueryAutoRuleVo mapRow(ResultSet res, int i) throws SQLException {
                AdQueryAutoRuleVo adQueryAutoRuleVo = AdQueryAutoRuleVo.builder()
                        .query(res.getString("query"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .campaignId(res.getString("campaign_id"))
                        .impressions(res.getLong("impressions"))
                        .clicks(res.getLong("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        .adSale(res.getBigDecimal("sales14d") != null ? res.getBigDecimal("sales14d") : BigDecimal.ZERO)
                        .adOrderNum(res.getInt("conversions14d"))
                        .isAsin(false)
                        .build();
                return adQueryAutoRuleVo;
            }
        });
    }

    @Override
    public List<Long> getAllIdByPuidLimit(Integer puid, int start, int limit) {
        String sql = "select id from " + getJdbcHelper().getTable() + " where puid = ? and query_id = '' order by id limit ?, ?";
        return getJdbcTemplate(puid).queryForList(sql, Long.class, puid, start, limit);
    }

    @Override
    public void updateQueryId(Integer puid, Long startId, Long endId) {
        String sql = "update " + getJdbcHelper().getTable() + " set query_id = md5(CONCAT(keyword_id, query)) where puid = ? and id >= ? and id <= ?";
        getJdbcTemplate(puid).update(sql, puid, startId, endId);
    }

    @Override
    public List<CpcSbQueryKeywordReport> listSbByKeywordRule(AutoRuleObjectParam param, List<String> itemIdList) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sbKeywordWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
        StringBuilder selectSqlSb = new StringBuilder(" select 'sb' adType, 'sbQuery' queryType, u.id, u.query_id, t.campaign_id, t.ad_group_id, u.campaign_name, u.ad_group_name,u.query, u.puid, u.shop_id, u.marketplace_id, u.match_type, t.state, '' as serving_status,t.keyword_text as targetKeywordText " +
                "from t_cpc_sb_query_keyword_report u left join t_amazon_ad_keyword_sb t on u.puid = t.puid and u.shop_id = t.shop_id and u.marketplace_id = t.marketplace_id and u.keyword_id = t.keyword_id  ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            sbKeywordWhereSql.append(" and u.match_type = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            sbKeywordWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getState())) {
            sbKeywordWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            //过滤模板关系
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("u.query_id", itemIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            //过滤模板关系
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sbKeywordWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSb.append(sbKeywordWhereSql);
        selectSqlAll.append(selectSqlSb);
        return getJdbcTemplate(param.getPuid()).query(selectSqlAll.toString(),argsList.toArray(),getMapper());
    }

    @Override
    public List<CpcSbQueryKeywordReport> listSbKeywordReport(Integer puid, Integer shopId, Integer page) {
        StringBuilder selectSql = new StringBuilder("SELECT * from "+ getJdbcHelper().getTable() +" ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        if (page != null) {
            selectSql.append(" and puid=? and shop_id=? group by query_id order by update_time asc limit " + (page - 1) * LIMIT + "," + LIMIT);
        } else {
            selectSql.append(" and puid=? and shop_id=? group by query_id order by update_time desc limit " + LIMIT);
        }
        argsList.add(puid);
        argsList.add(shopId);
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<CpcSbQueryKeywordReport> listSbKeywordReportByTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        StringBuilder selectSql = new StringBuilder("SELECT * from "+ getHotTableName());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        selectSql.append(" and puid=? and shop_id=? and update_time > ? group by query_id order by update_time desc limit " + LIMIT);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<String> listQueryIdByQueryWordDto(CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("select distinct `query_id` queryId from ( ");
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,`marketplace_id`,count_date , `query_id`, `query`, `query_cn`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append(" sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM `t_cpc_sb_query_keyword_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(dto.getPuid(), dto, argsList);
        selectSql.append(whereSql);
        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getSbReportField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    selectSql.append(" desc");
                }
                selectSql.append(" , query desc ");
            }
        }
        sql.append(selectSql);
        sql.append(") t  ");

        return getJdbcTemplate(dto.getPuid()).queryForList(sql.toString(), argsList.toArray(), String.class);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<String> listQueryId(ProcessTaskParam param) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sbKeywordWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
        StringBuilder selectSqlSb = new StringBuilder(" select u.query_id " +
                "from t_cpc_sb_query_keyword_report u left join t_amazon_ad_keyword_sb t on u.puid = t.puid and u.shop_id = t.shop_id and u.marketplace_id = t.marketplace_id and u.keyword_id = t.keyword_id  ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            sbKeywordWhereSql.append(" and u.match_type = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            sbKeywordWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getState())) {
            sbKeywordWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            //过滤模板关系
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("u.query_id", param.getItemIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            //过滤模板关系
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            sbKeywordWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sbKeywordWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSb.append(sbKeywordWhereSql);
        selectSqlAll.append(selectSqlSb);
        return getJdbcTemplate(param.getPuid()).queryForList(selectSqlAll.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<SearchQueryTagParam> listAdGroupIdByQueryWordDto(CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("select `ad_group_id` adGroupId, query query from ( ");
        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,`marketplace_id`,count_date , `query_id`, `query`, `query_cn`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append(" sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share`, ")
                .append(" sum(`video5second_views`) AS `video5second_views`, ")
                .append(" sum(`video_complete_views`) AS `video_complete_views`, ")
                .append(" sum(`video_first_quartile_views`) AS `video_first_quartile_views`, ")
                .append(" sum(`video_midpoint_views`) AS `video_midpoint_views`, ")
                .append(" sum(`video_third_quartile_views`) AS `video_third_quartile_views`, ")
                .append(" sum(`video_unmutes`) AS `video_unmutes`, ")
                .append(" sum(`viewable_impressions`) AS `viewable_impressions`")
                .append(" FROM `t_cpc_sb_query_keyword_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(dto.getPuid(), dto, argsList);
        selectSql.append(whereSql);
        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getSbReportField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    selectSql.append(" desc");
                }
                selectSql.append(" , query desc ");
            }
        }
        sql.append(selectSql);
        sql.append(") t  ");

        return getJdbcTemplate(dto.getPuid()).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(SearchQueryTagParam.class));
    }

    @Override
    public List<WordBo> listWordBo(Integer puid, Integer shopId, Integer page) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable() + " use index (uk_p_s_m_c_a_k_q) ");
        List<Object> argsList = Lists.newArrayList();
        if (page != null) {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + (page - 1) * dynamicRefreshConfiguration.getWordRootCalculateLimit() + "," + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        } else {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        }
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
    }

    @Override
    public List<WordBo> listWordBoTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable() + " use index (uk_p_s_m_c_a_k_q) ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid=? and shop_id=? and count_date >= ? and update_time > ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + Constants.WORD_ROOT_QUERY_INCREMENT_LIMIE);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
    }

    @Override
    public List<CpcSbQueryKeywordReport> wordListByIds(int puid, Integer shopId, List<Long> ids) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, puid, shop_id, query_id, query, query_cn, keyword_id, keyword_text, marketplace_id, campaign_id, ad_group_id, count_date, match_type,")
                .append(" cost, impressions, clicks, ad_format, sales14d, conversions14d, impression_share, impression_rank, sales_new_to_brand14d, orders_new_to_brand14d,")
                .append(" units_ordered_new_to_brand_percentage14d, units_ordered_new_to_brand14d, sales_new_to_brand_percentage14d, order_rate_new_to_brand14d, orders_new_to_brand_percentage14d,")
                .append(" vtr, viewable_impressions, video_unmutes, video_third_quartile_views, video_midpoint_views, video_first_quartile_views, video_complete_views, video5second_views, video5second_view_rate, vctr from ")
                .append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("id", ids, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CpcSbQueryKeywordReport.class));
    }


    private String getAutoRuleWhereSqlCountByKeyword(int puid, AutoRuleQueryWordDto dto, List<Object> argsList) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_cpc_sb_query_keyword_report` (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_format`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,")
                .append("`units_ordered_new_to_brand14d`,`units_ordered_new_to_brand_percentage14d`,")
                .append("`vctr`,`video5second_view_rate`,`video5second_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views` ,")
                .append("`video_unmutes`,`viewable_impressions`,`video_complete_views`,`vtr`,`query_id`,`create_time`,`update_time`) values");
        StringBuilder whereSql = new StringBuilder();

        //end
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        List<String> campaignList = StringUtil.splitStr(dto.getCampaignId());
        whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        List<String> groupList = StringUtil.splitStr(dto.getGroupId());
        whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupList, argsList));
        if (StringUtils.isNotBlank(dto.getQueryId())) {
            List<String> list = StringUtil.splitStr(dto.getQueryId());
            whereSql.append(SqlStringUtil.dealInList("query_id", list, argsList));
            whereSql.append(" group by query_id ");
            whereSql.append(" having 1=1 ");
        } else {
            whereSql.append(" group by keyword_id,`query` ");
            whereSql.append(" having 1=1 ");
        }
        dto.getRuleParams().forEach(e->{
            argsList.add(e.getValue());
            if (RuleOperatorType.BE == e.getComparator()) {
                argsList.add(e.getAfterValue());
            }
            whereSql.append(assemblyQuery(e));
        });
        return whereSql.toString();
    }

    private String assemblyQuery(AutoRuleQueryWordRuleParam param){
        if(RuleStatisticalModeType.avg == param.getRuleStatisticalModeType()){
            if (RuleOperatorType.BE == param.getComparator()) {
                return " and ROUND(ifnull("+getField(param.getRuleIndex())+"/"+param.getDay()+ ",0),"+getFields(param.getRuleIndex())+") "+" "+ "BETWEEN ? AND "+" ? ";
            } else {
                return " and ROUND(ifnull("+getField(param.getRuleIndex())+"/"+param.getDay()+ ",0),"+getFields(param.getRuleIndex())+") "+param.getComparator().getValue() +" ? ";
            }
        }  else {
            if (RuleOperatorType.BE == param.getComparator()) {
                return " and "+getField(param.getRuleIndex())+" "+ "BETWEEN ? AND "+" ? ";
            } else {
                return " and "+getField(param.getRuleIndex())+" "+ " "+param.getComparator().getValue()+" ? ";
            }
        }
    }

    private String getField(RuleIndexType ruleIndex){
        if (ruleIndex == null ){
            return null;
        }
        switch (ruleIndex) {
            case cost:
                return "cost";
            case acos:
                return "ROUND(ifnull(cost/sales14d,0)*100,2)";
            case adOrderNum:
                return "conversions14d";
            case adSale:
                return "sales14d";
            case conversionRate:
                return "ROUND(ifnull(conversions14d/clicks,0)*100,2)";
            case roas:
                return "ROUND(ifnull(sales14d/cost,0),2)";
            case adImpressions:
                return "impressions";
            case clicks:
                return "clicks";
            case clickRate:
                return "ROUND(ifnull(clicks/impressions,0)*100,2)";
            case cpc:
                return "ROUND(ifnull(cost/clicks,0),2)";
            case cpa:
                return "ROUND(ifnull(cost/conversions14d,0),2)";
            default:
                return null;
        }
    }

    private String getFields(RuleIndexType ruleIndex){
        if (ruleIndex == null ){
            return null;
        }
        switch (ruleIndex) {
            case cost:
                return "2";
            case acos:
                return "2";
            case adOrderNum:
                return "0";
            case orderNum:
                return "0";
            case adSale:
                return "2";
            case conversionRate:
                return "2";
            case roas:
                return "2";
            case adImpressions:
                return "0";
            case clicks:
                return "2";
            case clickRate:
                return "2";
            case cpc:
                return "2";
            case cpa:
                return "2";
            default:
                return "2";
        }
    }

    @Override
    public List<CpcSbQueryKeywordReport> getDetailList(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        selectSql.append(" count_date, cost, ")
                .append("  sales14d, impressions, clicks, conversions14d, ")
                .append("  `orders_new_to_brand14d`,  `sales_new_to_brand14d`, `impression_rank`,")
                .append(" `impression_share`, ")
                .append(" `video5second_views`, ")
                .append(" `video_complete_views`, ")
                .append("  `video_first_quartile_views`, ")
                .append(" `video_midpoint_views`, ")
                .append("  `video_third_quartile_views`, ")
                .append(" `video_unmutes`, ")
                .append(" `viewable_impressions`");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and keyword_id=? and `query`=? ");
        argsList.add(dto.getKeywordId());
        argsList.add(dto.getQuery());

        selectSql.append(whereSql);

        Object[] args = argsList.toArray();
        return this.getJdbcTemplate(puid).query(selectSql.toString(), getMapper(), args);
    }
}