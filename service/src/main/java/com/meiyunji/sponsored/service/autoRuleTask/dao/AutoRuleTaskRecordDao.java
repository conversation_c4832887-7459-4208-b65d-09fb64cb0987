package com.meiyunji.sponsored.service.autoRuleTask.dao;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecord;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:36
 */
public interface AutoRuleTaskRecordDao extends IBaseShardingDao<AutoRuleTaskRecord> {

    Page<AutoRuleTaskRecord> pageListByTaskId(Integer puid, Long id, Integer pageNo, Integer pageSize);

    List<AutoRuleTaskRecord> getListByTaskId(Integer puid, Integer shopId, Long id, Long taskId);

    int updateByPrimaryKey(int puid, AutoRuleTaskRecord record);

    int batchInsert(int puid, List<AutoRuleTaskRecord> list);

    int queryCount(Integer puid, Integer shopId, Long taskId, Integer state);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

}
