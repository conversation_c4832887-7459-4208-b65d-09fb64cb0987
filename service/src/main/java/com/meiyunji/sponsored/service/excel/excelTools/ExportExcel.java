package com.meiyunji.sponsored.service.excel.excelTools;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.util.ExcelWorkBookUtils;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.enums.ExcelTypeEnum;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.BeanInfo;
import java.beans.FeatureDescriptor;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ExportExcel<T> {

    private static final Logger logger = LoggerFactory.getLogger(ExportExcel.class);
    private static final  Integer rowaccess = 1000;// 内存中缓存记录行数

//    /**
//     * @param title excel标题
//     * @param headers excel表头
//     * @param fields  需要导出的javabean字段
//     * @param dataList 需要导出的list类型结果集
//     * @param width  每列宽度
//     * @param response response
//     */
//    public void exportExcel(String title, String[] headers,String[] fields,  List<T> dataList,int width, HttpServletResponse response) {
//        SXSSFWorkbook workbook = generateWorkbook(title, headers, fields, dataList, width);
//        String sFileName = title + ".xlsx";
//        try {
//            sFileName=URLEncoder.encode(sFileName,"UTF-8");
//        } catch (UnsupportedEncodingException e1) {
//            e1.printStackTrace();
//        }
//        response.setContentType("application/vnd.ms-excel;charset=UTF-8");   //表示是以什么格式
//        response.setHeader("Content-Disposition", "attachment;filename="+sFileName);
//        response.setHeader("Connection", "close");
//        response.setHeader("Content-Type", "application/vnd.ms-excel");
//
//        try {
//            workbook.write(response.getOutputStream());
//            workbook.dispose();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 根据参数取得excel的byte数组
     * @param title excel标题
     * @param headers excel表头
     * @param fields 需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width 宽度
     * @return byte[]
     */
    public byte[] getExcelByte(String title, String[] headers,String[] fields, List<T> dataList, int width) {
        // 生成excel
        SXSSFWorkbook workbook = generateWorkbook(title, headers, fields, dataList,width);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    /**
     * SXSSFWorkbook 导出数据
     */
    public byte[] exportBySXSSF2(String title, String[] headers,String[] fields,  List<Object[]> dataList,int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        CellStyle titleStyle =  this.getDefaultTitleStyle(workbook);
        CellStyle bodyStyle = this.getDefaultStringStyle(workbook);

        //表头
        SXSSFRow titleRow = sheet.createRow(0);
        this.editHeaderRowsOnlyOneRow(headers,titleStyle,titleRow);
        // 表体数据
        this.insertDataToSheetByObject(fields,dataList,sheet,bodyStyle,1,false);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            String sFileName = title + ".xlsx";
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    /**
     * 仓库清单导出使用  通过 SXSSF
     * @param title
     * @param headerTwos
     * @param fields
     * @param dataList
     * @param width
     * @return
     */
    public byte[] exportExcelToHead(String title, String[] headerTwos,String[] fields, List<T> dataList,int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        sheet.setDefaultColumnWidth(width);
        CellStyle titleStyle =  this.getDefaultTitleStyle(workbook);
        CellStyle bodyStyle = this.getDefaultStringStyle(workbook);

        //表头
        SXSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeight((short)850);
        this.editHeaderRowsOnlyOneRow(headerTwos,titleStyle,titleRow);
        // 表体数据
        this.insertDataToSheetByT(fields,dataList,sheet,bodyStyle,1,false);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        Long l = System.currentTimeMillis();
        try {
            String sFileName = title + ".xlsx";
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    // 创建下拉列表值存储工作表并设置值
    public static void genearteOtherSheet(Sheet sheet, String[] typeArrays, int a) {
        Row row = sheet.createRow(a);
        // 循环往该sheet中设置添加下拉列表的值
        for (int i = 0; i < typeArrays.length; i++) {
            Cell cell = row.createCell((int) i);
            cell.setCellValue(typeArrays[i]);
        }
    }
    // 设置并引用其他Sheet作为绑定下拉列表数据
    public static DataValidation SetDataValidation(Workbook wb, String strFormula, int firstRow, int firstCol, int endRow, int endCol) {
        // 表示A列1-59行作为下拉列表来源数据
        // String formula = "typelist!$A$1:$A$59" ;
        // 原顺序为 起始行 起始列 终止行 终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) wb.getSheet("typelist"));
        DataValidationConstraint formulaListConstraint = dvHelper.createFormulaListConstraint(strFormula);
        DataValidation dataValidation = dvHelper.createValidation(formulaListConstraint, regions);
        dataValidation.createErrorBox("error", "请选择下拉框值");
        dataValidation.setShowErrorBox(true);
        dataValidation.setSuppressDropDownArrow(true);
        return dataValidation;
    }
    /**
     *  计算formula
     * @param offset 偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId 第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     *
     */
    public static String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
                if ((colCount - 25) % 26 == 0) {// 边界值
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

//    /**
//     * @param title excel标题
//     * @param headers excel表头
//     * @param fields  需要导出的javabean字段
//     * @param dataList 需要导出的list类型结果集
//     * @param width  每列宽度
//     * @param response response
//     */
//    public void exportExcelToHead(String title, String[] headers, String[] fields, List<T> dataList, int width, HttpServletResponse response, Map<Integer, List<String>> map) {
//        // 声明一个工作薄
//        XSSFWorkbook workbook = null;
//        InputStream inStream = null;
//        try {
//            URL urls = new URL("http://dl.dianxiaomi.com/template/newSmtTemplate1.xlsx");
//            HttpURLConnection conn 	= (HttpURLConnection) urls.openConnection();
//            inStream 	= conn.getInputStream();
//            /*fis = new FileInputStream("D:\\newSmtTemplate1.xlsx");*/
//            workbook = new XSSFWorkbook(inStream);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        XSSFSheet sheet = workbook.getSheetAt(0);
//        workbook.setSheetName(0,title);
//        //SXSSFSheet sheet = workbook.createSheet(title);
//        // 创建下拉列表值存储工作表
//        Sheet sheet2 = workbook.createSheet("dropDownValue");
//        // 设置表格默认列宽度
//        short defaultRowHeight = 550;
//        sheet.setDefaultColumnWidth(width);
//        sheet.setDefaultRowHeight(defaultRowHeight);
//        CellStyle titleStyle = workbook.createCellStyle();//标题样式
//        CellStyle stringStyle = workbook.createCellStyle();//内容样式
//        Font titleFont = workbook.createFont();//标题字体
//        titleFont.setBold(true);//加粗
//
//        //设置标题样式
//        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        titleStyle.setFont(titleFont);
//        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
//        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
//        titleStyle.setWrapText(true);// 指定单元格自动换行
//
//        //设置内容样式
//        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
//        stringStyle.setWrapText(true);// 指定单元格自动换行
//
//        XSSFRow row = sheet.createRow(0);
//        int a = 1;
//        for (Integer integer : map.keySet()) {
//            List list = map.get(integer);
//            genearteOtherSheet(sheet2, (String[]) list.toArray(new String[list.size()]),a-1);
//            String range = getRange(0, a, list.size());
//            // 设置下拉列表直绑定对哪一页起作用
//            sheet.addValidationData(SetDataValidation(workbook, "dropDownValue!"+range, 1, integer, 1000, integer));
//            a++;
//
//        }
//        //冻结首行
//        sheet.createFreezePane( 0, 1, 0, 1 );
//        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
//        //this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,1,false);
//        workbook.setSheetHidden(2, true);
//        String sFileName = title + ".xlsx";
//        try {
//            sFileName=URLEncoder.encode(sFileName,"UTF-8");
//        } catch (UnsupportedEncodingException e1) {
//            e1.printStackTrace();
//        }
//        response.setContentType("application/vnd.ms-excel;charset=UTF-8");   //表示是以什么格式
//        response.setHeader("Content-Disposition", "attachment;filename="+sFileName);
//        response.setHeader("Connection", "close");
//        response.setHeader("Content-Type", "application/vnd.ms-excel");
//        ServletOutputStream outputStream = null;
//        try {
//            outputStream = response.getOutputStream();
//            workbook.write(outputStream);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if (outputStream != null){
//                try {
//                    outputStream.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            if (inStream != null){
//                try {
//                    inStream.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
    /**
     * 生成excel对象  返回beta数组  通过 SXSSF
     * @param title excel标题
     * @param headers excel表头
     * @param fields 需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width 宽度
     * @return HSSFWorkbook
     */
    public byte[] exportBySXSSFForTwoHeader(String title, String[] headerOnes,String[] headers,String[] fields,  List<T> dataList,int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        sheet.setDefaultColumnWidth(width);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle =  this.getDefaultTitleStyle(workbook);
        CellStyle bodyStyle = this.getDefaultStringStyle(workbook);
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook,workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式
        //表头
        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short)750);

        logger.info("headers:----------------"+headers.length);
        this.editHeaderRowsOtherRowBySXSSF(headerOnes, headers, sheet, titleStyle, row,null);
        row = sheet.createRow(1);
        row.setHeight((short)750);
        this.editHeaderRowsOnlyOneRow(headers,titleTwoStyle,row);
        // 表体数据
        this.insertDataToSheetByT(fields,dataList,sheet,bodyStyle,2,false);

        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }


    /**
     * 生成excel对象  通过 SXSSF
     * @param title excel标题
     * @param headers excel表头
     * @param fields 需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width 宽度
     * @return XSSFWorkbook
     */
    private SXSSFWorkbook generateWorkbook(String title, String[] headers, String[] fields, List<T> dataList, int width) {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        stringStyle.setWrapText(true);// 指定单元格自动换行

        SXSSFRow row = sheet.createRow(0);
        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
        this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,1,false);

        return workbook;
    }

//    /**
//     *  通过 SXSSF
//     * @param title excel标题
//     * @param headers excel表头
//     * @param fields  需要导出的javabean字段
//     * @param dataList 需要导出的list类型结果集
//     * @param width  每列宽度
//     * @param response
//     */
//    public void exportExcelForInout(String title, String[] headers, String[] fields, List<T> dataList,int width, HttpServletResponse response) {
//        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
//        SXSSFSheet sheet = workbook.createSheet(title);
//        // 设置表格默认列宽度
//        sheet.setDefaultColumnWidth(width);
//        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);
//        CellStyle stringStyle = this.getDefaultStringStyle(workbook);
//
//        SXSSFRow row = sheet.createRow(0);
//        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
//        this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,1,false);
//        String sFileName = title + ".xlsx";
//        try {
//            sFileName=URLEncoder.encode(sFileName,"UTF-8");
//        } catch (UnsupportedEncodingException e1) {
//            e1.printStackTrace();
//        }
//        response.setContentType("application/vnd.ms-excel;charset=UTF-8");   //表示是以什么格式
//        response.setHeader("Content-Disposition", "attachment;filename="+sFileName);
//        response.setHeader("Connection", "close");
//        response.setHeader("Content-Type", "application/vnd.ms-excel");
//
//        try {
//            workbook.write(response.getOutputStream());
//            workbook.dispose();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    /**
     *  通过 SXSSF
     * @param title excel标题(数据导出用，传入list<Map>)
     * @param headers excel表头
     * @param fields  需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width  每列宽度
     * @param
     */
    public byte[] exportExcelMap(String title, String[] headers, String[] fields, List<Map<String,Object>> dataList, int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeightInPoints(20f);
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);

        SXSSFRow row = sheet.createRow(0);
        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
        this.insertDataToSheetByMap(fields, dataList, sheet, stringStyle,1,false);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        String sFileName = title + ".xlsx";
        try {
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }
    /**
     *  通过 SXSSF
     * @param title excel标题(数据导出用，传入List<Object[]>)
     * @param headers excel表头
     * @param fields  需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width  每列宽度
     */
    public byte[] exportExcelForInoutArray(String title, String[] headers, String[] fields, List<Object[]> dataList, int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeightInPoints(20f);
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);

        SXSSFRow row = sheet.createRow(0);
        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
        this.insertDataToSheetByObject(fields,dataList,sheet,stringStyle,1,false);
        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();

    }

    /**
     * 通过 SXSSF
     * 生成excel对象  返回beta数组
     * @param title excel标题
     * @param headers excel表头
     * @param fields 需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width 宽度
     * @return HSSFWorkbook
     */
    public byte[] generateWorkbook2007(String title, String[] headerOnes,String[] headers,String[] fields, List<T> dataList,int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook,workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式

        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short)750);

        logger.info("headers:----------------"+headers.length);
        this.editHeaderRowsOtherRowBySXSSF(headerOnes, headers, sheet, titleStyle, row,null);
        row = sheet.createRow(1);
        row.setHeight((short)750);
        this.editHeaderRowsOnlyOneRow(headers,titleTwoStyle,row);
        this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,2,false);
        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }

    /**
     * 通过 SXSSF
     * 生成excel对象  返回beta数组
     * @param title excel标题
     * @param headers excel表头
     * @param fields 需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width 宽度
     * @return HSSFWorkbook
     */
    public byte[] generateWaveStatWorkbook20071(String title, String[] headerOnes,String[] headers,String[] fields, List<T> dataList,int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook,workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式

        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short)750);

        logger.info("headers:----------------"+headers.length);
        this.editWaveStatHeaderRowsOtherRowBySXSSF(headerOnes, headers, sheet, titleStyle, row,null);
        row = sheet.createRow(1);
        row.setHeight((short)750);
        this.editHeaderRowsOnlyOneRow(headers,titleTwoStyle,row);
        this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,2,false);
        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName=URLEncoder.encode(sFileName,"UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }


    /**
     * 波次导出全部业绩用
     * 生成除与数据列数对应的标准表头外的表头 --1
     * -有多行标题时配合 editHeaderRowsOnlyOneRow()使用
     * @param headerOther  具有合并单元格等非标准表头
     * @param headerDefault 属性与列对应的标准表头
     * @param sheet
     * @param titleStyle
     * @param row
     */
    private void editWaveStatHeaderRowsOtherRowBySXSSF(String[] headerOther, String[] headerDefault, SXSSFSheet sheet, CellStyle titleStyle, SXSSFRow row, int MergedRegion[]) {
        SXSSFCell cell = null;
        XSSFRichTextString text = null;
        for (int i = 0; i < headerDefault.length; i++) {
            if (i == 0) {
                sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[0]);
            } else if (i >= 1 && i <= 5) {
                if (i == 1) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 5));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[1]);
            } else if (i > 5 && i <= 10) {
                if (i == 6) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 6, 10));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[2]);
            } else if (i > 10 && i <= 15) {
                if (i == 11) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 11, 15));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[3]);
            } else if (i > 15 && i <= 18) {
                if (i == 16) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 16, 18));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[4]);
            } else if (i > 18 && i <= 21) {
                if (i == 19) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 19, 21));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[5]);
            } else {
                if (i == 22) {
                    sheet.addMergedRegion(new CellRangeAddress(0, 0, 22, headerDefault.length - 1));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[6]);
            }
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }



    /**
     * 通过 SXSSF
     * 生成excel对象  返回beta数组， 只有一行表头
     *
     * @param title    excel标题
     * @param headers  excel表头
     * @param fields   需要导出的javabean字段
     * @param dataList 需要导出的list类型结果集
     * @param width    宽度
     * @return HSSFWorkbook
     */
    public byte[] generateWorkbook2007WithSingleHeader(String title, String[] headers, String[] fields, List<T> dataList, int width) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook, workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式

        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short) 750);

        this.editHeaderRowsOnlyOneRow(headers, titleTwoStyle, row);
        this.insertDataToSheetByT(fields, dataList, sheet, stringStyle, 1, false);
        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName = URLEncoder.encode(sFileName, "UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }

    public byte[] generateWorkbook2007WithSingleHeaderMerge(String title, String[] headers, String[] fields, List<T> dataList, int width, int[] mergeColumn)
            throws IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchMethodException,
            SecurityException {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook, workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式

        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short) 750);

        this.editHeaderRowsOnlyOneRow(headers, titleTwoStyle, row);
        this.insertDataToSheetByT(fields, dataList, sheet, stringStyle, 1, false);

        List<Merge> ml = getMerge(dataList, "getMergeField");
        for (Merge m : ml) {
            // 因为知道是第一列 的合并 所以写死 行+1是因为 第一行是列名
            try {
				/*for (int i = 0; i <= 6; i++) {
					sheet.addMergedRegion(new CellRangeAddress(m.getFromRow() + 1, m.getToRow() + 1, i, i));
				}*/
                for (int i : mergeColumn) {
                    sheet.addMergedRegion(new CellRangeAddress(m.getFromRow() + 1, m.getToRow() + 1, i, i));
                }
            } catch (Exception e) {
                logger.info("excel导出合并单元格数据出错");
            }
        }

        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName = URLEncoder.encode(sFileName, "UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }

    // 先用String
    public static <T> Object useMethod(T t, String sx) throws IllegalAccessException, IllegalArgumentException,
            InvocationTargetException, NoSuchMethodException, SecurityException {
        // 一般传入get方法
        return (Object) t.getClass().getMethod(sx, null).invoke(t, null);

    }

    public static <T> List<Merge> getMerge(List<T> list, String sx) throws IllegalAccessException,
            IllegalArgumentException, InvocationTargetException, NoSuchMethodException, SecurityException {
        // 可以传入 想合并的属性值 传入一个字符串 用反射找到相应的get方法 指定调用此方法。。这里先写死
        List<Merge> ml = new ArrayList<>();
        for (int i = 0; i < list.size() - 1; i++) {
            if (useMethod(list.get(i), sx).equals(useMethod(list.get(i + 1), sx))) {
                Object property = useMethod(list.get(i), sx);
                // System.out.println(property);
                // System.out.println("第" + (i + 1) + "个和第" + i + "个一样");
                Merge merge = new Merge();
                int fromRow = i, toRow = i + 1;
                if (i + 2 < list.size()) {
                    for (int j = i + 2; j < list.size(); j++) {
                        if (useMethod(list.get(j), sx).equals(property)) {
                            toRow++;
                        } else {
                            i = j - 1;
                            break;
                        }
                    }
                }
                merge.setFromRow(fromRow);
                merge.setToRow(toRow);
                ml.add(merge);
            }
        }
        return ml;
    }

    /**
     * 通过 SXSSF
     * 生成excel对象支持多个sheet  返回beta数组， 只有一行表头
     *
     * @param objects   包含标题,表头,需要导出的javabean字段,需要导出的list类型结果集
     * @param width    宽度
     * @return HSSFWorkbook
     */

    public byte[] generatWorkbook2007Sheet( ArrayList<Map> objects, int width){
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        for (Map object : objects) {
            if (object != null){
                generateWorkbook2007WithSingleHeaderManySheet(workbook,(String) object.get("title"),(String[])object.get("headers"),(String[]) object.get("fields"),(List<T>) object.get("list"),width);
            }
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();

    }

    public void  generateWorkbook2007WithSingleHeaderManySheet(SXSSFWorkbook workbook, String title, String[] headers, String[] fields, List<T> dataList, int width) {
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 560;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook, workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式
        SXSSFRow row = sheet.createRow(0);
        row.setHeight((short) 750);
        this.editHeaderRowsOnlyOneRow(headers, titleTwoStyle, row);
        this.insertDataToSheetByT(fields, dataList, sheet, stringStyle, 1, false);
    }


    /** ------------------------------------华丽丽的分割线   上面是对外的调用  下面是class类 private调用 -----------------------------***/

    /**
     * 生成除与数据列数对应的标准表头外的表头 --1
     * -有多行标题时配合 editHeaderRowsOnlyOneRow()使用
     * @param headerOther  具有合并单元格等非标准表头
     * @param headerDefault 属性与列对应的标准表头
     * @param sheet
     * @param titleStyle
     * @param row
     */
    private void editHeaderRowsOtherRow(String[] headerOther, String[] headerDefault, XSSFSheet sheet, XSSFCellStyle titleStyle, XSSFRow row, int MergedRegion[]) {
        XSSFCell cell = null;
        XSSFRichTextString text = null;
        for (int i = 0; i < headerDefault.length; i++) {
            if(i >= 0 && i<= 9){
                if(i == 0){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,0,9));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[0]);
            }else if(i > 9 && i<= 19){
                if(i == 10){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,10,19));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[1]);
            }else{
                if(i == 20){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,20,headerDefault.length-1));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[2]);
            }
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }

    /**
     * 生成除与数据列数对应的标准表头外的表头 --1
     * -有多行标题时配合 editHeaderRowsOnlyOneRow()使用
     * @param headerOther  具有合并单元格等非标准表头
     * @param headerDefault 属性与列对应的标准表头
     * @param sheet
     * @param titleStyle
     * @param row
     */
    private void editHeaderRowsOtherRowBySXSSF(String[] headerOther, String[] headerDefault, SXSSFSheet sheet, CellStyle titleStyle, SXSSFRow row, int MergedRegion[]) {
        SXSSFCell cell = null;
        XSSFRichTextString text = null;
        for (int i = 0; i < headerDefault.length; i++) {
            if(i >= 0 && i<= 9){
                if(i == 0){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,0,9));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[0]);
            }else if(i > 9 && i<= 19){
                if(i == 10){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,10,19));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[1]);
            }else{
                if(i == 20){
                    sheet.addMergedRegion(new CellRangeAddress(0,0,20,headerDefault.length-1));
                }
                cell = row.createCell(i);
                text = new XSSFRichTextString(headerOther[2]);
            }
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }
    /**
     * 设置标题行---仅仅只有一行标题
     * @param headerDefault
     * @param titleStyle
     * @param row
     */
    private void editHeaderRowsOnlyOneRow(String[] headerDefault, XSSFCellStyle titleStyle, XSSFRow row) {
        for (int i = 0; i < headerDefault.length; i++) {
            XSSFCell cell = row.createCell(i);
            XSSFRichTextString text = new XSSFRichTextString(headerDefault[i]);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }
    /**
     * 设置标题行---仅仅只有一行标题
     * @param headerDefault
     * @param titleStyle
     * @param row
     */
    private void editHeaderRowsOnlyOneRow(String[] headerDefault, CellStyle titleStyle, XSSFRow row) {
        for (int i = 0; i < headerDefault.length; i++) {
            XSSFCell cell = row.createCell(i);
            XSSFRichTextString text = new XSSFRichTextString(headerDefault[i]);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }
    /**
     * 设置标题行---仅仅只有一行标题
     * @param headerDefault
     * @param titleStyle
     * @param row
     */
    private void editHeaderRowsOnlyOneRow(String[] headerDefault, CellStyle titleStyle, SXSSFRow row) {
        for (int i = 0; i < headerDefault.length; i++) {
            SXSSFCell cell = row.createCell(i);
            XSSFRichTextString text = new XSSFRichTextString(headerDefault[i]);
            cell.setCellStyle(titleStyle);
            cell.setCellValue(text);
        }
    }
    /**
     * 把数据插入到Sheet中 --数据是 泛型T   SXSSFWork
     * @param fields
     * @param dataList List<T>
     * @param sheet
     * @param stringStyle
     * @param index 从哪一行开始创建当前标题行  一行标题时 index=1  两行 index=2
     * @param isCheckUrl 是否需要处理 图片url 画图
     */
    private void insertDataToSheetByT(String[] fields, List<T> dataList, SXSSFSheet sheet, CellStyle stringStyle, int index, boolean isCheckUrl) {
        SXSSFRow row;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            T t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Class  tCls = t.getClass();
                    Method getMethod = tCls.getMethod(getMethodName, new Class[] {});
                    Object value = getMethod.invoke(t, new Object[] {});
                    setCellValue(cell,value);
                } catch (IllegalArgumentException e) {
                    logger.error("导出Excel出错：", e);
                    cell.setCellValue(fieldName);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }

            }
        }
    }

    /**
     * 把数据插入到Sheet中--数据是 Object数组  通过 XSSF
     * @param fields
     * @param dataList List<Object[]>
     * @param sheet
     * @param stringStyle
     * @param index
     * @param isCheckUrl 是否需要处理 图片url 画图
     */
    private void insertDataToSheetByObject(String[] fields, List<Object[]> dataList, XSSFSheet sheet, XSSFCellStyle stringStyle, int index, boolean isCheckUrl) {
        XSSFRow row;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            Object[] t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            for (int k = 0; k < fields.length; k++) {
                XSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    setCellValue(cell,t[k]);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }
            }
        }
    }
    /**
     * 把数据插入到Sheet中--数据是 Object数组   通过 SXSSF
     * @param fields
     * @param dataList List<Object[]>
     * @param sheet
     * @param bodyStyle
     * @param index
     * @param isCheckUrl 是否需要处理 图片url 画图
     */
    private void insertDataToSheetByObject(String[] fields, List<Object[]> dataList, SXSSFSheet sheet, CellStyle bodyStyle, int index, boolean isCheckUrl) {
        SXSSFRow row;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            Object[] t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(bodyStyle);//居中显示
                String fieldName = fields[k];
                try {
                    setCellValue(cell,t[k] != null ? t[k].toString():null);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }
            }
        }
    }
    /**
     * 把数据插入到Sheet中--数据是 map
     * @param fields
     * @param dataList
     * @param sheet
     * @param stringStyle
     * @param index
     * @param isCheckUrl 是否需要处理 图片url 画图
     */
    private void insertDataToSheetByMap(String[] fields, List<Map<String, Object>> dataList, XSSFSheet sheet, XSSFCellStyle stringStyle, int index, boolean isCheckUrl) {
        XSSFRow row;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            Map<String,Object> t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            for (int k = 0; k < fields.length; k++) {
                XSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    Object value = t.get(fieldName);
                    setCellValue(cell,value);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }
            }
        }
    }
    /**
     * 把数据插入到Sheet中--数据是 map
     * @param fields
     * @param dataList
     * @param sheet
     * @param stringStyle
     * @param index
     * @param isCheckUrl 是否需要处理 图片url 画图
     */
    private void insertDataToSheetByMap(String[] fields, List<Map<String, Object>> dataList, SXSSFSheet sheet, CellStyle stringStyle, int index, boolean isCheckUrl) {
        SXSSFRow row;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            Map<String,Object> t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    Object value = t.get(fieldName);
                    setCellValue(cell,value != null?value.toString():value);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }
            }
        }
    }

    /**
     * 单元格赋值 XSSFCell
     * @param cell
     * @param value
     */
    private void setCellValue(XSSFCell cell, Object value){
        String strValue = String.valueOf(value);
        if(value instanceof Integer){
            cell.setCellValue(Integer.parseInt(strValue));
        }else if(value instanceof Long){
            cell.setCellValue(strValue);
        }else if(value instanceof Float){
            BigDecimal b = new BigDecimal(Double.parseDouble(strValue));
            cell.setCellValue(b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }else if(value instanceof Double) {
            BigDecimal b = new BigDecimal(Double.parseDouble(strValue));
            cell.setCellValue(b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }else{
            cell.setCellValue(strValue.equals("null")?"":strValue);
        }
    }
    /**
     * 单元格赋值 SXSSFCell
     * @param cell
     * @param value
     */
    private void setCellValue(SXSSFCell cell, Object value){
        String strValue = String.valueOf(value);
        if(value instanceof Integer){
            cell.setCellValue(Integer.parseInt(strValue));
        }else if(value instanceof Long){
            cell.setCellValue(strValue);
        }else if(value instanceof Float){
            BigDecimal b = new BigDecimal(Double.parseDouble(strValue));
            cell.setCellValue(b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }else if(value instanceof Double) {
            BigDecimal b = new BigDecimal(Double.parseDouble(strValue));
            cell.setCellValue(b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }else if(value instanceof BigDecimal) {
            BigDecimal b = value == null? new BigDecimal("0"): (BigDecimal) value;
            cell.setCellValue(b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }else{
            cell.setCellValue(strValue.equals("null")?"":strValue);
        }
    }

    /**
     * 获取填充数据的cell样式  XSSFWorkbook
     * @param workbook
     * @return
     */
//	@NotNull
    private XSSFCellStyle getDefaultStringStyle(XSSFWorkbook workbook) {
        XSSFCellStyle stringStyle = workbook.createCellStyle();//内容样式
        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        stringStyle.setWrapText(true);// 指定单元格自动换行
        XSSFFont stringFont = workbook.createFont();//标题字体
        stringFont.setFontName("宋体");  //设置字体类型
        stringFont.setFontHeightInPoints((short) 11);    //设置字体大小
        stringStyle.setFont(stringFont);
        return stringStyle;
    }

    /**
     * 获取填充数据的cell样式 SXSSFWorkbook
     * @param workbook
     * @return
     */
//	@NotNull
    private CellStyle getDefaultStringStyle(SXSSFWorkbook workbook) {
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        stringStyle.setWrapText(true);// 指定单元格自动换行
        Font stringFont = workbook.createFont();//标题字体
        stringFont.setFontName("宋体");  //设置字体类型
        stringFont.setFontHeightInPoints((short) 11);    //设置字体大小
        stringStyle.setFont(stringFont);
        return stringStyle;
    }

    /**
     * 获取填充表头的cell 默认样式  XSSFWorkbook
     * @param workbook
     * @return
     */
//	@NotNull
    private XSSFCellStyle getDefaultTitleStyle(XSSFWorkbook workbook) {
        XSSFCellStyle titleStyle = workbook.createCellStyle();//标题样式
        XSSFFont titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗
        titleFont.setFontName("宋体");  //设置字体类型
        titleFont.setFontHeightInPoints((short) 14);    //设置字体大小

        //设置标题样式
        titleStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行
        return titleStyle;
    }
    /**
     * 获取填充表头的cell 默认样式  SXSSFWorkbook
     * @param workbook
     * @return
     */
//	@NotNull
    private CellStyle getDefaultTitleStyle(SXSSFWorkbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗
        titleFont.setFontName("宋体");  //设置字体类型
        titleFont.setFontHeightInPoints((short) 14);    //设置字体大小

        //设置标题样式
        titleStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行
        return titleStyle;
    }
    private CellStyle getDefaultTitleStyle3(SXSSFWorkbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗
        titleFont.setFontName("宋体");  //设置字体类型
        titleFont.setFontHeightInPoints((short) 16);    //设置字体大小

        //设置标题样式
        titleStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.LEFT);//单元格居左显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行
        return titleStyle;
    }

    /**
     * 获取填充表头的cell 默认样式  SXSSFWorkbook
     * @param workbook
     * @return
     */
//	@NotNull
    private CellStyle getDefaultTitleStyle2(SXSSFWorkbook workbook) {
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗
        titleFont.setFontName("宋体");  //设置字体类型
        titleFont.setFontHeightInPoints((short) 11);    //设置字体大小

        //设置标题样式
        titleStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行
        return titleStyle;
    }

    /**
     * 获取填充表头的cell  其他样式
     * @param workbook
     * @return
     */
//	@NotNull
    private XSSFCellStyle getTwoTitleStyleStyle(XSSFWorkbook workbook, XSSFFont titleFont) {
        XSSFCellStyle titleTwoStyle = workbook.createCellStyle();//标题样式
        //设置标题样式
        titleTwoStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleTwoStyle.setFont(titleFont);
        titleTwoStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleTwoStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleTwoStyle.setWrapText(true);// 指定单元格自动换行
        return titleTwoStyle;
    }
    /**
     * 获取填充表头的cell  其他样式
     * @param workbook
     * @return
     */
//	@NotNull
    private CellStyle getTwoTitleStyleStyle(SXSSFWorkbook workbook, Font titleFont) {
        CellStyle titleTwoStyle = workbook.createCellStyle();//标题样式
        //设置标题样式
        titleTwoStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleTwoStyle.setFont(titleFont);
        titleTwoStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleTwoStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleTwoStyle.setWrapText(true);// 指定单元格自动换行
        return titleTwoStyle;
    }

    /*	*//**
     * 读取图片
     * @param sheet         excel表格
     * @param row           excel行
     * @param column        顶级容器
     * @param srcPath       图片路径
     *//*
	private ByteArrayOutputStream readImageBySXSSF(SXSSFSheet sheet, SXSSFRow row, SXSSFCell cell , int column, String srcPath) throws Exception {
		ByteArrayOutputStream byteArrayOut = null;
		try{
			ImageUtils imageUtils = new ImageUtils();
			byteArrayOut = imageUtils.getImageFromSrc(srcPath);
			if(byteArrayOut == null){
                srcPath = "https://www.dianxiaomi.com/static/img/download-img-fail.jpg";
                byteArrayOut =  imageUtils.getImageFromSrc(srcPath);
				if (byteArrayOut == null) {
					throw new Exception("读取图片失败");
				}
			}
			row.setHeightInPoints(50);// 有图片时，设置行高为50px;
			sheet.setColumnWidth(column, (short) (35.7 * 60));// 设置图片所在列宽度为80px,注意这里单位的一个换算
		}catch (Exception e){
			logger.error("图片异常",e);
			cell.setCellValue("");
			throw new Exception(e);
		}finally {
			if (byteArrayOut != null) {
				try {
					byteArrayOut.close();
				} catch (IOException ignore) {}
			}
		}
		return byteArrayOut;
	}*/

    /**
     * FBA 箱子上传excel
     * @param title
     * @param titleHeaders
     * @param headers
     * @param dataList
     * @param width
     * @param columnMerge
     * @return
     * @throws SecurityException
     */
    public byte[] generateWorkbook2007WithFBA(String title, List<String[]> titleHeaders, String[] headers,
                                              List<List<Object>> dataList,int width,List<ExcelMerge> columnMerge)
            throws SecurityException {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook, workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式
        //设置颜色样式隔开数据
        CellStyle colorStyle = getColorStyle(workbook);
        SXSSFRow row;
        int r = 0;
        for (String[] titles : titleHeaders){
            row = sheet.createRow(r++);
            row.setHeight((short) 350);
            this.editHeaderRowsOnlyOneRow(titles, r == 1 ? titleTwoStyle : stringStyle, row);
        }
        //隔离的空行,设置颜色
        row = sheet.createRow(r++);
        setRowColor(colorStyle,row,200);

        row = sheet.createRow(r++);
        row.setHeight((short) 350);
        this.editHeaderRowsOnlyOneRow(headers, stringStyle, row);

        for (List<Object> data : dataList){
            row = sheet.createRow(r++);
            row.setHeight((short) 350);
            //空的那一行渲染颜色
            if(data.isEmpty()){
                setRowColor(colorStyle,row,200);
            }
            Object[] strings = new Object[data.size()];
            this.editRowsOnlyOneRow(data.toArray(strings), stringStyle, row);
        }

        // 对象生成
//        this.insertDataToSheetByT(fields, dataList, sheet, stringStyle, r, false);

        if (columnMerge != null) {
            for (ExcelMerge mg : columnMerge) {
                sheet.addMergedRegion(new CellRangeAddress(mg.getFromRow(), mg.getToRow(), mg.getFromIndex(), mg.getToIndex()));
            }
        }

        // 创建第二个工作表
        SXSSFSheet sheet2 = workbook.createSheet("Metadata");
        row = sheet2.createRow(0);
        sheet2.setDefaultColumnWidth(30);
        this.editHeaderRowsOnlyOneRow( new String[]{"Locale","zh_CN"}, stringStyle, row);

        // 创建第三个工作表
        SXSSFSheet sheet3 = workbook.createSheet("Help");
        row = sheet3.createRow(0);
        sheet3.setDefaultColumnWidth(100);
        this.editHeaderRowsOnlyOneRow( new String[]{"Help guide","https://images-na.ssl-images-amazon.com/images/G/01/fba-help/Excel_pack_list_guide.pdf"}, stringStyle, row);

        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName = URLEncoder.encode(sFileName, "UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }	/**
     * FBA 箱子上传excel
     * @param title
     * @param titleHeaders
     * @param headers
     * @param dataList
     * @param width
     * @param columnMerge
     * @return
     * @throws SecurityException
     */
    public byte[] generateWorkbook2007WithFBA1(String title, List<String[]> titleHeaders, String[] headers,
                                               List<List<Object>> dataList,int width,List<ExcelMerge> columnMerge)
            throws SecurityException {
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(width);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle stringStyle = this.getDefaultStringStyle(workbook);//内容样式
        CellStyle titleStyle = this.getDefaultTitleStyle(workbook);//设置标题样式
        CellStyle titleStyle1 = this.getDefaultTitleStyle3(workbook);//设置标题样式
        CellStyle titleTwoStyle = this.getTwoTitleStyleStyle(workbook, workbook.getFontAt(titleStyle.getFontIndex()));//其他标题样式
        //设置颜色样式隔开数据
        CellStyle colorStyle = getColorStyle(workbook);
        SXSSFRow row;
        int r = 0;
        for (String[] titles : titleHeaders){
            row = sheet.createRow(r++);
            row.setHeight((short) 350);
            if(r == 1){
                row.setHeight((short) 400);
                this.editHeaderRowsOnlyOneRow(titles,titleStyle1, row);
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,dataList.get(0).size() -1 ));
            }else if(r == 2){
                this.editHeaderRowsOnlyOneRow(titles, stringStyle, row);
                row.getCell(0).setCellStyle(titleStyle);
                row.getCell(2).setCellStyle(titleStyle);
            }else{
                this.editHeaderRowsOnlyOneRow(titles,stringStyle, row);
            }

        }
        row = sheet.createRow(r++);
        row.setHeight((short) 350);
//		setRowColor(colorStyle,row,headers.length);
        this.editHeaderRowsOnlyOneRow(headers, colorStyle, row);
        for (List<Object> data : dataList){
            row = sheet.createRow(r++);
            row.setHeight((short) 350);
//			//空的那一行渲染颜色
//			if(data.isEmpty()){
//				setRowColor(colorStyle,row,200);
//			}
            if(data.contains("合计")){
                Object[] strings = new Object[data.size()];
//				setRowColor(colorStyle,row,data.size());
                this.editRowsOnlyOneRow(data.toArray(strings), colorStyle, row);
            }else{
                Object[] strings = new Object[data.size()];
                this.editRowsOnlyOneRow(data.toArray(strings), stringStyle, row);
            }
        }

        // 对象生成
//        this.insertDataToSheetByT(fields, dataList, sheet, stringStyle, r, false);

        if (columnMerge != null) {
            for (ExcelMerge mg : columnMerge) {
                sheet.addMergedRegion(new CellRangeAddress(mg.getFromRow(), mg.getToRow(), mg.getFromIndex(), mg.getToIndex()));
            }
        }

        // 创建第二个工作表
        SXSSFSheet sheet2 = workbook.createSheet("Metadata");
        row = sheet2.createRow(0);
        sheet2.setDefaultColumnWidth(30);
        this.editHeaderRowsOnlyOneRow( new String[]{"Locale","zh_CN"}, stringStyle, row);

        // 创建第三个工作表
        SXSSFSheet sheet3 = workbook.createSheet("Help");
        row = sheet3.createRow(0);
        sheet3.setDefaultColumnWidth(100);
        this.editHeaderRowsOnlyOneRow( new String[]{"Help guide","https://images-na.ssl-images-amazon.com/images/G/01/fba-help/Excel_pack_list_guide.pdf"}, stringStyle, row);

        String sFileName = title + ".xlsx";
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            sFileName = URLEncoder.encode(sFileName, "UTF-8");
            workbook.dispose();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return os.toByteArray();
    }

    private void setRowColor(CellStyle colorStyle, SXSSFRow row, int length) {
        for (int i = 0; i < length; i++) {
            SXSSFCell cell = row.createCell(i);
            cell.setCellStyle(colorStyle);
        }
    }

    private CellStyle getColorStyle(SXSSFWorkbook workbook) {
        CellStyle colorStyle = workbook.createCellStyle();//内容样式
        //设置内容样式
        colorStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        colorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        colorStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        colorStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        return colorStyle;
    }

    /**
     * 设置一行
     * @param headerDefault
     * @param titleStyle
     * @param row
     */
    private void editRowsOnlyOneRow(Object[] headerDefault, CellStyle titleStyle, SXSSFRow row) {
        for (int i = 0; i < headerDefault.length; i++) {
            SXSSFCell cell = row.createCell(i);
            cell.setCellStyle(titleStyle);
            if (headerDefault[i] instanceof String){
                cell.setCellValue((String)headerDefault[i]);
            }else if (headerDefault[i] instanceof Integer){
                cell.setCellValue((Integer)headerDefault[i]);
            }else if (headerDefault[i] instanceof Float){
                cell.setCellValue((Float)headerDefault[i]);
            }else if (headerDefault[i] instanceof Double){
                cell.setCellValue((Double)headerDefault[i]);
            }
        }
    }

    /**
     *
     * @return
     */
    private SXSSFWorkbook generateWorkbookWithFixedheader(String title, String[] headers,String[] fields, List<T> dataList, int width){
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(width);
        //设置固定表头
        sheet.createFreezePane(0,1);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        stringStyle.setWrapText(true);// 指定单元格自动换行

        SXSSFRow row = sheet.createRow(0);
        this.editHeaderRowsOnlyOneRow(headers,titleStyle,row);
        this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,1,false);

        return workbook;
    }

    /**
     * 获得固定表头的格式
     * @param title
     * @param headers
     * @param fields
     * @param dataList
     * @param width
     * @return
     */
    public byte[] getExcelByteWithFixedheader(String title, String[] headers,String[] fields, List<T> dataList, int width) {
        // 生成excel
        SXSSFWorkbook workbook = generateWorkbookWithFixedheader(title, headers, fields, dataList,width);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    public byte[] getExcelByteToTransactionServiceImplShopReport(String title, String[] headers, String[] fields, List<T> dataList, int width, String[] crossColumn, String[] crossRow, boolean fifo) {
        SXSSFWorkbook workbook = generateWorkbookToTransactionServiceImplShopReport(title, headers, fields, dataList,width,crossColumn,crossRow,fifo);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    private SXSSFWorkbook generateWorkbookToTransactionServiceImplShopReport(String title, String[] headers, String[] fields, List<T> dataList, int width, String[] crossColumn, String[] crossRow, boolean fifo) {

        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        DataFormat format = workbook.createDataFormat();
        // 设置表格默认列宽度
        short defaultRowHeight = 480;
        sheet.setDefaultColumnWidth(width);
        //设置固定表头
        sheet.createFreezePane(0,2);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        CellStyle currencyStyle = workbook.createCellStyle();//货币单元格样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
//		stringStyle.setWrapText(true);// 指定单元格自动换行

        SXSSFRow oneRow = sheet.createRow(0);
        //判断先进先出设置不同的表头
        if (fifo) {//先进先出
            fifoMergedRegion(crossColumn, crossRow, sheet, titleStyle, oneRow);
        }else {//固定值
            fixedMergedRegion(crossColumn, crossRow, sheet, titleStyle, oneRow);
        }

        SXSSFRow twoRow = sheet.createRow(1);
        this.editHeaderRowsWithBeginIndex(headers,stringStyle,twoRow,2);

        //this.insertDataToSheetByT(fields,dataList,sheet,stringStyle,2,false);
        this.insertDataToSheetByWhtiSumTitle(fields,dataList,sheet,stringStyle,2,false,titleStyle,currencyStyle,format);
        return workbook;

    }

    public byte[] getExcelByteToDailyReport(String title, String[] headers, String[] fields, List<T> dataList, int width, String[] crossColumn, String[] crossRow) {
        SXSSFWorkbook workbook = generateWorkbookToDailyReport(title, headers, fields, dataList,width,crossColumn,crossRow);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    private SXSSFWorkbook generateWorkbookToDailyReport(String title, String[] headers, String[] fields, List<T> dataList, int width, String[] crossColumn, String[] crossRow) {

        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(title);
        DataFormat format = workbook.createDataFormat();
        // 设置表格默认列宽度
        short defaultRowHeight = 480;
        sheet.setDefaultColumnWidth(width);
        //设置固定表头
        sheet.createFreezePane(0,2);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        CellStyle currencyStyle = workbook.createCellStyle();//货币单元格样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
//		stringStyle.setWrapText(true);// 指定单元格自动换行

        SXSSFRow oneRow = sheet.createRow(0);
        //设置表头
        setMergedRegion(crossColumn, crossRow, sheet, titleStyle, oneRow);

        SXSSFRow twoRow = sheet.createRow(1);
        this.editHeaderRowsWithBeginIndex(headers,stringStyle,twoRow,5);
        this.insertDataToSheetByStyle(fields,dataList,sheet,stringStyle,titleStyle,currencyStyle,2,format);
        return workbook;

    }


    //广告日报导出
    private void setMergedRegion(String[] crossColumn, String[] crossRow, SXSSFSheet sheet, CellStyle titleStyle, SXSSFRow oneRow) {
        int rowIndex = 0;
        int coluIndex = 0;

        //设置合并单元格 	//设置跨列的属性
        sheet.addMergedRegion(new CellRangeAddress(0,1,0,0));//店铺的合并
        setCellValue(titleStyle,oneRow.createCell(0),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,1,1));//asin合并
        setCellValue(titleStyle,oneRow.createCell(1),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,2,2));//msku的合并
        setCellValue(titleStyle,oneRow.createCell(2),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,3,3));//时间的合并
        setCellValue(titleStyle,oneRow.createCell(3),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,4,4));//星期的合并
        setCellValue(titleStyle,oneRow.createCell(4),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0, ExcelWorkBookUtils.convertColStringToIndex("F"),ExcelWorkBookUtils.convertColStringToIndex("L")));//销售数据
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("F")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0, ExcelWorkBookUtils.convertColStringToIndex("M"),ExcelWorkBookUtils.convertColStringToIndex("U")));//广告数据
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("M")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0, ExcelWorkBookUtils.convertColStringToIndex("V"),ExcelWorkBookUtils.convertColStringToIndex("X")));//产品数据
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("V")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("Y"),ExcelWorkBookUtils.convertColStringToIndex("AB")));//BSR
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("Y")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("AC"),ExcelWorkBookUtils.convertColStringToIndex("AC")));//备注
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AC")),crossRow[rowIndex++]);

    }

    private void fixedMergedRegion(String[] crossColumn, String[] crossRow, SXSSFSheet sheet, CellStyle titleStyle, SXSSFRow oneRow) {
        int rowIndex = 0;
        int coluIndex = 0;
        //设置合并单元格 	//设置跨列的属性
        sheet.addMergedRegion(new CellRangeAddress(0,1,0,0));//店铺的合并
        setCellValue(titleStyle,oneRow.createCell(0),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,1,1));//时间的合并
        setCellValue(titleStyle,oneRow.createCell(1),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0, ExcelWorkBookUtils.convertColStringToIndex("C"),ExcelWorkBookUtils.convertColStringToIndex("K")));//销售数量
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("C")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("L"),ExcelWorkBookUtils.convertColStringToIndex("O")));//订单收入
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("L")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("P")),crossColumn[coluIndex++]);//赔偿收入
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("Q"),ExcelWorkBookUtils.convertColStringToIndex("T")));//销售订单
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("Q")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("U"),ExcelWorkBookUtils.convertColStringToIndex("W")));//退款订单
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("U")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("X")),crossColumn[coluIndex++]);//订单费用—多渠道订单
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("Y"),ExcelWorkBookUtils.convertColStringToIndex("AG")));//仓储费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("Y")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AH")),crossColumn[coluIndex++]);//广告费用
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AI"),ExcelWorkBookUtils.convertColStringToIndex("AK")));//推广费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AI")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AL"),ExcelWorkBookUtils.convertColStringToIndex("AM")));//税费
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AL")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AN"),ExcelWorkBookUtils.convertColStringToIndex("AO")));//商品成本—售出商品
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AN")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AP"),ExcelWorkBookUtils.convertColStringToIndex("AQ")));//商品成本—销毁成本
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AP")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AR"),ExcelWorkBookUtils.convertColStringToIndex("AS")));//商品成本—退货成本
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AR")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AT"),ExcelWorkBookUtils.convertColStringToIndex("AU")));//商品成本—FBA库存补偿
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AT")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AV"),ExcelWorkBookUtils.convertColStringToIndex("AW")));//商品成本—多渠道订单商品
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AV")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AX"),ExcelWorkBookUtils.convertColStringToIndex("AY")));//商品成本—补发成本
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AX")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AZ"),ExcelWorkBookUtils.convertColStringToIndex("BA")));//平台其他费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AZ")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("BB"),ExcelWorkBookUtils.convertColStringToIndex("BD")));//自定义费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BB")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BE"),ExcelWorkBookUtils.convertColStringToIndex("BE")));//毛利润
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BE")),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BF"),ExcelWorkBookUtils.convertColStringToIndex("BF")));//毛利率
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BF")),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BG"),ExcelWorkBookUtils.convertColStringToIndex("BG")));//ROI
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BG")),crossRow[rowIndex++]);
    }

    private void fifoMergedRegion(String[] crossColumn, String[] crossRow, SXSSFSheet sheet, CellStyle titleStyle, SXSSFRow oneRow) {
        int rowIndex = 0;
        int coluIndex = 0;

        //设置合并单元格 	//设置跨列的属性
        sheet.addMergedRegion(new CellRangeAddress(0,1,0,0));//店铺的合并
        setCellValue(titleStyle,oneRow.createCell(0),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,1,1));//时间的合并
        setCellValue(titleStyle,oneRow.createCell(1),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0, ExcelWorkBookUtils.convertColStringToIndex("C"),ExcelWorkBookUtils.convertColStringToIndex("K")));//销售数量
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("C")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("L"),ExcelWorkBookUtils.convertColStringToIndex("O")));//订单收入
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("L")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("P")),crossColumn[coluIndex++]);//赔偿收入
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("Q"),ExcelWorkBookUtils.convertColStringToIndex("T")));//销售订单
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("Q")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("U"),ExcelWorkBookUtils.convertColStringToIndex("W")));//退款订单
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("U")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("X")),crossColumn[coluIndex++]);//订单费用—多渠道订单
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("Y"),ExcelWorkBookUtils.convertColStringToIndex("AG")));//仓储费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("Y")),crossColumn[coluIndex++]);
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AH")),crossColumn[coluIndex++]);//广告费用
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AI"),ExcelWorkBookUtils.convertColStringToIndex("AK")));//推广费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AI")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AL"),ExcelWorkBookUtils.convertColStringToIndex("AM")));//税费
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AL")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AN"),ExcelWorkBookUtils.convertColStringToIndex("AO")));//商品成本—售出商品
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AN")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AP"),ExcelWorkBookUtils.convertColStringToIndex("AQ")));//商品成本—买家退货
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AP")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AR"),ExcelWorkBookUtils.convertColStringToIndex("AS")));//商品成本—库存调整
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AR")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AT"),ExcelWorkBookUtils.convertColStringToIndex("AU")));//商品成本—库存移除
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AT")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AV"),ExcelWorkBookUtils.convertColStringToIndex("AW")));//商品成本—库存差异
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AV")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AX"),ExcelWorkBookUtils.convertColStringToIndex("AY")));//平台其他费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AX")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,ExcelWorkBookUtils.convertColStringToIndex("AZ"),ExcelWorkBookUtils.convertColStringToIndex("BB")));//自定义费用
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("AZ")),crossColumn[coluIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BC"),ExcelWorkBookUtils.convertColStringToIndex("BC")));//毛利润
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BC")),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BD"),ExcelWorkBookUtils.convertColStringToIndex("BD")));//毛利率
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BC")),crossRow[rowIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,1,ExcelWorkBookUtils.convertColStringToIndex("BE"),ExcelWorkBookUtils.convertColStringToIndex("BE")));//ROI
        setCellValue(titleStyle,oneRow.createCell(ExcelWorkBookUtils.convertColStringToIndex("BE")),crossRow[rowIndex++]);
    }

    private void insertDataToSheetByWhtiSumTitle(String[] fields, List<T> dataList, SXSSFSheet sheet, CellStyle stringStyle, int index, boolean b, CellStyle titleStyle, CellStyle currencyStyle, DataFormat format) {
        SXSSFRow row;
        //设置货币单元格样式
        String currency;
        CurrencyUnitEnum unitEnum;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            T t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            Class tCls = t.getClass();
            try {
                currency = (String) tCls.getMethod("getCurrency", new Class[]{}).invoke(t, new Object[]{});
            } catch (Exception e) {
                currency = null;
            }
            if (currency != null) {
                unitEnum = CurrencyUnitEnum.getByCurrency(currency);
                if (unitEnum != null) {
                    currencyStyle.setDataFormat(format.getFormat("[$"+unitEnum.getUnit()+"]#,##0.00"));
                }else {
                    currencyStyle.setDataFormat(format.getFormat("[$"+currency+"]#,##0.00"));
                }
            }
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = tCls.getMethod(getMethodName, new Class[] {});
                    Object value = getMethod.invoke(t, new Object[] {});
                    if(value!=null && value.equals("合计")){
                        cell.setCellStyle(titleStyle);//加粗标题
                    }
                    if (value != null && currency != null  && value.toString().startsWith(currency)) {
                        String valStr = value.toString().substring(currency.length());
                        if (StringUtil.isNumber(valStr) || StringUtils.isBlank(valStr)) {//货币格式
                            value = Double.valueOf(valStr);
                            cell.setCellStyle(currencyStyle);
                        }
                    }
                    setCellValue(cell,value);
                } catch (IllegalArgumentException e) {
                    logger.error("导出Excel出错：", e);
                    cell.setCellValue(fieldName);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }

            }
        }
    }

    private void insertDataToSheetByStyle(String[] fields, List<T> dataList, SXSSFSheet sheet, CellStyle stringStyle, CellStyle titleStyle,CellStyle currencyStyle, int index, DataFormat format) {
        SXSSFRow row;
        //设置货币单元格样式
        String currency;
        CurrencyUnitEnum unitEnum;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            T t = dataList.get(i);
            // 利用反射，动态调用getXxx()方法得到属性值
            Class tCls = t.getClass();
            try {
                currency = (String) tCls.getMethod("getCurrency", new Class[]{}).invoke(t, new Object[]{});
            } catch (Exception e) {
                currency = null;
            }
            if (currency != null) {
                unitEnum = CurrencyUnitEnum.getByCurrency(currency);
                if (unitEnum != null) {
                    currencyStyle.setDataFormat(format.getFormat("[$"+unitEnum.getUnit()+"]#,##0.00"));
                }else {
                    currencyStyle.setDataFormat(format.getFormat("[$"+currency+"]#,##0.00"));
                }
            }
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = tCls.getMethod(getMethodName, new Class[] {});
                    Object value = getMethod.invoke(t, new Object[] {});
                    if(value!=null && value.equals("总计")){
                        cell.setCellStyle(titleStyle);//加粗标题
                    }
                    if (value != null && currency != null  && value.toString().startsWith(currency)) {
                        String valStr = value.toString().substring(currency.length());
                        if (StringUtil.isNumber(valStr) || StringUtils.isBlank(valStr)) {//货币格式
                            value = Double.valueOf(valStr);
                            cell.setCellStyle(currencyStyle);
                        }
                    }
                    setCellValue(cell,value);
                } catch (IllegalArgumentException e) {
                    logger.error("导出Excel出错：", e);
                    cell.setCellValue(fieldName);
                } catch (Exception e) {
                    cell.setCellValue(fieldName);
                }

            }
        }
    }

    /**
     * 设置某个列的值跟样式
     */
    private void setCellValue(CellStyle cellStyle,SXSSFCell cell,String text){
        cell.setCellValue(new XSSFRichTextString(text));
        cell.setCellStyle(cellStyle);
    }
    private void editHeaderRowsWithBeginIndex(String [] headers,CellStyle cellStyle,SXSSFRow row,int index){
        for (int i = index,j=0; j < headers.length;j++,i++) {
            if(!headers[j].equals("none")){
                SXSSFCell cell = row.createCell(i);
                XSSFRichTextString text = new XSSFRichTextString(headers[j]);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(text);
            }
        }
    }

    public byte[] getExcelByteWithTransactionPageType(String fileName, String[] headers, String[] fields, List dataList, int width, String[] crossRow, String[] crossColumn, int offset, String[] featureHeader, boolean fifo, boolean isAsin) {
        SXSSFWorkbook workbook = generateWorkbookWithTransactionPageType(fileName, headers, fields, dataList,width,crossRow,crossColumn,offset,featureHeader,fifo,isAsin);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    /**
     * 根据偏移量获得workbook
     * @param fileName
     * @param fields
     * @param dataList
     * @param width
     * @param crossRow
     * @param offset
     * @param featureHeader
     * @param fifo
     * @param isAsin
     * @return
     */
    private SXSSFWorkbook generateWorkbookWithTransactionPageType(String fileName, String[] headers, String[] fields, List dataList, int width,
                                                                  String[] crossRow, String[] crossColumn, int offset, String[] featureHeader,
                                                                  boolean fifo, boolean isAsin) {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(fileName);
        DataFormat format = workbook.createDataFormat();
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(width);
        //设置固定表头
        sheet.createFreezePane(0,2);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
//		stringStyle.setWrapText(true);// 指定单元格自动换行
        //以下创建出了workbook的样式 下面整个方法 将构建出excel公共的部分
        SXSSFRow oneRow = sheet.createRow(0);
        //构建各维度表头公共部分
        if (fifo){//先进先出
            createTransactionPublicHeader(oneRow,offset,crossRow,crossColumn,titleStyle,sheet,isAsin);
        }else{//固定值
            createTransactionPublicFixedHeader(oneRow,offset,crossRow,crossColumn,titleStyle,sheet,isAsin);
        }
        //以下方法在构建每个维度特有的表头
        createTransactionFeatureHeader(oneRow,sheet,featureHeader,titleStyle);

        SXSSFRow twoRow = sheet.createRow(1);
        this.editHeaderRowsWithBeginIndex(headers,stringStyle,twoRow,offset);

        //数据插入
        this.insertDataToSheetWithCurrenyStyle(fields,dataList,sheet,stringStyle,2,workbook,format);
        return workbook;

    }

    /**
     * 数据插入，带币种符号表格
     */
    private void insertDataToSheetWithCurrenyStyle(String[] fields, List<T> dataList, SXSSFSheet sheet, CellStyle stringStyle, int index, SXSSFWorkbook workbook, DataFormat format) {
        SXSSFRow row;
        Map<String, CellStyle> styleMap = Maps.newHashMap();//币种单元格样式
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(i + index);
            T t = dataList.get(i);
            //设置货币单元格样式
            Class tCls = t.getClass();
            for (int k = 0; k < fields.length; k++) {
                SXSSFCell cell = row.createCell(k);
                cell.setCellStyle(stringStyle);//居中显示
                String fieldName = fields[k];
                try {
                    // 利用反射，动态调用getXxx()方法得到属性值
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = tCls.getMethod(getMethodName, new Class[] {});
                    Object value = getMethod.invoke(t, new Object[] {});
                    CurrencyUnitEnum unitEnum = CurrencyUnitEnum.getStartsWith(value);
                    if (unitEnum != null) {
                        String valStr = value.toString().trim().substring(unitEnum.getCurrency().length());
                        if (StringUtil.isNumber(valStr) || StringUtils.isBlank(valStr)) {
                            CellStyle currencyStyle;
                            if (styleMap.get(unitEnum.getCurrency()) != null) {
                                currencyStyle = styleMap.get(unitEnum.getCurrency());
                            }else {
                                currencyStyle = workbook.createCellStyle();
                                currencyStyle.setDataFormat(format.getFormat("[$"+unitEnum.getUnit()+"]#,##0.00"));
                                styleMap.put(unitEnum.getCurrency(),currencyStyle);
                            }
                            cell.setCellStyle(currencyStyle);
                            value = Double.valueOf(valStr);
                        }
                    }
                    setCellValue(cell,value);
                } catch (IllegalArgumentException e) {
                    logger.error("导出Excel出错：", e);
                    cell.setCellValue(fieldName);
                } catch (Exception e) {
                    logger.error("导出Excel出错：",e);
                    cell.setCellValue(fieldName);
                }
            }
        }
    }

    private void createTransactionPublicFixedHeader(SXSSFRow oneRow, int offset, String[] crossRow, String[] crossColumn, CellStyle titleStyle, SXSSFSheet sheet, boolean isAsin) {

        int rowIndex = 0;
        int columnIndex = 0;
        //这个是跨列的单元格
        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("A"),offset +ExcelWorkBookUtils.convertColStringToIndex("S")));//收入
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("A")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("T"),offset +ExcelWorkBookUtils.convertColStringToIndex("V")));//售出订单
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("T")),crossColumn[columnIndex++]);

        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("W")),crossColumn[columnIndex++]);                                                                //多渠道订单

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("X"),offset +ExcelWorkBookUtils.convertColStringToIndex("Y")));//退款订单
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("X")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("Z"),offset +ExcelWorkBookUtils.convertColStringToIndex("AA")));//广告费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("Z")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AB"),offset +ExcelWorkBookUtils.convertColStringToIndex("AD")));//推广费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AB")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AE"),offset +ExcelWorkBookUtils.convertColStringToIndex("AG")));//FBA仓储费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AE")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AH"),offset +ExcelWorkBookUtils.convertColStringToIndex("AM")));//FBA其他仓储费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AH")),crossColumn[columnIndex++]);

        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AN")),crossColumn[columnIndex++]);                                                                  //平台支出-库存调整

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AO"),offset +ExcelWorkBookUtils.convertColStringToIndex("AP")));//平台其他费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AO")),crossColumn[columnIndex++]);
        //这是跨行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0,1,offset + ExcelWorkBookUtils.convertColStringToIndex("AQ"),offset +ExcelWorkBookUtils.convertColStringToIndex("AQ")));//VAT税费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AQ")),crossRow[rowIndex++]);//VAT税费
        sheet.addMergedRegion(new CellRangeAddress(0,1,offset + ExcelWorkBookUtils.convertColStringToIndex("AR"),offset +ExcelWorkBookUtils.convertColStringToIndex("AR")));//税费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AR")),crossRow[rowIndex++]);//税费

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AS"),offset +ExcelWorkBookUtils.convertColStringToIndex("AX")));//采购成本
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AS")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AY"),offset +ExcelWorkBookUtils.convertColStringToIndex("BD")));//头程费用
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AY")),crossColumn[columnIndex++]);

        if (isAsin) {
            sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("BE"),offset +ExcelWorkBookUtils.convertColStringToIndex("BF")));//自定义费用
            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("BE")),crossColumn[columnIndex++]);
            sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("BG"),offset +ExcelWorkBookUtils.convertColStringToIndex("BH")));//利润
            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("BG")),crossColumn[columnIndex]);
        }else {
            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("BE")),crossColumn[columnIndex++]);                                                                  //自定义费用
            sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("BF"),offset +ExcelWorkBookUtils.convertColStringToIndex("BG")));//利润
            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("BF")),crossColumn[columnIndex]);
        }

    }

    /**
     * 创建特定维度的表头
     * @param oneRow
     * @param sheet
     * @param featureHeader
     * @param titleStyle
     */
    private void createTransactionFeatureHeader(SXSSFRow oneRow, SXSSFSheet sheet, String[] featureHeader, CellStyle titleStyle) {
        for (int i = 0; i < featureHeader.length; i++) {
            sheet.addMergedRegion(new CellRangeAddress(0,1,i,i));
            setCellValue(titleStyle,oneRow.createCell(i),featureHeader[i]);
        }
    }

    private void createTransactionPublicHeader(SXSSFRow oneRow, int offset, String[] crossRow, String[] crossColumn, CellStyle titleStyle, SXSSFSheet sheet, boolean isAsin) {

        int rowIndex = 0;
        int columnIndex = 0;
        //这个是跨列的单元格
        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("A"),offset +ExcelWorkBookUtils.convertColStringToIndex("S")));//收入
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("A")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("T"),offset +ExcelWorkBookUtils.convertColStringToIndex("V")));//售出订单
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("T")),crossColumn[columnIndex++]);

        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("W")),crossColumn[columnIndex++]);                                                                //多渠道订单

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("X"),offset +ExcelWorkBookUtils.convertColStringToIndex("Y")));//退款订单
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("X")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("Z"),offset +ExcelWorkBookUtils.convertColStringToIndex("AA")));//广告费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("Z")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AB"),offset +ExcelWorkBookUtils.convertColStringToIndex("AD")));//推广费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AB")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AE"),offset +ExcelWorkBookUtils.convertColStringToIndex("AG")));//FBA仓储费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AE")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AH"),offset +ExcelWorkBookUtils.convertColStringToIndex("AM")));//FBA其他仓储费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AH")),crossColumn[columnIndex++]);

        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AN")),crossColumn[columnIndex++]);                                                                  //平台支出-库存调整

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AO"),offset +ExcelWorkBookUtils.convertColStringToIndex("AP")));//平台其他费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AO")),crossColumn[columnIndex++]);
        //这是跨行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0,1,offset + ExcelWorkBookUtils.convertColStringToIndex("AQ"),offset +ExcelWorkBookUtils.convertColStringToIndex("AQ")));//税费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AQ")),crossRow[rowIndex++]);//VAT税费
        sheet.addMergedRegion(new CellRangeAddress(0,1,offset + ExcelWorkBookUtils.convertColStringToIndex("AR"),offset +ExcelWorkBookUtils.convertColStringToIndex("AR")));//税费
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AR")),crossRow[rowIndex++]);//税费

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AS"),offset +ExcelWorkBookUtils.convertColStringToIndex("AT")));//采购成本
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AS")),crossColumn[columnIndex++]);

        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AU"),offset +ExcelWorkBookUtils.convertColStringToIndex("AV")));//头程费用
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AU")),crossColumn[columnIndex++]);

//        if (isAsin) {
        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AW"),offset +ExcelWorkBookUtils.convertColStringToIndex("AY")));//自定义费用
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AW")),crossColumn[columnIndex++]);
        sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AZ"),offset +ExcelWorkBookUtils.convertColStringToIndex("BA")));//利润
        setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AZ")),crossColumn[columnIndex]);
//        } else {
//            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AW")),crossColumn[columnIndex++]);                                                                 //自定义费用
//            sheet.addMergedRegion(new CellRangeAddress(0,0,offset + ExcelWorkBookUtils.convertColStringToIndex("AX"),offset +ExcelWorkBookUtils.convertColStringToIndex("AY")));//利润
//            setCellValue(titleStyle,oneRow.createCell(offset + ExcelWorkBookUtils.convertColStringToIndex("AX")),crossColumn[columnIndex]);
//        }

    }

    /**
     * 获取单行无合并表头导出文件字节流
     * @param fileName
     * @param headers
     * @param fields
     * @param dataList
     * @return
     */
    public byte[] getSingleHeaderExportExcel(String fileName, String[] headers, String[] fields, List dataList) {
        SXSSFWorkbook workbook = generateWorkbookForSettlementExport(fileName, headers, fields, dataList);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
            workbook.dispose();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return os.toByteArray();
    }

    private SXSSFWorkbook generateWorkbookForSettlementExport(String fileName, String[] headers, String[] fields, List dataList) {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook(rowaccess);
        SXSSFSheet sheet = workbook.createSheet(fileName);
        DataFormat format = workbook.createDataFormat();
        // 设置表格默认列宽度
        short defaultRowHeight = 360;
        sheet.setDefaultColumnWidth(15);
        //设置固定表头
        sheet.createFreezePane(0,1);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        //设置内容样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        stringStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        stringStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        SXSSFRow oneRow = sheet.createRow(0);
        //填充表头
        this.editHeaderRowsWithBeginIndex(headers,titleStyle,oneRow,0);
        //数据插入
        this.insertDataToSheetWithCurrenyStyle(fields,dataList,sheet,stringStyle,1,workbook,format);
        return workbook;
    }

    /**
     * 创建带下拉框和批注的导入模板
     * @param fileName
     * @param typeEnum
     * @param headers
     * @param selectMap 要增加的下拉框； key，要增加的列 value 要增加的下拉内容
     * @param commentMap 要增加的批注； key，要增加的列 value 要增加的批注内容
     * @param commentCol 批注长度
     * @param commentRow 批注高度
     */
    public byte[] getImportSelectTemplate(String fileName, ExcelTypeEnum typeEnum, String[] headers, Map<Integer, String[]> selectMap,
                                          Map<Integer, String> commentMap, Integer commentCol, Integer commentRow) {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(fileName);
        // 设置表格默认列宽度
        short defaultRowHeight = 420;
        sheet.setDefaultColumnWidth(15);
        //设置固定表头
//        sheet.createFreezePane(0,1);
        sheet.setDefaultRowHeight(defaultRowHeight);
        CellStyle titleStyle = workbook.createCellStyle();//标题样式
        CellStyle stringStyle = workbook.createCellStyle();//内容样式
        Font titleFont = workbook.createFont();//标题字体
        titleFont.setBold(true);//加粗

        //设置标题样式
        stringStyle.setFillBackgroundColor(IndexedColors.GREEN.getIndex());
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
        titleStyle.setWrapText(true);// 指定单元格自动换行

        SXSSFRow oneRow = sheet.createRow(0);

        //填充表头
        this.editHeaderRowsWithBeginIndex(headers,titleStyle,oneRow,0);
        //设置标红字体
        if (Arrays.stream(headers).anyMatch(s -> s.startsWith("*"))) {
            Font redFont = workbook.createFont();//标红字体
            redFont.setBold(true);//加粗
            redFont.setColor(Font.COLOR_RED);//红色
            CellStyle redStyle = workbook.createCellStyle();
            redStyle.setFont(redFont);
            redStyle.setAlignment(HorizontalAlignment.CENTER);//单元格居中显示
            redStyle.setVerticalAlignment(VerticalAlignment.CENTER); //指定单元格垂直居中对齐
            redStyle.setWrapText(true);// 指定单元格自动换行
            for (int i = 0; i < headers.length; i++) {
                if (headers[i].startsWith("*")) {
                    Cell cell = sheet.getRow(0).getCell(i);
//                    XSSFRichTextString ts = new XSSFRichTextString(headers[i]);//单元格内容
//                    ts.applyFont(0,1,redFont);
//                    ts.applyFont(1,ts.length(),titleFont);
                    cell.setCellStyle(redStyle);
                }
            }
        }

        //设置下拉框
        if (MapUtils.isNotEmpty(selectMap)) {
            int i = 1;
            DataValidationHelper helper = sheet.getDataValidationHelper();
            for (Map.Entry<Integer, String[]> entry : selectMap.entrySet()) {
                String[] value = entry.getValue();
                if (value == null || value.length < 1) continue;
                String str = StringUtils.join(value, "");
                if (str.length() < 240){
                    //字符数少的直接设置下拉框
                    this.setHSSFValidation(sheet,value,helper,typeEnum,entry.getKey());
                }else {
                    //字符数多的使用隐藏的sheet 来存放动态下拉的数据
                    SXSSFSheet select = workbook.createSheet("hidden"+i);
                    workbook.setSheetHidden(i++,true);
                    String listFormula = this.buildHiddenSheet(select, value,"A");
                    this.addValidationData(sheet, helper, typeEnum, listFormula,entry.getKey());
                }
            }
        }
        //设置批注内容
        if (MapUtils.isNotEmpty(commentMap)) {
            for (Map.Entry<Integer, String> entry : commentMap.entrySet()) {
                Cell cell = sheet.getRow(0).getCell(entry.getKey());
                Drawing draw = sheet.createDrawingPatriarch();
                Comment comment = draw.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, entry.getKey(), 0, commentCol, commentRow));
                comment.setString(new XSSFRichTextString(entry.getValue()));//设置批注内容
                cell.setCellComment(comment);
            }
        }

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        byte[] bytes = null;
        try {
            workbook.write(os);
            bytes = os.toByteArray();
            workbook.dispose();
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return bytes;
    }


    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.(下拉字符数限制小于255)
     * @param sheet  模板sheet页（需要设置下拉框的sheet）
     * @param textlist 下拉框显示的内容
     */
    public static void setHSSFValidation(SXSSFSheet sheet, String[] textlist,DataValidationHelper helper,ExcelTypeEnum typeEnum, int index){
        // 生成下拉列表
        // 设置范围
        CellRangeAddressList regions = new CellRangeAddressList(1,typeEnum.getLimit(), index, index);
        // 生成下拉框内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 绑定下拉框和作用区域
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        //处理Excel兼容性问题
        if(dataValidation instanceof XSSFDataValidation){
            //数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }else{
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 对sheet页生效
        sheet.addValidationData(dataValidation);
    }


    /**
     * 创建一个隐藏sheet存储下拉选项
     * @param values 下拉内容
     */
    private String buildHiddenSheet(SXSSFSheet select, String[] values,String cName) {
        for(int j = 0 ; j < values.length ; j++){
            SXSSFRow row = select.getRow(j);
            if(null == row){
                row = select.createRow(j);
            }
            SXSSFCell cell = row.createCell(0);
            cell.setCellValue(values[j]);
        }
        String listFormula = select.getSheetName() + "!$"+cName+"$1:$"+ cName +"$" + values.length;
        return listFormula;
    }

    /**
     *生成下拉列表
     */
    private void addValidationData(SXSSFSheet sheet, DataValidationHelper helper, ExcelTypeEnum typeEnum, String listFormula, int cellIndex) {
        //区域
        CellRangeAddressList regions = new CellRangeAddressList(1, typeEnum.getLimit(), cellIndex, cellIndex);
        // 生成下拉框内容
        DataValidationConstraint constraint = helper.createFormulaListConstraint(listFormula);
        // 绑定下拉框和作用区域
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        //处理Excel兼容性问题
        if(dataValidation instanceof XSSFDataValidation){
            //数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        }else{
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 对sheet页生效
        sheet.addValidationData(dataValidation);
    }



    /**
     *
     * @param title
     * @param headers
     * @param fields
     * @param dataList
     * @return
     */
    public byte[] getExcelByteWithEasyExcel(String title, String[] headers, String[] fields, List<T> dataList) {
        // 生成excel
        List<List<String>> header = Lists.newArrayList();
        List<List<Object>> objs = Lists.newArrayList();
        try {
            T t = dataList.get(0);
            BeanInfo info = Introspector.getBeanInfo(t.getClass());
            PropertyDescriptor[] descriptors = info.getPropertyDescriptors();
            Map<String, PropertyDescriptor> descriptorMap = Arrays.stream(descriptors).collect(Collectors.toMap(FeatureDescriptor::getName, o -> o));
            for (String s : headers) {
                header.add(Lists.newArrayList(s));
            }
            for (T t1 : dataList) {
                List<Object> objList = Lists.newArrayList();
                for (int i = 0; i < headers.length; i++) {
                    String field = fields[i];
                    if (descriptorMap.containsKey(field)) {
                        Method method = descriptorMap.get(field).getReadMethod();
                        Object obj = method.invoke(t1);
                        objList.add(obj);
                    }
                }
                objs.add(objList);
            }
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            EasyExcelUtil.write(objs, header, os, 1);
            return os.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}

class Merge {
    private int fromRow;
    private int toRow;
    private int fromIndex;
    private int toIndex;

    public int getFromRow() {
        return fromRow;
    }

    public void setFromRow(int fromRow) {
        this.fromRow = fromRow;
    }

    public int getToRow() {
        return toRow;
    }

    @Override
    public String toString() {
        return "Merge [fromRow=" + fromRow + ", toRow=" + toRow + ", fromIndex=" + fromIndex + ", toIndex=" + toIndex
                + "]";
    }

    public void setToRow(int toRow) {
        this.toRow = toRow;
    }

    public int getFromIndex() {
        return fromIndex;
    }

    public void setFromIndex(int fromIndex) {
        this.fromIndex = fromIndex;
    }

    public int getToIndex() {
        return toIndex;
    }

    public void setToIndex(int toIndex) {
        this.toIndex = toIndex;
    }
}
