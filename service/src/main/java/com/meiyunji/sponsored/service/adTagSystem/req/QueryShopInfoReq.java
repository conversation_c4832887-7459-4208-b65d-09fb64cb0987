package com.meiyunji.sponsored.service.adTagSystem.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class QueryShopInfoReq implements Serializable {

    private static final long serialVersionUID = 123456L;

    private Integer puid;

    @NotNull(message = "shopId不能为空")
    private List<String> shopIdList;

    private List<String> marketplaceIdList;

    @NotNull(message = "tagId不能为空")
    private List<String> tagIdList;
}
