package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: wade
 * @date: 2021/9/7 17:14
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AmazonAdNeTargetingDto {
    private String atype;
    private Long id;
    private Integer shopId;
    private Integer dxmAdGroupId;
    private String campaignId;
    private String groupId;
    private String campaignName;
    private String campaignTargetingType;
    private String adGroupName;
    private String state;
    private String targetText;
    private String targetId;
    private String asin;
    private String type;
    private String title;
    private String imgUrl;
    private String domain;
    private String createTime;
    private String expression;
    private LocalDateTime creationDate;
}
