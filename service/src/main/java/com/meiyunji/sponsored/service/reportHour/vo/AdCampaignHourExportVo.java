package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.excel.excelTools.converter.StringConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/12/1 20:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdCampaignHourExportVo {

    @ExcelProperty(value = "日期")
    private String label;
    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cost;
    @ExcelProperty(value = "广告花费占比")
    private String adCostPercentage;
    @ExcelProperty(value = "广告花费(对比)")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String costCompare;
    @ExcelProperty(value = "广告花费增长率")
    private String costCompareRate;
    @ExcelProperty(value = "广告曝光量")
    private Long impressions;
    @ExcelProperty(value = "广告曝光量(对比)")
    private Long impressionsCompare;
    /**
     * （对比周期对应的数值-统计周期数值）/统计周期数值
     */
    @ExcelProperty(value = "广告曝光量增长率")
    private String impressionsCompareRate;

    @ExcelProperty(value = "广告点击量")
    private Long clicks;
    @ExcelProperty(value = "广告点击量（对比）")
    private Long clicksCompare;
    /**
     * （对比周期对应的数值-统计周期数值）/统计周期数值
     */
    @ExcelProperty(value = "广告点击量增长率")
    private String clicksCompareRate;

    /**
     * 品牌新买家订单量
     */
    @ExcelProperty(value = "可见展示次数")
    private Integer viewableImpressions;

    @ExcelProperty(value = "VTR")
    private String vrt;

    /**
     * vCTR
     */
    @ExcelProperty(value = "vCTR")
    private String vctr;

    /**
     * 广告花费/广告订单量
     */
    @ExcelProperty(value = "CPA")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;

    @ExcelProperty(value = "CPC")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpc;
    @ExcelProperty(value = "CPC(对比)")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpcCompare;
    @ExcelProperty(value = "CPC(比率)")
    private String cpcCompareRate;

    /**
     * VTR
     */
    @ExcelProperty(value = "VCPM")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String vcpm;

    @ExcelProperty(value = "广告点击率")
    private String clickRate;
    @ExcelProperty(value = "广告点击率(对比)")
    private String clickRateCompare;
    @ExcelProperty(value = "广告点击率(比率)")
    private String clickRateCompareRate;
    @ExcelProperty(value = "广告转化率")
    private String salesConversionRate;
    @ExcelProperty(value = "广告转化率(对比)")
    private String salesConversionRateCompare;
    @ExcelProperty(value = "广告转化率(比率)")
    private String salesConversionRateCompareRate;

    @ExcelProperty(value = "ACoS")
    private String acos;
    @ExcelProperty(value = "ACoS(对比)")
    private String acosCompare;
    @ExcelProperty(value = "ACoS(比率)")
    private String acosCompareRate;
    @ExcelProperty(value = "ROAS")
    private BigDecimal roas;
    @ExcelProperty(value = "ROAS(对比)")
    private BigDecimal roasCompare;
    @ExcelProperty(value = "ROAS(比率)")
    private String roasCompareRate;
    @ExcelProperty(value = "ACOTS")
    private String acots;
    @ExcelProperty(value = "ASOTS")
    private String asots;
    @ExcelProperty(value = "广告订单量")
    private Integer adOrderNum;
    @ExcelProperty(value = "广告订单量（对比）")
    private Integer adOrderNumCompare;
    /**
     * （对比周期对应的数值-统计周期数值）/统计周期数值
     */
    @ExcelProperty(value = "广告订单量增长率")
    private String adOrderNumCompareRate;

    @ExcelProperty(value = "广告订单量占比")
    private String adOrderNumPercentage;

    @ExcelProperty(value = "本广告产品订单量")
    private Integer selfAdOrderNum;
    @ExcelProperty(value = "其他产品广告订单量")
    private Integer otherAdOrderNum;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSales;

    @ExcelProperty(value = "广告销售额占比")
    private String adSalePercentage;

    @ExcelProperty(value = "广告销售额(对比)")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSalesCompare;
    @ExcelProperty(value = "广告销售额增长率")
    private String adSalesCompareRate;

    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSelfSales;
    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSales;
    @ExcelProperty(value = "广告销量")
    private Integer adSaleNum;
    @ExcelProperty(value = "广告销量（对比）")
    private Integer adSaleNumCompare;
    @ExcelProperty(value = "广告销量增长率")
    private String adSaleNumCompareRate;

    @ExcelProperty(value = "广告销量占比")
    private String orderNumPercentage;

    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;
    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;

    /**
     * 广告笔单价
     */
    @ExcelProperty(value = "广告笔单价")
    private String advertisingUnitPrice;

    /**
     * 本广告产品笔单价
     */
    @ExcelProperty(value = "本广告产品笔单价")
    private String advertisingProductUnitPrice;

    /**
     * 其它产品广告笔单价
     */
    @ExcelProperty(value = "其它产品广告笔单价")
    private String advertisingOtherProductUnitPrice;

    /**
     * 品牌新买家订单量
     */
    @ExcelProperty(value = "品牌新买家订单量")
    private Integer ordersNewToBrand;

    /**
     * 品牌新买家订单占比
     */
    @ExcelProperty(value = "品牌新买家订单占比")
    private String ordersNewToBrandPercentage;

    /**
     * 品牌新买家销售额
     */
    @ExcelProperty(value = "品牌新买家销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String salesNewToBrand;

    /**
     * 品牌新买家销售额
     */
    @ExcelProperty(value = "品牌新买家销售额占比")
    private String salesNewToBrandPercentage;

    /**
     * 品牌新买家销量
     */
    @ExcelProperty(value = "品牌新买家销量")
    private Integer unitsOrderedNewToBrand;

    /**
     * 品牌新买家销量占比
     */
    @ExcelProperty(value = "品牌新买家销量占比")
    private String unitsOrderedNewToBrandPercentage;


    public AdCampaignHourExportVo(String currency, AdCampaignHourVo vo) {

        this.label = vo.getLabel();
        this.cost = vo.getAdCost() == null ? currency + 0 : currency + vo.getAdCost().setScale(2, RoundingMode.HALF_UP);
        this.costCompare = vo.getAdCostCompare() == null ? currency + 0 : currency + vo.getAdCostCompare().setScale(2, RoundingMode.HALF_UP);
        this.costCompareRate = vo.getAdCostCompareRate() == null ? null : vo.getAdCostCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.impressions = vo.getImpressions();
        this.impressionsCompare = vo.getImpressionsCompare();
        this.impressionsCompareRate = vo.getImpressionsCompareRate() == null ? null : vo.getImpressionsCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.clicks = vo.getClicks();
        this.clicksCompare = vo.getClicksCompare();
        this.clicksCompareRate = vo.getClicksCompareRate() == null ? null : vo.getClicksCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.cpa = vo.getCpa() == null ? currency + 0 : currency + vo.getCpa().setScale(2, RoundingMode.HALF_UP);
        this.cpc = vo.getAdCostPerClick() == null ? currency + 0 : currency + vo.getAdCostPerClick().setScale(2, RoundingMode.HALF_UP);
        this.clickRate = vo.getCtr() == null ? null : vo.getCtr().setScale(2, RoundingMode.HALF_UP) + "%";
        this.salesConversionRate = vo.getCvr() == null ? null : vo.getCvr().setScale(2, RoundingMode.HALF_UP) + "%";
        this.acos = vo.getAcos() == null ? null : vo.getAcos().setScale(2, RoundingMode.HALF_UP) + "%";
        this.roas = vo.getRoas() == null ? null : vo.getRoas().setScale(2, RoundingMode.HALF_UP);
        this.acots = vo.getAcots() == null ? null : vo.getAcots().setScale(2, RoundingMode.HALF_UP) + "%";
        this.asots = vo.getAsots() == null ? null : vo.getAsots().setScale(2, RoundingMode.HALF_UP) + "%";
        this.adOrderNum = vo.getAdOrderNum();
        this.adOrderNumCompare = vo.getAdOrderNumCompare();
        this.adOrderNumCompareRate = vo.getAdOrderNumCompareRate() == null ? null : vo.getAdOrderNumCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.selfAdOrderNum = vo.getSelfAdOrderNum();
        this.otherAdOrderNum = vo.getOtherAdOrderNum();
        this.adSales = vo.getAdSale() == null ? currency + 0 : currency + vo.getAdSale().setScale(2, RoundingMode.HALF_UP);
        this.adSalesCompare = vo.getAdSaleCompare() == null ? currency + 0 : currency + vo.getAdSaleCompare().setScale(2, RoundingMode.HALF_UP);
        this.adSalesCompareRate = vo.getAdSaleCompareRate() == null ? null : vo.getAdSaleCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.adSelfSales = vo.getAdSelfSale() == null ? currency + 0 : currency + vo.getAdSelfSale().setScale(2, RoundingMode.HALF_UP);
        this.adOtherSales = vo.getAdOtherSale() == null ? currency + 0 : currency + vo.getAdOtherSale().setScale(2, RoundingMode.HALF_UP);
        this.adSaleNum = vo.getAdSaleNum();
        this.adSaleNumCompare = vo.getAdSaleNumCompare();
        this.adSaleNumCompareRate = vo.getAdSaleNumCompareRate() == null ? null : vo.getAdSaleNumCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.adSelfSaleNum = vo.getAdSelfSaleNum();
        this.adOtherSaleNum = vo.getAdOtherSaleNum();

        this.viewableImpressions = vo.getViewableImpressions();
        this.vctr = vo.getVCtr() == null ? null : vo.getVCtr().setScale(2, RoundingMode.HALF_UP) + "%";
        this.vrt = vo.getVrt() == null ? null : vo.getVrt().setScale(2, RoundingMode.HALF_UP) + "%";
        this.vcpm = vo.getVcpm() == null ? currency + 0 : currency + vo.getVcpm().setScale(2, RoundingMode.HALF_UP);
        this.advertisingUnitPrice = vo.getAdvertisingUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP);
        this.salesNewToBrand = vo.getSalesNewToBrand() == null ? currency + 0 : currency + vo.getSalesNewToBrand().setScale(2, RoundingMode.HALF_UP);
        this.unitsOrderedNewToBrand = vo.getUnitsOrderedNewToBrand();
        this.ordersNewToBrand = vo.getOrdersNewToBrand();

        this.salesNewToBrandPercentage = vo.getSalesNewToBrandPercentage() == null ? null : vo.getSalesNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.unitsOrderedNewToBrandPercentage = vo.getUnitsOrderedNewToBrandPercentage() == null ? null : vo.getUnitsOrderedNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.ordersNewToBrandPercentage = vo.getOrdersNewToBrandPercentage() == null ? null : vo.getOrdersNewToBrandPercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.advertisingOtherProductUnitPrice = vo.getAdvertisingOtherProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingOtherProductUnitPrice().setScale(2, RoundingMode.HALF_UP);
        this.advertisingProductUnitPrice = vo.getAdvertisingProductUnitPrice() == null ? currency + 0 : currency + vo.getAdvertisingProductUnitPrice().setScale(2, RoundingMode.HALF_UP);
        this.adCostPercentage = vo.getAdCostPercentage() == null ? "0.00%" : vo.getAdCostPercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.adSalePercentage = vo.getAdSalePercentage() == null ? "0.00%" : vo.getAdSalePercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.adOrderNumPercentage = vo.getAdOrderNumPercentage() == null ? "0.00%" : vo.getAdOrderNumPercentage().setScale(2, RoundingMode.HALF_UP) + "%";
        this.orderNumPercentage = vo.getOrderNumPercentage() == null ? "0.00%" : vo.getOrderNumPercentage().setScale(2, RoundingMode.HALF_UP) + "%";

        this.cpcCompare = vo.getAdCostPerClickCompare() == null ? currency + 0 : currency + vo.getAdCostPerClickCompare().setScale(2, RoundingMode.HALF_UP);
        this.clickRateCompare = vo.getCtrCompare() == null ? null : vo.getCtrCompare().setScale(2, RoundingMode.HALF_UP) + "%";
        this.salesConversionRateCompare = vo.getCvrCompare() == null ? null : vo.getCvrCompare().setScale(2, RoundingMode.HALF_UP) + "%";
        this.acosCompare = vo.getAcosCompare() == null ? null : vo.getAcosCompare().setScale(2, RoundingMode.HALF_UP) + "%";
        this.roasCompare = vo.getRoasCompare() == null ? null : vo.getRoasCompare().setScale(2, RoundingMode.HALF_UP);
        this.cpcCompareRate = vo.getAdCostPerClickCompareRate() == null ? null : vo.getAdCostPerClickCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.clickRateCompareRate = vo.getCtrCompareRate() == null ? null : vo.getCtrCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.salesConversionRateCompareRate = vo.getCvrCompareRate() == null ? null : vo.getCvrCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.acosCompareRate = vo.getAcosCompareRate() == null ? null : vo.getAcosCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
        this.roasCompareRate = vo.getRoasCompareRate() == null ? null : vo.getRoasCompareRate().setScale(2, RoundingMode.HALF_UP) + "%";
    }
}
