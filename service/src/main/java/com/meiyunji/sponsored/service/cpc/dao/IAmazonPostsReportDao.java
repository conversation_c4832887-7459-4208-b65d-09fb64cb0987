package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonPostReport;
import com.meiyunji.sponsored.service.post.request.GetPostsRequest;
import com.meiyunji.sponsored.service.post.request.PostPageFilterParam;
import com.meiyunji.sponsored.service.post.response.GetPostsResponse;
import com.meiyunji.sponsored.service.post.vo.PostsAggregateVo;

import java.util.List;

public interface IAmazonPostsReportDao extends IBaseShardingDao<AmazonPostReport> {

    List<GetPostsResponse.Posts> getReportByPostIds(PostPageFilterParam param, List<String> postProfileId, List<String> postIds);

    /**
     * 根据天数分组统计汇总数据
     *
     * @param puid
     * @param req
     * @return
     */
    List<PostsAggregateVo> getChartData(Integer puid, GetPostsRequest req);

    /**
     * 聚合统计
     *
     * @param filterDto
     * @return
     */
    PostsAggregateVo getAggregateReport(PostPageFilterParam filterDto);

    void upsertPostReports(List<AmazonPostReport> postReports);
}
