package com.meiyunji.sponsored.service.product.service;

import com.meiyunji.sponsored.service.product.po.AsinImage;

import java.util.List;
import java.util.Map;

/**
 * @ClassName ISyncProductService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/13 16:56
 **/
public interface ISyncAsinImageService {
    /**
     * 获取指定asin图片
     * @param asin
     * @param marketplaceId
     * @return
     */
    String getMainImage(String asin, String marketplaceId);

    void syncAsinImage(List<AsinImage> asins, int index, int total,int limit);

    List<AsinImage> getListByAsins(Integer puid, String marketplaceId, List<String> asins);

    void syncAsinTitle(String date, int index, int total);

    List<AsinImage> getListByAsinsNoSave(Integer puid, String marketplaceId, List<String> asins);

    void syncAsinInvalid(int index, int total);
}
