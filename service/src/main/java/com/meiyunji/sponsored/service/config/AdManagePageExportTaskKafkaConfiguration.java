package com.meiyunji.sponsored.service.config;

import cn.hutool.core.collection.CollectionUtil;
import com.meiyunji.sponsored.service.kafka.AdManagePageExportTaskKafkaProducer;
import com.meiyunji.sponsored.service.kafka.trace.TracingProducerMethodInterceptor;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @create: 2024-07-17 18:36
 */
@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@Slf4j
public class AdManagePageExportTaskKafkaConfiguration {
    @Value("${spring.kafka.ad-manage-page-export-task.bootstrap-servers}")
    private String bootstrapServers;

    private ProducerFactory<String, byte[]> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
        props.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, CollectionUtil.newArrayList(TracingProducerMethodInterceptor.class.getName()));
        return props;
    }

    @Bean
    public KafkaTemplate<String, byte[]> adManagePageExportTaskKafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }


    @Bean(name = "adManagePageExportTaskKafkaProducer")
    public AdManagePageExportTaskKafkaProducer adManagePageExportTaskKafkaProducer(
            KafkaProperties kafkaProperties, KafkaTemplate<String, byte[]> adManagePageExportTaskKafkaTemplate) {
        KafkaProperties.ProducerProperties producerProperty = kafkaProperties.getProducers().get("ad-manage-page-export-task");
        return new AdManagePageExportTaskKafkaProducer(producerProperty.getTopic(), adManagePageExportTaskKafkaTemplate);
    }
}
