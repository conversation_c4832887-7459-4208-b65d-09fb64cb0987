package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterSdTargetBaseDataBO;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdTargetingReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.export.ReportFillBaseDataHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.SD_TARGET_DOWNLOAD_TYPE;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Service
@Slf4j
public class AmazonAdSdTargetingReportServiceImpl implements IAmazonAdSdTargetingReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IAmazonAdSdTargetingReportDao sdTargetingReportDao;
    @Autowired
    private IAmazonSdAdTargetingDao sdAdTargetingDao;
    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private ReportFillBaseDataHelper reportFillBaseDataHelper;

    @Override
    public Page pageList(int puid, SearchVo search, Page page) {

        page = sdTargetingReportDao.getPageList(puid,search,page);

        if (page != null && page.getRows().size() > 0) {
            List<AmazonAdSdTargetingReport> poList = page.getRows();
            if(poList!= null && poList.size()>0) {
                List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
                ReportDataVo vo;
                Map<String, AmazonSdAdTargeting> sdAdTargetMap = null;
                //获取投放信息
                List<String> targetIdList = poList.stream().map(AmazonAdSdTargetingReport::getTargetId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(targetIdList)) {
                    List<AmazonSdAdTargeting> sdAdTargetingList = sdAdTargetingDao.listByTargetId(puid, targetIdList);
                    if (!CollectionUtils.isEmpty(sdAdTargetingList)) {
                        sdAdTargetMap = sdAdTargetingList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity(), (key1, key2) -> key2));
                    }
                }
                for (AmazonAdSdTargetingReport report : poList) {
                    vo = new ReportDataVo();

                    reportConvertDataVo(report, vo);
                    //设置投放名称
                    if (!MapUtils.isEmpty(sdAdTargetMap) && SD_TARGET_DOWNLOAD_TYPE.equals(search.getDownloadType())) {
                        AmazonSdAdTargeting sdAdTargeting = sdAdTargetMap.get(report.getTargetId());
                        vo.setTargetingText(sdAdTargeting != null ? sdAdTargeting.getTargetText() : "");
                    }
                    list.add(vo);
                }
                page.setRows(list);
            }
        }
        return page;
    }

    @Override
    public Page pageList4DownloadCenterExport(int puid, SearchVo search, Page page) {

        page = sdTargetingReportDao.getPageList(puid,search,page);

        if (page != null && page.getRows().size() > 0) {
            List<AmazonAdSdTargetingReport> poList = page.getRows();
            if(poList!= null && poList.size()>0) {
                List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
                ReportDataVo vo;

                //分组查询活动和投放基础数据
                Map<Integer, Set<String>> campaignIdCollect = new HashMap<>();
                Map<Integer, Set<String>> targetIdCollect = new HashMap<>();
                poList.parallelStream().forEach(report -> {
                    int shopId = report.getShopId();
                    String campaignId = report.getCampaignId();
                    campaignIdCollect.computeIfAbsent(shopId, k -> new HashSet<>()).add(campaignId);
                    targetIdCollect.computeIfAbsent(shopId, k -> new HashSet<>()).add(report.getTargetId());
                });

                Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, campaignIdCollect, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));
                Map<Integer, Map<String, DownloadCenterSdTargetBaseDataBO>> targetBaseDataMap = reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, targetIdCollect, (a, b, c) -> sdAdTargetingDao.queryBaseData4DownloadByTargetIdList(a, b, c));

                for (AmazonAdSdTargetingReport report : poList) {
                    vo = new ReportDataVo();
                    reportConvertDataVo(report, vo);

                    reportFillBaseDataHelper.fillSdTargetBaseData4ReportDataVo(vo, report.getShopId(), report.getCampaignId(),
                            report.getTargetId(), campaignBaseDataMap, targetBaseDataMap, search.getDownloadType());

                    list.add(vo);
                }
                page.setRows(list);
            }
        }
        return page;
    }

    private void reportConvertDataVo(AmazonAdSdTargetingReport report, ReportDataVo vo) {
        vo.setShopId(report.getShopId());
        vo.setCampaignName(report.getCampaignName());
        vo.setAdGroupName(report.getAdGroupName());
        vo.setTargetId(report.getTargetId());
        vo.setTargetingText(report.getTargetingText());
        vo.setTargetingExpression(report.getTargetingExpression());
        vo.setCost(report.getCost());
        vo.setSales(report.getSales14d());
        vo.setImpressions(report.getImpressions());
        //可见展示次数
        vo.setViewImpressions(report.getViewImpressions());
        vo.setClicks(report.getClicks());
        //attributedConversions14d  字段作为订单量更为准确
        vo.setOrderNum(report.getConversions14d());
        //计算acos
        vo.setAcos(new BigDecimal(getAcos(report)));
        vo.setCpc(vo.getCpc());
        vo.setClickRate(vo.getClickRate());
        //广告转化率
        vo.setSalesConversionRate(Double.valueOf(getSalesConversionRate(report)));
        vo.setCostType(report.getCostType());
        vo.setCpc(report.getCpc());
        //计算vcpm
        vo.setVcpm(getVcpm(report));
        vo.setAttributedDetailPageView14d(report.getDetailPageView14d());
        vo.setRoas(report.getRoas());
        vo.setCountDate(report.getCountDate());

        vo.setAttributedConversions14d(Optional.ofNullable(report.getConversions14d()).orElse(0));

        //本广告产品订单量
        vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getConversions14dSameSKU()).orElse(0));
        if (report.getConversions14d() != null) {
            if (report.getConversions14dSameSKU() != null ) {
                vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d() - report.getConversions14dSameSKU());
            } else {
                vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d());
            }
        } else {
            vo.setAttributedConversions14dOtherSameSKU(0);
        }


        vo.setAttributedSales14d(Optional.ofNullable(report.getSales14d()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));

        //本广告产品销售额
        vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getSales14dSameSKU()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        if (report.getSales14d() != null) {
            if (report.getSales14dSameSKU() != null) {
                vo.setAttributedSales14dOtherSameSKU(report.getSales14d().subtract(report.getSales14dSameSKU()));
            } else {
                vo.setAttributedSales14dOtherSameSKU(report.getSales14d());
            }
        } else {
            vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
        }

        //广告销量
        vo.setAttributedUnitsOrdered14d(Optional.ofNullable(report.getUnitsOrdered14d()).orElse(0));
        //品牌新买家订单量
        vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
        if (report.getAttributedOrderRateNewToBrand14d() != null) {
            vo.setAttributedOrderRateNewToBrand14d(report.getAttributedOrderRateNewToBrand14d().doubleValue());
        } else {
            vo.setAttributedOrderRateNewToBrand14d(0.00);
        }
        //品牌新买家销售额
        vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).
                orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
        if (report.getAttributedSalesNewToBrandPercentage14d() != null) {
            vo.setAttributedSalesNewToBrandPercentage14d(report.getAttributedSalesNewToBrandPercentage14d().doubleValue());
        } else {
            vo.setAttributedSalesNewToBrandPercentage14d(0.00);
        }
        vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
        if (report.getAttributedUnitsOrderedNewToBrandPercentage14d() != null) {
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(report.getAttributedUnitsOrderedNewToBrandPercentage14d().doubleValue());
        } else {
            vo.setAttributedUnitsOrderedNewToBrandPercentage14d(0.00);
        }
    }

    /**
     * 计算acos
     * @param report
     * @return
     */
    private String getAcos(AmazonAdSdTargetingReport report) {
        if (report.getCost() == null || report.getSales14d() == null) {
            return BigDecimal.ZERO.toString();
        }
        if (report.getSales14d().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.toString();
        }
        return MathUtil.multiply(MathUtil.divide(report.getCost().toString(), report.getSales14d().toString()), "100");
    }


    /**
     * 计算广告转化率
     * @param report
     * @return
     */
    private String getSalesConversionRate(AmazonAdSdTargetingReport report) {
        if (report.getConversions14d() == null || report.getClicks() == null) {
            return BigDecimal.ZERO.toString();
        }
        if (report.getConversions14d().intValue() == 0 || report.getClicks().intValue() == 0) {
            return BigDecimal.ZERO.toString();
        }
        return MathUtil.multiply(MathUtil.divide(report.getConversions14d().toString(), report.getClicks().toString()), "100");
    }

    @Override
    public Page detailPageList(int puid, ReportParam param, Page page) {
        param.setStartDate(DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd"));
        param.setEndDate(DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd"));

        page = sdTargetingReportDao.detailPageList(puid, param.getShopId(), param.getMarketplaceId(), param, page);

        List<AmazonAdSdTargetingReport> poList = page.getRows();
        if(poList!= null && poList.size()>0) {
            List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            ReportDataVo vo;
            for (AmazonAdSdTargetingReport report : poList) {
                vo = new ReportDataVo();
                String countDate = report.getCountDate();
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(report.getCountDate())){
                    countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
                    vo.setCountDate(countDate);
                }
                vo.setShopId(report.getShopId());
                vo.setCampaignName(report.getCampaignName());
                vo.setAdGroupName(report.getAdGroupName());
                vo.setTargetId(report.getTargetId());
                vo.setTargetingText(report.getTargetingText());
                vo.setCost(report.getCost());
                vo.setSales(report.getSales14d());
                vo.setImpressions(report.getImpressions());
                vo.setClicks(report.getClicks());
                vo.setOrderNum(report.getConversions14d());
                vo.setAcos(vo.getAcos());
                vo.setCpc(vo.getCpc());
                vo.setClickRate(vo.getClickRate());
                vo.setSalesConversionRate(vo.getSalesConversionRate());
                list.add(vo);
            }
            page.setRows(list);
        }
        return page;
    }

    @Override
    public SumReportDataVo getSumReport(int puid, Integer shopId, String marketplaceId, ReportParam param) {
        SumReportDataVo vo = new SumReportDataVo();
        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd");
        AmazonAdSdTargetingReport sumReport = null;

        sumReport = sdTargetingReportDao.getSumReportByTargetId(puid,shopId,marketplaceId,startStr,endStr,param.getTargetId());

        ReportDataVo dataVo = null;
        if (sumReport != null) {
            dataVo = new ReportDataVo();
            dataVo.setCost(sumReport.getCost());
            dataVo.setSales(sumReport.getSales14d());
            dataVo.setImpressions(sumReport.getImpressions());
            dataVo.setClicks(sumReport.getClicks());
            dataVo.setOrderNum(sumReport.getConversions14d());
            dataVo.setAcos(dataVo.getAcos());
            dataVo.setCpc(dataVo.getCpc());
            dataVo.setClickRate(dataVo.getClickRate());
            dataVo.setSalesConversionRate(dataVo.getSalesConversionRate());
        }
        vo.setReportVo(dataVo);
        //获取上个时间段的数据
        Map<String,String> map = getLastTime(param.getStart(),param.getEnd(),param.getLastMonth());
        AmazonAdSdTargetingReport lastSumReport = null;
        lastSumReport = sdTargetingReportDao.getSumReportByTargetId(puid,shopId,marketplaceId,map.get("startStr"),map.get("endStr"),param.getCampaignId());
        ReportDataVo lastDataVo = null;
        if (lastSumReport != null) {
            lastDataVo = new ReportDataVo();
            lastDataVo.setCost(lastSumReport.getCost());
            lastDataVo.setSales(lastSumReport.getSales14d());
            lastDataVo.setImpressions(lastSumReport.getImpressions());
            lastDataVo.setClicks(lastSumReport.getClicks());
            lastDataVo.setOrderNum(lastSumReport.getConversions14d());
            lastDataVo.setAcos(lastDataVo.getAcos());
            lastDataVo.setCpc(lastDataVo.getCpc());
            lastDataVo.setClickRate(lastDataVo.getClickRate());
            lastDataVo.setSalesConversionRate(lastDataVo.getSalesConversionRate());
        }
        vo.setLastReportVo(lastDataVo);
        vo.calculationGrow();
        return vo;
    }

    @Override
    public List<ReportDataVo> getChartList(int puid, Integer shopId, String marketplaceId, ReportParam param) {
        String startStr = DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd");
        List<AmazonAdSdTargetingReport> poList = sdTargetingReportDao.getChartList(puid,shopId,marketplaceId,startStr,endStr,param.getTargetId());

        List<ReportDataVo> list = null;
        if (poList != null && poList.size() > 0) {
            list = Lists.newArrayListWithExpectedSize(poList.size());
            ReportDataVo vo;
            for (AmazonAdSdTargetingReport report : poList) {
                vo = new ReportDataVo();
                String countDate = report.getCountDate();
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(report.getCountDate())){
                    countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
                    vo.setCountDate(countDate);
                }
                vo.setShopId(report.getShopId());
                vo.setCampaignName(report.getCampaignName());
                vo.setTargetId(report.getTargetId());
                vo.setTargetingText(report.getTargetingText());
                vo.setCost(report.getCost());
                vo.setSales(report.getSales14d());
                vo.setImpressions(report.getImpressions());
                vo.setClicks(report.getClicks());
                vo.setOrderNum(report.getConversions14d());
                vo.setAcos(vo.getAcos());
                vo.setCpc(vo.getCpc());
                vo.setClickRate(vo.getClickRate());
                vo.setSalesConversionRate(vo.getSalesConversionRate());
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public Map<String, String> getAdProductMap(int puid, Integer shopId, String marketplaceId, String campaignId) {
        List<Map<String, Object>> list;
        if (StringUtils.isBlank(campaignId)) {
            list = sdTargetingReportDao.getCampaignOrAdGroupNames(puid, shopId, marketplaceId, null);
        } else{
            list = sdTargetingReportDao.getCampaignOrAdGroupNames(puid, shopId, marketplaceId, campaignId);
        }
        return getItemIdAndNameMap(list);
    }

    @Override
    public List<TargetReportVo> getReportVoListByGroupIds(Integer puid, List<String> sdGroupIds, TargetReportSearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        List<TargetReportVo> voList = new ArrayList<>();
        long t = Instant.now().toEpochMilli();
        List<AmazonAdSdTargetingReport> reportList = sdTargetingReportDao.getReportVoListByGroupIds(puid, sdGroupIds, searchVo);

        log.info("查找sd 投放 花费时间 {}", (Instant.now().toEpochMilli()-t));
        if (CollectionUtils.isEmpty(reportList)) {
            return voList;
        }

        String marketplaceId = reportList.get(0).getMarketplaceId();
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId).getCurrencyCode();

        List<String> campaignIds = reportList.stream().distinct().map(AmazonAdSdTargetingReport::getCampaignId).collect(Collectors.toList());
        List<String> groupIds = reportList.stream().distinct().map(AmazonAdSdTargetingReport::getAdGroupId).collect(Collectors.toList());

        List<AmazonAdCampaignAll> sdAdCampaigns = amazonSdAdCampaignDao.getByCampaignIds(puid, searchVo.getShopId(), campaignIds);
        Map<String, AmazonAdCampaignAll> campaignMap = sdAdCampaigns.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<AmazonSdAdGroup> sdAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, searchVo.getShopId(), groupIds);
        Map<String, AmazonSdAdGroup> groupMap = sdAdGroups.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));

        TargetReportVo vo;
        for (AmazonAdSdTargetingReport report : reportList) {
            vo = new TargetReportVo();
            vo.setShopId(report.getShopId());
            vo.setMarketplaceId(report.getMarketplaceId());
            vo.setQuery(report.getTargetingText());
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setAdGroupId(report.getAdGroupId());
            vo.setAdGroupName(report.getAdGroupName());
            vo.setType("sd");
            vo.setTargetType("target");
            vo.setTargetId(report.getTargetId());
            if (campaignMap.containsKey(report.getCampaignId())) {
                vo.setCampaignName(campaignMap.get(report.getCampaignId()).getName());
                vo.setCampaignTargetingType(campaignMap.get(report.getCampaignId()).getTactic());
            }
            if (groupMap.containsKey(report.getAdGroupId())) {
                vo.setAdGroupName(groupMap.get(report.getAdGroupId()).getName());
            }
            vo.setCurrency(currencyCode);

            if (StringUtils.isNotBlank(report.getTargetingText())) {
                if (report.getTargetingText().contains("views")) {
                    vo.setMatchType("再营销浏览定向");
                } else if (report.getTargetingText().contains("category=")) {
                    vo.setMatchType("类目定位");
                } else if (report.getTargetingText().contains("purchases=")) {
                    vo.setMatchType("购买再营销");
                } else if (report.getTargetingText().contains("asin=")) {
                    vo.setMatchType("ASIN定位");
                } else {
                    vo.setMatchType(report.getTargetingText());
                }
            }

            vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
            vo.setAdOrderNum(report.getConversions14d() != null ? report.getConversions14d() : 0);
            vo.setAdSale(report.getSales14d() != null ?  String.valueOf(report.getSales14d().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");

            // 点击率
            Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
            vo.setCtr(String.valueOf(ctr));
            //订单转化率
            Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
            vo.setCvr(String.valueOf(cvr));
            //acos
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setAcos("0");
            } else {
                Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
                vo.setAcos(String.valueOf(acos));
            }
            //acots
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAcots("0");
            } else {
                Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAcots(String.valueOf(acots));
            }
            //asots
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAsots("0");
            } else {
                Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAsots(String.valueOf(asots));
            }
            //adCostPerClick
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                vo.setAdCostPerClick("0");
            } else {
                Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                vo.setAdCostPerClick(String.valueOf(adCostPerClick));
            }
            //roas
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setRoas("0");
            } else {
                Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                vo.setRoas(String.valueOf(roas));
            }

            voList.add(vo);
        }

        return voList;
    }

    @Override
    public void getDetailsSumVo(TargetReportDetailsVo detailsVo, CpcCommPageVo vo, BigDecimal sumShopSale) {
        AmazonAdSdTargetingReport report = sdTargetingReportDao.getDetailsSumVo(detailsVo.getPuid(), detailsVo);
        if (report == null || report.getShopId() == null) {
            return;
        }
        // 组装报告数据
        setReportData(report, vo, sumShopSale);
    }

    @Override
    public void getTargetDetailsDay(AdReportDetailsVo adReportDetailsVo, TargetReportDetailsVo detailsVo) {
        List<AmazonAdSdTargetingReport> reportList = sdTargetingReportDao.getListTargetDetailsDay(detailsVo.getPuid(), detailsVo);

        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }
        List<CpcCommPageVo> list = new ArrayList<>(reportList.size());
        CpcCommPageVo vo;
        for (AmazonAdSdTargetingReport report : reportList) {
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), report.getCountDate(), report.getCountDate());
            BigDecimal sumShopSale = BigDecimal.ZERO;
            if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
                sumShopSale = shopSaleDto.getSumRange();
            }
            vo = new CpcCommPageVo();
            // 组装报告数据
            setReportData(report, vo, sumShopSale);
            vo.setCountDate(report.getCountDate());
            list.add(vo);
        }
        adReportDetailsVo.setList(list);
    }

    private void setReportData(AmazonAdSdTargetingReport report, CpcCommPageVo vo, BigDecimal sumShopSale) {
        vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
        vo.setAdOrderNum(report.getConversions14d() != null ? report.getConversions14d() : 0);
        vo.setAdSale(report.getSales14d() != null ?  String.valueOf(report.getSales14d().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }
    }

    private Map<String, String> getItemIdAndNameMap(List<Map<String, Object>> list) {
        if (list != null && list.size() > 0) {
            Map<String, String> map = Maps.newHashMapWithExpectedSize(list.size());
            list.forEach(temp -> {
                map.put(String.valueOf(temp.get("id")), String.valueOf(temp.get("name")));
            });
            return map;
        }
        return null;
    }


    /**
     * 获取汇总报告对比时间段
     * @param start
     * @param end
     * @param lastMonth
     * @return
     */
    private Map<String,String> getLastTime(Date start, Date end, Integer lastMonth){
        Map<String,String> map = Maps.newHashMapWithExpectedSize(2);
        //获取上个时间段的数据
        if(lastMonth!=null&&lastMonth == 1){ //获取上一个月的同时间段的数据
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(start,-1),"yyyyMMdd"));
            map.put("endStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(end,-1),"yyyyMMdd"));
        }else {
            //获取时间差
            int days = DateUtil.getDayBetween(start,end);
            end = DateUtil.addDay(start,-1);
            map.put("endStr",DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addDay(end,-days),"yyyyMMdd"));
        }
        return map;
    }


    private BigDecimal getVcpm(AmazonAdSdTargetingReport report) {
        if (report.getCost() == null || report.getViewImpressions() == null) {
            return null;
        }
        return report.getViewImpressions() == 0 ? BigDecimal.ZERO : (report.getCost().divide(BigDecimal.valueOf(report.getViewImpressions()),
                5, RoundingMode.HALF_UP)).multiply(BigDecimal.valueOf(1000)).setScale(2, RoundingMode.HALF_UP);
    }


}
