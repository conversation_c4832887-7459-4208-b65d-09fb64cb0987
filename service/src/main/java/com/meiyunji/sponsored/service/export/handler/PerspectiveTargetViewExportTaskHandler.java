package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.TargetingDataRequest;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.CategoryTargetViewRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.TargetViewAggregateExcelVO;
import com.meiyunji.sponsored.service.export.vo.perspective.TargetViewExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.ICategoryTargetViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl.ViewManageServiceImpl;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregateVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_TARGET_VIEW)
public class PerspectiveTargetViewExportTaskHandler implements AdManagePageExportTaskHandler {

    /**
     * 透视接口
     * @see com.meiyunji.sponsored.api.productPerspectiveAnalysis.ViewManageRpcService#getCategoryTargetView(CategoryTargetViewRequest, StreamObserver)
     * @see ViewManageServiceImpl#getAllCategoryTargetView(Integer, TargetViewParam)
     *
     * 广告管理下载
     * @see TargetPageExportTaskHandler#export(AdManagePageExportTask)
     */

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICategoryTargetViewService categoryTargetViewService;


    @Override
    public void export(AdManagePageExportTask task) {
        TargetViewParam param = JSONUtil.jsonToObject(task.getParam(), TargetViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 商品投放视图列表页 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        Integer puid = param.getPuid();
        String uuid = param.getUuid();
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);

        Page<CategoryTargetViewVo> page = categoryTargetViewService.getAllTargetViewPageVoList(puid, param);
        if (Objects.isNull(page)) {
            log.error(String.format("产品广告透视 商品投放视图列表页 export error, pageVO is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        if (CollectionUtils.isEmpty(page.getRows())) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();
        List<String> excludeFields = Lists.newArrayList();
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            excludeFields.addAll(Lists.newArrayList("selectType"));
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            excludeFields.addAll(Lists.newArrayList("selectType"));
            if (StringUtils.equalsIgnoreCase(param.getTargetText(), "与您推广商品类似的商品")) {
                excludeFields.addAll(Lists.newArrayList("categoryDetail"));
            }
        }
        if (StringUtils.isNotEmpty(param.getTargetText()) && Pattern.compile(Constants.ASIN_REGEX).matcher(param.getTargetText()).matches()) {
            excludeFields.addAll(Lists.newArrayList("categoryDetail"));
        }

        List<TargetViewExcelVO> dataList = page.getRows().stream().map(i -> new TargetViewExcelVO(currency, i)).collect(Collectors.toList());

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), TargetViewExcelVO.class,
                build.currencyNew(TargetViewExcelVO.class), excludeFields));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(uuid, new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }
}
