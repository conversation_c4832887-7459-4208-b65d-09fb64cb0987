package com.meiyunji.sponsored.service.attribution.entity;

import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_amazon_ad_attribution")
public class AmazonAdAttribution implements Serializable {

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 类型:商品页,店铺首页
     */
    @DbColumn(value = "type")
    private String type;

    /**
     * 广告主ID
     */
    @DbColumn(value = "advertiser_id")
    private String advertiserId;

    /**
     * 广告主名称
     */
    @DbColumn(value = "advertiser_name")
    private String advertiserName;

    /**
     * 渠道id
     */
    @DbColumn(value = "publisher_id")
    private String publisherId;

    /**
     * 渠道名称
     */
    @DbColumn(value = "publisher_name")
    private String publisherName;

    /**
     * 是否宏标签 0:否,1:是
     */
    @DbColumn(value = "macro_enable")
    private Integer macroEnable;

    /**
     * 名称
     */
    @DbColumn(value = "name")
    private String name;

    /**
     * 产品图片URL
     */
    @DbColumn(value = "img_url")
    private String imgUrl;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 广告组id
     */
    @DbColumn(value = "ad_group_id")
    private String adGroupId;

    /**
     * 创意ID
     */
    @DbColumn(value = "creative_id")
    private String creativeId;

    /**
     * asin
     */
    @DbColumn(value = "asin")
    private String asin;

    /**
     * 商品名称
     */
    @DbColumn(value = "product_name")
    private String productName;

    /**
     * msku
     */
    @DbColumn(value = "msku")
    private String msku;


    /**
     * msku
     */
    @DbColumn(value = "top_url")
    private String topUrl;


    /**
     * 推广URL
     */
    @DbColumn(value = "sponsored_url")
    private String sponsoredUrl;

    /**
     * 创建人名称
     */
    @DbColumn(value = "create_user_name")
    private String createUserName;

    /**
     * 备注
     */
    @DbColumn(value = "remark")
    private String remark;

    /**
     * 创建人id
     */
    @DbColumn(value = "create_id")
    private Integer createId;

    /**
     * 修改人id
     */
    @DbColumn(value = "update_id")
    private Integer updateId;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @DbColumn(value = "update_time")
    private LocalDateTime updateTime;

    public enum AttributionTypeEnum implements BaseEnum {
        product("product", "产品类型"),
        top("top","店铺首页类型");

        private String code;
        private String description;

        AttributionTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return description;
        }
    }

    public enum SearchTypeEnum implements BaseEnum {
        name("name","名称"),
        asin("asin","商品asin"),
        msku("msku","商品msku");

        private String code;
        private String description;

        SearchTypeEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getDescription() {
            return description;
        }
    }

    public enum OrderTypeEnum implements BaseEnum {
        asc("asc","升序"),
        desc("desc","降序");

        private String type;
        private String description;

        OrderTypeEnum(String type, String description) {
            this.type = type;
            this.description = description;
        }

        @Override
        public String getCode() {
            return type;
        }

        @Override
        public String getDescription() {
            return description;
        }

        public String getType() {
            return type;
        }
    }

    public enum OrderByEnum implements BaseEnum {
        views("views", "views"),
        totalView("totalView", "totalView"),
        click("click", "click"),
        addToCartNum("addToCartNum", "addToCartNum"),
        totalAddToCartNum("totalAddToCartNum", "totalAddToCartNum"),
        sold("sold", "sold"),
        totalSold("totalSold", "totalSold"),
        purchases("purchases", "purchases"),
        totalPurchases("totalPurchases", "totalPurchases"),
        sales("sales", "sales"),
        totalSales("totalSales", "totalSales"),
        createTime("createTime", "id");   //时间排序,使用主键排序

        private String name;
        private String column;

        OrderByEnum(String name, String column) {
            this.name = name;
            this.column = column;
        }


        @Override
        public String getCode() {
            return name;
        }

        @Override
        public String getDescription() {
            return column;
        }

        public String getColumn() {
            return column;
        }
    }
}