package com.meiyunji.sponsored.service.autoRule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 添加受控对象校验受控对象是否合法请求参数
 * @author: zzh
 * @date: 2024/12/16 11:35
 **/
@Data
public class CheckItemTemplateParam {

    @ApiModelProperty("商户id")
    private int puid;

    @ApiModelProperty("受控对象id集合")
    private List<ItemParam> itemList;

    @ApiModelProperty("受控对象类型")
    private String itemType;

    @ApiModelProperty("模板id")
    private Long templateId;

    @Data
    public static class ItemParam{

        @ApiModelProperty("受控对象id")
        private String itemId;

        @ApiModelProperty("店铺id")
        private Integer shopId;

        @ApiModelProperty("受控对象名称")
        private String itemName;

        @ApiModelProperty("广告类型")
        private String adType;

        @ApiModelProperty("投放类型")
        private String targetType;
    }
}
