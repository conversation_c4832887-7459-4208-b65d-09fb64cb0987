package com.meiyunji.sponsored.service.sellfoxApi.impl;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.enums.SellfoxApiEnum;
import com.meiyunji.sponsored.service.sellfoxApi.IAmazonBsrApi;
import com.meiyunji.sponsored.service.util.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.TreeMap;

@Service
@Slf4j
public class AmazonBsrApiImpl implements IAmazonBsrApi {

    @Value("${services.amzup.prefix}")
    private String amzupPrefix;

    @Override
    public String getByParentAsin(Integer puid, String marketplace, String asin, LocalDate localDate) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateStr = df.format(localDate);
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("marketplace",marketplace);
        map.put("asin",asin);
        map.put("date",dateStr);
        log.info("======================开始请求商品Bsr接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getBsrByParentAsin,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<String> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, Result.class);
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求Bsr评价接口出现异常======================", e);

        }
        log.info("======================结束请求Bsr评价接口======================");
        return null;
    }

    @Override
    public Map<String, String> getBsrByParentAsinMap(Integer puid, String marketplace, String asin, String dates) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("marketplace",marketplace);
        map.put("asin",asin);
        map.put("dates",dates);
        log.info("======================开始请求商品Bsr接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.getBsrByParentAsinMap,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<Map<String, String>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, Result.class);
            return result==null ? null : result.getData();
        } catch (IOException e) {
            log.info("======================请求Bsr评价接口出现异常======================", e);

        }
        log.info("======================结束请求Bsr评价接口======================");
        return null;
    }
}
