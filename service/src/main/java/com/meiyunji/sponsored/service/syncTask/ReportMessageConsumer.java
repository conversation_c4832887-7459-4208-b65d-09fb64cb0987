package com.meiyunji.sponsored.service.syncTask;

import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * @author: wade
 * @date: 2021/12/28 16:17
 * @describe: 广告报告同步数据处理
 */

@Slf4j
@ConditionalOnProperty(value = {"cos.buckets.data.bucket", "cos.app-id"})
@Component
public class ReportMessageConsumer extends AbstractSyncMessageConsumer<ReportReadyNotification> {

    private final StringRedisTemplate redisTemplate;

    public ReportMessageConsumer(AdvertiseSyncProcessFactory advertiseSyncProcessFactory, CosBucketClient dataBucketClient, StringRedisTemplate redisTemplate) {
        super(advertiseSyncProcessFactory, dataBucketClient);
        this.redisTemplate = redisTemplate;
    }


    public void process(ReportReadyNotification notification) throws Exception {
        if (notification.getMarketplaceIdentifier() == null || notification.getSellerIdentifier() == null) {
            //确保数据完整才处理入库,避免污染数据
            return;
        }
        String cacheKey = RedisConstant.SELLFOX_AMAZON_REPORT_DATA_PROCESS_LOCK.concat(notification.getSellerIdentifier()
                + "_" + notification.getMarketplaceIdentifier() + "_" + notification.getVersion() +"_"+
                (notification.getVersion() == 2 ? notification.getType().name() : notification.getV3Type().name())
                + "_" + (notification.getVersion() == 2 ? notification.getDate().format(DateTimeFormatter.ISO_LOCAL_DATE)
                : notification.getV3StartDate().format(DateTimeFormatter.ISO_LOCAL_DATE).concat("_")
                .concat(notification.getV3StartDate().format(DateTimeFormatter.ISO_LOCAL_DATE))));
        Boolean lock = null;
        try {
            lock = redisTemplate.opsForValue().setIfAbsent(cacheKey, "true", 2, TimeUnit.MINUTES);
            if (lock) {
                Instant now = Instant.now();
                advertiseSyncProcessFactory.getReportStrategy(notification).processReport(notification);
                log.info("puid: {} shopId: {} report-path: {} 广告报告持久化花费时间: {}",
                        notification.getSellerIdentifier(),notification.getMarketplaceIdentifier(),
                        notification.getPath(), Duration.between(now, Instant.now()).getSeconds());
            } else {
                log.error("Duplicate report version: {}, path: {}", notification.getVersion(), notification.getPath());
            }
        } finally {
           if (lock != null && lock) {
               redisTemplate.delete(cacheKey);
           }
        }
    }

}
