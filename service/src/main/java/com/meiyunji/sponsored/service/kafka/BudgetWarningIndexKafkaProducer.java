package com.meiyunji.sponsored.service.kafka;

import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;

import java.nio.charset.StandardCharsets;

public class BudgetWarningIndexKafkaProducer {
    private final String topic;
    private final KafkaTemplate<String, byte[]> kafkaTemplate;

    public BudgetWarningIndexKafkaProducer(String topic, KafkaTemplate<String, byte[]> kafkaTemplate) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(Object messageContent) throws Exception {
        kafkaTemplate.send(topic, GZipUtils.compressStr(JSONUtil.objectToJson(messageContent), 0).getBytes(StandardCharsets.UTF_8));
    }
}
