package com.meiyunji.sponsored.service.strategyTask.service.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleProcessTaskVo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdQueryAutoRuleVo;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.kafka.StrategyProcessTaskProducer;
import com.meiyunji.sponsored.service.kafka.message.AdvertiseStrategyProcessTaskMessage;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyAdGroupDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.strategy.enums.StartStopItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyAdGroup;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyQueryService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.bo.TemplateTaskHourglassBo;
import com.meiyunji.sponsored.service.strategyTask.dao.*;
import com.meiyunji.sponsored.service.strategyTask.enums.ItemType;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskAdType;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskItemType;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyRealTimeBid;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import com.meiyunji.sponsored.service.strategyTask.service.AdvertiseStrategyTaskService;
import com.meiyunji.sponsored.service.strategyTask.api.StrategyApiFactory;
import com.meiyunji.sponsored.service.strategyTask.vo.*;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  10:12
 */
@Service
@Slf4j
public class AdvertiseStrategyTaskServiceImpl implements AdvertiseStrategyTaskService {
    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private AdvertiseStrategyTaskRecordDao advertiseStrategyTaskRecordDao;
    @Autowired
    private AdvertiseStrategyTaskSequenceDao advertiseStrategyTaskSequenceDao;
    @Autowired
    private AdvertiseStrategyTaskRecordSequenceDao advertiseStrategyTaskRecordSequenceDao;
    @Autowired
    private RedisService redisService;
    @Autowired
    private StrategyProcessTaskProducer strategyProcessTaskProducer;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonAdStrategyTaskSupportDao amazonAdStrategyTaskSupportDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private AdvertiseStrategyRealTimeBidDao advertiseStrategyRealTimeBidDao;


    @Override
    public Result<List<AdvertiseStrategyTask>> queryTaskHourglass(QueryTaskHourglassParam param) {
        Result<List<AdvertiseStrategyTask>> result = new Result<>();
        try {
            List<AdvertiseStrategyTask> taskList = advertiseStrategyTaskDao.queryListByTemplateId(param);
            if (CollectionUtils.isNotEmpty(taskList)) {
                taskList.forEach(e->{
                    String cacheKey = String.format(RedisConstant.SPONSORED_AD_STRATEGY_SCHEDULE_KEY, e.getId());
                    Integer processCount = Optional.ofNullable(redisService.getInteger(cacheKey)).orElse(0);
                    e.setSchedule(MathUtil.percent(processCount, e.getCount()));
                });
            }
            result.setData(taskList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("分时策略受控对象页面查询任务沙漏接口异常:", e);
        }
        return result;
    }

    @Override
    public Result<Page<AdvertiseStrategyTaskRecord>> pageListByTaskId(Integer puid, Integer shopId, Long id, Integer pageNo, Integer pageSize) {
        Result<Page<AdvertiseStrategyTaskRecord>> result = new Result<>();
        try {
            AdvertiseStrategyTask advertiseStrategyTask = advertiseStrategyTaskDao.queryTaskById(puid, id);
            Page<AdvertiseStrategyTaskRecord> page = new Page<>();
            if ((advertiseStrategyTask.getTaskAction() == 11 || advertiseStrategyTask.getTaskAction() == 6) && advertiseStrategyTask.getItemType() == 5) {
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                List<AdvertiseStrategyTaskRecord> recordList = advertiseStrategyTaskRecordDao.getListByTaskId(puid, shopId, id);
                if (CollectionUtils.isNotEmpty(recordList)) {
                    List<Long> recordIdList = recordList.stream().map(AdvertiseStrategyTaskRecord::getId).collect(Collectors.toList());
                    List<String> adGroupIdList = recordList.stream().map(AdvertiseStrategyTaskRecord::getItemId).collect(Collectors.toList());
                    List<Long> realTimeBidIdList = Lists.newArrayList();
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIdList);
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIdList);
                    List<AmazonSdAdGroup> amazonSdAdGroupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIdList);
                    Map<String, AmazonAdGroup> amazonAdGroupMap = null;
                    Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
                    Map<String, AmazonSdAdGroup> amazonSdAdGroupMap = null;
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupMap = amazonAdGroupList.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                    }
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonSbAdGroupMap = amazonSbAdGroupList.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                    }
                    if (CollectionUtils.isNotEmpty(amazonSdAdGroupList)) {
                        amazonSdAdGroupMap = amazonSdAdGroupList.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                    }
                    List<AdvertiseStrategyRealTimeBid> advertiseStrategyRealTimeBidList = advertiseStrategyRealTimeBidDao.listByRecordId(puid, recordIdList);
                    List<AdvertiseStrategyTaskRecord> advertiseStrategyTaskRecordList = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(advertiseStrategyRealTimeBidList)) {
                        realTimeBidIdList = advertiseStrategyRealTimeBidList.stream().map(AdvertiseStrategyRealTimeBid::getRecordId).distinct().collect(Collectors.toList());
                        for (AdvertiseStrategyRealTimeBid advertiseStrategyRealTimeBid : advertiseStrategyRealTimeBidList) {
                            AdvertiseStrategyTaskRecord advertiseStrategyTaskRecord = new AdvertiseStrategyTaskRecord();
                            advertiseStrategyTaskRecord.setPuid(advertiseStrategyRealTimeBid.getPuid());
                            advertiseStrategyTaskRecord.setShopId(advertiseStrategyRealTimeBid.getShopId());
                            advertiseStrategyTaskRecord.setCreateTime(advertiseStrategyRealTimeBid.getCreateTime());
                            advertiseStrategyTaskRecord.setUpdateTime(advertiseStrategyRealTimeBid.getUpdateTime());
                            String itemName = AutoTargetTypeEnum.getAutoTargetValue(advertiseStrategyRealTimeBid.getTargetName());
                            if (StringUtils.isNotBlank(itemName)) {
                                advertiseStrategyTaskRecord.setItemName(itemName);
                            } else {
                                advertiseStrategyTaskRecord.setItemName(advertiseStrategyRealTimeBid.getTargetName());
                            }
                            advertiseStrategyTaskRecord.setItemId(advertiseStrategyRealTimeBid.getTargetId());
                            advertiseStrategyTaskRecord.setAdType(advertiseStrategyRealTimeBid.getAdType());
                            advertiseStrategyTaskRecord.setStateError(advertiseStrategyRealTimeBid.getStateError());
                            advertiseStrategyTaskRecord.setAddWayType("TARGET");
                            if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(advertiseStrategyRealTimeBid.getAdGroupId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonAdGroupMap.get(advertiseStrategyRealTimeBid.getAdGroupId()).getName());
                            } else if (MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(advertiseStrategyRealTimeBid.getAdGroupId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonSbAdGroupMap.get(advertiseStrategyRealTimeBid.getAdGroupId()).getName());
                            } else if (MapUtils.isNotEmpty(amazonSdAdGroupMap) && amazonSdAdGroupMap.containsKey(advertiseStrategyRealTimeBid.getAdGroupId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonSdAdGroupMap.get(advertiseStrategyRealTimeBid.getAdGroupId()).getName());
                            }
                            advertiseStrategyTaskRecordList.add(advertiseStrategyTaskRecord);
                        }
                    }
                    for (AdvertiseStrategyTaskRecord advertiseStrategyTaskRecord : recordList) {
                        if (CollectionUtils.isNotEmpty(realTimeBidIdList) && realTimeBidIdList.contains(advertiseStrategyTaskRecord.getId())) {
                            continue;
                        }
                        if (advertiseStrategyTaskRecord.getState().equals(-1)) {
                            if (MapUtils.isNotEmpty(amazonAdGroupMap) && amazonAdGroupMap.containsKey(advertiseStrategyTaskRecord.getItemId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonAdGroupMap.get(advertiseStrategyTaskRecord.getItemId()).getName());
                            }
                            if (MapUtils.isNotEmpty(amazonSbAdGroupMap) && amazonSbAdGroupMap.containsKey(advertiseStrategyTaskRecord.getItemId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonSbAdGroupMap.get(advertiseStrategyTaskRecord.getItemId()).getName());
                            }
                            if (MapUtils.isNotEmpty(amazonSdAdGroupMap) && amazonSdAdGroupMap.containsKey(advertiseStrategyTaskRecord.getItemId())) {
                                advertiseStrategyTaskRecord.setAdGroupName(amazonSdAdGroupMap.get(advertiseStrategyTaskRecord.getItemId()).getName());
                            }
                            advertiseStrategyTaskRecord.setAddWayType("TARGET");
                            advertiseStrategyTaskRecordList.add(advertiseStrategyTaskRecord);
                        }
                    }
                    page = PageUtil.getPage(page, advertiseStrategyTaskRecordList);
                }
            } else {
                page = advertiseStrategyTaskRecordDao.pageListByTaskId(puid, id, pageNo, pageSize);
                if (page != null && CollectionUtils.isNotEmpty(page.getRows())) {
                    List<String> itemId = page.getRows().stream().map(AdvertiseStrategyTaskRecord::getItemId).collect(Collectors.toList());
                    Map<String, AmazonAdPortfolio> amazonAdPortfolioMap = null;
                    Map<String, AmazonAdGroup> amazonAdGroupMap = null;
                    Map<String, AmazonSbAdGroup> amazonSbAdGroupMap = null;
                    Map<String, AmazonSdAdGroup> amazonSdAdGroupMap = null;
                    Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = null;
                    if (TaskItemType.PORTFOLIO.getCode().equals(page.getRows().get(0).getItemType())) {
                        List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.getPortfolioList(puid, shopId,itemId);
                        if (CollectionUtils.isNotEmpty(amazonAdPortfolioList)) {
                            amazonAdPortfolioMap = amazonAdPortfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (a, b)->a));
                        }
                    } else if (TaskItemType.TARGET.getCode().equals(page.getRows().get(0).getItemType())) {

                    } else if (TaskItemType.AD_GROUP_TARGET.getCode().equals(page.getRows().get(0).getItemType())){
                        List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getAdGroupByIds(puid, shopId, itemId);
                        List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, itemId);
                        List<AmazonSdAdGroup> amazonSdAdGroupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, itemId);
                        if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                            amazonAdGroupMap = amazonAdGroupList.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                        }
                        if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                            amazonSbAdGroupMap = amazonSbAdGroupList.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                        }
                        if (CollectionUtils.isNotEmpty(amazonSdAdGroupList)) {
                            amazonSdAdGroupMap = amazonSdAdGroupList.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId,Function.identity(),(e1,e2)->e1));
                        }
                    } else {
                        List<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(puid, shopId,itemId);
                        if (CollectionUtils.isNotEmpty(amazonAdCampaignAllList)) {
                            amazonAdCampaignAllMap = amazonAdCampaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (a, b)->a));
                        }
                    }
                    Map<String, AmazonAdCampaignAll> finalAmazonAdCampaignAllMap = amazonAdCampaignAllMap;
                    Map<String, AmazonAdPortfolio> finalAmazonAdPortfolioMap = amazonAdPortfolioMap;
                    Map<String, AmazonAdGroup> finalAmazonAdGroupMap = amazonAdGroupMap;
                    Map<String, AmazonSbAdGroup> finalAmazonSbAdGroupMap = amazonSbAdGroupMap;
                    Map<String, AmazonSdAdGroup> finalAmazonSdAdGroupMap = amazonSdAdGroupMap;
                    page.getRows().forEach(e->{
                        if (advertiseStrategyTask.getItemType() == 5) {
                            e.setAddWayType("AD_GROUP_TARGET");
                        } else if (advertiseStrategyTask.getItemType() == 2) {
                            e.setAddWayType("TARGET");
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdCampaignAllMap) && finalAmazonAdCampaignAllMap.containsKey(e.getItemId())) {
                            e.setItemName(finalAmazonAdCampaignAllMap.get(e.getItemId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdPortfolioMap) && finalAmazonAdPortfolioMap.containsKey(e.getItemId())) {
                            e.setItemName(finalAmazonAdPortfolioMap.get(e.getItemId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonAdGroupMap) && finalAmazonAdGroupMap.containsKey(e.getItemId())) {
                            e.setItemName(finalAmazonAdGroupMap.get(e.getItemId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonSbAdGroupMap) && finalAmazonSbAdGroupMap.containsKey(e.getItemId())) {
                            e.setItemName(finalAmazonSbAdGroupMap.get(e.getItemId()).getName());
                        }
                        if (MapUtils.isNotEmpty(finalAmazonSdAdGroupMap) && finalAmazonSdAdGroupMap.containsKey(e.getItemId())) {
                            e.setItemName(finalAmazonSdAdGroupMap.get(e.getItemId()).getName());
                        }
                    });
                }
            }
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("分时策略查询结果详情接口异常:", e);
        }
        return result;
    }

    @Override
    public Result<String> processTask(ProcessTaskParam param) {
        Result<String> result = new Result<>();
        try {
            Integer count = advertiseStrategyTaskDao.queryCountByTemplateId(param.getPuid(), param.getShopId(), param.getOldTemplateId(), param.getTaskAction());
            if (count > 0) {
                result.setCode(Result.ERROR);
                if (0 == param.getTaskAction() || 1 == param.getTaskAction()) {
                    result.setMsg("更新任务未完成，请稍后再试");
                } else if (2 == param.getTaskAction() || 3 == param.getTaskAction()) {
                    result.setMsg("移除任务未完成，请稍后再试");
                } else if (4 == param.getTaskAction() || 5 == param.getTaskAction()) {
                    result.setMsg("转移任务未完成，请稍后再试");
                } else if (6 == param.getTaskAction()) {
                    result.setMsg("应用实时竞价任务未完成，请稍后再试");
                } else if (7 == param.getTaskAction()) {
                    result.setMsg("修改原始竞价任务未完成，请稍后再试");
                } else if (8 == param.getTaskAction()) {
                    result.setMsg("应用实时预算任务未完成，请稍后再试");
                } else if (9 == param.getTaskAction()) {
                    result.setMsg("修改原始预算任务未完成，请稍后再试");
                } else  {
                    result.setMsg("当前任务未完成，请稍后再试");
                }
                return result;
            }
            AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.selectByPrimaryKey(param.getPuid(), param.getOldTemplateId());
            if (advertiseStrategyTemplate == null) {
                result.setCode(Result.ERROR);
                result.setMsg("当前模板已被删除");
                return result;
            }
            param.setItemType(advertiseStrategyTemplate.getItemType());
            param.setShopId(advertiseStrategyTemplate.getShopId());
            Map<Long, StatusVo> statusVoMap = null;
            if (CollectionUtils.isNotEmpty(param.getStatusVoList())) {
                statusVoMap = param.getStatusVoList().stream().collect(Collectors.toMap(StatusVo::getStatusId, Function.identity(), (key1, key2) -> key2));
            }
            Map<Long, StatusVo> finalStatusVoMap = statusVoMap;
            if ((param.getTaskAction() != 0
                    && param.getTaskAction() != 3
                    && param.getTaskAction() != 5
                    && param.getTaskAction() != 14
                    && param.getTaskAction() != 15) || "AD_GROUP_TARGET".equals(param.getAddWayType())) {
                List<Long> statusIdList = null;
                if ("AD_GROUP_TARGET".equals(param.getAddWayType())) {
                    if (param.getTaskAction() != 0 && param.getTaskAction() != 3 && param.getTaskAction() != 5 && param.getTaskAction() != 14 && param.getTaskAction() != 15) {
                        statusIdList = advertiseStrategyAdGroupDao.getIdByPuidAndShopIds(param.getPuid(), param.getShopId(), param.getOldTemplateId(), advertiseStrategyTemplate.getItemType(), param.getStatusVoList(), param.getExcludedStatusIdList());
                    } else {
                        statusIdList = getAdGroupStatusIdList(param);
                    }
                    if (CollectionUtils.isEmpty(statusIdList)) {
                        result.setCode(Result.ERROR);
                        result.setMsg("当前没有数据需要处理");
                        return result;
                    }
                } else {
                    statusIdList = advertiseStrategyStatusDao.getIdByPuidAndShopIds(param.getPuid(), param.getShopId(), param.getOldTemplateId(), advertiseStrategyTemplate.getItemType(), param.getStatusVoList(), param.getExcludedStatusIdList());
                    if (CollectionUtils.isEmpty(statusIdList)) {
                        result.setCode(Result.ERROR);
                        result.setMsg("当前没有数据需要处理");
                        return result;
                    }
                }
                List<AdvertiseStrategyTask> taskList = Lists.newArrayList();
                Long taskId = advertiseStrategyTaskSequenceDao.genId();
                AdvertiseStrategyTask advertiseStrategyTask = new AdvertiseStrategyTask();
                advertiseStrategyTask.setId(taskId);
                advertiseStrategyTask.setPuid(param.getPuid());
                advertiseStrategyTask.setShopId(param.getShopId());
                advertiseStrategyTask.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                advertiseStrategyTask.setTaskAction(param.getTaskAction());
                advertiseStrategyTask.setOldTemplateId(param.getOldTemplateId());
                advertiseStrategyTask.setNewTemplateId(param.getNewTemplateId());
                advertiseStrategyTask.setState(0);
                advertiseStrategyTask.setStatus(param.getStatus());
                advertiseStrategyTask.setLoginIp(param.getLoginIp());
                advertiseStrategyTask.setPreTaskId(param.getPreTaskId());
                if ("AD_GROUP_TARGET".equals(param.getAddWayType())) {
                    advertiseStrategyTask.setItemType(TaskItemType.getCode(param.getAddWayType()));
                } else {
                    advertiseStrategyTask.setItemType(TaskItemType.getCode(advertiseStrategyTemplate.getItemType()));
                }
                advertiseStrategyTask.setOperation(param.getOperation());
                advertiseStrategyTask.setCreateId(param.getUid());
                advertiseStrategyTask.setUpdateId(param.getUid());
                if ("AD_GROUP_TARGET".equals(param.getAddWayType())) {
                    insertAdGroupTargetTaskRecord(param, taskId, statusIdList, advertiseStrategyTask, finalStatusVoMap);
                } else {
                    insertTaskRecord(param, taskId, statusIdList, advertiseStrategyTask, finalStatusVoMap);
                }
                if (advertiseStrategyTask.getCount() <= 0) {
                    result.setCode(Result.ERROR);
                    result.setMsg("当前没有数据需要处理");
                    return result;
                }
                taskList.add(advertiseStrategyTask);
                advertiseStrategyTaskDao.batchInsert(advertiseStrategyTask.getPuid(), taskList);
            } else {
                int statusCount = 0;
                List<AdvertiseStrategyStatusTaskVo> advertiseStrategyStatusTaskVoList = getAdvertiseStrategyStatusTaskVoList(param);
                if (CollectionUtils.isNotEmpty(advertiseStrategyStatusTaskVoList)) {
                    statusCount = advertiseStrategyStatusTaskVoList.size();
                }
                if (statusCount <= 0) {
                    result.setCode(Result.ERROR);
                    result.setMsg("当前没有数据需要处理");
                    return result;
                }
                List<AdvertiseStrategyTask> taskList = Lists.newArrayList();
                Long taskId = advertiseStrategyTaskSequenceDao.genId();
                AdvertiseStrategyTask advertiseStrategyTask = new AdvertiseStrategyTask();
                advertiseStrategyTask.setId(taskId);
                advertiseStrategyTask.setPuid(param.getPuid());
                advertiseStrategyTask.setShopId(param.getShopId());
                advertiseStrategyTask.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                advertiseStrategyTask.setCount(statusCount);
                advertiseStrategyTask.setTaskAction(param.getTaskAction());
                advertiseStrategyTask.setOldTemplateId(param.getOldTemplateId());
                advertiseStrategyTask.setNewTemplateId(param.getNewTemplateId());
                advertiseStrategyTask.setState(0);
                advertiseStrategyTask.setStatus(param.getStatus());
                advertiseStrategyTask.setLoginIp(param.getLoginIp());
                advertiseStrategyTask.setItemType(TaskItemType.getCode(advertiseStrategyTemplate.getItemType()));
                advertiseStrategyTask.setOperation(param.getOperation());
                advertiseStrategyTask.setCreateId(param.getUid());
                advertiseStrategyTask.setUpdateId(param.getUid());
                List<List<AdvertiseStrategyStatusTaskVo>> statusIdPartition = Lists.partition(advertiseStrategyStatusTaskVoList, Constants.STRATEGY_PROCESS_TASK_ID_SIZE);
                ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getTimeSharingStrategyThreadPool();
                CountDownLatch countDownLatch = new CountDownLatch(statusIdPartition.size());
                for (List<AdvertiseStrategyStatusTaskVo> advertiseStrategyStatusTaskVos : statusIdPartition) {
                    threadPoolExecutor.execute(() -> {
                        try {
                            insertTaskRecordAllUpdate(param, taskId, advertiseStrategyStatusTaskVos, advertiseStrategyTemplate, finalStatusVoMap);
                        } catch (Exception e) {
                            log.error("processTaskThread request fail: {}, puid: {}, taskId{}", e.getMessage(), param.getPuid(), taskId);
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
                }
                countDownLatch.await();
                taskList.add(advertiseStrategyTask);
                advertiseStrategyTaskDao.batchInsert(advertiseStrategyTask.getPuid(), taskList);
            }
            amazonAdStrategyTaskSupportDao.insertOrUpdate(param.getPuid());
            //发送消息
            AdvertiseStrategyProcessTaskMessage message = new AdvertiseStrategyProcessTaskMessage();
            message.setPuid(param.getPuid());
            message.setShopId(param.getShopId());
            message.setTemplateId(param.getOldTemplateId());
            strategyProcessTaskProducer.send(message);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("分时策略异步处理任务异常:", e);
        }
        return result;
    }

    private List<AdvertiseStrategyStatusTaskVo> getAdvertiseStrategyStatusTaskVoList(ProcessTaskParam param) {
        //组合转活动id
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds()) && CollectionUtils.isEmpty(param.getCampaignIds())) {
            param.setCampaignIds(amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(param.getPuid(), param.getShopId(), param.getPortfolioIds()));
        }
        //查询受控对象
        List<ProcessTaskVo> processTaskVoList = advertiseStrategyStatusDao.getStatusIdsByProcessTaskParam(param);

        if (CollectionUtils.isEmpty(processTaskVoList)) {
            return Lists.newArrayList();
        }

        //广告产品启停类型
        String startStopItemType = processTaskVoList.get(0).getStartStopItemType();
        if (param.getItemType().equals(ItemType.START_STOP.name()) && StartStopItemTypeEnum.PRODUCT.getValue().equals(startStopItemType)) {
            return advertiseStrategyStatusDao.getAdvertiseStrategyStatusTaskVoList4ProductStartStop(param);
        }

        //受控对象id
        param.setItemIdList(processTaskVoList.stream().map(ProcessTaskVo::getItemId).collect(Collectors.toList()));
        List<String> itemIdList = Lists.newArrayList();

        //再去反查基础表，进行模糊查询searchValue
        if (param.getItemType().equals(ItemType.CAMPAIGN.name()) || param.getItemType().equals(ItemType.CAMPAIGN_PLACEMENT.name()) || param.getItemType().equals(ItemType.START_STOP.name())) {
            if (StringUtils.isNotBlank(param.getSearchValue())) {
                itemIdList = amazonAdCampaignAllDao.getCampaignIds(param);
            }
        } else if (param.getItemType().equals(ItemType.TARGET.name())) {
            if (StringUtils.isNotBlank(param.getMatchType())) {
                itemIdList.addAll(amazonAdKeywordShardingDao.getKeywordIdList(param));
                itemIdList.addAll(amazonSbAdKeywordDao.getKeywordIdList(param));
            }
        } else if (param.getItemType().equals(ItemType.PORTFOLIO.name())) {
            if (StringUtils.isNotBlank(param.getSearchValue())) {
                itemIdList = amazonAdPortfolioDao.getPortfolioIdList(param);
            }
        }

        //过滤itemId
        List<Long> statusIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            for (ProcessTaskVo vo : processTaskVoList) {
                if (itemIdList.contains(vo.getItemId())) {
                    statusIdList.add(vo.getStatusId());
                }
            }
        } else {
            statusIdList = processTaskVoList.stream().map(ProcessTaskVo::getStatusId).collect(Collectors.toList());
        }

        //再进行一次受控对象查询
        return advertiseStrategyStatusDao.getAdvertiseStrategyStatusTaskVoList(param.getPuid(),  param.getOldTemplateId(), 0L, statusIdList);
    }

    private List<Long> getAdGroupStatusIdList(ProcessTaskParam param) {
        List<String> adAllGroupIdList = Lists.newArrayList();
        List<Long> statusIdList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(param.getPortfolioIds()) && CollectionUtils.isEmpty(param.getCampaignIds())) {
            param.setCampaignIds(amazonAdCampaignAllDao.getCampaignIdListByPortfolioIdNoType(param.getPuid(), param.getShopId(), param.getPortfolioIds()));
        }
        List<String> adGroupIdList = null;
        List<String> adSbGroupIdList = null;
        List<String> adSdGroupIdList = null;
        if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SP.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
            adGroupIdList = amazonAdGroupDao.queryAdGroupIdList(param);
            adAllGroupIdList.addAll(adGroupIdList);
        }
        if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SB.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
            adSbGroupIdList = amazonSbAdGroupDao.queryAdGroupIdList(param);
            adAllGroupIdList.addAll(adSbGroupIdList);
        }
        if (CollectionUtils.isEmpty(param.getAdTypeList()) || AdType.SD.name().equalsIgnoreCase(param.getAdTypeList().get(0))) {
            adSdGroupIdList = amazonSdAdGroupDao.queryAdGroupIdList(param);
            adAllGroupIdList.addAll(adSdGroupIdList);
        }
        param.setGroupIds(adAllGroupIdList);
        if ("exact".equals(param.getAdGroupScreen())) {
            param.setItemIdList(advertiseStrategyStatusDao.queryIsTargetItem(param.getPuid(), param.getGroupIds()));
        }
        statusIdList = advertiseStrategyAdGroupDao.getAdGroupStatusIdList(param);
        return statusIdList;
    }

    @Override
    public Result<List<TemplateTaskHourglassBo>> queryTemplateTaskHourglass(Integer puid, List<Long> templateIdList) {
        Result<List<TemplateTaskHourglassBo>> result = new Result<>();
        try {
            List<AdvertiseStrategyTask> taskList = advertiseStrategyTaskDao.queryListByTemplateIdList(puid, templateIdList);
            List<TemplateTaskHourglassBo> templateTaskHourglassBoList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(taskList)) {
                Map<Long, List<AdvertiseStrategyTask>> taskMap = taskList.stream().collect(Collectors.groupingBy(AdvertiseStrategyTask::getOldTemplateId));
                for (Long id :taskMap.keySet()){
                    TemplateTaskHourglassBo bo = new TemplateTaskHourglassBo();
                    List<Integer> stateList = taskMap.get(id).stream().map(AdvertiseStrategyTask::getState).collect(Collectors.toList());
                    bo.setTemplateId(id);
                    if (stateList.contains(0)) {
                        bo.setState(0);
                    } else {
                        bo.setState(1);
                    }
                    templateTaskHourglassBoList.add(bo);
                }
            }
            result.setData(templateTaskHourglassBoList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("分时策略模板页面查询任务沙漏接口异常:", e);
        }
        return result;
    }

    @Override
    public Result<QueryAdGroupRealTimeBidResult> queryAdGroupRealTimeBidState(Integer puid, Integer shopId, Integer isTaskRequest, Integer createId, String loginIp, Long templateId, Long taskId, List<QueryAdGroupRealTimeBidStateParam> strategyStatusVoList) {
        Result<QueryAdGroupRealTimeBidResult> result = new Result<>();
        QueryAdGroupRealTimeBidResult queryAdGroupRealTimeBidResult = new QueryAdGroupRealTimeBidResult();
        List<QueryAdGroupRealTimeBidStateResult> queryAdGroupRealTimeBidStateResults = Lists.newArrayList();
        try {
            AdvertiseStrategyTemplate advertiseStrategyTemplate = advertiseStrategyTemplateDao.selectByPrimaryKey(puid, templateId);
            if (advertiseStrategyTemplate == null) {
                result.setCode(Result.ERROR);
                result.setMsg("当前模板已被删除");
                return result;
            }
            //正常请求
            if (0 == isTaskRequest) {
                Map<Long, QueryAdGroupRealTimeBidStateParam> statusVoMap = null;
                if (CollectionUtils.isNotEmpty(strategyStatusVoList)) {
                    statusVoMap = strategyStatusVoList.stream().collect(Collectors.toMap(QueryAdGroupRealTimeBidStateParam::getStatusId, Function.identity(), (key1, key2) -> key2));
                }
                Map<Long, QueryAdGroupRealTimeBidStateParam> finalStatusVoMap = statusVoMap;
                List<AdvertiseStrategyTask> taskList = Lists.newArrayList();
                Long id = advertiseStrategyTaskSequenceDao.genId();
                queryAdGroupRealTimeBidResult.setPreTaskId(id);
                AdvertiseStrategyTask advertiseStrategyTask = new AdvertiseStrategyTask();
                advertiseStrategyTask.setId(id);
                advertiseStrategyTask.setPuid(puid);
                advertiseStrategyTask.setShopId(shopId);
                advertiseStrategyTask.setMarketplaceId(advertiseStrategyTemplate.getMarketplaceId());
                advertiseStrategyTask.setCount(strategyStatusVoList.size());
                advertiseStrategyTask.setTaskAction(11);
                advertiseStrategyTask.setOldTemplateId(templateId);
                advertiseStrategyTask.setState(0);
                advertiseStrategyTask.setItemType(TaskItemType.getCode("AD_GROUP_TARGET"));
                advertiseStrategyTask.setCreateId(createId);
                advertiseStrategyTask.setUpdateId(createId);
                advertiseStrategyTask.setLoginIp(loginIp);
                taskList.add(advertiseStrategyTask);
                advertiseStrategyTaskDao.batchInsert(advertiseStrategyTask.getPuid(), taskList);
                List<AdvertiseStrategyAdGroup> list = advertiseStrategyAdGroupDao.getListByLongIdList(puid, strategyStatusVoList.stream().map(QueryAdGroupRealTimeBidStateParam::getStatusId).collect(Collectors.toList()));
                List<AdvertiseStrategyTaskRecord> recordList = Lists.newArrayList();
                List<Long> recordIdList = advertiseStrategyTaskRecordSequenceDao.batchGenId(list.size());
                for (int i = 0; i < list.size(); i++) {
                    QueryAdGroupRealTimeBidStateResult queryAdGroupRealTimeBidStateResult = new QueryAdGroupRealTimeBidStateResult();
                    Long recordId = recordIdList.get(i);
                    AdvertiseStrategyAdGroup advertiseStrategyAdGroup = list.get(i);
                    AdvertiseStrategyTaskRecord record = new AdvertiseStrategyTaskRecord();
                    record.setId(recordId);
                    record.setPuid(puid);
                    record.setShopId(shopId);
                    record.setMarketplaceId(advertiseStrategyAdGroup.getMarketplaceId());
                    record.setRetryCount(0);
                    record.setState(0);
                    record.setTaskId(id);
                    record.setStateError("");
                    Integer code = TaskAdType.getCode(advertiseStrategyAdGroup.getAdType());
                    if (code == null) {
                        record.setAdType(3);
                    } else {
                        record.setAdType(TaskAdType.getCode(advertiseStrategyAdGroup.getAdType()));
                    }
                    record.setItemType(TaskItemType.AD_GROUP_TARGET.getCode());
                    record.setStatusId(advertiseStrategyAdGroup.getId());
                    record.setStatus(advertiseStrategyAdGroup.getStatus());
                    record.setStatusTaskId(advertiseStrategyAdGroup.getTaskId());
                    record.setIsRetry(0);
                    record.setItemId(advertiseStrategyAdGroup.getAdGroupId());
                    if (MapUtils.isNotEmpty(finalStatusVoMap) && finalStatusVoMap.containsKey(advertiseStrategyAdGroup.getId())) {
                        QueryAdGroupRealTimeBidStateParam queryAdGroupRealTimeBidStateParam = finalStatusVoMap.get(advertiseStrategyAdGroup.getId());
                        queryAdGroupRealTimeBidStateResult.setGroupName(queryAdGroupRealTimeBidStateParam.getGroupName());
                        queryAdGroupRealTimeBidStateResult.setStatusId(advertiseStrategyAdGroup.getId());
                        queryAdGroupRealTimeBidStateResult.setRealTimeBidState(0);
                    }
                    queryAdGroupRealTimeBidStateResults.add(queryAdGroupRealTimeBidStateResult);
                    recordList.add(record);
                }
                advertiseStrategyTaskRecordDao.batchInsert(puid, recordList, false);
                amazonAdStrategyTaskSupportDao.insertOrUpdate(puid);
                //发送消息
                AdvertiseStrategyProcessTaskMessage message = new AdvertiseStrategyProcessTaskMessage();
                message.setPuid(puid);
                message.setShopId(shopId);
                message.setTemplateId(templateId);
                strategyProcessTaskProducer.send(message);
            } else if (1 == isTaskRequest) {
                Map<Long, QueryAdGroupRealTimeBidStateParam> statusVoMap = null;
                if (CollectionUtils.isNotEmpty(strategyStatusVoList)) {
                    statusVoMap = strategyStatusVoList.stream().collect(Collectors.toMap(QueryAdGroupRealTimeBidStateParam::getStatusId, Function.identity(), (key1, key2) -> key2));
                }
                AdvertiseStrategyTask task = advertiseStrategyTaskDao.queryTaskById(puid, taskId);
                List<AdvertiseStrategyTaskRecord> recordList = advertiseStrategyTaskRecordDao.getListByTaskId(puid, shopId, task.getId());
                if (CollectionUtils.isNotEmpty(recordList)) {
                    for (AdvertiseStrategyTaskRecord record : recordList) {
                        if (MapUtils.isNotEmpty(statusVoMap) && statusVoMap.containsKey(record.getStatusId())) {
                            QueryAdGroupRealTimeBidStateResult queryAdGroupRealTimeBidStateResult = new QueryAdGroupRealTimeBidStateResult();
                            QueryAdGroupRealTimeBidStateParam queryAdGroupRealTimeBidStateParam = statusVoMap.get(record.getStatusId());
                            queryAdGroupRealTimeBidStateResult.setGroupName(queryAdGroupRealTimeBidStateParam.getGroupName());
                            queryAdGroupRealTimeBidStateResult.setStatusId(record.getStatusId());
                            if (record.getState() == 0) {
                                queryAdGroupRealTimeBidStateResult.setRealTimeBidState(0);
                            } else {
                                queryAdGroupRealTimeBidStateResult.setRealTimeBidState(1);
                            }
                            queryAdGroupRealTimeBidStateResults.add(queryAdGroupRealTimeBidStateResult);
                        }
                    }
                }
                queryAdGroupRealTimeBidResult.setPreTaskId(taskId);
            }
            queryAdGroupRealTimeBidResult.setQueryAdGroupRealTimeBidStateResultList(queryAdGroupRealTimeBidStateResults);
            result.setCode(Result.SUCCESS);
            result.setData(queryAdGroupRealTimeBidResult);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("分时策略获取实时竞价接口异常:", e);
        }
        return result;
    }

    @Override
    public Result<Page<QueryAdGroupTargetStrategyResult>> queryAdGroupTargetStrategy(QueryAdGroupTargetStrategyParam param) {
        Result<Page<QueryAdGroupTargetStrategyResult>> result = new Result<>();
        try {
            Page<QueryAdGroupTargetStrategyResult> page = new Page<>();
            page.setPageNo(param.getPageNo());
            page.setPageSize(param.getPageSize());
            Page<AdvertiseStrategyStatus> advertiseStrategyStatusPage = advertiseStrategyStatusDao.pageAdGroupTargetStrategy(param);
            if (advertiseStrategyStatusPage != null) {
                page.setTotalPage(advertiseStrategyStatusPage.getTotalPage());
                page.setTotalSize(advertiseStrategyStatusPage.getTotalSize());
                if (CollectionUtils.isNotEmpty(advertiseStrategyStatusPage.getRows())) {
                    List<Long> templateIdList = advertiseStrategyStatusPage.getRows().stream().map(AdvertiseStrategyStatus::getTemplateId).distinct().collect(Collectors.toList());
                    List<AdvertiseStrategyTemplate> advertiseStrategyTemplateList = advertiseStrategyTemplateDao.getListByTemplateId(param.getPuid(), templateIdList);
                    Map<Long, AdvertiseStrategyTemplate> advertiseStrategyTemplateMap = null;
                    if (CollectionUtils.isNotEmpty(advertiseStrategyTemplateList)) {
                        advertiseStrategyTemplateMap = advertiseStrategyTemplateList.stream().collect(Collectors.toMap(AdvertiseStrategyTemplate::getId, Function.identity(), (e1,e2)->e1));
                    }
                    List<QueryAdGroupTargetStrategyResult> queryAdGroupTargetStrategyResults = Lists.newArrayList();
                    for (AdvertiseStrategyStatus advertiseStrategyStatus : advertiseStrategyStatusPage.getRows()) {
                        QueryAdGroupTargetStrategyResult vo = new QueryAdGroupTargetStrategyResult();
                        vo.setTargetId(advertiseStrategyStatus.getItemId());
                        vo.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
                        if ("autoTarget".equals(advertiseStrategyStatus.getTargetType())) {
                            String targetName = AutoTargetTypeEnum.getAutoTargetValue(advertiseStrategyStatus.getTargetName());
                            if (StringUtils.isNotBlank(targetName)) {
                                advertiseStrategyStatus.setTargetName(targetName);
                            } else {
                                advertiseStrategyStatus.setTargetName(advertiseStrategyStatus.getTargetName());
                            }
                        } else {
                            vo.setTargetName(advertiseStrategyStatus.getTargetName());
                        }
                        vo.setTargetName(advertiseStrategyStatus.getTargetName());

                        vo.setRule(advertiseStrategyStatus.getRule());
                        vo.setTemplateId(advertiseStrategyStatus.getTemplateId());
                        if (MapUtils.isNotEmpty(advertiseStrategyTemplateMap) && advertiseStrategyTemplateMap.containsKey(advertiseStrategyStatus.getTemplateId())) {
                            vo.setTemplateName(advertiseStrategyTemplateMap.get(advertiseStrategyStatus.getTemplateId()).getTemplateName());
                        }
                        queryAdGroupTargetStrategyResults.add(vo);
                    }
                    page.setRows(queryAdGroupTargetStrategyResults);
                }
            }
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            log.error("查询当前广告组部分投放脱离广告组策略异常:", e);
        }
        return result;
    }

    private void insertTaskRecord(ProcessTaskParam param, Long taskId,List<Long> statusIds, AdvertiseStrategyTask task, Map<Long, StatusVo> finalStatusVoMap) {
        List<AdvertiseStrategyStatus> list = advertiseStrategyStatusDao.getListByLongIdList(param.getPuid(), statusIds);
        task.setCount(list.size());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<AdvertiseStrategyTaskRecord> recordList = Lists.newArrayList();
        List<Long> recordIdList = advertiseStrategyTaskRecordSequenceDao.batchGenId(list.size());
        for (int i = 0; i < list.size(); i++) {
            Long recordId = recordIdList.get(i);
            AdvertiseStrategyStatus advertiseStrategyStatus = list.get(i);
            AdvertiseStrategyTaskRecord record = new AdvertiseStrategyTaskRecord();
            record.setId(recordId);
            record.setPuid(param.getPuid());
            record.setShopId(param.getShopId());
            record.setMarketplaceId(advertiseStrategyStatus.getMarketplaceId());
            record.setRetryCount(0);
            record.setState(0);
            record.setTaskId(taskId);
            record.setStateError("");
            Integer code = TaskAdType.getCode(advertiseStrategyStatus.getAdType());
            if (code == null) {
                record.setAdType(3);
            } else {
                record.setAdType(TaskAdType.getCode(advertiseStrategyStatus.getAdType()));
            }
            record.setItemType(TaskItemType.getCode(advertiseStrategyStatus.getItemType()));
            record.setStatusId(advertiseStrategyStatus.getId());
            record.setStatus(advertiseStrategyStatus.getStatus());
            record.setStatusTaskId(advertiseStrategyStatus.getTaskId());
            record.setTargetType(advertiseStrategyStatus.getTargetType());
            record.setIsRetry(0);
            record.setItemId(advertiseStrategyStatus.getItemId());
            record.setItemName(advertiseStrategyStatus.getTargetName());
            if (MapUtils.isNotEmpty(finalStatusVoMap) && finalStatusVoMap.containsKey(advertiseStrategyStatus.getId())) {
                StatusVo statusVo = finalStatusVoMap.get(advertiseStrategyStatus.getId());
                if (statusVo.getOriginBudgetValue() != null ) {
                    record.setOriginBudgetValue(statusVo.getOriginBudgetValue());
                }
                if (statusVo.getOriginAdPlaceTopValue() != null ) {
                    record.setOriginAdPlaceTopValue(statusVo.getOriginAdPlaceTopValue());
                }
                if (statusVo.getOriginAdPlaceProductValue() != null ) {
                    record.setOriginAdPlaceProductValue(statusVo.getOriginAdPlaceProductValue());
                }
                if (StringUtils.isNotBlank(statusVo.getOriginStrategy())) {
                    record.setOriginStrategy(statusVo.getOriginStrategy());
                }
                if (statusVo.getOriginBiddingValue() != null) {
                    record.setOriginBiddingValue(statusVo.getOriginBiddingValue());
                }
                if (StringUtils.isNotBlank(statusVo.getOriginState())) {
                    record.setOriginState(statusVo.getOriginState());
                }
            }
            recordList.add(record);
        }
        advertiseStrategyTaskRecordDao.batchInsert(param.getPuid(), recordList, false);
    }

    private void insertTaskRecordAllUpdate(ProcessTaskParam param, Long taskId,List<AdvertiseStrategyStatusTaskVo> list , AdvertiseStrategyTemplate advertiseStrategyTemplate, Map<Long, StatusVo> finalStatusVoMap) {
        List<AdvertiseStrategyTaskRecord> recordList = Lists.newArrayList();
        List<Long> recordIdList = advertiseStrategyTaskRecordSequenceDao.batchGenId(list.size());
        for (int i = 0; i < list.size(); i++) {
            Long recordId = recordIdList.get(i);
            AdvertiseStrategyStatusTaskVo advertiseStrategyStatusTaskVo = list.get(i);
            AdvertiseStrategyTaskRecord record = new AdvertiseStrategyTaskRecord();
            record.setId(recordId);
            record.setPuid(param.getPuid());
            record.setShopId(param.getShopId());
            record.setMarketplaceId(advertiseStrategyStatusTaskVo.getMarketplaceId());
            record.setRetryCount(0);
            record.setState(0);
            record.setTaskId(taskId);
            record.setStateError("");
            Integer code = TaskAdType.getCode(advertiseStrategyStatusTaskVo.getAdType());
            if (code == null) {
                record.setAdType(3);
            } else {
                record.setAdType(TaskAdType.getCode(advertiseStrategyStatusTaskVo.getAdType()));
            }
            record.setItemType(TaskItemType.getCode(advertiseStrategyStatusTaskVo.getItemType()));
            record.setStatusId(advertiseStrategyStatusTaskVo.getId());
            record.setStatus(advertiseStrategyStatusTaskVo.getStatus());
            record.setStatusTaskId(advertiseStrategyStatusTaskVo.getTaskId());
            record.setTargetType(advertiseStrategyStatusTaskVo.getTargetType());
            record.setIsRetry(0);
            record.setItemId(advertiseStrategyStatusTaskVo.getItemId());
            record.setItemName(advertiseStrategyStatusTaskVo.getTargetName());
            if (MapUtils.isNotEmpty(finalStatusVoMap) && finalStatusVoMap.containsKey(advertiseStrategyStatusTaskVo.getId())) {
                StatusVo statusVo = finalStatusVoMap.get(advertiseStrategyStatusTaskVo.getId());
                if (statusVo.getOriginBudgetValue() != null ) {
                    record.setOriginBudgetValue(statusVo.getOriginBudgetValue());
                }
                if (statusVo.getOriginAdPlaceTopValue() != null ) {
                    record.setOriginAdPlaceTopValue(statusVo.getOriginAdPlaceTopValue());
                }
                if (statusVo.getOriginAdPlaceProductValue() != null ) {
                    record.setOriginAdPlaceProductValue(statusVo.getOriginAdPlaceProductValue());
                }
                if (StringUtils.isNotBlank(statusVo.getOriginStrategy())) {
                    record.setOriginStrategy(statusVo.getOriginStrategy());
                }
                if (statusVo.getOriginBiddingValue() != null) {
                    record.setOriginBiddingValue(statusVo.getOriginBiddingValue());
                }
                if (StringUtils.isNotBlank(statusVo.getOriginState())) {
                    record.setOriginState(statusVo.getOriginState());
                }
            }
            recordList.add(record);
        }
        advertiseStrategyTaskRecordDao.batchInsert(param.getPuid(), recordList, false);
    }


    private void insertAdGroupTargetTaskRecord(ProcessTaskParam param, Long taskId,List<Long> statusIds, AdvertiseStrategyTask task, Map<Long, StatusVo> finalStatusVoMap) {
        List<AdvertiseStrategyAdGroup> list = advertiseStrategyAdGroupDao.getListByLongIdList(param.getPuid(), statusIds);
        List<AdvertiseStrategyTaskRecord> recordList = Lists.newArrayList();
        List<Long> recordIdList = advertiseStrategyTaskRecordSequenceDao.batchGenId(list.size());
        for (int i = 0; i < list.size(); i++) {
            Long recordId = recordIdList.get(i);
            AdvertiseStrategyAdGroup advertiseStrategyAdGroup = list.get(i);
            AdvertiseStrategyTaskRecord record = new AdvertiseStrategyTaskRecord();
            record.setId(recordId);
            record.setPuid(param.getPuid());
            record.setShopId(param.getShopId());
            record.setMarketplaceId(advertiseStrategyAdGroup.getMarketplaceId());
            record.setRetryCount(0);
            record.setState(0);
            record.setTaskId(taskId);
            record.setStateError("");
            Integer code = TaskAdType.getCode(advertiseStrategyAdGroup.getAdType());
            if (code == null) {
                record.setAdType(3);
            } else {
                record.setAdType(TaskAdType.getCode(advertiseStrategyAdGroup.getAdType()));
            }
            record.setItemType(TaskItemType.getCode(param.getAddWayType()));
            record.setStatusId(advertiseStrategyAdGroup.getId());
            record.setStatus(advertiseStrategyAdGroup.getStatus());
            record.setStatusTaskId(advertiseStrategyAdGroup.getTaskId());
            record.setIsRetry(0);
            record.setItemId(advertiseStrategyAdGroup.getAdGroupId());
            recordList.add(record);
        }
        task.setCount(recordIdList.size());
        advertiseStrategyTaskRecordDao.batchInsert(param.getPuid(), recordList, false);
    }


}
