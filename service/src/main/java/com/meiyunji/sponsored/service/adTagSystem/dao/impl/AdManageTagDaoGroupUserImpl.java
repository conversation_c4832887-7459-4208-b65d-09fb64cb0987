package com.meiyunji.sponsored.service.adTagSystem.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.adTagSystem.dao.IAdManageTagGroupUserDao;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagGroupUser;
import com.meiyunji.sponsored.service.cpc.vo.TagGroupVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-22  22:44
 */
@Repository
public class AdManageTagDaoGroupUserImpl extends BaseShardingDaoImpl<AdManageTagGroupUser> implements IAdManageTagGroupUserDao {

    @Override
    public void batchInsert(int puid, List<AdManageTagGroupUser> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_ad_manage_tag_group_user` (`puid`, `type`, `group_id`, `user_id`)");
        sql.append(" VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AdManageTagGroupUser groupUser : list) {
            sql.append("(?,?,?,?),");
            argsList.add(groupUser.getPuid());
            argsList.add(groupUser.getType());
            argsList.add(groupUser.getGroupId());
            argsList.add(groupUser.getUserId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update ")
                .append("`create_time`=now(),`update_time`=now(),`type`=? ");
        argsList.add(list.get(0).getType());
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void delByPuidGroupId(int puid, Long groupId) {
        List<Object> argsList = Lists.newArrayList();
        String sql = "delete from t_ad_manage_tag_group_user where puid=? and group_id=?";
        argsList.add(puid);
        argsList.add(groupId);
        getJdbcTemplate(puid).update(sql, argsList.toArray());
    }

    @Override
    public void delByPuidGroupIds(int puid, List<Long> groupIds) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("delete from t_ad_manage_tag_group_user where puid=? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("group_id", groupIds, argsList));
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<Long> listGroupIdByUid(Integer puid, Integer type, Integer uid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("type", type)
                .equalTo("user_id", uid);
        return this.listDistinctFieldByCondition(puid, "group_id", builder.build(), Long.class);
    }

    @Override
    public List<Long> listGroupIdByPuid(Integer puid, Integer type) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("type", type);
        return this.listDistinctFieldByCondition(puid, "group_id", builder.build(), Long.class);
    }

    @Override
    public List<Long> listGroupIdByUid(Integer puid, Integer uid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("user_id", uid);
        List<Long> list = listDistinctFieldByCondition(puid, "group_id", builder.build(), Long.class);
        return CollectionUtils.isNotEmpty(list) ? list : Lists.newArrayList();
    }

    @Override
    public List<AdManageTagGroupUser> listByGroupIds(Integer puid, List<Long> groupIds) {
        List<Object> args = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select * from t_ad_manage_tag_group_user where puid=?");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("group_id", groupIds, args));
        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(AdManageTagGroupUser.class));
    }

    @Override
    public List<Long> getTagGroup(int puid, Integer uid, Integer type) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select group_id id from t_ad_manage_tag_group_user ");
        sql.append(" where puid=? and type = ?");
        argsList.add(puid);
        argsList.add(type);
        if(uid != null){
            sql.append(" and user_id =?");
            argsList.add(uid);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), Long.class, argsList.toArray());
    }

    @Override
    public boolean hasGroupPermission(int puid, int uid, long groupId) {
        String sql = "select 1 from t_ad_manage_tag_group_user where puid=? and user_id=? and group_id=? limit 1";
        try {
            Integer i = getJdbcTemplate(puid).queryForObject(sql, Integer.class, puid, uid, groupId);
            return Objects.nonNull(i) && i > 0;
        } catch (EmptyResultDataAccessException e) {
            return false;
        }
    }

    @Override
    public List<Long> filterPermissionGroupIds(int puid, int uid, List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .in("group_id", groupIds.toArray())
                .equalTo("user_id", uid);
        List<Long> list = listDistinctFieldByCondition(puid, "group_id", builder.build(), Long.class);
        return CollectionUtils.isNotEmpty(list) ? list : Lists.newArrayList();
    }

    @Override
    public List<Integer> listUserIdByGroupId(Integer puid, Long groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("group_id", groupId);
        List<Integer> list = listDistinctFieldByCondition(puid, "user_id", builder.build(), Integer.class);
        return CollectionUtils.isNotEmpty(list) ? list : Lists.newArrayList();
    }
}