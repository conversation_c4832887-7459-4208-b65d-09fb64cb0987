package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.doris.dao.IDimCurrencyRateDao;
import com.meiyunji.sponsored.service.doris.po.DimCurrencyRate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 汇率表-来源数据组doris 数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-29 10:46:22
 */
@Repository
public class DimCurrencyRateDaoImpl extends DorisBaseDaoImpl<DimCurrencyRate> implements IDimCurrencyRateDao {


    @Override
    public DimCurrencyRate getLastRate(Integer puid, String from, String to) {
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        sql.append(" select rate from dim_currency_rate where ")
                .append(" puid = ? ")
                .append(" and `from` = ?")
                .append(" and `to` = ?")
                .append(" order by month desc limit 1");
        argsList.add(puid);
        argsList.add(from);
        argsList.add(to);
        List<DimCurrencyRate> decimalList = getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(DimCurrencyRate.class));
        return CollectionUtils.isNotEmpty(decimalList) ? decimalList.get(0) : null;
    }



    @Override
    public DimCurrencyRate getRateByMonth(Integer puid, String from, String to, String month) {
        if (StringUtils.isBlank(month)) {
            return null;
        }
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        sql.append(" select rate from dim_currency_rate where ")
                .append(" puid = ? ")
                .append(" and `from` = ?")
                .append(" and `to` = ?")
                .append(" and `month` = ? limit 1");
        argsList.add(puid);
        argsList.add(from);
        argsList.add(to);
        argsList.add(month);
        List<DimCurrencyRate> decimalList = getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(DimCurrencyRate.class));
        return CollectionUtils.isNotEmpty(decimalList) ? decimalList.get(0) : null;
    }

    @Override
    public List<DimCurrencyRate> getRateList(Integer puid, String to, List<String> fromList, List<Integer> monthList) {
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        sql.append(" select * from dim_currency_rate where ")
                .append(" puid = ? ")
                .append(" and `to` = ?");
        argsList.add(puid);
        argsList.add(to);
        sql.append(SqlStringUtil.dealInList("`from`", fromList, argsList));
        sql.append(SqlStringUtil.dealInList("month", monthList, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(DimCurrencyRate.class));
    }

    @Override
    public List<DimCurrencyRate> getRateList(Integer puid, List<String> toList, List<String> fromList, List<Integer> monthList) {
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();
        sql.append(" select * from dim_currency_rate where ")
                .append(" puid = ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("`to`", toList, argsList));
        sql.append(SqlStringUtil.dealInList("`from`", fromList, argsList));
        sql.append(SqlStringUtil.dealInList("month", monthList, argsList));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(DimCurrencyRate.class));
    }
}

