package com.meiyunji.sponsored.service.multiPlatform.walmart.dto;

public class WalmartProposalSetDTO {
    private Long id;                        //海外仓库存Id
    private Long shopId;                    //店铺Id
    private Integer safeStockNum;           //安全库存
    private Integer minSafeStockNum;        //警戒值
    private Integer optionMode;             //建议模式：1-固定值 2-权重 3-备货天数
    private String weightSet;               //智能模式下的权重
    private Integer warningDay;             //备货天数
    private  Integer minWarningDay;         //最小备货天数
    private Integer purchasingDay;          //采购天数
    private Integer transitDay;             //中转仓到海外仓天数
    private Double saleCoefficient;         //销售系数
    private Integer arithmeticMode;         //权重方案

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getSafeStockNum() {
        return safeStockNum;
    }

    public void setSafeStockNum(Integer safeStockNum) {
        this.safeStockNum = safeStockNum;
    }

    public Integer getMinSafeStockNum() {
        return minSafeStockNum;
    }

    public void setMinSafeStockNum(Integer minSafeStockNum) {
        this.minSafeStockNum = minSafeStockNum;
    }

    public Integer getOptionMode() {
        return optionMode;
    }

    public void setOptionMode(Integer optionMode) {
        this.optionMode = optionMode;
    }

    public String getWeightSet() {
        return weightSet;
    }

    public void setWeightSet(String weightSet) {
        this.weightSet = weightSet;
    }

    public Integer getWarningDay() {
        return warningDay;
    }

    public void setWarningDay(Integer warningDay) {
        this.warningDay = warningDay;
    }

    public Integer getMinWarningDay() {
        return minWarningDay;
    }

    public void setMinWarningDay(Integer minWarningDay) {
        this.minWarningDay = minWarningDay;
    }

    public Integer getPurchasingDay() {
        return purchasingDay;
    }

    public void setPurchasingDay(Integer purchasingDay) {
        this.purchasingDay = purchasingDay;
    }

    public Integer getTransitDay() {
        return transitDay;
    }

    public void setTransitDay(Integer transitDay) {
        this.transitDay = transitDay;
    }

    public Double getSaleCoefficient() {
        return saleCoefficient;
    }

    public void setSaleCoefficient(Double saleCoefficient) {
        this.saleCoefficient = saleCoefficient;
    }

    public Integer getArithmeticMode() {
        return arithmeticMode;
    }

    public void setArithmeticMode(Integer arithmeticMode) {
        this.arithmeticMode = arithmeticMode;
    }
}
