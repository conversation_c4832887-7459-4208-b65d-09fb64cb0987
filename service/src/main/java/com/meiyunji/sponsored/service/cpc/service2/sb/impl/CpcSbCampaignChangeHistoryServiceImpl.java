package com.meiyunji.sponsored.service.cpc.service2.sb.impl;


import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.adCommon.CampaignBeyondBudgetHourVo;
import com.meiyunji.sponsored.rpc.adCommon.CampaignBeyondBudgetPageResponse;
import com.meiyunji.sponsored.rpc.adCommon.CampaignBeyondBudgetPageVo;
import com.meiyunji.sponsored.rpc.adCommon.CampaignBeyondBudgetvo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignSbChangeHistoryDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignSbChangeHistory;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignSpChangeHistory;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbCampaignChangeHistoryService;
import com.meiyunji.sponsored.service.cpc.vo.AdCampaignChangeHistoryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


/**
 * Created by xp on 2021/3/29.
 * 实现类
 */
@Service
@Slf4j
public class CpcSbCampaignChangeHistoryServiceImpl implements ICpcSbCampaignChangeHistoryService {

    @Autowired
    private IAmazonAdCampaignSbChangeHistoryDao amazonAdCampaignSbChangeHistoryDao;

    @Override
    public List<CampaignBeyondBudgetvo> getAllHistoryByCampaignId(AdCampaignChangeHistoryParam param) {

        List<AmazonAdCampaignSbChangeHistory> allHistoryByCampaignId = amazonAdCampaignSbChangeHistoryDao.getAllHistoryByCampaignId(param);
        if(CollectionUtils.isNotEmpty(allHistoryByCampaignId)){
            List<CampaignBeyondBudgetvo> collect = allHistoryByCampaignId.stream().map(e -> {
                CampaignBeyondBudgetvo.Builder builder = CampaignBeyondBudgetvo.newBuilder();
                if (e.getChangeTime() != null) {
                    builder.setDate(DateUtil.format(e.getChangeTime(),"yyyy-MM-dd HH:mm"));
                }
                if (e.getNewValue() != null) {
                    builder.setValue(e.getNewValue());
                }
                if (e.getDailyBudget() != null) {
                    builder.setDailyBudget(e.getDailyBudget().toString());
                }
                return builder.build();
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    @Override
    public List<CampaignBeyondBudgetHourVo> getAllHistoryByCampaignIdHour(AdCampaignChangeHistoryParam param) {

        List<AmazonAdCampaignSbChangeHistory> allHistoryByCampaignId = amazonAdCampaignSbChangeHistoryDao.getAllHistoryByCampaignIdHour(param);
        if(CollectionUtils.isNotEmpty(allHistoryByCampaignId)){
            List<CampaignBeyondBudgetHourVo> collect = allHistoryByCampaignId.stream().map(e -> {
                CampaignBeyondBudgetHourVo.Builder builder = CampaignBeyondBudgetHourVo.newBuilder();
                builder.setSales(Int32Value.of(0));
                if (StringUtils.isNotBlank(e.getDateCount())) {
                    List<String> list = StringUtil.splitStr(e.getDateCount(), ",");
                    builder.setCount(Int32Value.of(list.size()));
                }
                if (e.getChangeHour() != null) {
                    builder.setHour(Int32Value.of(e.getChangeHour()));
                }
                return builder.build();
            }).collect(Collectors.toList());
            return collect;
        }
        return null;
    }

    @Override
    public CampaignBeyondBudgetPageResponse getAllHistoryByCampaignIdPage(AdCampaignChangeHistoryParam param) {
        Page<AmazonAdCampaignSbChangeHistory> voPage = amazonAdCampaignSbChangeHistoryDao.pageList(param);
        CampaignBeyondBudgetPageResponse.Builder builder = CampaignBeyondBudgetPageResponse.newBuilder();
        CampaignBeyondBudgetPageResponse.Page.Builder pageBuilder = CampaignBeyondBudgetPageResponse.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        if (voPage.getRows() != null) {
            List<CampaignBeyondBudgetPageVo> collect = voPage.getRows().stream().map(e -> {
                CampaignBeyondBudgetPageVo.Builder buid = CampaignBeyondBudgetPageVo.newBuilder();
                if (e.getChangeTime() != null) {
                    buid.setChangeTime(DateUtil.format(e.getChangeTime(),"yyyy-MM-dd HH:mm"));
                }
                if (e.getNewValue() != null) {
                    buid.setState(e.getNewValue());
                }
                if (e.getDailyBudget() != null) {
                    buid.setDailyBudget(e.getDailyBudget().toString());
                }
                return buid.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(collect);
        }
        builder.setData(pageBuilder);
        builder.setCode(Int32Value.of(Result.SUCCESS));
        return builder.build();
    }

}
