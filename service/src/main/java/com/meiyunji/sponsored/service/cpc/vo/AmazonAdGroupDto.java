package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wade
 * @date: 2021/8/30 17:10
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AmazonAdGroupDto {

    /**
     * 广告类型 sp sd  (广告组无sd)
     */
    private String type;
    /**
     * id
     */
    private Long id;
    /**
     * 商户uid
     */
    private Integer puid;
    /**
     * 店铺ID
     */
    private Integer shopId;
    /**
     * 站点
     */
    private String marketplaceId;
    /**
     * 广告组id
     */
    private String adGroupId;
    /**
     * 活动id
     */
    private String campaignId;
    /**
     * 广告组名称
     */
    private String name;
    /**
     * 默认竞价
     */
    private BigDecimal bid;
    /**
     * SP:  手动:keyword,targeting,自动:auto  SD: T00020(Product), T00030(audience)'
     */
    private String adGroupType;
    /**
     * 广告组状态（enabled，paused，archived）
     */
    private String state;

    /**
     * 创建人id
     */
    private Integer createId;
    /**
     * 修改人id
     */
    private Integer updateId;

    private Date createTime;

    private Date updateTime;

}
