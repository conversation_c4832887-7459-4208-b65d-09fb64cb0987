package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.nekeyword.*;
import com.amazon.advertising.sb.mode.keyword.Keyword;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdNeKeyword;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.BatchNeKeywordVo;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.enums.AdSbNeTargetingStateEnum;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/30.
 * 对接口二次封装，直接和广告接口交互
 */

@Component
@Slf4j
public class CpcSbNeKeywordApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    /**
     * 同步所有的否定关键词
     * @param shop：
     */
    public void syncNeKeywords(ShopAuth shop, String campaignId, List<String> keywordIds) {
        syncNeKeywords(shop, campaignId, null, keywordIds, null, false);
    }

    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, List<String> keywordIds, List<AdSbNeTargetingStateEnum> stateList, boolean throwException) {
        syncNeKeywords(shop, campaignId, groupId, keywordIds, stateList, throwException, false);
    }

    /**
     * 同步所有的否定关键词
     * @param shop：
     */
    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, List<String> keywordIds, List<AdSbNeTargetingStateEnum> stateList, boolean throwException, boolean isProxy) {
        if(CollectionUtils.isNotEmpty(keywordIds)){
            // 亚马逊接口是get请求当参数拼接过多的时候会报request too large异常，因此分批请求。
            Lists.partition(keywordIds, 200).forEach(subList -> {
                doSyncNeKeywords(shop, campaignId, groupId, subList, stateList, throwException, isProxy);
            });
        }else{
            doSyncNeKeywords(shop, campaignId, groupId, keywordIds, stateList, throwException, isProxy);
        }
    }

    /**
     * 执行同步所有的否定关键词
     */
    private void doSyncNeKeywords(ShopAuth shop, String campaignId, String groupId, List<String> keywordIds, List<AdSbNeTargetingStateEnum> stateList, boolean throwException, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSbNeKeywords--配置信息为空");
            return;
        }


        NeKeywordClient client = NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());

        if (isProxy) {
            client = NeKeywordClient.getInstance(true);
        }
        int startIndex = 0;
        int count = 500;
        ListNeKeywordsResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(AdSbNeTargetingStateEnum.values()).map(AdSbNeTargetingStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(AdSbNeTargetingStateEnum::getStateType).collect(Collectors.joining(","));
        }

        String keywordFilter = CollectionUtils.isNotEmpty(keywordIds) ? StringUtils.join(keywordIds,",") : null;

        while (true) {
            int finalStatIndex = startIndex;
            NeKeywordClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalStatIndex, count, null, null, stateFilter, campaignId, groupId, keywordFilter, null));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SB neKeyword rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                NeKeywordClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalStatIndex, count, null, null, stateFilter, campaignId, groupId, keywordFilter, null));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && throwException) {
                throw new ServiceException("sb syncNeKeyword error");
            }

            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();

            // 入库
            AmazonSbAdNeKeyword amazonSbAdNeKeyword;
            List<AmazonSbAdNeKeyword> amazonSbAdNeKeywords = new ArrayList<>(size);
            for (Keyword keyword : response.getResultList()) {
                amazonSbAdNeKeyword = turnEntityToPO(keyword);
                if (StringUtils.isNotBlank(amazonSbAdNeKeyword.getKeywordId())) {
                    amazonSbAdNeKeyword.setPuid(shop.getPuid());
                    amazonSbAdNeKeyword.setShopId(shop.getId());
                    amazonSbAdNeKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonSbAdNeKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonSbAdNeKeywords.add(amazonSbAdNeKeyword);
                }
            }

            if (amazonSbAdNeKeywords.size() > 0) {
                Map<String, AmazonSbAdNeKeyword> neKeywordMap = amazonSbAdNeKeywordDao.listByKeywordId(shop.getPuid(), shop.getId(),
                        amazonSbAdNeKeywords.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonSbAdNeKeyword::getKeywordId, Function.identity()));

                List<AmazonSbAdNeKeyword> insertList = new ArrayList<>();
                List<AmazonSbAdNeKeyword> updateList = new ArrayList<>();
                AmazonSbAdNeKeyword old;

                for (AmazonSbAdNeKeyword newNeKeyword : amazonSbAdNeKeywords) {
                    if (neKeywordMap.containsKey(newNeKeyword.getKeywordId())) {
                        old = neKeywordMap.get(newNeKeyword.getKeywordId());
                        if (StringUtils.isNotBlank(newNeKeyword.getKeywordText())) {
                            old.setKeywordText(newNeKeyword.getKeywordText());
                        }
                        if (StringUtils.isNotBlank(newNeKeyword.getMatchType())) {
                            old.setMatchType(newNeKeyword.getMatchType());
                        }
                        if (StringUtils.isNotBlank(newNeKeyword.getState())) {
                            old.setState(newNeKeyword.getState());
                        }
                        updateList.add(old);
                    } else {
                        newNeKeyword.setCreateInAmzup(0);
                        insertList.add(newNeKeyword);
                    }
                }

                try {
                    amazonSbAdNeKeywordDao.batchAdd(shop.getPuid(), insertList);
                    amazonSbAdNeKeywordDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdNeKeyword:", e);
                }

            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    public Result createNeKeywords(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdNeKeyword> amazonAdNeKeywords) {
        List<Keyword> keywordList = makeNeKeywords(amazonAdNeKeywords);
        CreateNeKeywordResponse response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error(SBCreateErrorEnum.TRY_AGAIN_LATER.getMsg());
        }

        //处理返回结果中的错误信息
        String errMsg = "";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbNeKeyWordResult> resultList = response.getResultList();
            int index = 0;
            for (SbNeKeyWordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    amazonAdNeKeywords.get(index).setKeywordId(String.valueOf(result.getKeywordId()));
                } else {
                    amazonAdNeKeywords.get(index).setErrMsg("第" + (index+1) +"个:" + (AmazonErrorUtils.getError(StringUtils.isNotBlank(result.getDetails())
                            ? result.getDetails() : result.getDescription())));
                }
                index++;
            }
            return ResultUtil.success();
        }else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            } else {
                errMsg = response.getError().getDescription();
            }
            errMsg = AmazonErrorUtils.getError(errMsg);
        }
        return ResultUtil.error(errMsg);

    }

    public Result createNeKeywordsNew(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdNeKeyword> amazonAdNeKeywords) {
        List<Keyword> keywordList = makeNeKeywords(amazonAdNeKeywords);
        CreateNeKeywordResponse response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error(SBCreateErrorEnum.TRY_AGAIN_LATER.getMsg());
        }

        //处理返回结果中的错误信息
        String errMsg = "";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbNeKeyWordResult> resultList = response.getResultList();
            int index = 0;
            for (SbNeKeyWordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    amazonAdNeKeywords.get(index).setKeywordId(String.valueOf(result.getKeywordId()));
                } else {
                    amazonAdNeKeywords.get(index).setErrMsg(StringUtils.isNotBlank(result.getDetails())
                            ? result.getDetails() : result.getDescription());
                }
                index++;
            }
            return ResultUtil.success();
        }else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            } else {
                errMsg = response.getError().getDescription();
            }
        }
        return ResultUtil.error(errMsg);

    }

    public Result archive(ShopAuth shop, AmazonAdProfile amazonAdProfile, String keywordId) {

        ArchiveNeKeywordResponse response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Long.valueOf(keywordId)));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getKeywordId() != null) {
            if ("SUCCESS".equalsIgnoreCase(response.getResult().getCode())) {
                return ResultUtil.success();
            }
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                msg = response.getResult().getDetails();
            } else {
                msg = response.getResult().getDescription();
            }
            msg = AmazonErrorUtils.getError(msg);
        }
        return ResultUtil.error(msg);
    }

    /**
     * 批量更新SB否定关键词
     * @param shop
     * @param amazonAdProfile
     * @param amazonAdNeKeywords
     * @return
     */
    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdNeKeyword> amazonAdNeKeywords) {
        BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword> batchResponseVo = new BatchResponseVo();
        List<Keyword> keywordList = toNeKeywords(amazonAdNeKeywords);
        Map<String, AmazonSbAdNeKeyword> amazonSbAdNeKeywordMap = amazonAdNeKeywords.stream().collect(Collectors.toMap(AmazonSbAdNeKeyword::getKeywordId, e -> e));
        UpdateNeKeywordResponse response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        List<AmazonSbAdNeKeyword> successList = Lists.newArrayList();
        List<BatchNeKeywordVo> errorList = Lists.newArrayList();
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = cpcApiHelper.call(shop, () -> NeKeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                    shop.getMarketplaceId(), keywordList));
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResultList() != null && response.getResultList().size() >0) {
            List<SbNeKeyWordResult> resultList = response.getResultList();
            for (SbNeKeyWordResult sbNeKeyWordResult : resultList) {

                if ("SUCCESS".equals(sbNeKeyWordResult.getCode())) {
                    AmazonSbAdNeKeyword amazonSbAdNeKeywordSuccess = amazonSbAdNeKeywordMap.remove(String.valueOf(sbNeKeyWordResult.getKeywordId()));
                    if (amazonSbAdNeKeywordSuccess != null) {
                        amazonSbAdNeKeywordSuccess.setState(CpcStatusEnum.archived.name());
                        successList.add(amazonSbAdNeKeywordSuccess);
                    }

                } else {
                    AmazonSbAdNeKeyword amazonSbAdNeKeywordFail = amazonSbAdNeKeywordMap.remove(String.valueOf(sbNeKeyWordResult.getKeywordId()));
                    if (amazonSbAdNeKeywordFail != null) {
                        BatchNeKeywordVo sbUpdateNeKeywordVoError = new BatchNeKeywordVo();
                        sbUpdateNeKeywordVoError.setId(amazonSbAdNeKeywordFail.getId());
                        sbUpdateNeKeywordVoError.setKeywordId(amazonSbAdNeKeywordFail.getKeywordId());
                        sbUpdateNeKeywordVoError.setKeywordText(amazonSbAdNeKeywordFail.getKeywordText());

                        // 更新失败数据处理
                        if (StringUtils.isNotBlank(sbNeKeyWordResult.getDescription())) {
                            sbUpdateNeKeywordVoError.setFailReason(AmazonErrorUtils.getError(sbNeKeyWordResult.getDescription()));
                        } else {
                            sbUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(sbUpdateNeKeywordVoError);
                    }
                }
            }
            // 剩余未匹配到的数据是接口未返回KeywordId的数据,一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSbAdNeKeywordMap)) {
                amazonSbAdNeKeywordMap.forEach((k, v) -> {
                    BatchNeKeywordVo sbUpdateCampaignVoError = new BatchNeKeywordVo();
                    sbUpdateCampaignVoError.setId(v.getId());
                    sbUpdateCampaignVoError.setKeywordId(v.getKeywordId());
                    sbUpdateCampaignVoError.setKeywordText(v.getKeywordText());
                    sbUpdateCampaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sbUpdateCampaignVoError);
                });
            }
        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())){
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //所有数据没有执行成功
            if (MapUtils.isNotEmpty(amazonSbAdNeKeywordMap)) {
                amazonSbAdNeKeywordMap.forEach((k, v) -> {
                    BatchNeKeywordVo sbUpdateCampaignVoError = new BatchNeKeywordVo();
                    sbUpdateCampaignVoError.setId(v.getId());
                    sbUpdateCampaignVoError.setKeywordId(v.getKeywordId());
                    sbUpdateCampaignVoError.setKeywordText(v.getKeywordText());
                    sbUpdateCampaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sbUpdateCampaignVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdNeKeywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }


    public List<Keyword> makeNeKeywords(List<AmazonSbAdNeKeyword> amazonAdNeKeywords) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdNeKeywords.size());
        Keyword keyword;
        for (AmazonSbAdNeKeyword sbAdNeKeyword : amazonAdNeKeywords) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(sbAdNeKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(sbAdNeKeyword.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(sbAdNeKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(sbAdNeKeyword.getCampaignId()));
            }
            keyword.setKeywordText(sbAdNeKeyword.getKeywordText());
            keyword.setMatchType(sbAdNeKeyword.getMatchType());
            list.add(keyword);
        }
        return list;
    }

    private List<Keyword> toNeKeywords(List<AmazonSbAdNeKeyword> amazonAdNeKeywords) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdNeKeywords.size());
        Keyword keyword;
        for (AmazonSbAdNeKeyword sbAdNeKeyword : amazonAdNeKeywords) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(sbAdNeKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(sbAdNeKeyword.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(sbAdNeKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(sbAdNeKeyword.getCampaignId()));
            }
            keyword.setKeywordId(Long.valueOf(sbAdNeKeyword.getKeywordId()));
            keyword.setKeywordText(sbAdNeKeyword.getKeywordText());
            keyword.setMatchType(sbAdNeKeyword.getMatchType());
            keyword.setState(CpcStatusEnum.archived.name());
            list.add(keyword);
        }
        return list;
    }


    // 把接口返回的dto转换成po
    private AmazonSbAdNeKeyword turnEntityToPO(Keyword keyword) {
        AmazonSbAdNeKeyword amazonSbAdNeKeyword = new AmazonSbAdNeKeyword();
        if (keyword.getKeywordId() != null) {
            amazonSbAdNeKeyword.setKeywordId(keyword.getKeywordId().toString());
        }
        if (keyword.getCampaignId() != null) {
            amazonSbAdNeKeyword.setCampaignId(keyword.getCampaignId().toString());
        }
        if (keyword.getAdGroupId() != null) {
            amazonSbAdNeKeyword.setAdGroupId(keyword.getAdGroupId().toString());
        }
        if (StringUtils.isNotBlank(keyword.getKeywordText())) {
            amazonSbAdNeKeyword.setKeywordText(keyword.getKeywordText());
        }
        if (StringUtils.isNotBlank(keyword.getMatchType())) {
            amazonSbAdNeKeyword.setMatchType(keyword.getMatchType());
        }
        if (StringUtils.isNotBlank(keyword.getState())) {
            amazonSbAdNeKeyword.setState(keyword.getState());
        }
        return amazonSbAdNeKeyword;
    }
}
