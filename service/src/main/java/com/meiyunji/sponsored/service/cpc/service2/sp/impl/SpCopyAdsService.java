package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sp.campaign.*;
import com.meiyunji.sponsored.rpc.vo.ProductRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.enums.AutoAndKeywordAndTargetingEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpCopyConstants;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CountCreateResultDto;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.CreateGroupResultDto;
import com.meiyunji.sponsored.service.cpc.dto.sp.CopySpResultDto;
import com.meiyunji.sponsored.service.cpc.dto.sp.CopySpTargetDto;
import com.meiyunji.sponsored.service.cpc.manager.CpcSpTargetingManager;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.CampaignVo;
import com.meiyunji.sponsored.service.cpc.vo.CreateAutoTargetingVo;
import com.meiyunji.sponsored.service.cpc.vo.SPadGroupVo;
import com.meiyunji.sponsored.service.cpc.vo.SpNeTargetingVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.util.WxNotificationUtil.CREATE_SP_ADS_WX_URL;

/**
 * sp异步复制service
 *
 * @author: zzh
 * @date: 2024/9/29 14:26
 * @describe:
 */
@Component
@Slf4j
public class SpCopyAdsService {

    @Resource
    private ThreadPoolTaskExecutor spBatchCreateTaskExecutor;

    @Resource
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Resource
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Resource
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Resource
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Resource
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Resource
    private IAmazonAdCampaignNetargetingSpDao amazonAdCampaignNetargetingSpDao;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Resource
    private ICpcProductService productService;
    @Resource
    private ICopyAdsResultStatisticsDao copyAdsResultStatisticsDao;
    @Resource
    private CpcSpTargetingManager spTargetingManager;
    @Resource
    private CpcSpTargetingManager cpcSpTargetingManager;
    @Resource
    private SpGroupService spGroupService;
    @Resource
    private SpCampaignService spCampaignService;
    @Resource
    private ICpcAdSyncService cpcAdSyncService;
    @Resource
    private ICpcTargetingService cpcTargetingService;
    @Resource
    private ICpcNeKeywordsService cpcNeKeywordsService;
    @Resource
    private CpcTargetingApiService cpcTargetingApiService;

    private static final int MAX_SIZE = 999;

    /**
     * 业务参数校验:1,校验广告活动名称不重复。2,校验广告活动id。3，校验广告组数量和单个广告组下广告产品数量。
     *
     * @param request request
     */
    public void checkParam(CopySpAdsReq request) {
        if (!request.hasUid()) {
            throw new SponsoredBizException("uid为空");
        }
        if (!request.hasLoginIp()) {
            throw new SponsoredBizException("loginIp为空");
        }
        if (!request.hasCopyTargetStrategy()) {
            throw new SponsoredBizException("复制投放策略为空");
        }
        if (!request.hasCopyTargetBidStrategy()) {
            throw new SponsoredBizException("复制投放竞价策略为空");
        }
        if (!request.hasCopyGroupNeTargetStrategy()) {
            throw new SponsoredBizException("复制广告组层级否定投放策略为空");
        }
        if (!request.hasGroupStatus()) {
            throw new SponsoredBizException("广告组状态为空");
        }
        if (!request.hasTargetStatus()) {
            throw new SponsoredBizException("广告投放状态为空");
        }
        if (CopyTargetBidStrategyEnum.NEW_BID.getValue() == request.getCopyTargetStrategy() && !request.hasUnifyBid()) {
            throw new SponsoredBizException("未统一设置新的竞价");
        }
        //1，校验广告活动id
        AmazonAdCampaignAll campaign = amazonAdCampaignAllDao.getCampaignByCampaignId(request.getPuid(), request.getShopId(), request.getCampaignId(), CampaignTypeEnum.sp.getCampaignType());
        if (campaign == null) {
            throw new SponsoredBizException("源广告活动不存在");
        }
        if (Constants.ARCHIVED.equalsIgnoreCase(campaign.getState())) {
            throw new SponsoredBizException("不支持复制已归档广告活动");
        }
        //2，校验广告组数量和单个广告组下广告产品数量
        List<CopySpGroupInfoReq> editedSpGroupInfoList = request.getEditedSpGroupInfoListList();
        if (CollectionUtils.isNotEmpty(editedSpGroupInfoList)) {
            int count = 0;
            for (CopySpGroupInfoReq copySpGroupInfoReq : editedSpGroupInfoList) {
                if (CollectionUtils.isNotEmpty(copySpGroupInfoReq.getAddedProductsList())) {
                    count += copySpGroupInfoReq.getAddedProductsList().size();
                }
            }
            Integer productLimit = dynamicRefreshConfiguration.getProductLimit(request.getPuid());
            if (count > productLimit) {
                throw new SponsoredBizException("该广告活动所包含的广告产品超过"+productLimit+"个，暂不支持复制，请调整后再进行复制");
            }
        }
    }

    /**
     * 创建广告活动
     *
     * @param request         request
     * @param shop            shop
     * @param amazonAdProfile amazonAdProfile
     * @return 成功返回campaignId，失败返回null
     */
    public String createCopyCampaign(CopySpAdsReq request, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        // 1，构建活动创建vo
        CampaignVo campaignVo;
        try {
            campaignVo = JSON.parseObject(request.getSpCampaignInfoJson(), CampaignVo.class);
        } catch (Exception e) {
            log.error("复制广告：广告活动格式错误, puid:{}, shopId:{}, campaignInfoJson:{}----------e-----------", shop.getPuid(), shop.getId(), request.getSpCampaignInfoJson());
            throw new SponsoredBizException("广告活动格式错误");
        }
        campaignVo.setUid(request.getUid());
        campaignVo.setPuid(request.getPuid());
        campaignVo.setShopId(request.getTargetShopId());
        campaignVo.setLoginIp(request.getLoginIp());
        campaignVo.setState(request.getCampaignStatus());
        // 2，创建活动
        CampaignResp spCampaign = spCampaignService.createSpCampaign(campaignVo, shop, amazonAdProfile);
        //异步打印错误告警
        if (StringUtils.isBlank(spCampaign.getCampaignId())) {
            CompletableFuture.runAsync(() -> {
                if (!"广告活动名称已存在".equalsIgnoreCase(spCampaign.getCampaignErrMsg())) {
                    statisticsResultForCampaign(request);
                }
                String msg = String.format("复制广告：创建广告活动失败，活动名称：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", campaignVo.getName(), spCampaign.getCampaignErrMsg(), request.getPuid(), request.getShopId(), shop.getId());
                WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
            }, ThreadPoolUtil.getPrintWxLogExecutor()).exceptionally((e) -> null);

            throw new SponsoredBizException(StringUtils.isNotBlank(spCampaign.getCampaignErrMsg()) ? spCampaign.getCampaignErrMsg() : "复制广告活动失败！");
        }
        return spCampaign.getCampaignId();
    }

    private void statisticsResultForCampaign(CopySpAdsReq request) {
        // 统计数量
        CopyAdsResultStatistics statistics = new CopyAdsResultStatistics();
        statistics.setPuid(request.getPuid());
        statistics.setCreatorId(request.getUid());
        statistics.setSourceCampaignId(request.getCampaignId());
        statistics.setCampaignSuccess(Boolean.FALSE);
        // 是否全部成功
        statistics.setAllSuccess(Boolean.FALSE);
        // 记录数据库
        try {
            copyAdsResultStatisticsDao.save(statistics);
        } catch (Exception e) {
            log.error("复制广告结果统计广告活动数据入库异常");
        }
    }

    /**
     * 过滤得到创建成功的目标广告组id，构成源广告组id-》目标广告组id的map
     */
    private static Map<String, String> getSourceGroupIdTargetGroupIdMap(Map<String, CreateGroupResultDto> groupResultMap) {
        Map<String, String> sourceGroupIdTargetGroupIdMap = new HashMap<>(groupResultMap.size());
        groupResultMap.forEach((sourceGroupId, createGroupResultDto) -> {
            if (StringUtils.isBlank(sourceGroupId)) {
                return;
            }
            if (Objects.isNull(createGroupResultDto) || Objects.isNull(createGroupResultDto.getResult()) || !createGroupResultDto.getResult() || StringUtils.isBlank(createGroupResultDto.getGroupId())) {
                return;
            }
            sourceGroupIdTargetGroupIdMap.put(sourceGroupId, createGroupResultDto.getGroupId());
        });
        return sourceGroupIdTargetGroupIdMap;
    }

    /**
     * 多线程并发批量创建广告产品,一个广告组一个线程
     *
     * @param request                       request
     * @param sourceGroupIdTargetGroupIdMap 源广告组id -》 目标广告组id
     * @param targetShop                    目标shop
     * @param targetCampaignId              目标广告活动id
     * @return List<Result < List < ProductInfoResp>>>
     */
    public List<Result<List<ProductInfoResp>>> createBatchProducts(CopySpAdsReq request, Map<String, String> sourceGroupIdTargetGroupIdMap, ShopAuth targetShop, String targetCampaignId, AmazonAdProfile targetAmazonAdProfile) {
        List<CopySpGroupInfoReq> editedSpGroupInfoListList = request.getEditedSpGroupInfoListList();
        if (CollectionUtils.isEmpty(editedSpGroupInfoListList)) {
            return Collections.emptyList();
        }
        //1,构建 目标广告组id -》 需要添加的广告产品列表
        Map<String, List<CpcProductDto>> targetGroupIdProductsMap = new HashMap<>(editedSpGroupInfoListList.size());
        for (CopySpGroupInfoReq copySpGroupInfoReq : editedSpGroupInfoListList) {
            if (Objects.isNull(copySpGroupInfoReq) || StringUtils.isBlank(copySpGroupInfoReq.getGroupId()) || CollectionUtils.isEmpty(copySpGroupInfoReq.getAddedProductsList())) {
                continue;
            }
            List<ProductRpcVo> addedProductsList = copySpGroupInfoReq.getAddedProductsList();
            String sourceGroupId = copySpGroupInfoReq.getGroupId();
            if (sourceGroupIdTargetGroupIdMap.containsKey(sourceGroupId)) {
                List<CpcProductDto> cpcProductDtos = new ArrayList<>(addedProductsList.size());
                for (ProductRpcVo productRpcVo : addedProductsList) {
                    CpcProductDto cpcProductDto = new CpcProductDto();
                    cpcProductDto.setAsin(productRpcVo.getAsin());
                    cpcProductDto.setSku(productRpcVo.getSku());
                    cpcProductDtos.add(cpcProductDto);
                }
                targetGroupIdProductsMap.put(sourceGroupIdTargetGroupIdMap.get(sourceGroupId), cpcProductDtos);
            }
        }
        //2，按广告组维度多线程创建广告产品
        List<Result<List<ProductInfoResp>>> ProductInfoRespResults = new ArrayList<>(targetGroupIdProductsMap.size());
        targetGroupIdProductsMap.forEach((targetGroupId, productDtoList) -> {
            List<List<CpcProductDto>> productsPartitionList = Lists.partition(productDtoList, MAX_SIZE);
            for (int i = 0; i < productsPartitionList.size(); i++) {
                List<CpcProductDto> productList = productsPartitionList.get(i);
                Result<List<ProductInfoResp>> listResult = productService.addProductToGroup(targetShop, targetCampaignId, targetGroupId, productList, request.getLoginIp(), request.getUid(), targetAmazonAdProfile);
                ProductInfoRespResults.add(listResult);
            }
        });

        return ProductInfoRespResults;
    }

    /**
     * 多线程批量创建投放,一个广告组一个线程
     *
     * @param request                       request 不能为null
     * @param sourceGroupIdTargetGroupIdMap 源广告组id-》目标广告组id 的map
     * @param sourceShop                    源shop 不能为null
     * @param targetShop                    目标shop 不能为null
     * @param targetAmazonAdProfile         目标profile 不能为null
     * @return List<TargetResp> 每个元素代表一个广告组
     */
    public List<Result<List<TargetInfoResp>>> createBatchTargets(CopySpAdsReq request, Map<String, String> sourceGroupIdTargetGroupIdMap, ShopAuth sourceShop, ShopAuth targetShop, AmazonAdProfile targetAmazonAdProfile, String targetCampaignId) {
        if (CopyTargetStrategyEnum.NOT_COPY.getValue() == request.getCopyTargetStrategy()) {
            log.info("不复制投放，puid：{}， shopId:{}, campaignId:{}", request.getPuid(), sourceShop.getId(), targetCampaignId);
            return Collections.emptyList();
        }
        if (sourceGroupIdTargetGroupIdMap == null) {
            return Collections.emptyList();
        }
        //1,清洗sourceGroupIdTargetGroupIdMap,将key或者value为null的entry过滤
        sourceGroupIdTargetGroupIdMap = sourceGroupIdTargetGroupIdMap.entrySet().stream().filter(entry -> entry.getKey() != null && entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (sourceGroupIdTargetGroupIdMap.isEmpty()) {
            return Collections.emptyList();
        }
        //2,构建 目标广告组id -》 需要添加的投放列表 的map,表示需要调用亚马逊接口创建的投放
        Map<String, CopySpTargetDto> targetGroupIdCreateSpTargetDtoMap = buildTargetGroupIdTargetListMap(request, sourceShop, targetShop, sourceGroupIdTargetGroupIdMap);
        // 构建 目标广告组id -》 广告组po 的map，因为创建投放需要用到广告组po
        List<String> targetGroupIdList = targetGroupIdCreateSpTargetDtoMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getListByAdGroupIds(request.getPuid(), targetShop.getId(), targetGroupIdList);
        Map<String, AmazonAdGroup> targetGroupIdGroupMap = amazonAdGroupList.stream().filter(groupPo -> groupPo != null && groupPo.getAdGroupId() != null).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity()));

        //2，为创建成功的广告组添加投放,按广告组维度多线程创建投放
        List<Result<List<TargetInfoResp>>> targetRespList = new ArrayList<>(targetGroupIdCreateSpTargetDtoMap.size());
        targetGroupIdCreateSpTargetDtoMap.forEach((targetGroupId, copySpTargetDto) -> {
            if (!targetGroupIdGroupMap.containsKey(targetGroupId)) {
                return;
            }
            //自动投放
            if (copySpTargetDto.getAutoTarget()) {
                CreateAutoTargetingVo createAutoTargetingVo = new CreateAutoTargetingVo();
                BeanUtils.copyProperties(copySpTargetDto, createAutoTargetingVo);
                Result<List<TargetInfoResp>> autoTargetingResult = cpcTargetingService.createAutoTargetingWithAuthed(targetAmazonAdProfile, targetGroupIdGroupMap.get(targetGroupId), createAutoTargetingVo, targetShop, request.getLoginIp(), Boolean.TRUE);
                targetRespList.add(autoTargetingResult);
                return;
            }
            //关键词投放
            if (copySpTargetDto.getKeywordTarget()) {
                List<AmazonAdKeyword> keywords = copySpTargetDto.getKeywords();
                if (CopyTargetStrategyEnum.COPY_TRANSLATED_ALL.getValue() == request.getCopyTargetStrategy()) {
                    spTargetingManager.dealTranslateKeyword(keywords, sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), targetShop, targetAmazonAdProfile);
                }
                List<List<AmazonAdKeyword>> keywordPartitionList = Lists.partition(keywords, MAX_SIZE);
                for (int i = 0; i < keywordPartitionList.size(); i++) {
                    List<AmazonAdKeyword> keywordList = keywordPartitionList.get(i);
                    int indexOffset = i * MAX_SIZE;
                    Result<List<TargetInfoResp>> keywordResult = cpcTargetingService.createKeywordsTargetingWithAuthed(keywordList, targetGroupIdGroupMap.get(targetGroupId), request.getLoginIp(), targetShop, indexOffset);
                    targetRespList.add(keywordResult);
                }
                return;
            }
            //商品投放
            List<AmazonAdTargeting> targetings = copySpTargetDto.getTargetings();
            List<List<AmazonAdTargeting>> targetingPartitionList = Lists.partition(targetings, MAX_SIZE);
            for (int i = 0; i < targetingPartitionList.size(); i++) {
                List<AmazonAdTargeting> targetingList = targetingPartitionList.get(i);
                int indexOffset = i * MAX_SIZE;
                Result<List<TargetInfoResp>> targetingResult = cpcTargetingService.createProductTargetingWithAuthed(targetingList, targetGroupIdGroupMap.get(targetGroupId), request.getLoginIp(), targetShop, indexOffset);
                targetRespList.add(targetingResult);
            }

        });
        return targetRespList;
    }

    /**
     * 构建 目标广告组id -》 需要添加的投放列表
     *
     * @param request                       request
     * @param sourceShop                    sourceShop
     * @param targetShop                    targetShop
     * @param sourceGroupIdTargetGroupIdMap sourceGroupIdTargetGroupIdMap
     * @return 目标广告组id -》 需要添加的投放列表
     */
    private Map<String, CopySpTargetDto> buildTargetGroupIdTargetListMap(CopySpAdsReq request, ShopAuth sourceShop, ShopAuth targetShop, Map<String, String> sourceGroupIdTargetGroupIdMap) {
        //1，构建关键词投放广告组id列表，和投放广告组id列表，为了之后查找相关投放列表；
        List<String> sourceGroupIdList = new ArrayList<>(sourceGroupIdTargetGroupIdMap.keySet());
        List<String> targetGroupIdList = new ArrayList<>(sourceGroupIdTargetGroupIdMap.values());
        List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getAdGroupByIds(request.getPuid(), sourceShop.getId(), sourceGroupIdList);
        List<String> keywordGroupIds = new ArrayList<>();
        List<String> targetingGroupIds = new ArrayList<>();
        for (AmazonAdGroup amazonAdGroup : adGroupList) {
            if (Objects.isNull(amazonAdGroup) || Objects.isNull(amazonAdGroup.getAdGroupType())) {
                continue;
            }
            if (AutoAndKeywordAndTargetingEnum.KEYWORD.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType())) {
                keywordGroupIds.add(amazonAdGroup.getAdGroupId());
            } else if (AutoAndKeywordAndTargetingEnum.TARGETING.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType()) || AutoAndKeywordAndTargetingEnum.AUTO.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType())) {
                targetingGroupIds.add(amazonAdGroup.getAdGroupId());
            }
        }
        List<AmazonAdGroup> targetGroupList = amazonAdGroupDao.getAdGroupByIds(request.getPuid(), targetShop.getId(), targetGroupIdList);
        Map<String, AmazonAdGroup> targetGroupMap = StreamUtil.toMap(targetGroupList, AmazonAdGroup::getAdGroupId);

        //2，处理关键词投放
        Map<String, CopySpTargetDto> targetGroupIdTargetDtoMap = new HashMap<>(keywordGroupIds.size());
        if (CollectionUtils.isNotEmpty(keywordGroupIds)) {
            List<AmazonAdKeyword> keywordList = amazonAdKeywordDaoRoutingService.getListByGroupIds(request.getPuid(), sourceShop.getId(), keywordGroupIds);
            for (AmazonAdKeyword sourceKeywordPo : keywordList) {
                if (Objects.isNull(sourceKeywordPo)) {
                    continue;
                }
                if (MatchTypeEnum.theme.getMatchType().equalsIgnoreCase(sourceKeywordPo.getMatchType())) {
                    continue;
                }
                String sourceAdGroupId = sourceKeywordPo.getAdGroupId();
                String targetAdGroupId = sourceGroupIdTargetGroupIdMap.get(sourceAdGroupId);
                if (targetGroupIdTargetDtoMap.containsKey(targetAdGroupId)) {
                    CopySpTargetDto copySpTargetDto = targetGroupIdTargetDtoMap.get(targetAdGroupId);
                    if (copySpTargetDto.getKeywords() != null) {
                        AmazonAdKeyword targetKeyword = new AmazonAdKeyword();
                        AmazonAdGroup targetGroup = targetGroupMap.get(targetAdGroupId);
                        fillAmazonAdKeyword(sourceShop, targetShop, targetKeyword, sourceKeywordPo, request, targetGroup);
                        copySpTargetDto.getKeywords().add(targetKeyword);
                    }
                } else {
                    CopySpTargetDto copySpTargetDto = new CopySpTargetDto();
                    copySpTargetDto.setUid(request.getUid());
                    copySpTargetDto.setPuid(request.getPuid());
                    copySpTargetDto.setShopId(targetShop.getId());
                    copySpTargetDto.setLoginIp(request.getLoginIp());
                    copySpTargetDto.setGroupId(targetAdGroupId);
                    copySpTargetDto.setAutoTarget(false);
                    copySpTargetDto.setKeywordTarget(true);
                    List<AmazonAdKeyword> keywords = new ArrayList<>();
                    AmazonAdKeyword targetKeyword = new AmazonAdKeyword();
                    AmazonAdGroup targetGroup = targetGroupMap.get(targetAdGroupId);
                    fillAmazonAdKeyword(sourceShop, targetShop, targetKeyword, sourceKeywordPo, request, targetGroup);
                    keywords.add(targetKeyword);
                    copySpTargetDto.setKeywords(keywords);
                    targetGroupIdTargetDtoMap.put(targetAdGroupId, copySpTargetDto);
                }
            }

        }

        //3，处理自动投放和商品投放
        if (CollectionUtils.isNotEmpty(targetingGroupIds)) {
            List<AmazonAdTargeting> targetingList = amazonAdTargetDaoRoutingService.getListByGroupIds(request.getPuid(), sourceShop.getId(), targetingGroupIds);
            for (AmazonAdTargeting sourceTargetingPo : targetingList) {
                if (sourceTargetingPo == null) {
                    continue;
                }
                String sourceAdGroupId = sourceTargetingPo.getAdGroupId();
                String targetAdGroupId = sourceGroupIdTargetGroupIdMap.get(sourceAdGroupId);
                if (targetGroupIdTargetDtoMap.containsKey(targetAdGroupId)) {
                    CopySpTargetDto copySpTargetDto = targetGroupIdTargetDtoMap.get(targetAdGroupId);
                    if (Constants.MANUAL.equalsIgnoreCase(sourceTargetingPo.getExpressionType())) {
                        AmazonAdTargeting targetTargetingPo = new AmazonAdTargeting();
                        AmazonAdGroup targetGroup = targetGroupMap.get(targetAdGroupId);
                        fillAmazonAdTargeting(sourceShop, targetShop, targetTargetingPo, sourceTargetingPo, request, targetGroup);
                        copySpTargetDto.getTargetings().add(targetTargetingPo);
                    } else {
                        fillAutoTargeting(copySpTargetDto, sourceTargetingPo, request, sourceShop, targetShop);
                    }
                } else {
                    CopySpTargetDto copySpTargetDto = new CopySpTargetDto();
                    copySpTargetDto.setUid(request.getUid());
                    copySpTargetDto.setPuid(request.getPuid());
                    copySpTargetDto.setShopId(targetShop.getId());
                    copySpTargetDto.setLoginIp(request.getLoginIp());
                    copySpTargetDto.setGroupId(targetAdGroupId);
                    if (sourceTargetingPo.getExpressionType() == null) {
                        log.error("sourceTargetingPo.getExpressionType()为null, puid:{}, shopId:{}, campaignId:{}, groupId:{}, targetingId:{}", sourceShop.getPuid(), sourceShop.getId(), sourceTargetingPo.getCampaignId(), sourceAdGroupId, sourceTargetingPo.getTargetId());
                    }
                    if (Constants.MANUAL.equalsIgnoreCase(sourceTargetingPo.getExpressionType())) {
                        copySpTargetDto.setAutoTarget(false);
                        copySpTargetDto.setKeywordTarget(false);
                        ArrayList<AmazonAdTargeting> targetings = new ArrayList<>();
                        AmazonAdTargeting targetTargetingPo = new AmazonAdTargeting();
                        AmazonAdGroup targetGroup = targetGroupMap.get(targetAdGroupId);
                        fillAmazonAdTargeting(sourceShop, targetShop, targetTargetingPo, sourceTargetingPo, request, targetGroup);
                        targetings.add(targetTargetingPo);
                        copySpTargetDto.setTargetings(targetings);
                    } else {
                        copySpTargetDto.setAutoTarget(true);
                        copySpTargetDto.setKeywordTarget(false);
                        fillAutoTargeting(copySpTargetDto, sourceTargetingPo, request, sourceShop, targetShop);
                    }
                    targetGroupIdTargetDtoMap.put(targetAdGroupId, copySpTargetDto);
                }
            }
        }
        return targetGroupIdTargetDtoMap;
    }

    private void fillAutoTargeting(CopySpTargetDto copySpTargetDto, AmazonAdTargeting sourceTargetingPo, CopySpAdsReq request, ShopAuth sourceShop, ShopAuth targetShop) {

        Double bid;
        if (CopyTargetBidStrategyEnum.CONVERTED_COPY.getValue() == request.getCopyTargetBidStrategy()) {
            bid = spTargetingManager.exchangeRate(sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), sourceTargetingPo.getBid(), request.getPuid());
        } else if (CopyTargetBidStrategyEnum.NEW_BID.getValue() == request.getCopyTargetBidStrategy()) {
            bid = request.getUnifyBid().getValue();
        } else {
            bid = sourceTargetingPo.getBid();
        }
        // 竞价设置超过站点最大值取站点最大值
        if (bid != null && bid > request.getMaxBid().getValue()) {
            bid = request.getMaxBid().getValue();
        }
        // 竞价设置小于站点最小值取站点最小值
        if (bid != null && bid < request.getMinBid().getValue()) {
            bid = request.getMinBid().getValue();
        }
        String state;
        if (TargetStatusEnum.PAUSE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.paused.name();
        } else if (TargetStatusEnum.ENABLE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.enabled.name();
        } else {
            state = sourceTargetingPo.getState();
        }

        String match = sourceTargetingPo.getTargetingValue();
        if (ExpressionEnum.queryHighRelMatches.value().equals(match)) {
            if (Objects.nonNull(bid)) {
                copySpTargetDto.setQueryHighRelMatchesBid(bid.toString());
            }
            if (CpcStatusEnum.enabled.name().equalsIgnoreCase(state)) {
                copySpTargetDto.setQueryHighRelMatchesState(true);
            }
        } else if (ExpressionEnum.queryBroadRelMatches.value().equals(match)) {
            if (Objects.nonNull(bid)) {
                copySpTargetDto.setQueryBroadRelMatchesBid(bid.toString());
            }
            if (CpcStatusEnum.enabled.name().equalsIgnoreCase(state)) {
                copySpTargetDto.setQueryBroadRelMatchesState(true);
            }
        } else if (ExpressionEnum.asinSubstituteRelated.value().equals(match)) {
            if (Objects.nonNull(bid)) {
                copySpTargetDto.setAsinSubstituteRelatedBid(bid.toString());
            }
            if (CpcStatusEnum.enabled.name().equalsIgnoreCase(state)) {
                copySpTargetDto.setAsinSubstituteRelatedState(true);
            }
        } else if (ExpressionEnum.asinAccessoryRelated.value().equals(match)) {
            if (Objects.nonNull(bid)) {
                copySpTargetDto.setAsinAccessoryRelatedBid(bid.toString());
            }
            if (CpcStatusEnum.enabled.name().equalsIgnoreCase(state)) {
                copySpTargetDto.setAsinAccessoryRelatedState(true);
            }
        }
    }

    private void fillAmazonAdKeyword(ShopAuth sourceShop, ShopAuth targetShop, AmazonAdKeyword targetKeywordPo, AmazonAdKeyword sourceKeywordPo, CopySpAdsReq request, AmazonAdGroup targetGroup) {
        targetKeywordPo.setPuid(targetGroup.getPuid());
        targetKeywordPo.setShopId(targetGroup.getShopId());
        targetKeywordPo.setMarketplaceId(targetGroup.getMarketplaceId());
        targetKeywordPo.setAdGroupId(targetGroup.getAdGroupId());
        targetKeywordPo.setDxmGroupId(targetGroup.getId());
        targetKeywordPo.setCampaignId(targetGroup.getCampaignId());
        targetKeywordPo.setProfileId(targetGroup.getProfileId());
        targetKeywordPo.setKeywordText(sourceKeywordPo.getKeywordText());
        targetKeywordPo.setMatchType(sourceKeywordPo.getMatchType());
        targetKeywordPo.setType(Constants.BIDDABLE);

        String state;
        if (TargetStatusEnum.PAUSE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.paused.name();
        } else if (TargetStatusEnum.ENABLE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.enabled.name();
        } else {
            state = sourceKeywordPo.getState();
        }
        targetKeywordPo.setState(state);

        Double bid;
        if (CopyTargetBidStrategyEnum.CONVERTED_COPY.getValue() == request.getCopyTargetBidStrategy()) {
            bid = spTargetingManager.exchangeRate(sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), sourceKeywordPo.getBid(), request.getPuid());
        } else if (CopyTargetBidStrategyEnum.NEW_BID.getValue() == request.getCopyTargetBidStrategy()) {
            bid = request.getUnifyBid().getValue();
        } else {
            bid = sourceKeywordPo.getBid();
        }
        // 竞价设置超过站点最大值取站点最大值
        if (bid != null && bid > request.getMaxBid().getValue()) {
            bid = request.getMaxBid().getValue();
        }
        // 竞价设置小于站点最小值取站点最小值
        if (bid != null && bid < request.getMinBid().getValue()) {
            bid = request.getMinBid().getValue();
        }
        targetKeywordPo.setBid(bid);

        targetKeywordPo.setSuggested(sourceKeywordPo.getSuggested());
        targetKeywordPo.setRangeStart(sourceKeywordPo.getRangeStart());
        targetKeywordPo.setRangeEnd(sourceKeywordPo.getRangeEnd());

        targetKeywordPo.setCreateId(request.getUid());
    }

    private void fillAmazonAdTargeting(ShopAuth sourceShop, ShopAuth targetShop, AmazonAdTargeting targetTargetingPo, AmazonAdTargeting sourceTargetingPo, CopySpAdsReq request, AmazonAdGroup targetGroup) {
        targetTargetingPo.setPuid(targetGroup.getPuid());
        targetTargetingPo.setShopId(targetGroup.getShopId());
        targetTargetingPo.setMarketplaceId(targetGroup.getMarketplaceId());
        targetTargetingPo.setAdGroupId(targetGroup.getAdGroupId());
        targetTargetingPo.setDxmGroupId(targetGroup.getId());
        targetTargetingPo.setCampaignId(targetGroup.getCampaignId());
        targetTargetingPo.setProfileId(targetGroup.getProfileId());
        targetTargetingPo.setExpressionType(Constants.MANUAL);
        targetTargetingPo.setType(sourceTargetingPo.getType());

        String state;
        if (TargetStatusEnum.PAUSE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.paused.name();
        } else if (TargetStatusEnum.ENABLE.getValue() == request.getTargetStatus()) {
            state = CpcStatusEnum.enabled.name();
        } else {
            state = sourceTargetingPo.getState();
        }
        targetTargetingPo.setState(state);

        Double bid;
        if (CopyTargetBidStrategyEnum.CONVERTED_COPY.getValue() == request.getCopyTargetBidStrategy()) {
            bid = spTargetingManager.exchangeRate(sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), sourceTargetingPo.getBid(), request.getPuid());
        } else if (CopyTargetBidStrategyEnum.NEW_BID.getValue() == request.getCopyTargetBidStrategy()) {
            bid = request.getUnifyBid().getValue();
        } else {
            bid = sourceTargetingPo.getBid();
        }
        // 竞价设置超过站点最大值取站点最大值
        if (bid != null && bid > request.getMaxBid().getValue()) {
            bid = request.getMaxBid().getValue();
        }
        // 竞价设置小于站点最小值取站点最小值
        if (bid != null && bid < request.getMinBid().getValue()) {
            bid = request.getMinBid().getValue();
        }
        targetTargetingPo.setBid(bid);

        targetTargetingPo.setSuggested(sourceTargetingPo.getSuggested());
        targetTargetingPo.setRangeStart(sourceTargetingPo.getRangeStart());
        targetTargetingPo.setRangeEnd(sourceTargetingPo.getRangeEnd());

        targetTargetingPo.setCreateId(request.getUid());

        if (TargetTypeEnum.asin.name().equals(sourceTargetingPo.getType())) {
            targetTargetingPo.setSelectType(sourceTargetingPo.getSelectType());
            targetTargetingPo.setTargetingValue(sourceTargetingPo.getTargetingValue());
            targetTargetingPo.setImgUrl(sourceTargetingPo.getImgUrl());
            targetTargetingPo.setTitle(sourceTargetingPo.getTitle());
        } else {
            targetTargetingPo.setTargetingValue(sourceTargetingPo.getTargetingValue());
        }
        targetTargetingPo.setExpression(sourceTargetingPo.getExpression());
    }

    /**
     * 多线程批量创建否定关键词,一个广告组一个线程
     *
     * @param request                       request 不能为null
     * @param sourceGroupIdTargetGroupIdMap 源广告组id-》目标广告组id 的map
     * @param sourceShop                    源shop 不能为null
     * @param targetShop                    目标shop 不能为null
     * @param targetAmazonAdProfile         目标profile 不能为null
     * @return List<TargetResp> 每个元素代表一个广告组
     */
    public List<Result<List<NeKeywordInfoResp>>> createBatchNeKeywords(CopySpAdsReq request, Map<String, String> sourceGroupIdTargetGroupIdMap, ShopAuth sourceShop, ShopAuth targetShop, String targetCampaignId, AmazonAdProfile targetAmazonAdProfile) {
        if (CopyGroupNeTargetStrategyEnum.NOT_COPY.getValue() == request.getCopyGroupNeTargetStrategy()) {
            log.info("不复制广告组层级否定关键词，puid：{}， shopId:{}, campaignId:{}", request.getPuid(), sourceShop.getId(), targetCampaignId);
            return Collections.emptyList();
        }
        if (sourceGroupIdTargetGroupIdMap == null) {
            return Collections.emptyList();
        }
        //1,清洗sourceGroupIdTargetGroupIdMap,将key或者value为null的entry过滤
        sourceGroupIdTargetGroupIdMap = sourceGroupIdTargetGroupIdMap.entrySet().stream().filter(entry -> entry.getKey() != null && entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (sourceGroupIdTargetGroupIdMap.isEmpty()) {
            return Collections.emptyList();
        }
        //2,构建 目标广告组id -》 需要添加的否定投放列表 的map,表示需要调用亚马逊接口创建的投放
        Map<String, CopySpTargetDto> targetGroupIdCreateSpNeKeywordDtoMap = buildTargetGroupIdNeKeywordListMap(request, targetCampaignId, sourceShop, targetShop, sourceGroupIdTargetGroupIdMap, targetAmazonAdProfile);
        // 构建 目标广告组id -》 广告组po 的map，因为创建投放需要用到广告组po
        List<String> targetGroupIdList = targetGroupIdCreateSpNeKeywordDtoMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getListByAdGroupIds(request.getPuid(), targetShop.getId(), targetGroupIdList);
        Map<String, AmazonAdGroup> targetGroupIdGroupMap = amazonAdGroupList.stream().filter(groupPo -> groupPo != null && groupPo.getAdGroupId() != null).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity()));

        //2，为创建成功的广告组添加关键词投放,按广告组维度多线程创建投放
        List<Result<List<NeKeywordInfoResp>>> neKeywordRespList = new ArrayList<>(targetGroupIdCreateSpNeKeywordDtoMap.size());
        targetGroupIdCreateSpNeKeywordDtoMap.forEach((targetGroupId, copySpTargetDto) -> {
            if (!targetGroupIdGroupMap.containsKey(targetGroupId)) {
                return;
            }
            List<AmazonAdKeyword> neKeywords = copySpTargetDto.getNeKeywords();
            //翻译关键词
            if (CopyGroupNeTargetStrategyEnum.COPY_TRANSLATED_ALL.getValue() == request.getCopyGroupNeTargetStrategy()) {
                spTargetingManager.dealTranslateKeyword(neKeywords, sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), targetShop, targetAmazonAdProfile);
            }
            List<List<AmazonAdKeyword>> neKeywordPartitionList = Lists.partition(neKeywords, MAX_SIZE);
            for (int i = 0; i < neKeywordPartitionList.size(); i++) {
                List<AmazonAdKeyword> neKeywordList = neKeywordPartitionList.get(i);
                int indexOffset = i * MAX_SIZE;
                Result<List<NeKeywordInfoResp>> neKeywordsResult = cpcNeKeywordsService.addNeKeywordsWithAuthed(targetGroupIdGroupMap.get(targetGroupId), neKeywordList, request.getLoginIp(), targetShop, indexOffset);
                neKeywordRespList.add(neKeywordsResult);
            }
        });
        return neKeywordRespList;
    }

    /**
     * 构建 目标广告组id -》 需要添加的否定关键词列表
     *
     * @param request                       request
     * @param targetCampaignId              目标广告活动id
     * @param sourceShop                    源店铺
     * @param targetShop                    目标店铺
     * @param sourceGroupIdTargetGroupIdMap 源广告组id -》目标广告组id map
     * @param targetAmazonAdProfile         目标amazonAdProfile
     * @return 目标广告组id -》 需要添加的否定关键词列表
     */
    private Map<String, CopySpTargetDto> buildTargetGroupIdNeKeywordListMap(CopySpAdsReq request, String targetCampaignId, ShopAuth sourceShop, ShopAuth targetShop, Map<String, String> sourceGroupIdTargetGroupIdMap, AmazonAdProfile targetAmazonAdProfile) {
        //1，构建关键词投放广告组id列表，和投放广告组id列表，为了之后查找相关投放列表；
        List<String> sourceGroupIdList = new ArrayList<>(sourceGroupIdTargetGroupIdMap.keySet());
        List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getAdGroupByIds(request.getPuid(), sourceShop.getId(), sourceGroupIdList);
        List<String> keywordGroupIds = new ArrayList<>();
        for (AmazonAdGroup amazonAdGroup : adGroupList) {
            if (Objects.isNull(amazonAdGroup) || Objects.isNull(amazonAdGroup.getAdGroupType())) {
                continue;
            }
            if (AutoAndKeywordAndTargetingEnum.KEYWORD.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType()) || AutoAndKeywordAndTargetingEnum.AUTO.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType())) {
                keywordGroupIds.add(amazonAdGroup.getAdGroupId());
            }
        }
        //2，处理否定关键词投放
        Map<String, CopySpTargetDto> targetGroupIdNeKeywordDtoMap = new HashMap<>(keywordGroupIds.size());
        if (CollectionUtils.isNotEmpty(keywordGroupIds)) {
            List<AmazonAdNeKeyword> nekeywordList = amazonAdNekeywordDao.getListByGroupIds(request.getPuid(), sourceShop.getId(), keywordGroupIds);
            for (AmazonAdNeKeyword sourceNeKeywordPo : nekeywordList) {
                if (Objects.isNull(sourceNeKeywordPo)) {
                    continue;
                }
                String sourceAdGroupId = sourceNeKeywordPo.getAdGroupId();
                String targetGroupId = sourceGroupIdTargetGroupIdMap.get(sourceAdGroupId);
                if (StringUtils.isBlank(targetGroupId)) {
                    continue;
                }
                AmazonAdGroup targetGroup = amazonAdGroupDao.getByAdGroupId(request.getPuid(), targetShop.getId(), targetGroupId);
                if (targetGroupIdNeKeywordDtoMap.containsKey(targetGroupId)) {
                    CopySpTargetDto copySpTargetDto = targetGroupIdNeKeywordDtoMap.get(targetGroupId);
                    if (copySpTargetDto.getNeKeywords() != null) {
                        AmazonAdKeyword targetNeKeyword = new AmazonAdKeyword();
                        fillAmazonAdNeKeyword(targetShop, targetCampaignId, targetGroup, targetNeKeyword, sourceNeKeywordPo, request.getUid(), targetAmazonAdProfile);
                        copySpTargetDto.getNeKeywords().add(targetNeKeyword);
                    }
                } else {
                    CopySpTargetDto copySpTargetDto = new CopySpTargetDto();
                    copySpTargetDto.setUid(request.getUid());
                    copySpTargetDto.setPuid(request.getPuid());
                    copySpTargetDto.setShopId(targetShop.getId());
                    copySpTargetDto.setLoginIp(request.getLoginIp());
                    copySpTargetDto.setGroupId(targetGroupId);
                    List<AmazonAdKeyword> keywords = new ArrayList<>();
                    AmazonAdKeyword keyword = new AmazonAdKeyword();
                    fillAmazonAdNeKeyword(targetShop, targetCampaignId, targetGroup, keyword, sourceNeKeywordPo, request.getUid(), targetAmazonAdProfile);
                    keywords.add(keyword);
                    copySpTargetDto.setNeKeywords(keywords);
                    targetGroupIdNeKeywordDtoMap.put(targetGroupId, copySpTargetDto);
                }
            }

        }

        return targetGroupIdNeKeywordDtoMap;
    }

    private void fillAmazonAdNeKeyword(ShopAuth targetShop, String targetCampaignId, AmazonAdGroup targetGroup, AmazonAdKeyword targetKeywordPo, AmazonAdNeKeyword sourceKeywordPo, Integer uid, AmazonAdProfile targetAmazonAdProfile) {
        targetKeywordPo.setPuid(targetShop.getPuid());
        targetKeywordPo.setShopId(targetShop.getId());
        targetKeywordPo.setMarketplaceId(targetShop.getMarketplaceId());
        targetKeywordPo.setProfileId(targetAmazonAdProfile.getProfileId());
        targetKeywordPo.setAdGroupId(targetGroup.getAdGroupId());
        targetKeywordPo.setDxmGroupId(targetGroup.getId());
        targetKeywordPo.setCampaignId(targetCampaignId);
        targetKeywordPo.setKeywordText(sourceKeywordPo.getKeywordText());
        targetKeywordPo.setMatchType(sourceKeywordPo.getMatchType());
        targetKeywordPo.setType(Constants.NEGATIVE);
        targetKeywordPo.setState(CpcStatusEnum.enabled.name());

        targetKeywordPo.setCreateId(uid);
    }

    /**
     * 多线程批量创建否定商品投放,一个广告组一个线程
     *
     * @param request                       request 不能为null
     * @param sourceGroupIdTargetGroupIdMap 源广告组id-》目标广告组id 的map
     * @param sourceShop                    源shop 不能为null
     * @param targetShop                    目标shop 不能为null
     * @return List<TargetResp> 每个元素代表一个广告组
     */
    public List<Result<List<NeTargetInfoResp>>> createBatchNeTargetings(CopySpAdsReq request, Map<String, String> sourceGroupIdTargetGroupIdMap, ShopAuth sourceShop, ShopAuth targetShop, String targetCampaignId, AmazonAdProfile targetAmazonAdProfile) {
        if (CopyGroupNeTargetStrategyEnum.NOT_COPY.getValue() == request.getCopyGroupNeTargetStrategy()) {
            log.info("不复制广告组层级否定投放，puid：{}， shopId:{}, campaignId:{}", request.getPuid(), sourceShop.getId(), targetCampaignId);
            return Collections.emptyList();
        }
        if (sourceGroupIdTargetGroupIdMap == null) {
            return Collections.emptyList();
        }
        //1,清洗sourceGroupIdTargetGroupIdMap,将key或者value为null的entry过滤
        sourceGroupIdTargetGroupIdMap = sourceGroupIdTargetGroupIdMap.entrySet().stream().filter(entry -> entry.getKey() != null && entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (sourceGroupIdTargetGroupIdMap.isEmpty()) {
            return Collections.emptyList();
        }
        //2,构建 目标广告组id -》 需要添加的否定投放列表 的map,表示需要调用亚马逊接口创建的投放
        Map<String, CopySpTargetDto> targetGroupIdCreateSpNeTargetingDtoMap = buildTargetGroupIdNeTargetingListMap(targetCampaignId, request, sourceShop, targetShop, sourceGroupIdTargetGroupIdMap, targetAmazonAdProfile);
        // 构建 目标广告组id -》 广告组po 的map，因为创建投放需要用到广告组po
        List<String> targetGroupIdList = targetGroupIdCreateSpNeTargetingDtoMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getListByAdGroupIds(request.getPuid(), targetShop.getId(), targetGroupIdList);
        Map<String, AmazonAdGroup> targetGroupIdGroupMap = amazonAdGroupList.stream().filter(groupPo -> groupPo != null && groupPo.getAdGroupId() != null).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity()));

        //2，为创建成功的广告组添加广告产品,按广告组维度多线程创建投放
        List<Result<List<NeTargetInfoResp>>> neTargetingRespList = new ArrayList<>(targetGroupIdCreateSpNeTargetingDtoMap.size());
        targetGroupIdCreateSpNeTargetingDtoMap.forEach((targetGroupId, copySpTargetDto) -> {
            if (!targetGroupIdGroupMap.containsKey(targetGroupId)) {
                return;
            }
            List<SpNeTargetingVo> neTargetings = copySpTargetDto.getNeTargetings();
            List<List<SpNeTargetingVo>> neTargetingsPartitionList = Lists.partition(neTargetings, MAX_SIZE);
            for (int i = 0; i < neTargetingsPartitionList.size(); i++) {
                List<SpNeTargetingVo> spNeTargetingVos = neTargetingsPartitionList.get(i);
                int indexOffset = i * MAX_SIZE;
                Result<List<NeTargetInfoResp>> neTargetingResult = cpcTargetingService.createNeProductTargetingWithAuthed(spNeTargetingVos, targetGroupIdGroupMap.get(targetGroupId), request.getLoginIp(), targetShop, indexOffset);
                neTargetingRespList.add(neTargetingResult);
            }

        });
        return neTargetingRespList;
    }

    /**
     * 构建 目标广告组id -》 需要添加的否定商品投放列表
     *
     * @param targetCampaignId              目标广告活动id
     * @param request                       request
     * @param sourceShop                    源shop
     * @param targetShop                    目标shop
     * @param sourceGroupIdTargetGroupIdMap 源广告组id -》目标广告组idmap
     * @return 目标广告组id -》 需要添加的否定商品投放列表
     */
    private Map<String, CopySpTargetDto> buildTargetGroupIdNeTargetingListMap(String targetCampaignId, CopySpAdsReq request, ShopAuth sourceShop, ShopAuth targetShop, Map<String, String> sourceGroupIdTargetGroupIdMap, AmazonAdProfile targetAmazonAdProfile) {
        //1，构建关键词投放广告组id列表，和投放广告组id列表，为了之后查找相关投放列表；
        List<String> sourceGroupIdList = new ArrayList<>(sourceGroupIdTargetGroupIdMap.keySet());
        List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getAdGroupByIds(request.getPuid(), sourceShop.getId(), sourceGroupIdList);
        List<String> targetingGroupIds = new ArrayList<>();
        for (AmazonAdGroup amazonAdGroup : adGroupList) {
            if (Objects.isNull(amazonAdGroup) || Objects.isNull(amazonAdGroup.getAdGroupType())) {
                continue;
            }
            if (AutoAndKeywordAndTargetingEnum.TARGETING.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType()) || AutoAndKeywordAndTargetingEnum.AUTO.getCode().equalsIgnoreCase(amazonAdGroup.getAdGroupType())) {
                targetingGroupIds.add(amazonAdGroup.getAdGroupId());
            }
        }

        //2，处理否定商品投放
        Map<String, CopySpTargetDto> targetGroupIdTargetDtoMap = new HashMap<>(adGroupList.size());
        if (CollectionUtils.isNotEmpty(targetingGroupIds)) {
            List<AmazonAdNeTargeting> neTargetingList = amazonAdNeTargetingDao.getListByGroupIds(request.getPuid(), sourceShop.getId(), sourceGroupIdList);
            for (AmazonAdNeTargeting sourceTargetingPo : neTargetingList) {
                if (sourceTargetingPo == null) {
                    continue;
                }
                String sourceAdGroupId = sourceTargetingPo.getAdGroupId();
                String targetGroupId = sourceGroupIdTargetGroupIdMap.get(sourceAdGroupId);
                if (StringUtils.isBlank(targetGroupId)) {
                    continue;
                }
                AmazonAdGroup targetGroup = amazonAdGroupDao.getByAdGroupId(request.getPuid(), targetShop.getId(), targetGroupId);
                if (targetGroupIdTargetDtoMap.containsKey(targetGroupId)) {
                    CopySpTargetDto copySpTargetDto = targetGroupIdTargetDtoMap.get(targetGroupId);
                    if (copySpTargetDto.getNeTargetings() != null) {
                        SpNeTargetingVo targetTargetingPo = new SpNeTargetingVo();
                        fillAmazonAdNeTargeting(targetShop, targetCampaignId, targetGroup, targetTargetingPo, sourceTargetingPo, request.getUid(), targetAmazonAdProfile);
                        copySpTargetDto.getNeTargetings().add(targetTargetingPo);
                    }
                } else {
                    CopySpTargetDto copySpTargetDto = new CopySpTargetDto();
                    copySpTargetDto.setUid(request.getUid());
                    copySpTargetDto.setPuid(request.getPuid());
                    copySpTargetDto.setShopId(targetShop.getId());
                    copySpTargetDto.setLoginIp(request.getLoginIp());
                    copySpTargetDto.setGroupId(targetGroupId);
                    SpNeTargetingVo targetTargetingPo = new SpNeTargetingVo();
                    fillAmazonAdNeTargeting(targetShop, targetCampaignId, targetGroup, targetTargetingPo, sourceTargetingPo, request.getUid(), targetAmazonAdProfile);
                    List<SpNeTargetingVo> spNeTargetingVos = new ArrayList<>();
                    spNeTargetingVos.add(targetTargetingPo);
                    copySpTargetDto.setNeTargetings(spNeTargetingVos);
                    targetGroupIdTargetDtoMap.put(targetGroupId, copySpTargetDto);
                }
            }
        }
        return targetGroupIdTargetDtoMap;
    }

    private void fillAmazonAdNeTargeting(ShopAuth targetShop, String targetCampaignId, AmazonAdGroup targetGroup, SpNeTargetingVo neTargetTargetingPo, AmazonAdNeTargeting sourceTargetingPo, Integer uid, AmazonAdProfile targetAmazonAdProfile) {
        neTargetTargetingPo.setPuid(sourceTargetingPo.getPuid());
        neTargetTargetingPo.setShopId(targetShop.getId());
        neTargetTargetingPo.setMarketplaceId(targetShop.getMarketplaceId());
        neTargetTargetingPo.setAdGroupId(targetGroup.getAdGroupId());
        neTargetTargetingPo.setDxmGroupId(targetGroup.getId());
        neTargetTargetingPo.setCampaignId(targetCampaignId);
        neTargetTargetingPo.setProfileId(targetAmazonAdProfile.getProfileId());
        neTargetTargetingPo.setState(sourceTargetingPo.getState());
        neTargetTargetingPo.setType(sourceTargetingPo.getType());

        neTargetTargetingPo.setIndex(sourceTargetingPo.getIndex());
        neTargetTargetingPo.setCreateId(uid);

        neTargetTargetingPo.setTargetingValue(sourceTargetingPo.getTargetingValue());
        neTargetTargetingPo.setImgUrl(sourceTargetingPo.getImgUrl());
        neTargetTargetingPo.setTitle(sourceTargetingPo.getTitle());

        neTargetTargetingPo.setExpression(sourceTargetingPo.getExpression());
    }

    /**
     * 异步复制广告组及以下层级
     */
    @Async("spCopyTaskExecutor")
    public void copySpAds(CopySpAdsReq request, String sourceCampaignId, String targetCampaignId, AmazonAdProfile targetAmazonAdProfile, ShopAuth sourceShop, ShopAuth targetShop) {
        // 批量创建广告组
        Map<String, CreateGroupResultDto> groupResultMap = createBatchGroup(request, targetCampaignId, targetShop);
        // 过滤得到创建成功的目标广告组id，构成源广告组id-》目标广告组id的map
        Map<String, String> sourceGroupIdTargetGroupIdMap = getSourceGroupIdTargetGroupIdMap(groupResultMap);

        List<CompletableFuture<Void>> futureList = new ArrayList<>(6);
        CopySpResultDto copySpResultDto = new CopySpResultDto();
        CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
            // 复制广告产品
            List<Result<List<ProductInfoResp>>> batchProducts = createBatchProducts(request, sourceGroupIdTargetGroupIdMap, targetShop, targetCampaignId, targetAmazonAdProfile);
            copySpResultDto.setBatchProducts(batchProducts);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制广告产品", e);
            return null;
        });
        futureList.add(future1);
        CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
            // 复制投放
            List<Result<List<TargetInfoResp>>> batchTargets = createBatchTargets(request, sourceGroupIdTargetGroupIdMap, sourceShop, targetShop, targetAmazonAdProfile, targetCampaignId);
            copySpResultDto.setBatchTargets(batchTargets);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制投放", e);
            return null;
        });
        futureList.add(future2);
        CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> {
            // 复制广告组层级否定关键词
            List<Result<List<NeKeywordInfoResp>>> batchNeKeywords = createBatchNeKeywords(request, sourceGroupIdTargetGroupIdMap, sourceShop, targetShop, targetCampaignId, targetAmazonAdProfile);
            copySpResultDto.setBatchNeKeywords(batchNeKeywords);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制广告组层级否定关键词", e);
            return null;
        });
        futureList.add(future3);
        CompletableFuture<Void> future4 = CompletableFuture.runAsync(() -> {
            // 复制广告组层级否定投放
            List<Result<List<NeTargetInfoResp>>> batchNeTargetings = createBatchNeTargetings(request, sourceGroupIdTargetGroupIdMap, sourceShop, targetShop, targetCampaignId, targetAmazonAdProfile);
            copySpResultDto.setBatchNeTargetings(batchNeTargetings);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制广告组层级否定投放", e);
            return null;
        });
        futureList.add(future4);
        CompletableFuture<Void> future5 = CompletableFuture.runAsync(() -> {
            // 复制广告活动层级否定关键词
            List<Result<List<CampaignNeKeywordInfoResp>>> batchCampaignNeKeywords = createBatchCampaignNekeywords(request, sourceCampaignId, targetCampaignId, sourceShop, targetShop, targetAmazonAdProfile);
            copySpResultDto.setBatchCampaignNeKeywords(batchCampaignNeKeywords);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制广告活动层级否定关键词", e);
            return null;
        });
        futureList.add(future5);
        CompletableFuture<Void> future6 = CompletableFuture.runAsync(() -> {
            // 复制广告活动层级否定投放
            List<Result<List<CampaignNeTargetInfoResp>>> batchCampaignNeTargetings = createBatchCampaignNeTargetings(request, sourceCampaignId, targetCampaignId, sourceShop, targetShop, targetAmazonAdProfile);
            copySpResultDto.setBatchCampaignNeTargetings(batchCampaignNeTargetings);
        }, spBatchCreateTaskExecutor).exceptionally((e) -> {
            log.warn("error for 复制广告活动层级否定投放", e);
            return null;
        });
        futureList.add(future6);
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        try {
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while get CompletableFuture FbaInfo", e);
        }

        //整个广告创建完成之后再同步一下活动状态
        try {
            cpcAdSyncService.syncSpCampaignState(targetShop, targetCampaignId);
            //同步所有投放
            if (StringUtils.isNotBlank(targetCampaignId)) {
                cpcTargetingApiService.syncTargetings(targetShop, targetCampaignId, null, null);
            }
        } catch (Exception e) {
            log.error("createCampaign, sync sp campaign state error:", e);
        }

        // 统计执行结果
        statisticsResult(request, targetCampaignId, groupResultMap, copySpResultDto);
        //企业微信告警
        printResultToWx(copySpResultDto, targetCampaignId, sourceShop, targetShop);
    }

    private void printResultToWx(CopySpResultDto copySpResultDto, String targetCampaignId, ShopAuth sourceShop, ShopAuth targetShop) {

        List<Result<List<ProductInfoResp>>> batchProducts = copySpResultDto.getBatchProducts();
        List<Result<List<TargetInfoResp>>> batchTargets = copySpResultDto.getBatchTargets();
        List<Result<List<NeKeywordInfoResp>>> batchNeKeywords = copySpResultDto.getBatchNeKeywords();
        List<Result<List<NeTargetInfoResp>>> batchNeTargetings = copySpResultDto.getBatchNeTargetings();
        List<Result<List<CampaignNeKeywordInfoResp>>> batchCampaignNeKeywords = copySpResultDto.getBatchCampaignNeKeywords();
        List<Result<List<CampaignNeTargetInfoResp>>> batchCampaignNeTargetings = copySpResultDto.getBatchCampaignNeTargetings();

        CompletableFuture.runAsync(() -> {
            for (Result<List<ProductInfoResp>> result : batchProducts) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告产品失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                    continue;
                }
                List<ProductInfoResp> data = result.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告产品失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n ------失败原因------", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (ProductInfoResp productInfoResp : data) {
                        if (productInfoResp != null && productInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(productInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            for (Result<List<TargetInfoResp>> result : batchTargets) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告投放失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                    continue;
                }
                List<TargetInfoResp> data = result.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告投放失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n index------失败原因", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (TargetInfoResp targetInfoResp : data) {
                        if (targetInfoResp != null && targetInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(targetInfoResp.getIndex()).append("------").append(targetInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            for (Result<List<NeKeywordInfoResp>> result : batchNeKeywords) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告组层级否定关键词失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                    continue;
                }
                List<NeKeywordInfoResp> data = result.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告组层级否定关键词失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n index------失败原因", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (NeKeywordInfoResp neKeywordInfoResp : data) {
                        if (neKeywordInfoResp != null && neKeywordInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(neKeywordInfoResp.getIndex()).append("------").append(neKeywordInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            for (Result<List<NeTargetInfoResp>> result : batchNeTargetings) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告组层级否定投放失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                    continue;
                }
                List<NeTargetInfoResp> data = result.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告组层级否定投放失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n index------失败原因", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (NeTargetInfoResp neTargetInfoResp : data) {
                        if (neTargetInfoResp != null && neTargetInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(neTargetInfoResp.getIndex()).append("------").append(neTargetInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            for (Result<List<CampaignNeKeywordInfoResp>> result : batchCampaignNeKeywords) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告活动层级否定关键词失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                }
                List<CampaignNeKeywordInfoResp> campaignNeKeywordsData = result.getData();
                if (CollectionUtils.isNotEmpty(campaignNeKeywordsData)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告活动层级否定关键词失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n index------失败原因", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (CampaignNeKeywordInfoResp campaignNeKeywordInfoResp : campaignNeKeywordsData) {
                        if (campaignNeKeywordInfoResp != null && campaignNeKeywordInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(campaignNeKeywordInfoResp.getIndex()).append("------").append(campaignNeKeywordInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

            for (Result<List<CampaignNeTargetInfoResp>> result : batchCampaignNeTargetings) {
                if (result.error()) {
                    String msg = String.format("复制广告：创建广告活动层级否定投放失败，活动id：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d", targetCampaignId, result.getMsg(), sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                }
                List<CampaignNeTargetInfoResp> campaignNeTargetingsData = result.getData();
                if (CollectionUtils.isNotEmpty(campaignNeTargetingsData)) {
                    boolean print = false;
                    StringBuilder stringBuilder = new StringBuilder();
                    String msg = String.format("复制广告：创建广告活动层级否定投放失败，活动id：%s，puid：%d，sourceShopId：%d, targetShopId：%d \n index------失败原因", targetCampaignId, sourceShop.getPuid(), sourceShop.getId(), targetShop.getId());
                    stringBuilder.append(msg);
                    for (CampaignNeTargetInfoResp campaignNeTargetInfoResp : campaignNeTargetingsData) {
                        if (campaignNeTargetInfoResp != null && campaignNeTargetInfoResp.getCode() != ResultUtil.SUCCESS) {
                            print = true;
                            stringBuilder.append("\n").append(campaignNeTargetInfoResp.getIndex()).append("------").append(campaignNeTargetInfoResp.getErrMsg());
                        }
                    }
                    if (print) {
                        WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, stringBuilder.toString());
                    }
                }
            }

        }, ThreadPoolUtil.getPrintWxLogExecutor()).exceptionally((e) -> null);

    }

    /**
     * 创建活动层级否定关键词
     * @param request request
     * @param sourceCampaignId 源广告活动id
     * @param targetCampaignId 目标广告活动id
     * @param sourceShop 源店铺
     * @param targetShop 目标店铺
     * @param targetAmazonAdProfile 目标amazonAdProfile
     * @return 创建活动层级否定关键词result
     */
    private List<Result<List<CampaignNeKeywordInfoResp>>> createBatchCampaignNekeywords(CopySpAdsReq request, String sourceCampaignId, String targetCampaignId, ShopAuth sourceShop, ShopAuth targetShop, AmazonAdProfile targetAmazonAdProfile) {
        if (CopyCampaignNeTargetStrategyEnum.NOT_COPY.getValue() == request.getCopyCampaignNeTargetStrategy()) {
            log.info("不复制活动层级否定关键词，puid：{}， shopId:{}, campaignId:{}", request.getPuid(), sourceShop.getId(), targetCampaignId);
            return Collections.emptyList();
        }
        List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords = amazonAdCampaignNeKeywordsDao.ListByCampaignId(request.getPuid(), sourceShop.getId(), sourceCampaignId);
        if (CollectionUtils.isEmpty(amazonAdCampaignNeKeywords)) {
            return Collections.emptyList();
        }
        //翻译关键词
        if (CopyCampaignNeTargetStrategyEnum.COPY_TRANSLATED_ALL.getValue() == request.getCopyCampaignNeTargetStrategy()) {
            cpcSpTargetingManager.dealTranslateNeKeyword(amazonAdCampaignNeKeywords, sourceShop.getMarketplaceId(), targetShop.getMarketplaceId(), targetShop, targetAmazonAdProfile);
        }
        //替换为目标属性
        amazonAdCampaignNeKeywords.forEach(k -> {
            k.setCampaignId(targetCampaignId);
            k.setPuid(targetShop.getPuid());
            k.setShopId(targetShop.getId());
            k.setMarketplaceId(targetShop.getMarketplaceId());
            k.setProfileId(targetAmazonAdProfile.getProfileId());
            k.setCreateId(request.getUid());
            k.setKeywordId(null);
            k.setUpdateId(null);
            k.setCreationDate(null);
            k.setCreateTime(null);
            k.setUpdateTime(null);
        });
        List<Result<List<CampaignNeKeywordInfoResp>>> results = new ArrayList<>(amazonAdCampaignNeKeywords.size());
        List<List<AmazonAdCampaignNeKeywords>> neKeywordPartition = Lists.partition(amazonAdCampaignNeKeywords, MAX_SIZE);
        for (int i = 0; i < neKeywordPartition.size(); i++) {
            List<AmazonAdCampaignNeKeywords> neKeywordsList = neKeywordPartition.get(i);
            int indexOffset = i * MAX_SIZE;
            Result<List<CampaignNeKeywordInfoResp>> listResult = cpcNeKeywordsService.addCampaignNeKeywordsWithAuthed(neKeywordsList, targetCampaignId, targetShop, request, sourceShop, targetAmazonAdProfile, indexOffset);
            results.add(listResult);
        }
        return results;
    }

    /**
     * 创建活动层级否定商品投放
     * @param request request
     * @param sourceCampaignId 源广告活动id
     * @param targetCampaignId 目标广告活动id
     * @param sourceShop 源店铺
     * @param targetShop 目标店铺
     * @param targetAmazonAdProfile 目标amazonAdProfile
     * @return 创建活动层级否定商品投放 result
     */
    private List<Result<List<CampaignNeTargetInfoResp>>> createBatchCampaignNeTargetings(CopySpAdsReq request, String sourceCampaignId, String targetCampaignId, ShopAuth sourceShop, ShopAuth targetShop, AmazonAdProfile targetAmazonAdProfile) {
        if (CopyCampaignNeTargetStrategyEnum.NOT_COPY.getValue() == request.getCopyCampaignNeTargetStrategy()) {
            log.info("不复制活动层级否定投放，puid：{}， shopId:{}, campaignId:{}", request.getPuid(), sourceShop.getId(), targetCampaignId);
            return Collections.emptyList();
        }
        List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps = amazonAdCampaignNetargetingSpDao.ListByCampaignId(request.getPuid(), sourceShop.getId(), sourceCampaignId);

        if (CollectionUtils.isEmpty(amazonAdCampaignNetargetingSps)) {
            return Collections.emptyList();
        }
        //po -> 创建dto
        List<AmazonAdCampaignNetargetingSp> campaignNetargetingDtos = new ArrayList<>(amazonAdCampaignNetargetingSps.size());
        for (AmazonAdCampaignNetargetingSp netargetingSp : amazonAdCampaignNetargetingSps) {
            if (StringUtils.isBlank(netargetingSp.getTargetId())) {
                continue;
            }
            AmazonAdCampaignNetargetingSp dto = new AmazonAdCampaignNetargetingSp();
            dto.setCampaignId(targetCampaignId);
            dto.setTargetText(netargetingSp.getTargetText());
            dto.setTitle(netargetingSp.getTitle());
            dto.setImgUrl(netargetingSp.getImgUrl());
            campaignNetargetingDtos.add(dto);
        }
        List<List<AmazonAdCampaignNetargetingSp>> partition = Lists.partition(campaignNetargetingDtos, MAX_SIZE);
        List<Result<List<CampaignNeTargetInfoResp>>> results = new ArrayList<>(campaignNetargetingDtos.size());
        for (int i = 0; i < partition.size(); i++) {
            List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps1 = partition.get(i);
            int indexOffset = i * MAX_SIZE;
            Result<List<CampaignNeTargetInfoResp>> listResult = cpcNeKeywordsService.addCampaignNeTargetingsWithAuthed(request.getUid(), amazonAdCampaignNetargetingSps1, targetCampaignId, request.getLoginIp(), targetShop, targetAmazonAdProfile, indexOffset);
            results.add(listResult);
        }
        return results;
    }

    /**
     * 批量复制广告组
     *
     * @param request          request
     * @param targetCampaignId 目标广告活动id
     * @param targetShop       目标店铺
     * @return 源广告组id -》新广告组创建结果
     */
    private Map<String, CreateGroupResultDto> createBatchGroup(CopySpAdsReq request, String targetCampaignId, ShopAuth targetShop) {
        // 1，构建请求参数
        List<SPadGroupVo> groupVoList = new ArrayList<>();
        List<CopySpGroupInfoReq> groupInfoListList = request.getEditedSpGroupInfoListList();

        Map<String, AmazonAdGroup> groupMap = new HashMap<>();
        // 与原状态保持一致从数据库获取原广告组状态
        if (SpCopyConstants.STATE_KEEP.equals(request.getGroupStatus())) {
            List<String> groudIdList = StreamUtil.toList(groupInfoListList, CopySpGroupInfoReq::getGroupId);
            List<AmazonAdGroup> adGroupList = amazonAdGroupDao.getListByAdGroupIds(request.getPuid(), request.getShopId(), groudIdList);
            groupMap = StreamUtil.toMap(adGroupList, AmazonAdGroup::getAdGroupId);
        }
        for (CopySpGroupInfoReq copySpGroupInfoReq : groupInfoListList) {
            if (copySpGroupInfoReq == null) {
                continue;
            }
            if (StringUtils.isBlank(copySpGroupInfoReq.getName())) {
                continue;
            }
            SPadGroupVo groupVo = new SPadGroupVo();
            double defaultBid = copySpGroupInfoReq.getDefaultBid().getValue();
            defaultBid = Math.max(defaultBid, request.getMinBid().getValue());
            defaultBid = Math.min(defaultBid, request.getMaxBid().getValue());
            groupVo.setDefaultBid(defaultBid);
            groupVo.setName(StringUtil.trimAndClearInvisibleChar(copySpGroupInfoReq.getName()));
            groupVo.setUid(request.getUid());
            groupVo.setPuid(request.getPuid());
            groupVo.setShopId(request.getTargetShopId());
            groupVo.setLoginIp(request.getLoginIp());
            groupVo.setCampaignId(targetCampaignId);
            groupVo.setGroupId(copySpGroupInfoReq.getGroupId());
            if (SpCopyConstants.STATE_KEEP.equals(request.getGroupStatus()) && groupMap.containsKey(copySpGroupInfoReq.getGroupId())) {
                AmazonAdGroup adGroup = groupMap.get(copySpGroupInfoReq.getGroupId());
                groupVo.setState(adGroup.getState());
            } else if (SpCopyConstants.STATE_ENABLED.equals(request.getGroupStatus())) {
                groupVo.setState(Constants.ENABLED);
            } else {
                groupVo.setState(Constants.PAUSED);
            }
            groupVoList.add(groupVo);
        }
        List<CreateGroupResultDto> resultList = new ArrayList<>();
        List<List<SPadGroupVo>> groupVoPartitionList = Lists.partition(groupVoList, MAX_SIZE);
        for (int i = 0; i < groupVoPartitionList.size(); i++) {
            List<SPadGroupVo> groupVoPartition = groupVoPartitionList.get(i);
            resultList.addAll(spGroupService.createBatchGroup(groupVoPartition, targetShop));
        }
        //异步打印错误告警
        CompletableFuture.runAsync(() -> {
            for (CreateGroupResultDto dto : resultList) {
                if (dto != null && dto.getResult() != null && !dto.getResult()) {
                    String msg = String.format("复制广告：创建广告组失败，广告组名称：%s，失败原因：%s，puid：%d，sourceShopId：%d, targetShopId：%d，活动id：%s", dto.getName(), dto.getFailMsg(), request.getPuid(), request.getShopId(), targetShop.getId(), targetCampaignId);
                    WxNotificationUtil.sendBigContent(CREATE_SP_ADS_WX_URL, msg);
                }
            }
        }, ThreadPoolUtil.getPrintWxLogExecutor()).exceptionally((e) -> null);

        Map<String, CreateGroupResultDto> resultMap = StreamUtil.toMap(resultList, CreateGroupResultDto::getName);
        // 返回 map 格式 key:原groupId  value:创建结果
        return StreamUtil.toMap(groupInfoListList, CopySpGroupInfoReq::getGroupId, it -> resultMap.get(it.getName()));
    }

    /**
     * 解析复制结果，记录成功失败次数
     */
    private void statisticsResult(CopySpAdsReq request, String targetCampaignId, Map<String, CreateGroupResultDto> groupResultMap, CopySpResultDto copySpResultDto) {

        List<Result<List<ProductInfoResp>>> batchProducts = copySpResultDto.getBatchProducts();
        List<Result<List<TargetInfoResp>>> batchTargets = copySpResultDto.getBatchTargets();
        List<Result<List<NeKeywordInfoResp>>> batchNeKeywords = copySpResultDto.getBatchNeKeywords();
        List<Result<List<NeTargetInfoResp>>> batchNeTargetings = copySpResultDto.getBatchNeTargetings();
        List<Result<List<CampaignNeKeywordInfoResp>>> batchCampaignNeKeywords = copySpResultDto.getBatchCampaignNeKeywords();
        List<Result<List<CampaignNeTargetInfoResp>>> batchCampaignNeTargetings = copySpResultDto.getBatchCampaignNeTargetings();
        // 解析结果
        CountCreateResultDto groupResult = statisticsGroup(groupResultMap);
        CountCreateResultDto productResult = statisticsList(batchProducts);
        CountCreateResultDto targetResult = statisticsList(batchTargets);
        CountCreateResultDto NeKeywordResult = statisticsList(batchNeKeywords);
        CountCreateResultDto NeTargetingResult = statisticsList(batchNeTargetings);
        CountCreateResultDto campaignNeKeywordResult = statisticsList(batchCampaignNeKeywords);
        CountCreateResultDto campaignNeTargetingResult = statisticsList(batchCampaignNeTargetings);

        // 统计数量
        CopyAdsResultStatistics statistics = new CopyAdsResultStatistics();
        statistics.setPuid(request.getPuid());
        statistics.setCreatorId(request.getUid());
        statistics.setSourceCampaignId(request.getCampaignId());
        statistics.setTargetCampaignId(targetCampaignId);
        statistics.setCampaignSuccess(Boolean.TRUE);
        statistics.setGroupSuccessNum(groupResult.getSuccessNum());
        statistics.setGroupFailNum(groupResult.getFailNum());
        statistics.setAdsSuccessNum(productResult.getSuccessNum());
        statistics.setAdsFailNum(productResult.getFailNum());
        statistics.setTargetSuccessNum(targetResult.getSuccessNum());
        statistics.setTargetFailNum(targetResult.getFailNum());
        statistics.setNeTargetSuccessNum(NeKeywordResult.getSuccessNum() + NeTargetingResult.getSuccessNum());
        statistics.setNeTargetFailNum(NeKeywordResult.getFailNum() + NeTargetingResult.getFailNum());
        statistics.setCampaignNeTargetSuccessNum(campaignNeKeywordResult.getSuccessNum() + campaignNeTargetingResult.getSuccessNum());
        statistics.setCampaignNeTargetFailNum(campaignNeKeywordResult.getFailNum() + campaignNeTargetingResult.getFailNum());
        // 是否全部成功
        if (statistics.getCampaignSuccess() && statistics.getGroupFailNum() == 0 && statistics.getAdsFailNum() == 0 && statistics.getTargetFailNum() == 0 && statistics.getNeTargetFailNum() == 0 && statistics.getCampaignNeTargetFailNum() == 0) {
            statistics.setAllSuccess(Boolean.TRUE);
        }
        // 记录数据库
        try {
            copyAdsResultStatisticsDao.save(statistics);
        } catch (Exception e) {
            log.error("复制广告结果统计数据入库异常");
        }
    }

    private CountCreateResultDto statisticsGroup(Map<String, CreateGroupResultDto> groupResultMap) {
        CountCreateResultDto result = new CountCreateResultDto();
        int successNum = 0;
        int failNum = 0;
        if (CollectionUtil.isEmpty(groupResultMap)) {
            result.setSuccessNum(successNum);
            result.setFailNum(failNum);
            return result;
        }
        for (CreateGroupResultDto value : groupResultMap.values()) {
            if (value == null) {
                continue;
            }
            if (Boolean.TRUE.equals(value.getResult())) {
                successNum++;
            } else {
                failNum++;
            }
        }
        result.setSuccessNum(successNum);
        result.setFailNum(failNum);
        return result;
    }

    private <T> CountCreateResultDto statisticsList(List<Result<List<T>>> batchData) {
        CountCreateResultDto result = new CountCreateResultDto();
        int successNum = 0;
        int failNum = 0;
        if (CollectionUtil.isEmpty(batchData)) {
            result.setSuccessNum(successNum);
            result.setFailNum(failNum);
            return result;
        }
        for (Result<List<T>> batch : batchData) {
            if (batch == null || CollectionUtil.isEmpty(batch.getData())) {
                continue;
            }
            for (T datum : batch.getData()) {
                if (getStatus(datum) == Result.SUCCESS) {
                    successNum++;
                } else {
                    failNum++;
                }
            }
        }
        result.setSuccessNum(successNum);
        result.setFailNum(failNum);
        return result;
    }

    private <T> CountCreateResultDto statisticsResult(Result<List<T>> batchData) {
        CountCreateResultDto result = new CountCreateResultDto();
        int successNum = 0;
        int failNum = 0;
        if (batchData == null || CollectionUtil.isEmpty(batchData.getData())) {
            result.setSuccessNum(successNum);
            result.setFailNum(failNum);
            return result;
        }
        for (T datum : batchData.getData()) {
            if (getStatus(datum) == Result.SUCCESS) {
                successNum++;
            } else {
                failNum++;
            }
        }
        result.setSuccessNum(successNum);
        result.setFailNum(failNum);
        return result;
    }

    private int getStatus(Object datum) {
        if (datum instanceof ProductInfoResp) {
            return ((ProductInfoResp) datum).getCode();
        } else if (datum instanceof TargetInfoResp) {
            return ((TargetInfoResp) datum).getCode();
        } else if (datum instanceof NeKeywordInfoResp) {
            return ((NeKeywordInfoResp) datum).getCode();
        } else if (datum instanceof NeTargetInfoResp) {
            return ((NeTargetInfoResp) datum).getCode();
        } else if (datum instanceof CampaignNeKeywordInfoResp) {
            return ((CampaignNeKeywordInfoResp) datum).getCode();
        } else if (datum instanceof CampaignNeTargetInfoResp) {
            return ((CampaignNeTargetInfoResp) datum).getCode();
        }
        return -1; // 默认返回一个不可能的值，表示无法确定状态码
    }
}
