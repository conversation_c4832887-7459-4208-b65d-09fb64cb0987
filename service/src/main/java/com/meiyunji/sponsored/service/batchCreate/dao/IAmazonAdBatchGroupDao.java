package com.meiyunji.sponsored.service.batchCreate.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.group.GroupInfoInTaskDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.task.TaskStatusSetDto;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchGroup;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-11-16  09:30
 */
public interface IAmazonAdBatchGroupDao extends IBaseShardingDao<AmazonAdBatchGroup> {

    void insertList(Integer puid, List<AmazonAdBatchGroup> groupList);

    void updateStatusByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> idList, Byte status);

    /**
     * 批量修改为失败状态
     */
    void updateErrTaskStatusByIdList(Integer puid, Map<Long, String> idErrMsgMap, boolean updateExecuteCount);

    /**
     * 根据广告活动id列表获取所有广告组
     *
     * @param puid
     * @param campaignIdList
     * @return
     */
    List<Long> listIdByCampaignId(Integer puid, List<Long> campaignIdList);

    Map<Long, String> listIdByTaskIdAndCampaignIdAndStatus(Integer puid, Integer shopId, Long taskId, List<Long> campaignIdList, List<Integer> status);


    List<GroupInfoInTaskDTO> getGroupInfoByTaskIdAndCampaignId(Integer puid, Integer shopId,
                                                               Long taskId, List<Long> campaignId);

    List<GroupInfoInTaskDTO> getGroupBasicInfoById(Integer puid, Collection<Long> idListList);

    List<GroupInfoInTaskDTO> getGroupListByTaskId(Integer puid, Integer shopId, Long taskId);

    List<AmazonAdBatchGroup> selectNeedSubmitAmazon(Integer puid, Integer shopId, Long taskId, List<Long> idList, boolean currentLevel);

    List<Long> idListByGroupIdListAndType(Integer puid, Integer shopId, Long taskId, List<Long> idList, String type);

    void updateStatusAndRetryByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> retryIdList, Byte status, Date nextRetryTime);

    void batchUpdateCampaignId(List<AmazonAdBatchCampaign> batchCampaignList);

    void batchUpdateStatusByIdList(Integer puid, List<AmazonAdBatchGroup> batchGroupList, Byte status);

    List<TaskStatusSetDto> distinctStatusByTaskId(Integer puid, Integer shopId, Long taskId);

    int terminateGroups(Integer puid, Long taskId, Byte status,
                        List<Byte> includeStatus, String errMsg);


    List<AmazonAdBatchGroup> selectNeedRetryAmazon(Integer puid, Integer shopId, Long taskId);

    List<AmazonAdBatchGroup> selectGroupsByTaskIdAndTypeAndStatus(Integer puid, Integer shopId,
                                                                  Long taskId, String adGroupType);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
