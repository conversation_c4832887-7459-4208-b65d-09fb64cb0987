package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleActionType;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adGroup.AdGroupDefaultOrderEnum;
import com.meiyunji.sponsored.service.autoRule.vo.AdGroupAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.GetNamesDto;
import com.meiyunji.sponsored.service.cpc.vo.GroupInfoPageVo;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.strategy.vo.AdStrategyGroupParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * Created by lm on 2021/7/29.
 */
@Repository
public class AmazonSbAdGroupDaoImpl extends BaseShardingDaoImpl<AmazonSbAdGroup> implements IAmazonSbAdGroupDao {

    @Override
    public void batchAdd(int puid, List<AmazonSbAdGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSbAdGroup group : list) {
            if (group.getPuid() == null || group.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(group.getPuid());
            arg.add(group.getShopId());
            arg.add(group.getMarketplaceId());
            arg.add(group.getProfileId());
            arg.add(group.getCampaignId());
            arg.add(group.getAdGroupId());
            arg.add(group.getName());
            arg.add(group.getBid());
            arg.add(group.getCreateInAmzup());
            arg.add(group.getCreationDate());
            arg.add(group.getLastUpdatedDate());
            arg.add(group.getServingStatus());
            arg.add(group.getState());
            arg.add(group.getCreateId());
            arg.add(group.getUpdateId());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_group_sb (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`campaign_id`,`ad_group_id`," +
                "`name`,`bid`,`create_in_amzup`,`creation_date`,`last_updated_date`,`serving_status`,state,`create_id`,`update_id`,`create_time`,`update_time`) values (?,?,?,?, ?,?, ?,?, ?,?, ?,?,?,?,?, now(), now())";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSbAdGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSbAdGroup group : list) {
            if (group.getPuid() == null || group.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(group.getName());
            arg.add(group.getBid());
            arg.add(group.getState());
            arg.add(group.getLastUpdatedDate());
            arg.add(group.getServingStatus());
            arg.add(group.getCreationDate());
            arg.add(group.getUpdateId());
            arg.add(group.getLandingPage());
            arg.add(group.getAdFormat());
            arg.add(group.getPuid());
            arg.add(group.getShopId());
            arg.add(group.getAdGroupId());
            argList.add(arg.toArray());
        }

        String sql = "update `t_amazon_ad_group_sb` set `name` =?,`bid` =?,`state`=?,`last_updated_date`=?,`serving_status`=?, " +
                " `creation_date` =?,`update_id`=?, `landing_page` = ?, `ad_format` = ? " +
                " where `puid` =? and `shop_id`=? and `ad_group_id`=?";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSbAdGroup> listByGroupId(int puid, int shopId, List<String> groupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", groupIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public AmazonSbAdGroup getByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonSbAdGroup getByAdGroupId(int puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAdGroup> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[] {}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAdGroup> getAdGroupByIds(Integer puid, List<String> sbGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", sbGroupIds.toArray(new String[] {}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAdGroup> getByCampaignIds(int puid, Integer shopId, List<String> campaignIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("campaign_id", campaignIds.toArray())
                .limit(1)
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonSbAdGroup> getGroupsByCampaignIdGroupId(int puid, String marketplaceId, List<Integer> shopIds, List<String> campaignIds, List<String> sbGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select ad_group_id from ").append(this.getJdbcHelper().getTable())
                .append(" where puid=? and marketplace_id=? ");
        argsList.add(puid);
        argsList.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        sql.append(SqlStringUtil.dealInList("ad_group_id", sbGroupIds, argsList));
        return getJdbcTemplate(puid).query(sql.toString(), argsList.toArray(), new ObjectMapper<>(AmazonSbAdGroup.class));
    }

    @Override
    public List<AmazonSbAdGroup> getList(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }

        if(CollectionUtils.isNotEmpty(param.getGroupIds())){
            builder.inStrList("ad_group_id",param.getGroupIds().toArray(new String[]{}));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            List<Object> argsList = new ArrayList<>();
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "ad_group_id", false);
            if(StringUtils.isNotEmpty(sql)){
                builder.custom(sql, argsList.toArray(), true);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue().trim());
            } else {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            builder.inStrList("name", param.getSearchValueList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }


        builder.orderByDesc("id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid,builder.build());
    }

    @Override
    public List<String> getPageAllGroupIdList(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }

        if(CollectionUtils.isNotEmpty(param.getGroupIds())){
            builder.inStrList("ad_group_id",param.getGroupIds().toArray(new String[]{}));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            List<Object> argsList = new ArrayList<>();
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "ad_group_id", false);
            if(StringUtils.isNotEmpty(sql)){
                builder.custom(sql, argsList.toArray(), true);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue().trim());
            } else {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            builder.inStrList("name", param.getSearchValueList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }


        builder.orderByDesc("id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listDistinctFieldByCondition(puid, "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<String> getAdGroupIdsByGroup(Integer puid, GroupPageParam param){
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.ad_group_id from t_amazon_ad_group_sb c where c.puid = ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if(param.getShopId() != null){
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        //广告标签筛选
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        sql.append(whereSql);
        List<String> adGroupIds = getJdbcTemplate(puid).queryForList(sql.toString(),argsList.toArray(),String.class);
        return adGroupIds;
    }

    @Override
    public Page getPageList(Integer puid, GroupPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_group_sb ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select id FROM `t_amazon_ad_group_sb` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告活动标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), argsList));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "ad_group_id", false);
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));;
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        selectSql.append(" order by id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSbAdGroup.class);
    }

    @Override
    public AmazonSbAdGroup getByGroupId(int puid, Integer shopId, String groupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", groupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public void batchUpdateAdGrpupType(Integer puid, List<AmazonSbAdGroup> amazonAdGroups) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSbAdGroup amazonAdGroup : amazonAdGroups) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdGroupType(),
                    puid,
                    amazonAdGroup.getId(),
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group_sb` set `ad_group_type` = ?, `update_time`=now(3) where puid = ? and id=?", batchArgs);
    }

    @Override
    public List<AmazonSbAdGroup> listNoAdGroupType(Integer puid, Integer shopId, String campaignId, String groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (StringUtils.isNotBlank(campaignId)) {
            List<String> campaignIds = StringUtil.splitStr(campaignId, ",");
            builder.in("campaign_id",campaignIds.toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(groupId)) {
            List<String> groupIds = StringUtil.splitStr(groupId, ",");
            builder.in("ad_group_id",groupIds.toArray(new String[]{}));
        }

        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_group_sb where (ad_group_type is null or ad_group_type='') and " + conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonSbAdGroup> listNoAdGroupType(Integer puid, Integer shopId, Set<String> campaignIdSet, Set<String> groupIdSet) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        builder.in("campaign_id",campaignIdSet.toArray(new String[]{}));
        builder.in("ad_group_id",groupIdSet.toArray(new String[]{}));


        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_group_sb where (ad_group_type is null or ad_group_type='') and " + conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonSbAdGroup> getListByNullField(Integer puid, Integer shopId, String campaignId, List<String> fieldList) {
        return getListByNullField(puid, shopId, campaignId, null, fieldList);
    }


    @Override
    public List<AmazonSbAdGroup> getListByNullField(Integer puid, Integer shopId, String campaignId, List<String> groupIdList, List<String> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return null;
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (StringUtils.isNotBlank(campaignId)) {
            List<String> campaignIds = StringUtil.splitStr(campaignId, ",");
            builder.in("campaign_id",campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(groupIdList)) {
            builder.in("ad_group_id",groupIdList.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(fieldList)) {
            for (int i = 0; i < fieldList.size(); i++) {
                if (i == 0) {
                    builder.and().leftBracket().equalToWithoutCheck(LogicType.EPT, fieldList.get(i), "");
                    builder.isNull(LogicType.OR, fieldList.get(i));
                } else {
                    builder.equalToWithoutCheck(LogicType.OR, fieldList.get(i), "");
                    builder.isNull(LogicType.OR, fieldList.get(i));
                }
                if (i == fieldList.size() - 1) {
                    builder.rightBracket();
                }
            }
        }

        ConditionBuilder conditionBuilder = builder.build();
        String sql = "select * from t_amazon_ad_group_sb where " + conditionBuilder.getSql();
        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public void batchUpdateAdFormat(Integer puid, List<AmazonSbAdGroup> amazonAdGroups) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSbAdGroup amazonAdGroup : amazonAdGroups) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdFormat(),
                    amazonAdGroup.getLandingPage(),
                    puid,
                    amazonAdGroup.getId(),
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group_sb` set `ad_format` = ?, `landing_page` = ?, `update_time`=now(3) where puid = ? and id=?", batchArgs);
    }


    @Override
    public List<String> getAdGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("ad_group_id", adGroupId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"ad_group_id", builder, String.class);
    }

    @Override
    public List<Map<String, Object>> getAdGroupNames(Integer puid, GetNamesDto dto) {
        StringBuilder sql = new StringBuilder("select `name`,ad_group_id id from t_amazon_ad_group_sb where puid=? and shop_id=? and marketplace_id=?" +
                " and state in ('enabled','paused') ");
        List<Object> args = Lists.newArrayListWithExpectedSize(4);
        args.add(puid);
        args.add(dto.getShopId());
        args.add(dto.getMarketplaceId());
        if (StringUtils.isNotEmpty(dto.getCampaignId())) {
            sql.append(" and campaign_id=?");
            args.add(dto.getCampaignId());
        }
        if (Constants.GROUP_TYPE_KEYWORD.equals(dto.getAdGroupType())) {
            sql.append(" and ad_group_type <> ?");
            args.add(Constants.GROUP_TYPE_PRODUCT);
        } else if (Constants.GROUP_TYPE_PRODUCT.equals(dto.getAdGroupType())) {
            sql.append(" and ad_group_type <> ?");
            args.add(Constants.GROUP_TYPE_KEYWORD);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), args.toArray());
    }

    /**
     * 关联关键词库查询
     * @param puid
     * @param shopIds
     * @param groupIds
     * @return
     */
    @Override
    public List<AmazonSbAdGroup> getListByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .inIntList("shop_id",shopIds.toArray(new Integer[0]))
                .inStrList("ad_group_id",groupIds.toArray(new String[0]))
                .build();
        return listByCondition(puid,builder);
    }

    /**
     * 关联关键词库查询
     * @param puid
     * @param shopIds
     * @param groupIds
     * @return
     */
    @Override
    public List<AmazonSbAdGroup> getNameListByShopIdsAndGroupIds(int puid, List<Integer> shopIds,
                                                             List<String> campaignList, List<String> groupIds,
                                                                 String groupName) {
        StringBuilder selectSql = new StringBuilder(" select ad_group_id, name, ad_format, bid from t_amazon_ad_group_sb ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid= ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotEmpty(groupName)) {
            whereSql.append(" and name LIKE ?");
            argsList.add("%" + groupName +"%");
        }
        selectSql.append(whereSql);
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getRowMapper());
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String campaignId, String name) {
        String sql = "select count(*) from t_amazon_ad_group_sb where puid = ? and shop_id = ? and campaign_id = ? and `name`=? and `state` in ('ENABLED','PAUSED')";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, campaignId, name);
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String adGroupId) {
        String sql = "select count(*) from t_amazon_ad_group_sb where puid = ? and shop_id = ? and ad_group_id = ?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, adGroupId);
    }

    @Override
    public List<AmazonSbAdGroup> listNoLandingPage(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }
        builder.isNull("landing_page");
        builder.equalToWithoutCheck(LogicType.OR, "landing_page", "");

        ConditionBuilder conditionBuilder = builder.build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSbAdGroup> autoRuleAdGroup(int puid, int shopId, List<String> campaignIds, List<String> adGroupIds, String state, String searchValue,List<String> servingStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("ad_group_id",adGroupIds.toArray());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.in("campaign_id",campaignIds.toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servingStatus)) {
            conditionBuilder.in("serving_status",servingStatus.toArray());
        }
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state",state);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            conditionBuilder.like("name",searchValue);
        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public List<AmazonSbAdGroup> queryGroupList(AllUpdateAutoRuleParam param) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", param.getPuid());
        conditionBuilder.equalTo("shop_id", param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            conditionBuilder.in("campaign_id",param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            conditionBuilder.equalTo("state",param.getState());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            conditionBuilder.like("name",param.getSearchValue());
        }
        return listByCondition(param.getPuid(), conditionBuilder.build());
    }

    @Override
    public List<AmazonSbAdGroup> listByPuidAndCampaignIds(int puid, List<String> campaignIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("campaign_id", campaignIds.toArray(new String[0]))
                .build());
    }

    @Override
    public List<AmazonSbAdGroup> listByCampaignId(Integer puid, Integer shopId, String campaignId) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("campaign_id", campaignId)
                .build());
    }

    @Override
    public List<AmazonSbAdGroup> getInfoByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId);
        if (StringUtils.isNotEmpty(groupId)) {
            builder.equalTo("ad_group_id", groupId);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonSbAdGroup> queryGroup(Integer puid, Integer shopId, List<String> types, String groupName,List<String> campaignIds, List<String> adGroupTypes,Integer pageSize, Integer pageNo) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(campaignIds)) {
            builder.in("campaign_id",campaignIds.toArray());
        }
        if (StringUtils.isNotBlank(groupName)) {
            builder.like("name",groupName);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupTypes)) {
            builder.in("ad_group_type",adGroupTypes.toArray());
        }
        String orderBySql = "";
        return listByCondition(puid,builder.build());
    }

    @Override
    public Page<AmazonSbAdGroup> getPageListByStrategy(AdStrategyGroupParam param) {
        StringBuilder selectSql = new StringBuilder("select g.puid,g.shop_id,g.marketplace_id,g.ad_group_id,g.campaign_id,g.name,g.bid,g.serving_status,g.state,g.create_time " +
                " from t_amazon_ad_group_sb g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id) ");
        StringBuilder countSql = new StringBuilder("select count(g.id) from t_amazon_ad_group_sb g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id)");
        StringBuilder whereSql = new StringBuilder(" where g.puid=? and g.shop_id=? and g.ad_group_id is not null and g.is_state_bidding = 0 and c.state in ('enabled','paused')");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getState())) {
            whereSql.append(" and g.state = ? ");
            argsList.add(param.getState());
        } else {
            whereSql.append(" and g.state in ('enabled','paused')");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getAdGroupName())) {
            whereSql.append(" and g.name like ? ");
            argsList.add("%" + param.getAdGroupName() + "%");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("g.serving_status", param.getServingStatusList(), argsList));
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSbAdGroup.class);
    }

    @Override
    public List<String> queryAdGroupIdList(ControlledObjectParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    public void updatePricing(Integer puid, Integer shopId, String adGroupId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_group_sb set is_state_bidding=?,pricing_state_bidding=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{isPricing, pricingState, updateId, puid, shopId, adGroupId});
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void batchUpdateAdGroupTypeAndAdFormat(Integer puid, List<AmazonSbAdGroup> updateList) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSbAdGroup amazonAdGroup : updateList) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdGroupType(),
                    amazonAdGroup.getAdFormat(),
                    amazonAdGroup.getLandingPage(),
                    puid,
                    amazonAdGroup.getId(),
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group_sb` set `ad_group_type` = ?, `ad_format` = ?, `landing_page` = ?, `update_time`=now(3) where puid = ? and id = ?", batchArgs);
    }

    @Override
    public void batchUpdateAdGroupType(Integer puid, List<AmazonSbAdGroup> updateList) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSbAdGroup amazonAdGroup : updateList) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdGroupType(),
                    puid,
                    amazonAdGroup.getId(),
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group_sb` set `ad_group_type` = ?, `update_time`=now(3) where puid = ? and id = ?", batchArgs);
    }

    @Override
    public List<String> getSbGroupIdListByParamAndIds(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id from t_amazon_ad_group_sb ");
        sb.append(this.getSbGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<AllGroupOrderBo> getSbGroupIdAndOrderFieldList(Integer puid, GroupPageParam param, List<String> adGroupIdList, String orderField) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id")
                .append(StringUtils.isNotBlank(orderField) ? "," + orderField + " orderField" : "")
                .append(" from t_amazon_ad_group_sb ");
        sb.append(this.getSbGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
    }

    @Override
    public List<GroupInfoPageVo> getSbGroupPageVoListByGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sb' as type, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, ad_format, campaign_id campaignId, name name, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" bid defaultBid, ad_group_type adGroupType, create_id createId ");
        selectSql.append(" FROM t_amazon_ad_group_sb g ");
        selectSql.append(" where g.puid = ? and g.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIdList)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIdList, argsList));
            selectSql.append(" order by field(ad_group_id, ").append(StringUtil.joinString(adGroupIdList)).append(")");
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GroupInfoPageVo.class));
    }

    @Override
    public Page<GroupInfoPageVo> getAllSbGroupPage(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer countSql = new StringBuffer("SELECT COUNT(*) FROM t_amazon_ad_group_sb ");
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sb' as type, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, ad_format adFormat, campaign_id campaignId, name name, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" bid defaultBid, ad_group_type adGroupType, create_id createId ");
        selectSql.append(" FROM t_amazon_ad_group_sb ");
        String whereSql = getSbGroupPageWhereSql(puid, param, null, argsList);
        selectSql.append(whereSql);
        AdGroupDefaultOrderEnum orderEn = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        if (StringUtils.isNotBlank(param.getOrderType()) && Objects.nonNull(orderEn)) {
            selectSql.append(" order by ").append(orderEn.getOrderField()).append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? " desc" : "").append(" ,id desc ");
        } else {
            selectSql.append(" order by id desc ");
        }
        countSql.append(whereSql);//check 右括号
        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, GroupInfoPageVo.class);
    }

    private String getSbGroupPageWhereSql(Integer puid, GroupPageParam param, List<String> groupIdList, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId(), ",");
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", StringUtil.splitStr(param.getMultiGroupId()), argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(groupIdList)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> status = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", status, argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and name = ? ");
                argsList.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and ").append(param.getSearchField()).append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        return whereSql.toString();
    }

    @Override
    public AmazonSbAdGroup getByCampaignIdAndGroupId(int puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public void batchUpdateAdGrpupTypeAndAdFormat(Integer puid, List<AmazonSbAdGroup> updateList) {
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonSbAdGroup amazonAdGroup : updateList) {
            batchArg = new Object[]{
                    amazonAdGroup.getAdGroupType(),
                    amazonAdGroup.getAdFormat(),
                    amazonAdGroup.getLandingPage(),
                    puid,
                    amazonAdGroup.getId(),
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate("update `t_amazon_ad_group_sb` set `ad_group_type` = ?, `ad_format` = ?, `landing_page` = ?, `update_time`=now(3) where puid = ? and id = ?", batchArgs);
    }

    @Override
    public List<String> queryAdGroupIdList(ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<String> queryAutoRuleAdGroupIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getItemIdList())) {
            builder.in("ad_group_id", param.getItemIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<AmazonSbAdGroup> getGroupByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT 'sb' as `type`, ad_group_id, campaign_id, shop_id, marketplace_id, name, ad_format, ad_group_type");
        selectSql.append(" FROM t_amazon_ad_group_sb g ");
        selectSql.append(" where g.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIds, argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(AmazonSbAdGroup.class));
    }

    @Override
    public void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message) {
        if (RuleActionType.stateClose.equals(message.getPerformOperation().get(0).getRuleAction())) {
            StringBuilder updateSql = new StringBuilder("update t_amazon_ad_group_sb ");
            String whereSql = " where puid = ? and shop_id = ? and ad_group_id = ?";
            List<Object> args = Lists.newArrayList();
            updateSql.append(" set state=?");
            args.add(message.getPerformOperation().get(0).getExecuteValue());
            updateSql.append(whereSql);
            args.add(message.getPuid());
            args.add(message.getShopId());
            args.add(message.getItemId());
            getJdbcTemplate(message.getPuid()).update(updateSql.toString(), args.toArray());
        }
    }

    @Override
    public List<AmazonSbAdGroup> getNameAndTypeByShopIdsAndGroupIds(Integer puid, List<Integer> shopIds, List<String> campaignList, List<String> groupIds, String groupName) {
        StringBuilder selectSql = new StringBuilder(" select ad_group_id, name, ad_group_type, state, ad_format from t_amazon_ad_group_sb ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid= ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotEmpty(groupName)) {
            whereSql.append(" and name LIKE ?");
            argsList.add("%" + groupName +"%");
        }
        selectSql.append(whereSql);
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getRowMapper());
    }

    @Override
    public List<String> getByShopIdsAndCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)) {
            builder.in("shop_id", shopIdList.toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignList)) {
            builder.in("campaign_id", campaignList.toArray());
        }
        return listDistinctFieldByCondition(puid, "ad_group_id", builder.build(), String.class);
    }
}