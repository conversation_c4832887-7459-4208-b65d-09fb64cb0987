package com.meiyunji.sponsored.service.syncAd.enums;


/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/12/28 15:51
 */
public enum ShopDataInitTaskStateEnum {
    SYNCING((byte) 0, "同步中"),
    SUCCESS((byte) 1, "同步成功"),
    FAIL((byte) 2, "同步失败");

    /**
     * 状态，0同步中，1同步成功，2同步失败
     */
    private final Byte state;

    /**
     * 说明
     */
    private final String desc;


    ShopDataInitTaskStateEnum(Byte state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public Byte getState() {
        return state;
    }

    public String getDesc() {
        return desc;
    }
}
