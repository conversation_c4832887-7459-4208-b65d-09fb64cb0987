package com.meiyunji.sponsored.service.export.convert.impl.sbv;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportDataVoExportConvert;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.vo.AdSbvSpaceVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.SBV_SPACE)
public class SpaceSbvReportExportConvert implements ReportDataVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.SBV_SPACE;
    }

    @Override
    public void processExcelDataList(ReportDataVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdSbvSpaceVo vo = new AdSbvSpaceVo();
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            Date dailyDate = DateUtil.strToDate(reportVo.getCountDate(), "yyyyMMdd");
            vo.setDailyDate(DateUtil.dateToStrWithFormat(dailyDate, "yyyy-MM-dd"));
        }
        vo.setPlacement(reportVo.getPlacement());
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setCost(reportVo.getCost());
        vo.setImpressions(reportVo.getImpressions());
        vo.setClicks(reportVo.getClicks());
        vo.setCpc(reportVo.getCpc());
        vo.setClickRate(reportVo.getClickRate());
        vo.setSalesConversionRate(reportVo.getSalesConversionRate());
        vo.setAcos(reportVo.getAcos());
        vo.setRoas(reportVo.getRoas());
        vo.setAttributedConversions14d(reportVo.getAttributedConversions14d());
        vo.setAttributedConversions14dSameSKU(reportVo.getAttributedConversions14dSameSKU());
        vo.setAttributedConversions14dOtherSameSKU(reportVo.getAttributedConversions14dOtherSameSKU());
        vo.setAttributedSales14d(reportVo.getAttributedSales14d());
        vo.setAttributedSales14dSameSKU(reportVo.getAttributedSales14dSameSKU());
        vo.setAttributedSales14dOtherSameSKU(reportVo.getAttributedSales14dOtherSameSKU());
        vo.setAttributedOrderRateNewToBrand14d(reportVo.getAttributedOrderRateNewToBrand14d());
        vo.setAttributedOrdersNewToBrand14d(reportVo.getAttributedOrdersNewToBrand14d());
        vo.setAttributedOrdersNewToBrandPercentage14d(reportVo.getAttributedOrdersNewToBrandPercentage14d());
        vo.setAttributedSalesNewToBrand14d(reportVo.getAttributedSalesNewToBrand14d());
        vo.setAttributedSalesNewToBrandPercentage14d(reportVo.getAttributedSalesNewToBrandPercentage14d());
        vo.setAttributedUnitsOrderedNewToBrand14d(reportVo.getAttributedUnitsOrderedNewToBrand14d());
        vo.setAttributedUnitsOrderedNewToBrandPercentage14d(reportVo.getAttributedUnitsOrderedNewToBrandPercentage14d());
        vo.setVctr(reportVo.getVctr());
        vo.setVideo5SecondViewRate(reportVo.getVideo5SecondViewRate());
        vo.setVideo5SecondViews(reportVo.getVideo5SecondViews());
        vo.setVideoFirstQuartileViews(reportVo.getVideoFirstQuartileViews());
        vo.setVideoMidpointViews(reportVo.getVideoMidpointViews());
        vo.setVideoThirdQuartileViews(reportVo.getVideoThirdQuartileViews());
        vo.setVideoUnmutes(reportVo.getVideoUnmutes());
        vo.setViewableImpressions(reportVo.getViewableImpressions());
        vo.setVideoCompleteViews(reportVo.getVideoCompleteViews());
        vo.setVtr(reportVo.getVtr());
        vo.setAttributedBrandedSearches14d(reportVo.getAttributedBrandedSearches14d());

        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shop.getMarketplaceId());
            if (null != m) {
                vo.setCurrency(m.getCurrencyCode());
            }
        }
        list.add(vo);
    }
}
