package com.meiyunji.sponsored.service.adTagSystem.param;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-25  16:51
 */
@Data
public class CampaignTagDataParam {
    private Integer pageNo;
    private Integer pageSize;
    private Integer puid;
    private Integer uid;
    //是否管理员
    private Boolean isAdmin;
    //店铺id
    private List<Integer> shopIdList;
    //站点id
    private List<String> marketplaceIdList;
    //标签id
    private List<Long> tagIdList;
    //开始时间
    private String startDate;
    //结束时间
    private String endDate;
    //是否对比
    private Boolean isCompare;
    //对比开始时间
    private String compareStartDate;
    //对比结束时间
    private String compareEndDate;
    //排序字段
    private String orderField;
    //排序类型
    private String orderType;
    //创建人id
    private List<Integer> createIdList;
    //币种
    private String currency;
    //高级筛选
    private AdvanceFilter advanceFilter;
    //是否开启高级筛选
    private Boolean useAdvanced;
    //请求标识
    private String pageSign;
    //是否只返回汇总条数
    private Boolean onlyCount;


    //非列表页传参
    private BigDecimal shopSales;
    private List<Long> groupIds;
}
