package com.meiyunji.sponsored.service.cpc.dto;

import com.amazon.advertising.changesHistory.Events;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单个event的额外填充数据
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-12-31  10:06
 */

@Data
@AllArgsConstructor
public class AmazonHistoryEventDataDto {

    //event
    private Events event;

    //广告类型
    private String adType;

    //活动名称
    private String campaignName;

    //广告组名称
    private String adGroupName;

    //广告产品asin值
    private String asinValue;

    //是否自动投放
    private boolean isAutoTargeting;

    //自动投放类型
    private String autoTargetingType;

}
