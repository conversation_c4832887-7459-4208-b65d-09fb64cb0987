package com.meiyunji.sponsored.service.multiPlatform.walmart.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingPlacementClientService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.WalmartAdvetisingUtil;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.dto.UpdateEditableCampaignPlacementsDTO;
import com.walmart.oms.advertiser.base.vo.CampaignResponseVO;
import com.walmart.oms.advertiser.model.UpdateEditableCampaignPlacementsResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/21 11:08
 * @describe:
 */
@Service
public class WalmartAdvertisingPlacementClientServiceImpl implements IWalmartAdvertisingPlacementClientService {
    @Override
    public CampaignResponseVO createPlacement(List<UpdateEditableCampaignPlacementsDTO> placementDto) throws ServiceException {
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        //调用Walmart接口
        UpdateEditableCampaignPlacementsResponse response = advertiserClient.updateEditableCampaignPlacements(placementDto);
        if (response == null) {
            throw new ServiceException("更新广告位失败！平台响应response is null");
        }
        if (StringUtils.isNotBlank(response.getError())) {
            throw new ServiceException("更新广告位失败！平台响应：" + response.getError());
        }
        if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
            throw new ServiceException("更新广告位失败！平台响应:" + response.getDetailsObj().getDescription());
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(response.getData())) {
            throw new ServiceException("更新广告位失败！平台响应response Data is null");
        }
        CampaignResponseVO responseVO = response.getData().get(0);
        if (StringUtils.equals("failure", responseVO.getCode()) || StringUtils.isNotBlank(responseVO.getDetails())) {
            throw new ServiceException(WalmartAdvetisingUtil.getErrorMsgByMsg(responseVO.getDetails()));
        }
        return responseVO;
    }

    @Override
    public void updateEditableCampaignPlacements(List<UpdateEditableCampaignPlacementsDTO> placementDto) throws ServiceException {
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        UpdateEditableCampaignPlacementsResponse response = advertiserClient.updateEditableCampaignPlacements(placementDto);
        if (response == null) {
            throw new ServiceException("更新广告位失败！平台无响应");
        }
        if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
            throw new ServiceException("更新广告位失败！平台响应:" + response.getDetailsObj().getDescription());
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            throw new ServiceException("更新广告位失败！平台响应无数据");
        }
        CampaignResponseVO responseVO = response.getData().get(0);
        if (StringUtils.equals("failure", responseVO.getCode()) || StringUtils.isNotBlank(responseVO.getDetails())) {
            throw new ServiceException("更新广告位失败！平台响应:" + responseVO.getDetails());
        }
    }
}
