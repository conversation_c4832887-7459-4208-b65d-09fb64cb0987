package com.meiyunji.sponsored.service.dashboard.dto;

import com.meiyunji.sponsored.common.util.DoubleUtil;
import lombok.Data;

@Data
public class DashboardHourReportDto {
    /**
     * 广告曝光量
     */
    private Long impressions = 0L;
    /**
     * 广告点击量
     */
    private Long clicks = 0L;
    /**
     * 广告点击率   点击量/曝光量 * 100%
     */
    private Double clickRate = 0.0;
    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private Double cpc = 0.0;
    /**
     * 广告转化率  广告订单量/点击量 * 100%
     */
    private Double salesConversionRate = 0.0;
    /**
     * 花费
     */
    private Double cost = 0.0;
    /**
     * 广告订单量
     */
    private Integer adOrder = 0;
    /**
     * 广告销售额
     */
    private Double adSales = 0.0;
    /**
     * acos  广告费/销售额*100%
     */
    private Double acos = 0.0;
    /**
     * roas 销售额/广告费(非百分比数据)
     */
    private Double roas = 0.0;
    /**
     * 更新时间
     */
    private String lastUpdateAt;

    /**
     * SPC=(广告销售额/点击)
     * 每次点击所带来的广告销售额
     */
    private Double spc;

    public Double getSpc() {
        if (adSales == null || clicks == null) {
            return null;
        }
        return calculationRateDouble(Double.valueOf(adSales), Double.valueOf(clicks), 2);
    }

    private Double calculationRateDouble(Double value1, Double value2, Integer per) {
        return value2 == null ? 0 : DoubleUtil.divide(value1, value2, per);
    }
}
