package com.meiyunji.sponsored.service.newDashboard.dto;

import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: ys
 * @date: 2024/4/12 18:05
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardAdProductTopDataDto extends DashboardAdCalDataDto {

    /**
     * asin, sku, parent_asin
     */
    private String label;
    private String mainImage;
    private String shopIds;
    private String asin;
    private BigDecimal allCost;//汇总广告花费
    private BigDecimal allTotalSales;//汇总广告销售额
    private BigDecimal allImpressions;//汇总广告曝光量
    private BigDecimal allClicks;//汇总广告点击量
    private BigDecimal allOrderNum;//汇总广告订单量
    private BigDecimal allSaleNum;//汇总广告销售额
    private BigDecimal subCost;
    private BigDecimal subTotalSales;
    private Long subImpressions;
    private Integer subClicks;
    private Integer subOrderNum;
    private Integer subSaleNum;
    private BigDecimal subAcos;
    private BigDecimal subRoas;
    private BigDecimal subClickRate;
    private BigDecimal subConversionRate;
    private BigDecimal subCpc;
    private BigDecimal subCpa;
    private BigDecimal allSubNum;//汇总对比数据
}
