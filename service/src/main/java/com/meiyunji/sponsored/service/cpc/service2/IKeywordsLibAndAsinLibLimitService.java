package com.meiyunji.sponsored.service.cpc.service2;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/12/11 14:13
 * @describe:
 */
public interface IKeywordsLibAndAsinLibLimitService {

    /**
     * 获取关键词库和ASIN库用户添加总数限制
     * @param puid
     * @param reqSize 请求中包含要添加的关键词或ASIN数量
     * @param reqExistAlready 请求包含已添加的关键词或ASIN数量
     */
    Boolean checkAddCountLimit(Integer puid, List<String> asinListReq);
}
