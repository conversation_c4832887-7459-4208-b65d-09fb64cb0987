package com.meiyunji.sponsored.service.newDashboard.service.globalView;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.doris.dao.globalView.IGlobalViewDao;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.GlobalViewPlacementDto;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.base.GlobalViewBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewConstant;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewProcessorEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.globalView.GlobalViewBaseReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局概览-广告位列表
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class GlobalViewPlacementProcessor extends AbstractGlobalViewProcessor<GlobalViewBaseReqVo, GlobalViewPlacementDto> {

    @Resource
    private IGlobalViewDao globalViewDao;


    @Override
    public Page<GlobalViewPlacementDto> getPageData(GlobalViewBaseReqVo req) {
        // 过滤广告位参数
        req.setPlacementList(AmazonAdvertisePredicateEnum.getValues());
        // 分页获取原始指标数据
        Page<GlobalViewBaseDataDto> page = globalViewDao.getPageData(req, GlobalViewProcessorEnum.PLACEMENT);
        // 构建列表返回参数
        List<GlobalViewPlacementDto> placementList = buildPlacement(page);
        return new Page<>(page.getPageNo(), page.getPageSize(), 1, placementList.size(), placementList);
    }

    @Override
    public GlobalViewBaseDataDto getSumData(GlobalViewBaseReqVo req, List<GlobalViewPlacementDto> dataList) {
        // 过滤广告位参数
        req.setPlacementList(AmazonAdvertisePredicateEnum.getValues());
        // 广告位无需店铺销售额数据
        return globalViewDao.getSumData(req, GlobalViewProcessorEnum.PLACEMENT);
    }

    @Override
    public List<GlobalViewPlacementDto> getMomData(GlobalViewBaseReqVo req, List<GlobalViewPlacementDto> dataList) {
        return getMomYoyData(req, req.getMomStartDate(), req.getMomEndDate(), dataList);
    }

    @Override
    public List<GlobalViewPlacementDto> getYoyData(GlobalViewBaseReqVo req, List<GlobalViewPlacementDto> dataList) {
        return getMomYoyData(req, req.getYoyStartDate(), req.getYoyEndDate(), dataList);
    }

    @Override
    public List<String> getExportHeader() {
        List<String> headerList = new ArrayList<>(GlobalViewConstant.baseHeaderList);
        headerList.add(0, "placement");
        // 广告位无需统计店铺销售额数据
        List<String> shopList = new ArrayList<>(GlobalViewConstant.shopHeaderList);
        headerList = headerList.stream().filter(x -> !shopList.contains(x)).collect(Collectors.toList());
        return headerList;
    }

    @Override
    public List<GlobalViewPlacementDto> getExportData(List<GlobalViewPlacementDto> dataList) {
        return dataList;
    }

    /**
     * 构建列表返回参数
     */
    private static List<GlobalViewPlacementDto> buildPlacement(Page<GlobalViewBaseDataDto> page) {
        List<GlobalViewPlacementDto> placementList = new ArrayList<>();
        Map<String, GlobalViewBaseDataDto> placementMap = StreamUtil.toMap(page.getRows(), GlobalViewBaseDataDto::getKey);
        for (GlobalViewBaseDataDto data : page.getRows()) {
            GlobalViewPlacementDto placement = new GlobalViewPlacementDto();
            BeanUtils.copyProperties(data, placement);
            AmazonAdvertisePredicateEnum enumByCode = AmazonAdvertisePredicateEnum.getEnumByValue(data.getKey());
            placement.setPlacement(enumByCode != null ? enumByCode.getChinese() : data.getKey());
            placementList.add(placement);
        }
        // 填充不存在的数据
        for (AmazonAdvertisePredicateEnum predicateEnum : AmazonAdvertisePredicateEnum.getEnums()) {
            GlobalViewPlacementDto placement = new GlobalViewPlacementDto();
            GlobalViewBaseDataDto data = placementMap.get(predicateEnum.getValue());
            if (data == null) {
                placement.setPlacement(predicateEnum.getChinese());
                placement.setKey(predicateEnum.getValue());
                placementList.add(placement);
            }
        }
        return placementList;
    }

    /**
     * 根据时间获取同环比原始指标数据
     */
    private List<GlobalViewPlacementDto> getMomYoyData(GlobalViewBaseReqVo req, String startDate, String endDate, List<GlobalViewPlacementDto> dataList) {
        List<GlobalViewPlacementDto> list = new ArrayList<>();
        List<GlobalViewBaseDataDto> listData = globalViewDao.getListData(req, GlobalViewProcessorEnum.PLACEMENT, startDate, endDate);
        Map<String, GlobalViewBaseDataDto> dataMap = StreamUtil.toMap(listData, GlobalViewBaseDataDto::getKey);
        for (GlobalViewPlacementDto dto : dataList) {
            GlobalViewPlacementDto placementDto = new GlobalViewPlacementDto();
            GlobalViewBaseDataDto data = dataMap.get(dto.getKey());
            if (data != null) {
                BeanUtils.copyProperties(data, placementDto);
            }
            placementDto.setKey(dto.getKey());
            list.add(placementDto);
        }
        return list;
    }
}
