package com.meiyunji.sponsored.service.sellfoxApi;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.qo.AmazonAdGroupPageQo;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import com.meiyunji.sponsored.service.vo.PurchasedProductInfoVO;

import java.util.List;


/**
 * Product
 *
 * <AUTHOR>
 */
public interface IProductApi {


    /**
     * 根据sku获取产品
     *
     * @param puid
     * @param shopId
     * @param sku
     * @return
     */
    ProductAdReportVo getProductBySku(int puid, Integer shopId, String sku);


    /**
     * 根据sku获取产品list
     *
     * @param puid
     * @param shopId
     * @param sku
     * @return
     */
    List<ProductAdReportVo> getListProductBySku(int puid, Integer shopId, String sku);

    List<ProductAdReportVo> getListProductBySkuList(Integer puid, Integer shopId, String skuList);


    /**
     * 根据asin 和sku 获取商品
     *
     * @param puid
     * @param shopId
     * @param asin
     * @param sku
     * @return
     */
    ProductAdReportVo getProductByAsinAndSku(int puid, Integer shopId, String asin, String sku);
    /**
     * 根据asin 和sku 获取商品list
     *
     * @param puid
     * @param shopId
     * @param asin
     * @param sku
     * @return
     */
    List<ProductAdReportVo> getListProductByAsinAndSku(int puid, Integer shopId, String asin, String sku);


    /**
     * 获取子sku
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param idList
     * @return
     */
    List<ProductAdReportVo> getListByParentId(int puid, Integer shopId, String marketplaceId, List<Integer> idList);

    /**
     * 获取产品图片
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param sku
     * @return
     */
    String getMainImageBySku(int puid, Integer shopId, String marketplaceId, String sku);

    /**
     * 获取sku的asin，mainImage,title
     *
     * @param puid
     * @param shopId
     * @param marketplace
     * @param skus
     * @return
     */
    List<ProductAdReportVo> getAsinBySkus(int puid, Integer shopId, String marketplace, List<String> skus);


    /**
     * 广告日报非广告产品列表
     *
     * @param puid
     * @param qo
     * @param page
     * @return
     */
    Page<ProductAdReportVo> getProductPageList(int puid, AmazonAdGroupPageQo qo, Page page);

    /**
     * 获取父asin维度的产品
     * @return
     */
    ProductAdReportVo getByAsinAndSku(int puid, Integer shopId, String asin, String msku);

    /**
     * 获取子asin产品
     * @return
     */
    ProductAdReportVo getChildProduct(int puid, Integer shopId, String asin, String msku);

    /**
     * 获取父asin下的子asin
     *
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param parentAsin
     * @return
     */
    List<ProductAdReportVo> getListByParentAsin(int puid, Integer shopId, String marketPlaceId, String parentAsin);



    List<ProductAdReportVo> getChildAsinInfo(Integer puid, Integer shopId, Integer parentId);

    List<ProductAdReportVo> getAsinMainImageAndTitle(Integer puid, String marketPlaceId, List<String> asins);

    List<ProductAdReportVo> getParentAsinsByChildAsins(Integer puid, Integer shopId, List<String> asins);

    ProductAdReportVo getProductByParentAsin(int puid, Integer shopId, String asin);

    PurchasedProductInfoVO getProductLabelDevs(int puid, int uid, String marketPlaceId, String asinType, List<Integer> shopIdList,
                                               List<String> asinList, List<String> skuList);
}