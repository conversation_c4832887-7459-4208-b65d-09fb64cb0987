package com.meiyunji.sponsored.service.cpc.service2.assets;

import com.amazon.advertising.assets.AssetsClient;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetResult;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetsResponse;
import com.amazon.advertising.assets.entity.assetsPut.PutAssetsResponse;
import com.amazon.advertising.assets.entity.assetsRegister.RegisterAssetResult;
import com.amazon.advertising.assets.entity.assetsRegister.RegisterAssetsResponse;
import com.amazon.advertising.assets.entity.assetsUpload.UploadAssetResult;
import com.amazon.advertising.assets.entity.assetsUpload.UploadAssetsResponse;
import com.amazon.advertising.assets.entity.searchAssets.SearchAssetsResponse;
import com.amazon.advertising.assets.entity.searchAssets.SearchAssetsResult;
import com.amazon.advertising.base.ApiResponse;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.base.StatusCode;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 素材亚马逊api接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11
 */
@Service
@Slf4j
public class CpcAssetsApiService {

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IShopAuthService shopAuthService;

    /**
     * 获取临时上传地址,有效15分钟
     *
     * @return
     */
    public Result<UploadAssetResult> getAssetUploadUrl(ShopAuth shop, AmazonAdProfile amazonAdProfile, String name) {

        UploadAssetsResponse response = cpcApiHelper.call(shop, () -> AssetsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getAssetUploadUrl(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), name));

        Result<UploadAssetResult> result = checkResponse(response);
        if (result.error()) return result;

        String errMsg = "网络原因，请稍后重试";
        UploadAssetResult assetResult = response.getResult();
        if (assetResult == null) return ResultUtil.returnErr(errMsg);

        if (StringUtils.isNotBlank(assetResult.getUrl())) {
            return ResultUtil.returnSucc(assetResult);
        }

        if (StringUtils.isNotBlank(assetResult.getDetails())) {
            errMsg = assetResult.getDetails();
        } else if (StringUtils.isNotBlank(assetResult.getDescription())) {
            errMsg = assetResult.getDescription();
        }

        return ResultUtil.returnErr(errMsg);
    }

    /**
     * 上传素材
     */
    public Result<?> uploadAsset(ShopAuth shop, String uploadLocation, byte[] bytes, String contentType) {
        PutAssetsResponse response = cpcApiHelper.call(shop, () -> AssetsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).uploadAssets(uploadLocation, bytes, contentType));
        return checkResponse(response);
    }

    /**
     * 注册素材，获取assetId以及versionId
     */
    public Result<RegisterAssetResult> registerAsset(ShopAuth shop, AmazonAdProfile amazonAdProfile, String fileName,
                                                     String uploadLocation, String assetType, List<String> assetSubTypeList,
                                                     List<String> tags, List<String> brandEntityIds, String linkedAssetId) {

        RegisterAssetsResponse response = cpcApiHelper.call(shop, () -> AssetsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getAssetIdAndVersionId(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), fileName, uploadLocation, assetType, assetSubTypeList, tags, brandEntityIds, linkedAssetId));

        Result<RegisterAssetResult> result = checkResponse(response);
        if (result.error()) return result;

        String errMsg = "网络原因，请稍后重试";
        RegisterAssetResult assetResult = response.getResult();
        if (assetResult == null) return ResultUtil.returnErr(errMsg);

        if (StringUtils.isNotBlank(assetResult.getAssetId()) && StringUtils.isNotBlank(assetResult.getVersionId())) {
            return ResultUtil.returnSucc(assetResult);
        }

        if (StringUtils.isNotBlank(assetResult.getDetails())) {
            errMsg = assetResult.getDetails();
        } else if (StringUtils.isNotBlank(assetResult.getDescription())) {
            errMsg = assetResult.getDescription();
        }

        return ResultUtil.returnErr(errMsg);
    }

    /**
     * 获取素材信息
     */
    public Result<GetAssetResult> getAsset(ShopAuth shop, AmazonAdProfile amazonAdProfile, String assetId, String version) {
        GetAssetsResponse response = cpcApiHelper.call(shop, () -> AssetsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getAsset(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), assetId, version));
        Result<GetAssetResult> result = checkResponse(response);
        if (result.error()) return result;

        return ResultUtil.success(response.getResult());
    }

    /**
     * 获取素材列表
     */
    public Result<SearchAssetsResult> searchAssets(ShopAuth shop, AmazonAdProfile amazonAdProfile, String text,
                                                   Map<String, List<String>> valueFilters,
                                                   Map<String, List<Map<String, String>>> rangeFilters,
                                                   String sortField, String sortOrder) {
        SearchAssetsResponse response = cpcApiHelper.call(shop, () -> AssetsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).searchAssets(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), text, valueFilters, rangeFilters, sortField, sortOrder));
        Result<SearchAssetsResult> result = checkResponse(response);
        if (result.error()) return result;

        return ResultUtil.success(response.getResult());
    }

    private <T> Result<T> checkResponse(ApiResponse response) {
        String message;
        if (response == null || response.getStatusCode() == null) {
            message = "网络原因，请稍后重试";
        } else if (response.getStatusCode() == StatusCode.success) {
            // 成功
            return ResultUtil.success();
        } else if (response.getStatusCode() == StatusCode.quotaExceed) {
            //接口超限提示
            message = "频繁操作，请稍后再重试";
        } else {
            message = "上传失败,请联系管理员";
            log.error("asset remote error:{}", response.toJson());
        }

        return ResultUtil.returnErr(message);
    }
}
