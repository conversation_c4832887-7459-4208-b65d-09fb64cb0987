package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dto.AdProductDetailDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageVo;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopGroupByProductParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AdGroupAndAdIdDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinCampaignNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetAsinListQo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * amazon广告产品表(OdsAmazonAdProduct)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Repository
@Slf4j
public class OdsAmazonAdProductDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdProduct> implements IOdsAmazonAdProductDao {

    @Override
    public List<String> getAdIdByState(Integer puid, Integer shopId, String campaignIds, String groupIds, String states) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_Id from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> campaignIdList = StringUtil.splitStr(campaignIds, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        if (StringUtils.isNotBlank(groupIds)) {
            List<String> groupIdList = StringUtil.splitStr(groupIds, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        if (StringUtils.isNotBlank(states)) {
            List<String> stateIdList = StringUtil.splitStr(states, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("state", stateIdList, argsList));
        }
        return getJdbcTemplate().queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<String> getGroupIdByState(Integer puid, Integer shopId, String campaignIds, String groupIds, String states, String asin, String sku) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        if (StringUtils.isNotBlank(campaignIds)) {
            List<String> campaignIdList = StringUtil.splitStr(campaignIds, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        if (StringUtils.isNotBlank(groupIds)) {
            List<String> groupIdList = StringUtil.splitStr(groupIds, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        if (StringUtils.isNotBlank(states)) {
            List<String> stateIdList = StringUtil.splitStr(states, StringUtil.SPLIT_COMMA);
            sb.append(SqlStringUtil.dealInList("state", stateIdList, argsList));
        }
        if (StringUtils.isNotBlank(asin)) {
            List<String> asinList = StringUtil.splitStr(asin, StringUtil.SPECIAL_COMMA);
            sb.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }

        if (StringUtils.isNotBlank(sku)) {
            List<String> skuList = StringUtil.splitStr(sku, StringUtil.SPECIAL_COMMA);
            sb.append(SqlStringUtil.dealInList("sku", skuList, argsList));
        }
        return getJdbcTemplate().queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public Page<AsinListDto> getAsinPage(Integer puid, GetAsinListQo qo) {
        StringBuilder selectSql = new StringBuilder("select r.shop_id shopId, r.asin, r.sku msku, any(p.parent_asin) parentAsin, any(r.marketplace_id) marketplaceId from ").append(this.getJdbcHelper().getTable()).append(" r ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select r.sku from ").append(this.getJdbcHelper().getTable()).append(" r ");
        StringBuilder sbSql = new StringBuilder(" join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
                .append(" and p.puid = ? and p.shop_id = ? ")
                .append(" where r.puid = ? and r.shop_id = ? and r.marketplace_id = ? ");

        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(qo.getShopId());
        argsList.add(puid);
        argsList.add(qo.getShopId());
        argsList.add(qo.getMarketplaceId());
        if (StringUtils.isNotBlank(qo.getQueryValue()) && StringUtils.isNotBlank(qo.getQueryType())) {
            GetAsinListQo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getQueryType(), GetAsinListQo.SearchTypeEnum.class);
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getQueryValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    sbSql.append(" and ").append(searchTypeEnum.getField()).append(" like ? ");
                    argsList.add("%" + asinList.get(0) + "%");
                } else {
                    sbSql.append(SqlStringUtil.dealInList(searchTypeEnum.getField(), asinList, argsList));
                }
            }
        }
        if (StringUtils.isNotBlank(qo.getCampaignId())) {
            List<String> campaignIdList = StringUtil.splitStr(qo.getCampaignId(), StringUtil.SPLIT_COMMA);
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIdList, argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupId())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getAdIdList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_id", qo.getAdIdList(), argsList));
        }
        sbSql.append(" and r.asin is not null and r.asin != ''")
                .append(" group by r.shop_id, r.asin, r.sku ");
        selectSql.append(sbSql);
        countSql.append(sbSql).append(") c");
        selectSql.append(" order by r.asin, r.sku ");
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AsinListDto.class);
    }

    /**
     * 新的 getAsinAllPage 方法，使用 Doris lateral view explode 功能处理 SB 广告数据
     * 解决多店铺查询时 ASIN 数量比实际多的问题
     */
    public Page<AsinListDto> getAsinAllPageWithExplode(Integer puid, AsinListReqVo qo, boolean isVc) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select asin, msku,shopId  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<String> sql = new ArrayList<>(3);
        if (StringUtils.isBlank(qo.getAdType())) {
            getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql, isVc);
            getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql, isVc);
            // 使用新的 SB 处理方法
            getSbAsinsWithExplode(puid, qo, argsList, sql, isVc);
        } else {
            if (qo.getAdType().contains(Constants.SP)) {
                getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql, isVc);
            }
            if (qo.getAdType().contains(Constants.SD)) {
                getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql, isVc);
            }
            if (qo.getAdType().contains(Constants.SB)) {
                // 使用新的 SB 处理方法
                getSbAsinsWithExplode(puid, qo, argsList, sql, isVc);
            }
        }
        if (CollectionUtils.isEmpty(sql)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize(), 0, 0, new ArrayList<>());
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(" ) c  order by asin, msku");
        countSql.append(allSql).append(") c");
        Object[] arg = argsList.toArray();
        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql.toString(), arg, selectSql.toString(), arg, AsinListDto.class);
    }

    @Override
    public Page<AsinListDto> getAsinAllPage(Integer puid, AsinListReqVo qo, boolean isVc) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select asin, msku,shopId  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<String> sql = new ArrayList<>(3);
        if (StringUtils.isBlank(qo.getAdType())) {
            getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql, isVc);
            getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql, isVc);
            getSbAsins(puid, qo, argsList, sql, isVc);
        } else {
            List<String> adTypeList = StringUtil.splitStr(qo.getAdType());
            for (String type : adTypeList) {
                if (StringUtils.isBlank(type)) {
                    continue;
                }
                if (Constants.SP.equals(type) || Constants.SD.equals(type)) {
                    getSpOrSdAsins(puid, qo, argsList, "ods_t_amazon_ad_product" + (Constants.SD.equals(type) ? "_sd" : ""), type, sql, isVc);
                    continue;
                }
                if (Constants.SB.equals(type)) {
                    getSbAsins(puid, qo, argsList, sql, isVc);
                }
            }
        }
        // sql为空 不处理
        if (CollectionUtils.isEmpty(sql)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(" ) c  order by asin, msku");
        countSql.append(allSql).append(") c");
        Object[] arg = argsList.toArray();
        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql.toString(), arg, selectSql.toString(), arg, AsinListDto.class);
    }


    @Override
    public Page<AsinListDto> getParentAsinAllPage(Integer puid, AsinListReqVo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select parentId, shopId  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from (select parentId, shopId from ( ");
        List<String> sql = new ArrayList<>(3);
        if (StringUtils.isBlank(qo.getAdType())) {
            getScSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql);
            getScSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql);
            getScSbParentAsins(puid, qo, argsList, sql);
        } else {
            List<String> adTypeList = StringUtil.splitStr(qo.getAdType());
            for (String type : adTypeList) {
                if (StringUtils.isBlank(type)) {
                    continue;
                }
                if (Constants.SP.equals(type) || Constants.SD.equals(type)) {
                    getScSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product" + (Constants.SD.equals(type) ? "_sd" : ""), type, sql);
                    continue;
                }
                if (Constants.SB.equals(type)) {
                    getScSbParentAsins(puid, qo, argsList, sql);
                }
            }
        }
        // sql为空 不处理
        if (CollectionUtils.isEmpty(sql)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(" ) c join ods_t_product ap on ap.id = c.parentId and ap.shop_id = c.shopId and ap.puid = ? order by c.parentId, c.shopId");
        countSql.append(allSql).append(") c join ods_t_product ap on ap.id = c.parentId and ap.shop_id = c.shopId and ap.puid = ? ) b ");
        argsList.add(puid);
        Object[] arg = argsList.toArray();
        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql.toString(), arg, selectSql.toString(), arg, AsinListDto.class);
    }


    private void getScSbParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  DISTINCT parent_id parentId , shop_id shopId from ods_t_product where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        if (qo.getSbAsin().size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));
        } else {
            List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
            selectSql.append(" and (");
            int size = lists.size() - 1;
            for (int i = 0; i <= size; i++) {
                selectSql.append(SqlStringUtil.dealInList("asin", lists.get(i), argsList));
                if (i < size) {
                    selectSql.append(" or ");
                }
            }
            selectSql.append(" ) ");
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
            List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
            if (asinList.size() == 1) {
                // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                if (qo.getSearchFlag()) {
                    selectSql.append(" and (");
                    selectSql.append(" ").append(searchTypeEnum.getField()).append(" = ? ");
                    argsList.add(asinList.get(0).trim());
                    selectSql.append(" or (asin = ? and ( parent_asin is null or parent_asin =''))) ");
                    argsList.add(asinList.get(0).trim());
                } else {
                    selectSql.append(" and (");
                    selectSql.append(" ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    selectSql.append(" or ( lower(asin) like ? and (parent_asin is null or parent_asin =''))) ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                selectSql.append(" and ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd(searchTypeEnum.getField(), asinList, argsList));
                selectSql.append(" or ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd("asin", asinList, argsList));
                selectSql.append(" and (parent_asin is null or parent_asin ='')))");
            }
        }
        sql.add(selectSql.toString());
    }


    private void getScSpOrSdParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql) {
        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  p.parent_id parentId, r.shop_id shopId  from " + tableName + " r ");
        selectSql.append(" join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
                .append(" and p.puid = ? and p.parent_id is not null ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", qo.getShopIdList(), argsList));
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            // 此处一定是父asin
            AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
            List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
            String field = "p." + searchTypeEnum.getField();
            if (asinList.size() == 1) {
                // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                if (qo.getSearchFlag()) {
                    selectSql.append(" and (");
                    selectSql.append(" ").append(field).append(" = ? ");
                    argsList.add(asinList.get(0).trim());
                    selectSql.append(" or ( r.asin = ? and (p.parent_asin is null or p.parent_asin =''))) ");
                    argsList.add(asinList.get(0).trim());
                } else {
                    selectSql.append(" and (");
                    selectSql.append(" ").append("lower(").append(field).append(")").append(" like ? ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    selectSql.append(" or ( lower(r.asin) like ? and (p.parent_asin is null or p.parent_asin =''))) ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                selectSql.append(" and ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd(field, asinList, argsList));
                selectSql.append(" or ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd("r.asin", asinList, argsList));
                selectSql.append(" and (p.parent_asin is null or p.parent_asin =''))) ");
            }
        }

        if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
        }

        if (StringUtils.isNotBlank(qo.getGroupId())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
        }
        selectSql.append(" and r.asin is not null and r.asin != '' and p.parent_id is not null  group by  p.parent_id, r.shop_id ");
        sql.add(selectSql.toString());
    }



    @Override
    public Page<AsinListDto> getParentAsinAllPageVc(Integer puid, AsinListReqVo qo, List<Integer> scShopIds, List<Integer> vcShopIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        String vcSql = null;
        String scSql = null;
        if (CollectionUtils.isNotEmpty(scShopIds)) {
            scSql = getScParentAsinAllPage(puid, qo, scShopIds, argsList);
        }
        if (CollectionUtils.isNotEmpty(vcShopIds)) {
            vcSql = getVcParentAsinAllPage(puid, qo, vcShopIds, argsList);
        }
        // sql为空 不处理
        if (vcSql == null && scSql == null) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        List<String> sql = new ArrayList<>();
        if (vcSql != null) {
            sql.add(vcSql);
        }
        if (scSql != null) {
            sql.add(scSql);
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql);
        if (CollectionUtils.isNotEmpty(scShopIds)) {
            selectSql.append("  order by c.parentId, c.shopId");
        }
        countSql.append(allSql).append(") b ");

        Object[] arg = argsList.toArray();

        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql.toString(), arg, selectSql.toString(), arg, AsinListDto.class);
    }






    private String getScParentAsinAllPage(Integer puid, AsinListReqVo qo, List<Integer> scShopIds, List<Object> argsList) {

        StringBuilder selectSql = new StringBuilder(" select parentId, shopId from ( ");
        List<String> sql = new ArrayList<>(3);
        if (StringUtils.isBlank(qo.getAdType())) {
            getSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql, scShopIds);
            getSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql, scShopIds);
            getSbParentAsins(puid, qo, argsList, sql, scShopIds);
        } else {
            List<String> adTypeList = StringUtil.splitStr(qo.getAdType());
            for (String type : adTypeList) {
                if (StringUtils.isBlank(type)) {
                    continue;
                }
                if (Constants.SP.equals(type) || Constants.SD.equals(type)) {
                    getSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product" + (Constants.SD.equals(type) ? "_sd" : ""), type, sql, scShopIds);
                    continue;
                }
                if (Constants.SB.equals(type)) {
                    getSbParentAsins(puid, qo, argsList, sql, scShopIds);
                }
            }
        }

        // sql为空 不处理
        if (CollectionUtils.isEmpty(sql)) {
            return null;
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(" ) c join ods_t_product ap on ap.id = c.parentId and ap.shop_id = c.shopId and ap.puid = ? ");
        argsList.add(puid);
        return  selectSql.toString();
    }



    public String getVcParentAsinAllPage(Integer puid, AsinListReqVo qo, List<Integer> vcShopIds, List<Object> argsList) {
        StringBuilder selectSql = new StringBuilder(" ");
        List<String> sql = new ArrayList<>(3);
        if (StringUtils.isBlank(qo.getAdType())) {
            getVcSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product", Constants.SP, sql, vcShopIds);
            getVcSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product_sd", Constants.SD, sql, vcShopIds);
            getSbVcParentAsins(puid, qo, argsList, sql, vcShopIds);
        } else {
            List<String> adTypeList = StringUtil.splitStr(qo.getAdType());
            for (String type : adTypeList) {
                if (StringUtils.isBlank(type)) {
                    continue;
                }
                if (Constants.SP.equals(type) || Constants.SD.equals(type)) {
                    getVcSpOrSdParentAsins(puid, qo, argsList, "ods_t_amazon_ad_product" + (Constants.SD.equals(type) ? "_sd" : ""), type, sql, vcShopIds);
                    continue;
                }
                if (Constants.SB.equals(type)) {
                    getSbVcParentAsins(puid, qo, argsList, sql, vcShopIds);
                }
            }
        }
        // sql为空 不处理
        if (CollectionUtils.isEmpty(sql)) {
            return null;
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(" ");

        return selectSql.toString();
    }

    @Override
    public Page<AsinListDto> getAdProductAllPage(Integer puid, AsinListReqVo qo) {
        String queryField = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class).getField();
        AsinListReqVo.SearchTypeEnum searchFieldEnum = UCommonUtil.getByCode(qo.getSearchField(), AsinListReqVo.SearchTypeEnum.class);
        String searchField = Objects.nonNull(searchFieldEnum) ? searchFieldEnum.getField() : "";
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select ")
                .append(" min(create_time_m) createTime,")
                .append(StringUtils.equalsIgnoreCase(queryField, "sku") ? "sku msku" : "asin")
                .append(" from ( ");
        List<String> unionSql = Lists.newArrayList();
        getSpAdProductSql(puid, qo, argsList, queryField, searchField, unionSql);
        getSbAdProductSql(puid, qo, argsList, queryField, searchField, unionSql);
        if (CollectionUtils.isEmpty(unionSql)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        String allSql = String.join(" union all ", unionSql);
        sql.append(allSql).append(" ) t group by ").append(queryField);
        String countSql = "select count(*) from ( " + sql + ") c";
        sql.append(" order by createTime ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc");
        Object[] arg = argsList.toArray();
        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql, arg, sql.toString(), arg, AsinListDto.class);
    }

    @Override
    public Page<AsinListDto> getAdParentAsinAllPage(Integer puid, AsinListReqVo qo) {
        List<Object> args = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append(" select ")
                .append(" min(ap.create_time_m) createTime,")
                .append(" GROUP_CONCAT(distinct cast(ap.shop_id as char),',') shopIdStr,")
                .append(" ap.parent_asin parentAsin")
                .append(" from ( ");
        List<String> unionSql = Lists.newArrayList();
        getSpAdParentAsinSql(puid, qo, args, unionSql);
        getSbAdParentAsinSql(puid, qo, args, unionSql);
        if (CollectionUtils.isEmpty(unionSql)) {
            return new Page<>(qo.getPageNo(), qo.getPageSize());
        }
        String allSql = String.join(" union all ", unionSql);
        selectSql.append(allSql).append(" ) ap ");

        selectSql.append(" group by ap.parent_asin");

        String countSql = "select count(*) from ( " + selectSql + ") c";
        selectSql.append(" order by createTime ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc");
        Object[] arg = args.toArray();

        log.info(" 查询所有推广父asin getAdParentAsinAllPage sql {}", SqlStringUtil.exactSql(selectSql.toString(), args));

        return this.getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSql, arg, selectSql.toString(), arg, AsinListDto.class);
    }

    private void getSpAdParentAsinSql(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> unionSql) {
        StringBuilder selectSql = new StringBuilder();
        selectSql.append(" select ")
                .append(" p.shop_id shop_id,")
                .append(" p.parent_asin parent_asin,")
                .append(" min(p.create_time) create_time_m")
                .append(" from ods_t_amazon_ad_product r join ods_t_product p")
                .append(" on r.puid=p.puid and r.marketplace_id=p.marketplace_id and r.shop_id=p.shop_id and r.asin=p.asin and r.sku=p.sku")
                .append(" and p.parent_asin is not null and p.parent_asin!='' and p.is_variation=2")
                .append(" and p.puid=? and p.marketplace_id=? ");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
        selectSql.append(" where r.asin is not null and r.asin!='' and r.puid=? and r.marketplace_id=? ");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", qo.getShopIdList(), argsList));

        if ((StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchValue()))
                || StringUtils.isNotBlank(qo.getOnlineStatus())) {
            StringBuilder inSql = new StringBuilder("select distinct asin from ods_t_product ")
                    .append(" where is_variation=1 and puid=? and marketplace_id=? ");
            argsList.add(puid);
            argsList.add(qo.getMarketplaceId());
            inSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
            if(StringUtils.isNotBlank(qo.getOnlineStatus())){
                if("Active".equals(qo.getOnlineStatus())){
                    inSql.append(" and online_status = 'Active' ");
                }else{
                    inSql.append(" and online_status != 'Active' ");
                }
            }
            if((StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchValue()))){
                String searchField = StringUtils.equals(qo.getSearchField(), "parentAsin") ? "asin" : "title";
                List<String> valueList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(s -> qo.getSearchFlag() ? s : SqlStringUtil.dealLikeSql(s)).distinct().collect(Collectors.toList());
                if (valueList.size() == 1) {
                    if (qo.getSearchFlag()) {
                        inSql.append(" and lower(").append(searchField).append(") = ? ");
                        argsList.add(valueList.get(0).trim().toLowerCase());
                    } else {
                        inSql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                        argsList.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    inSql.append(SqlStringUtil.dealInList(searchField, valueList, argsList));
                }
            }
            selectSql.append(" and p.parent_asin in (").append(inSql).append(")");
        }

        selectSql.append(" group by p.shop_id, p.parent_asin ")
                .append(" order by create_time_m ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc")
                .append(" limit 100000");
        unionSql.add("(" + selectSql + ")");
    }

    private void getSbAdParentAsinSql(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> unionSql) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        StringBuilder selectSql = new StringBuilder();
        selectSql.append(" select ")
                .append(" shop_id,")
                .append(" parent_asin,")
                .append(" min(create_time) create_time_m")
                .append(" from ods_t_product where parent_asin is not null and parent_asin!='' and is_variation=2")
                .append(" and puid=? and marketplace_id=?");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));

        if ((StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchValue()))
                || StringUtils.isNotBlank(qo.getOnlineStatus())) {

            StringBuilder inSql = new StringBuilder("select distinct asin from ods_t_product ")
                    .append(" where is_variation=1 and puid=? and marketplace_id=? ");
            argsList.add(puid);
            argsList.add(qo.getMarketplaceId());
            inSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
            if(StringUtils.isNotBlank(qo.getOnlineStatus())){
                if("Active".equals(qo.getOnlineStatus())){
                    inSql.append(" and online_status = 'Active' ");
                }else{
                    inSql.append(" and online_status != 'Active' ");
                }
            }
            if(StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchValue())){
                String searchField = StringUtils.equals(qo.getSearchField(), "parentAsin") ? "asin" : "title";
                List<String> valueList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(s -> qo.getSearchFlag() ? s : SqlStringUtil.dealLikeSql(s)).distinct().collect(Collectors.toList());
                if (valueList.size() == 1) {
                    if (qo.getSearchFlag()) {
                        inSql.append(" and lower(").append(searchField).append(") = ? ");
                        argsList.add(valueList.get(0).trim().toLowerCase());
                    } else {
                        inSql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                        argsList.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    inSql.append(SqlStringUtil.dealInList(searchField, valueList, argsList));
                }
            }
            selectSql.append(" and parent_asin in (").append(inSql).append(")");
        }

        selectSql.append(" group by shop_id, parent_asin ")
                .append(" order by create_time_m ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc")
                .append(" limit 100000");
        unionSql.add("(" + selectSql + ")");
    }

    private void getSpAdProductSql(Integer puid, AsinListReqVo qo, List<Object> argsList, String queryField, String searchField, List<String> unionSql) {
        StringBuilder sql = new StringBuilder("select min(p.create_time) create_time_m, ");
        sql.append("r.").append(queryField);
        sql.append(" from ods_t_amazon_ad_product r join ods_t_product p on r.puid=p.puid and r.marketplace_id=p.marketplace_id and r.shop_id=p.shop_id and r.asin=p.asin and r.sku=p.sku")
                .append(" and p.is_variation!=1 and p.puid=? and p.marketplace_id=? ");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        sql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
        sql.append(" where r.puid=? and r.marketplace_id=? ");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        if(StringUtils.isNotBlank(qo.getOnlineStatus())){
            if("Active".equals(qo.getOnlineStatus())){
                sql.append(" and p.online_status = 'Active' ");
            }else{
                sql.append(" and p.online_status != 'Active' ");
            }
        }
        if (StringUtils.isNotBlank(searchField) && StringUtils.isNotBlank(qo.getSearchValue())) {
            List<String> valueList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(s -> qo.getSearchFlag() ? s : SqlStringUtil.dealLikeSql(s)).distinct().collect(Collectors.toList());
            StringBuilder inSql = new StringBuilder("select distinct ")
                    .append(queryField).append(" from ods_t_product ")
                    .append(" where puid=? and marketplace_id=? ");
            argsList.add(puid);
            argsList.add(qo.getMarketplaceId());
            inSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
            if (valueList.size() == 1) {
                if (qo.getSearchFlag()) {
                    inSql.append(" and lower(").append(searchField).append(") = ? ");
                    argsList.add(valueList.get(0).trim().toLowerCase());
                } else {
                    inSql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                    argsList.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                inSql.append(SqlStringUtil.dealInList(searchField, valueList, argsList));
            }
            sql.append(" and ").append("r.").append(queryField)
                    .append(" in (").append(inSql).append(")");
        }

        sql.append(" and r.asin is not null and r.asin != '' group by r.").append(queryField)
                .append(" order by create_time_m ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc")
                .append(" limit 100000");
        unionSql.add("(" + sql + ")");
    }

    private void getSbAdProductSql(Integer puid, AsinListReqVo qo, List<Object> argsList, String queryField, String searchField, List<String> unionSql) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        StringBuilder sql = new StringBuilder("select min(create_time) create_time_m, ");
        sql.append(queryField).append(" from ods_t_product where is_variation!=1 and puid=? and marketplace_id=? ");
        argsList.add(puid);
        argsList.add(qo.getMarketplaceId());
        if(StringUtils.isNotBlank(qo.getOnlineStatus())){
            if("Active".equals(qo.getOnlineStatus())){
                sql.append(" and online_status = 'Active' ");
            }else{
                sql.append(" and online_status != 'Active' ");
            }
        }
        sql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        sql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));

        if (StringUtils.isNotBlank(searchField) && StringUtils.isNotBlank(qo.getSearchValue())) {
            List<String> valueList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(s -> qo.getSearchFlag() ? s : SqlStringUtil.dealLikeSql(s)).distinct().collect(Collectors.toList());
            if (valueList.size() == 1) {
                if (qo.getSearchFlag()) {
                    sql.append(" and lower(").append(searchField).append(") = ? ");
                    argsList.add(valueList.get(0).trim().toLowerCase());
                } else {
                    sql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                    argsList.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                sql.append(SqlStringUtil.dealInList(searchField, valueList, argsList));
            }
        }

        sql.append(" group by ").append(queryField)
                .append(" order by create_time_m ").append(StringUtils.equalsIgnoreCase(qo.getOrderType(), "asc") ? "asc" : "desc")
                .append(" limit 100000");
        unionSql.add("(" + sql + ")");
    }

    private void getSpOrSdAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql, boolean isVc) {
        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  r.asin asin, r.sku msku , r.shop_id shopId  from " + tableName + " r ");
        selectSql.append(" join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
                .append(" and p.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", qo.getShopIdList(), argsList));
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                String field = "r." + searchTypeEnum.getField();
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and ").append(field).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and ").append("lower(").append(field).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList(field, asinList, argsList));
                }
            }
        }

        if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
        }

        if (StringUtils.isNotBlank(qo.getGroupId())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
        }
        selectSql.append(" and r.asin is not null and r.asin != '' group by r.asin, r.sku , r.shop_id ");
        sql.add(selectSql.toString());
        if (isVc) {
           getSpOrSdAsinsVc(puid, qo, argsList, tableName, type, sql);
        }
    }



    private void getSpOrSdAsinsVc(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql) {
        AsinListReqVo.SearchTypeEnum searchTypeEnum = null;
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
        }
        if (AsinListReqVo.SearchTypeEnum.MSKU == searchTypeEnum) {
            return;
        }
        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  r.asin asin, '' msku , r.shop_id shopId  from " + tableName + " r ");
        selectSql.append(" join ods_t_vc_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin   ")
                .append(" and p.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", qo.getShopIdList(), argsList));
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {

            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                String field = "r." + searchTypeEnum.getField();
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and ").append(field).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and ").append("lower(").append(field).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList(field, asinList, argsList));
                }
            }
        }

        if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
        }

        if (StringUtils.isNotBlank(qo.getGroupId())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
        }
        selectSql.append(" and r.asin is not null and r.asin != '' group by r.asin, r.sku , r.shop_id ");
        sql.add(selectSql.toString());
    }



//    private void getSpOrSdAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql) {
//        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
//            return;
//        }
//        StringBuilder selectSql = new StringBuilder("select  r.asin asin, r.sku msku , r.shop_id shopId  from " + tableName + " r ");
//        selectSql.append(" join (select puid, id, shop_id, asin, sku from ods_t_product where puid = ? ")
//                .append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList))
//                .append(" union all select puid, id, shop_id, asin, '' sku from ods_t_vc_product where puid = ?  ")
//                .append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList))
//                .append(" ) ")
//                .append(" p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
//                .append(" and p.puid = ? ");
//        argsList.add(puid);
//        argsList.add(puid);
//        argsList.add(puid);
//        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));
//        selectSql.append(" where r.puid = ? ");
//        argsList.add(puid);
//        selectSql.append(SqlStringUtil.dealInList("r.shop_id", qo.getShopIdList(), argsList));
//        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
//            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
//            if (searchTypeEnum != null) {
//                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
//                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
//                String field = "r." + searchTypeEnum.getField();
//                if (asinList.size() == 1) {
//                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
//                    if (qo.getSearchFlag()) {
//                        selectSql.append(" and ").append(field).append(" = ? ");
//                        argsList.add(asinList.get(0).trim());
//                    } else {
//                        selectSql.append(" and ").append("lower(").append(field).append(")").append(" like ? ");
//                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
//                    }
//                } else {
//                    selectSql.append(SqlStringUtil.dealInList(field, asinList, argsList));
//                }
//            }
//        }
//
//        if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
//            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
//        }
//
//        if (StringUtils.isNotBlank(qo.getGroupId())) {
//            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
//            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
//        }
//        selectSql.append(" and r.asin is not null and r.asin != '' group by r.asin, r.sku , r.shop_id ");
//        sql.add(selectSql.toString());
//    }

    private void getSpOrSdParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql, List<Integer> scShopIds) {
        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(scShopIds)) {
            StringBuilder selectSql = new StringBuilder("select  p.parent_id parentId, r.shop_id shopId  from " + tableName + " r ");
            selectSql.append(" join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
                    .append(" and p.puid = ? and p.parent_id is not null ");
            argsList.add(puid);
            selectSql.append(SqlStringUtil.dealInList("p.shop_id", scShopIds, argsList));
            selectSql.append(" where r.puid = ? ");
            argsList.add(puid);
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", scShopIds, argsList));
            if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
                // 此处一定是父asin
                AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                String field = "p." + searchTypeEnum.getField();
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and (");
                        selectSql.append(" ").append(field).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                        selectSql.append(" or ( r.asin = ? and (p.parent_asin is null or p.parent_asin =''))) ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and (");
                        selectSql.append(" ").append("lower(").append(field).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                        selectSql.append(" or ( lower(r.asin) like ? and (p.parent_asin is null or p.parent_asin =''))) ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(" and ( ");
                    selectSql.append(SqlStringUtil.dealInListNotAnd(field, asinList, argsList));
                    selectSql.append(" or ( ");
                    selectSql.append(SqlStringUtil.dealInListNotAnd("r.asin", asinList, argsList));
                    selectSql.append(" and (p.parent_asin is null or p.parent_asin =''))) ");
                }
            }

            if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
            }

            if (StringUtils.isNotBlank(qo.getGroupId())) {
                List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
                selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
            }
            selectSql.append(" and r.asin is not null and r.asin != '' and p.parent_id is not null  group by  p.parent_id, r.shop_id ");
            sql.add(selectSql.toString());
        }


    }



    private void getVcSpOrSdParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, String tableName, String type, List<String> sql, List<Integer> vcShopIds) {
        if (qo.getIsGroup() && !qo.getCampaignIdMap().containsKey(type)) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  p2.id parentId, r.shop_id shopId  from " + tableName + " r ");
        selectSql.append(" join ods_t_vc_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin ")
                .append(" and p.puid = ? and p.parent_asin is not null ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", vcShopIds, argsList));
        selectSql.append(" join ods_t_vc_product p2 on p.puid = p2.puid and p.shop_id = p2.shop_id and p.parent_asin = p2.asin and p2.puid = ?  ") ;
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p2.shop_id", vcShopIds, argsList));
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", vcShopIds, argsList));
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            // 此处一定是父asin
            AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
            List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
            String field = "p." + searchTypeEnum.getField();
            if (asinList.size() == 1) {
                // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                if (qo.getSearchFlag()) {
                    selectSql.append(" and (");
                    selectSql.append(" ").append(field).append(" = ? ");
                    argsList.add(asinList.get(0).trim());
                    selectSql.append(" or ( r.asin = ? and (p.parent_asin is null or p.parent_asin =''))) ");
                    argsList.add(asinList.get(0).trim());
                } else {
                    selectSql.append(" and (");
                    selectSql.append(" ").append("lower(").append(field).append(")").append(" like ? ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    selectSql.append(" or ( lower(r.asin) like ? and (p.parent_asin is null or p.parent_asin =''))) ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                selectSql.append(" and ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd(field, asinList, argsList));
                selectSql.append(" or ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd("r.asin", asinList, argsList));
                selectSql.append(" and (p.parent_asin is null or p.parent_asin =''))) ");
            }
        }

        if (qo.getIsGroup() && qo.getCampaignIdMap().containsKey(type)) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdMap().get(type), argsList));
        }

        if (StringUtils.isNotBlank(qo.getGroupId())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupId(), StringUtil.SPLIT_COMMA);
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIdList, argsList));
        }
        selectSql.append(" and r.asin is not null and r.asin != '' and p.parent_asin is not null  group by  p2.id, r.shop_id ");
        sql.add(selectSql.toString());
    }

    private void getSbAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql, boolean isVc) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  asin ,sku msku, shop_id shopId from ods_t_product where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        if (qo.getSbAsin().size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));
        } else {
            List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
            selectSql.append(" and (");
            int size = lists.size() - 1;
            for (int i = 0; i <= size; i++) {
                selectSql.append(SqlStringUtil.dealInListNotAnd("asin", lists.get(i), argsList));
                if (i < size) {
                    selectSql.append(" or ");
                }
            }
            selectSql.append(" ) ");
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and ").append(searchTypeEnum.getField()).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList(searchTypeEnum.getField(), asinList, argsList));
                }
            }
        }
        sql.add(selectSql.toString());
        if (isVc) {
            getSbAsinsVc(puid, qo, argsList, sql);
        }
    }


    /**
     * 使用 Doris lateral view explode 功能获取 SB 广告相关的 ASIN（新方法）
     * 直接在数据库层处理逗号分割的 asins 字段，避免应用层拆分导致的重复数据问题
     */
    private void getSbAsinsWithExplode(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql, boolean isVc) {
        // 使用 Doris 的 lateral view explode 功能来拆分逗号分割的 asins 字段
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select distinct p.asin, p.sku msku, p.shop_id shopId ")
                 .append("from ods_t_product p ")
                 .append("join (")
                 .append("  select puid, shop_id, trim(asin_item) as asin_item ")
                 .append("  from ods_t_amazon_sb_ads ")
                 .append("  lateral view explode(split(asins, ',')) t as asin_item ")
                 .append("  where puid = ? ");

        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));

        // 如果有广告组筛选条件
        if (StringUtils.isNotBlank(qo.getGroupId())) {
            selectSql.append(" and ad_group_id = ? ");
            argsList.add(qo.getGroupId());
        }

        // 如果有活动筛选条件
        if (qo.getIsGroup() && qo.getCampaignIdMap() != null && qo.getCampaignIdMap().containsKey(Constants.SB)) {
            selectSql.append(SqlStringUtil.dealInList("campaign_id", qo.getCampaignIdMap().get(Constants.SB), argsList));
        }

        selectSql.append("  and asins is not null and asins != '' ")
                 .append("  and trim(asin_item) != '' ")
                 .append(") sb ")
                 .append("on p.puid = sb.puid and p.shop_id = sb.shop_id and p.asin = sb.asin_item ")
                 .append("where p.puid = ? ");

        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));

        // 添加搜索条件
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and p.").append(searchTypeEnum.getField()).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and lower(p.").append(searchTypeEnum.getField()).append(") like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList("p." + searchTypeEnum.getField(), asinList, argsList));
                }
            }
        }

        sql.add(selectSql.toString());
        if (isVc) {
            getSbAsinsVcWithExplode(puid, qo, argsList, sql);
        }
    }

    /**
     * 使用 Doris lateral view explode 功能获取 SB 广告相关的 VC 产品 ASIN（新方法）
     */
    private void getSbAsinsVcWithExplode(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql) {
        AsinListReqVo.SearchTypeEnum searchTypeEnum = null;
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
        }
        if (AsinListReqVo.SearchTypeEnum.MSKU == searchTypeEnum) {
            return;
        }

        // 使用 Doris 的 lateral view explode 功能来拆分逗号分割的 asins 字段，查询 VC 产品
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select distinct p.asin, '' msku, p.shop_id shopId ")
                 .append("from ods_t_vc_product p ")
                 .append("join (")
                 .append("  select puid, shop_id, trim(asin_item) as asin_item ")
                 .append("  from ods_t_amazon_sb_ads ")
                 .append("  lateral view explode(split(asins, ',')) t as asin_item ")
                 .append("  where puid = ? ");

        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));

        // 如果有广告组筛选条件
        if (StringUtils.isNotBlank(qo.getGroupId())) {
            selectSql.append(" and ad_group_id = ? ");
            argsList.add(qo.getGroupId());
        }

        // 如果有活动筛选条件
        if (qo.getIsGroup() && qo.getCampaignIdMap() != null && qo.getCampaignIdMap().containsKey(Constants.SB)) {
            selectSql.append(SqlStringUtil.dealInList("campaign_id", qo.getCampaignIdMap().get(Constants.SB), argsList));
        }

        selectSql.append("  and asins is not null and asins != '' ")
                 .append("  and trim(asin_item) != '' ")
                 .append(") sb ")
                 .append("on p.puid = sb.puid and p.shop_id = sb.shop_id and p.asin = sb.asin_item ")
                 .append("where p.puid = ? ");

        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", qo.getShopIdList(), argsList));

        // 添加搜索条件
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and p.").append(searchTypeEnum.getField()).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and lower(p.").append(searchTypeEnum.getField()).append(") like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList("p." + searchTypeEnum.getField(), asinList, argsList));
                }
            }
        }

        sql.add(selectSql.toString());
    }

    private void getSbAsinsVc(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        AsinListReqVo.SearchTypeEnum searchTypeEnum = null;
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
        }
        if (AsinListReqVo.SearchTypeEnum.MSKU == searchTypeEnum) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  asin ,'' msku, shop_id shopId from ods_t_vc_product where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
        if (qo.getSbAsin().size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));
        } else {
            List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
            selectSql.append(" and (");
            int size = lists.size() - 1;
            for (int i = 0; i <= size; i++) {
                selectSql.append(SqlStringUtil.dealInListNotAnd("asin", lists.get(i), argsList));
                if (i < size) {
                    selectSql.append(" or ");
                }
            }
            selectSql.append(" ) ");
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            if (searchTypeEnum != null) {
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and ").append(searchTypeEnum.getField()).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(SqlStringUtil.dealInList(searchTypeEnum.getField(), asinList, argsList));
                }
            }
        }
        sql.add(selectSql.toString());
    }


//    private void getSbAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql, boolean isVc) {
//        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
//            return;
//        }
//        StringBuilder selectSql = new StringBuilder("select  asin ,sku msku, shop_id shopId from (");
//        selectSql.append(" select  asin ,sku , shop_id  from ods_t_product where puid = ? ");
//        argsList.add(puid);
//        selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
//        if (qo.getSbAsin().size() < 10000) {
//            selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));
//        } else {
//            List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
//            selectSql.append(" and (");
//            int size = lists.size() - 1;
//            for (int i = 0; i <= size; i++) {
//                selectSql.append(SqlStringUtil.dealInListNotAnd("asin", lists.get(i), argsList));
//                if (i < size) {
//                    selectSql.append(" or ");
//                }
//            }
//            selectSql.append(" ) ");
//        }
//        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
//            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
//            if (searchTypeEnum != null) {
//                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
//                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
//                if (asinList.size() == 1) {
//                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
//                    if (qo.getSearchFlag()) {
//                        selectSql.append(" and ").append(searchTypeEnum.getField()).append(" = ? ");
//                        argsList.add(asinList.get(0).trim());
//                    } else {
//                        selectSql.append(" and ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
//                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
//                    }
//                } else {
//                    selectSql.append(SqlStringUtil.dealInList(searchTypeEnum.getField(), asinList, argsList));
//                }
//            }
//        }
//        AsinListReqVo.SearchTypeEnum searchTypeEnum = null;
//        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
//            searchTypeEnum = UCommonUtil.getByCode(qo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
//        }
//        if (isVc) {
//            if (AsinListReqVo.SearchTypeEnum.MSKU != searchTypeEnum) {
//                selectSql.append(" union all ");
//                selectSql.append(" select  asin ,'' sku, shop_id  from ods_t_vc_product where puid = ? ");
//                argsList.add(puid);
//                selectSql.append(SqlStringUtil.dealInList("shop_id", qo.getShopIdList(), argsList));
//                if (qo.getSbAsin().size() < 10000) {
//                    selectSql.append(SqlStringUtil.dealInList("asin", qo.getSbAsin(), argsList));
//                } else {
//                    List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
//                    selectSql.append(" and (");
//                    int size = lists.size() - 1;
//                    for (int i = 0; i <= size; i++) {
//                        selectSql.append(SqlStringUtil.dealInListNotAnd("asin", lists.get(i), argsList));
//                        if (i < size) {
//                            selectSql.append(" or ");
//                        }
//                    }
//                    selectSql.append(" ) ");
//                }
//                if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
//
//                    if (searchTypeEnum != null) {
//                        List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
//                                .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
//                        if (asinList.size() == 1) {
//                            // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
//                            if (qo.getSearchFlag()) {
//                                selectSql.append(" and ").append(searchTypeEnum.getField()).append(" = ? ");
//                                argsList.add(asinList.get(0).trim());
//                            } else {
//                                selectSql.append(" and ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
//                                argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
//                            }
//                        } else {
//                            selectSql.append(SqlStringUtil.dealInList(searchTypeEnum.getField(), asinList, argsList));
//                        }
//                    }
//                }
//            }
//        }
//        selectSql.append(" ) vs ");
//        sql.add(selectSql.toString());
//    }

    private void getSbParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql, List<Integer> scShopIds) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        if (CollectionUtils.isNotEmpty(scShopIds)) {
            StringBuilder selectSql = new StringBuilder("select  DISTINCT p.parent_id parentId , p.shop_id shopId from ods_t_product p where puid = ? ");
            argsList.add(puid);
            selectSql.append(SqlStringUtil.dealInList("p.shop_id", scShopIds, argsList));
            if (qo.getSbAsin().size() < 10000) {
                selectSql.append(SqlStringUtil.dealInList("p.asin", qo.getSbAsin(), argsList));
            } else {
                List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
                selectSql.append(" and (");
                int size = lists.size() - 1;
                for (int i = 0; i <= size; i++) {
                    selectSql.append(SqlStringUtil.dealInList("p.asin", lists.get(i), argsList));
                    if (i < size) {
                        selectSql.append(" or ");
                    }
                }
                selectSql.append(" ) ");
            }
            if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
                AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
                List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                        .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
                if (asinList.size() == 1) {
                    // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                    if (qo.getSearchFlag()) {
                        selectSql.append(" and (");
                        selectSql.append(" ").append(searchTypeEnum.getField()).append(" = ? ");
                        argsList.add(asinList.get(0).trim());
                        selectSql.append(" or (p.asin = ? and ( p.parent_asin is null or p.parent_asin =''))) ");
                        argsList.add(asinList.get(0).trim());
                    } else {
                        selectSql.append(" and (");
                        selectSql.append(" ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                        selectSql.append(" or ( lower(p.asin) like ? and (p.parent_asin is null or p.parent_asin =''))) ");
                        argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    }
                } else {
                    selectSql.append(" and ( ");
                    selectSql.append(SqlStringUtil.dealInListNotAnd(searchTypeEnum.getField(), asinList, argsList));
                    selectSql.append(" or ( ");
                    selectSql.append(SqlStringUtil.dealInListNotAnd("p.asin", asinList, argsList));
                    selectSql.append(" and (p.parent_asin is null or p.parent_asin ='')))");
                }
            }
            sql.add(selectSql.toString());
        }
    }




    private void getSbVcParentAsins(Integer puid, AsinListReqVo qo, List<Object> argsList, List<String> sql, List<Integer> vcShopIds) {
        if (CollectionUtils.isEmpty(qo.getSbAsin())) {
            return;
        }
        StringBuilder selectSql = new StringBuilder("select  DISTINCT p2.id parentId , p1.shop_id shopId from ods_t_vc_product p1 ");
        selectSql.append(" join ods_t_vc_product p2 on p1.puid = p2.puid and p1.shop_id = p2.shop_id and p1.parent_asin = p2.asin and p2.puid = ?  ") ;
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p2.shop_id", vcShopIds, argsList));
        selectSql.append(" where p1.puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("p1.shop_id", vcShopIds, argsList));
        if (qo.getSbAsin().size() < 10000) {
            selectSql.append(SqlStringUtil.dealInList("p1.asin", qo.getSbAsin(), argsList));
        } else {
            List<List<String>> lists = Lists.partition(qo.getSbAsin(), 8000);
            selectSql.append(" and (");
            int size = lists.size() - 1;
            for (int i = 0; i <= size; i++) {
                selectSql.append(SqlStringUtil.dealInList("p1.asin", lists.get(i), argsList));
                if (i < size) {
                    selectSql.append(" or ");
                }
            }
            selectSql.append(" ) ");
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = AsinListReqVo.SearchTypeEnum.PARENT_ASIN;
            List<String> asinList = StringUtil.splitStr(qo.getSearchValue(), StringUtil.SPECIAL_COMMA)
                    .stream().map(SqlStringUtil::dealLikeSql).distinct().collect(Collectors.toList());
            if (asinList.size() == 1) {
                // TODO doris查询 有大小写问题 可以都转换为小写  这里再看
                if (qo.getSearchFlag()) {
                    selectSql.append(" and (");
                    selectSql.append(" ").append(searchTypeEnum.getField()).append(" = ? ");
                    argsList.add(asinList.get(0).trim());
                    selectSql.append(" or (p1.asin = ? and ( p1.parent_asin is null or p1.parent_asin =''))) ");
                    argsList.add(asinList.get(0).trim());
                } else {
                    selectSql.append(" and (");
                    selectSql.append(" ").append("lower(").append(searchTypeEnum.getField()).append(")").append(" like ? ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                    selectSql.append(" or ( lower(p1.asin) like ? and (p1.parent_asin is null or p1.parent_asin =''))) ");
                    argsList.add("%" + asinList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                selectSql.append(" and ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd(searchTypeEnum.getField(), asinList, argsList));
                selectSql.append(" or ( ");
                selectSql.append(SqlStringUtil.dealInListNotAnd("p1.asin", asinList, argsList));
                selectSql.append(" and (p1.parent_asin is null or p1.parent_asin ='')))");
            }
        }

        sql.add(selectSql.toString());
    }

    @Override
    public List<OdsAmazonAdProduct> listByShopAsin(Integer puid, List<Integer> shopIdList, List<String> asinList, List<String> adGroupIds) {
        if (CollectionUtils.isEmpty(shopIdList) || CollectionUtils.isEmpty(asinList)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder(" select shop_id, asin, sku, ad_group_id from ods_t_amazon_ad_product where puid=? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        sql.append(" group by shop_id, asin, sku, ad_group_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsAmazonAdProduct.class), argsList.toArray());
    }

    @Override
    public List<OdsAmazonAdProduct> listByShopParentAsin(Integer puid, List<Integer> shopIdList, List<String> parentAsinList) {
        if (CollectionUtils.isEmpty(shopIdList) || CollectionUtils.isEmpty(parentAsinList)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder();
        List<Object> argsList = new ArrayList<>();

        sql.append(" select ")
                .append(" ap.shop_id shop_id,")
                .append(" ap.asin asin,")
                .append(" ap.sku sku,")
                .append(" if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) parent_asin");
        sql.append(" from ods_t_amazon_ad_product ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.puid=? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        sql.append(SqlStringUtil.dealInList("if(p.parent_asin is null or p.parent_asin='', p.asin, p.parent_asin)", parentAsinList, argsList));

        sql.append(" where ap.puid=? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("ap.shop_id", shopIdList, argsList));

        sql.append(" group by ap.shop_id, ap.asin, ap.sku, if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsAmazonAdProduct odsAmazonAdProduct = new OdsAmazonAdProduct();
            odsAmazonAdProduct.setShopId(rs.getInt("shop_id"));
            odsAmazonAdProduct.setAsin(rs.getString("asin"));
            odsAmazonAdProduct.setSku(rs.getString("sku"));
            odsAmazonAdProduct.setParentAsin(rs.getString("parent_asin"));
            return odsAmazonAdProduct;
        }, argsList.toArray());
    }

    @Override
    public List<OdsProduct> getOdsProductByIds(Integer puid, String marketplaceId, List<Integer> shopIds, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList) || CollectionUtils.isEmpty(shopIds)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();

        sql.append(" select ")
                .append(" ap.shop_id shop_id, ")
                .append(" ap.asin asin, ")
                .append(" ap.sku sku, ")
                .append(" if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) parent_asin")
                .append(" from ods_t_amazon_ad_product ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.marketplace_id=p.marketplace_id and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.asin is not null and p.asin!='' and p.puid=? and p.marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        if (idList.size() < 10000) {
            sql.append(SqlStringUtil.dealInList("p.id", idList, args));
        } else {
            sql.append(SqlStringUtil.dealBitMapDorisInList("p.id", idList, args));
        }

        sql.append(" where ap.puid=? and ap.marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("ap.shop_id", shopIds, args));
        sql.append(" group by ap.shop_id, ap.asin, ap.sku, if(p.parent_asin is null or p.parent_asin='',p.asin,p.parent_asin) ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsProduct product = new OdsProduct();
            product.setShopId(rs.getInt("shop_id"));
            product.setAsin(rs.getString("asin"));
            product.setSku(rs.getString("sku"));
            product.setParentAsin(rs.getString("parent_asin"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<OdsProduct> listOdsProduct(Integer puid, String marketplaceId, List<Integer> shopIds,
                                           List<String> skuList, List<String> asinList, List<String> parentAsinList) {
        if (CollectionUtils.isEmpty(skuList) && CollectionUtils.isEmpty(asinList) && CollectionUtils.isEmpty(parentAsinList)) {
            return Collections.emptyList();
        }
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();

        sql.append(" select ")
                .append(" shop_id, asin, sku ")
                .append(" from ods_t_amazon_ad_product where puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));

        if (CollectionUtils.isNotEmpty(skuList)) {
            sql.append(SqlStringUtil.dealInList("sku", skuList, args));
        }
        if (CollectionUtils.isNotEmpty(asinList)) {
            sql.append(SqlStringUtil.dealInList("asin", asinList, args));
        }
        if (CollectionUtils.isNotEmpty(parentAsinList)) {
            sql.append(" and concat_ws(',', shop_id, asin, sku) in (")
                    .append(" select concat_ws(',', shop_id, asin, sku) shopAsinSkuText")
                    .append(" from ods_t_product where puid=? and marketplace_id=? ");
            args.add(puid);
            args.add(marketplaceId);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
            sql.append(SqlStringUtil.dealInList("if(parent_asin is null or parent_asin='', asin, parent_asin)", parentAsinList, args));
            sql.append(")");
        }
        sql.append(" group by shop_id, asin, sku ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsProduct product = new OdsProduct();
            product.setShopId(rs.getInt("shop_id"));
            product.setAsin(rs.getString("asin"));
            product.setSku(rs.getString("sku"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<AdGroupAndAdIdDto> getAdIdAndAdGroupIdByAsin(int puid, List<Integer> shopIdList, List<String> asinList, List<String> mskuList, String campaignId, String groupId) {
        StringBuilder sqlBuilder = new StringBuilder("select ad_id, ad_group_id, shop_id from ods_t_amazon_ad_product where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        sqlBuilder.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sqlBuilder.append(SqlStringUtil.dealInList("asin", asinList, args));

        if (CollectionUtils.isNotEmpty(mskuList)) {
            sqlBuilder.append(SqlStringUtil.dealInList("sku", mskuList, args));
        }

        if (StringUtils.isNotBlank(campaignId)) {
            sqlBuilder.append(" and campaign_id = ? ");
            args.add(campaignId);
        }
        if (StringUtils.isNotBlank(groupId)) {
            sqlBuilder.append(" and ad_group_id = ? ");
            args.add(groupId);
        }
        return getJdbcTemplate().query(sqlBuilder.toString(), (rs, rowNum) -> {
            AdGroupAndAdIdDto dto = new AdGroupAndAdIdDto();
            dto.setAdGroupId(rs.getString("ad_group_id"));
            dto.setAdId(rs.getString("ad_id"));
            dto.setShopId(rs.getInt("shop_id"));
            return dto;
        }, args.toArray());
    }

    @Override
    public List<OdsAmazonAdProduct> getGroupIdByProduct(MultiShopGroupByProductParam param) {
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();

        String field = StringUtils.equalsIgnoreCase(param.getSearchType(), "asin") ? "asin" : "sku";
        List<String> searchValues = param.getSearchValue();

        sql.append("select")
                .append(" shop_id, ad_group_id ")
                .append(" from ods_t_amazon_ad_product where puid=? and marketplace_id=?");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIds(), args));
        }
        if (StringUtils.equalsIgnoreCase(MultiShopGroupByProductParam.CONTAIN_ANY, param.getContainType())) {
            sql.append(SqlStringUtil.dealInList(field, searchValues, args));
        }
        sql.append(" group by shop_id, ad_group_id ");
        if (StringUtils.equalsIgnoreCase(MultiShopGroupByProductParam.CONTAIN_ALL, param.getContainType())) {
            sql.append(" having count(distinct ").append(field).append(") >= ").append(searchValues.size());
            sql.append(" and count( ");
            sql.append(" distinct case when ").append(field).append(" in ('").append(StringUtils.join(searchValues, "','")).append("')").append(" then ").append(field).append(" end ");
            sql.append(" ) = ").append(searchValues.size());
        }


        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsAmazonAdProduct product = new OdsAmazonAdProduct();
            product.setShopId(rs.getInt("shop_id"));
            product.setAdGroupId(rs.getString("ad_group_id"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<OdsAmazonAdProduct> getGroupIdByParentAsin(MultiShopGroupByProductParam param) {
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();

        List<String> searchValues = param.getSearchValue();
        String field = "p.parent_asin";

        sql.append("select")
                .append(" ap.shop_id, ap.ad_group_id ")
                .append(" from ods_t_amazon_ad_product ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.parent_asin is not null and p.parent_asin!='' and p.is_variation=2")
                .append(" and p.puid=? and p.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            sql.append(SqlStringUtil.dealInList("p.shop_id", param.getShopIds(), args));
        }

        sql.append(" where ap.puid=? and ap.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            sql.append(SqlStringUtil.dealInList("ap.shop_id", param.getShopIds(), args));
        }
        if (StringUtils.equalsIgnoreCase(MultiShopGroupByProductParam.CONTAIN_ANY, param.getContainType())) {
            sql.append(SqlStringUtil.dealInList(field, searchValues, args));
        }
        sql.append(" group by ap.shop_id, ap.ad_group_id ");
        if (StringUtils.equalsIgnoreCase(MultiShopGroupByProductParam.CONTAIN_ALL, param.getContainType())) {
            sql.append(" having count(distinct ").append(field).append(") >= ").append(searchValues.size());
            sql.append(" and count( ");
            sql.append(" distinct case when ").append(field).append(" in ('").append(StringUtils.join(searchValues, "','")).append("')").append(" then ").append(field).append(" end ");
            sql.append(" ) = ").append(searchValues.size());
        }

        log.info("getGroupIdByParentAsin {}", SqlStringUtil.exactSql(sql.toString(), args));

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            OdsAmazonAdProduct product = new OdsAmazonAdProduct();
            product.setShopId(rs.getInt("shop_id"));
            product.setAdGroupId(rs.getString("ad_group_id"));
            return product;
        }, args.toArray());
    }

    @Override
    public List<AdProductPageVo> getProductClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup) {
        String field = StringUtils.equalsIgnoreCase(param.getSearchType(), "asin") ? "asin" : "sku";
        List<String> searchValues = param.getSearchValue();

        List<String> adGroupIds = productGroup.stream().map(OdsAmazonAdProduct::getAdGroupId).distinct().collect(Collectors.toList());
        List<Integer> shopIds = productGroup.stream().map(OdsAmazonAdProduct::getShopId).distinct().collect(Collectors.toList());

        List<Object> args = new ArrayList<>();

        StringBuilder reportSql = new StringBuilder();
        reportSql.append(" select")
                .append(" ap.shop_id shop_id,")
                .append(" ap.ad_group_id ad_group_id,")
                .append(" ap.asin asin,")
                .append(" ap.sku sku,")
                .append(" sum(ifnull(adr.clicks, 0)) clicks")
                .append(" from ods_t_amazon_ad_product ap left join ods_t_amazon_ad_product_report adr")
                .append(" on ap.puid=adr.puid and ap.marketplace_id=adr.marketplace_id and ap.shop_id=adr.shop_id and ap.ad_id=adr.ad_id")
                .append(" and adr.puid=? and adr.marketplace_id=? and adr.count_day>=? and adr.count_day<=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        reportSql.append(SqlStringUtil.dealInList("adr.shop_id", shopIds, args));
        if (adGroupIds.size() < 10000) {
            reportSql.append(SqlStringUtil.dealInList("adr.ad_group_id", adGroupIds, args));
        } else {
            reportSql.append(SqlStringUtil.dealBitMapDorisInList("adr.ad_group_id", adGroupIds, args));
        }
        reportSql.append(SqlStringUtil.dealInList("adr." + field, searchValues, args));

        reportSql.append(" where ap.asin is not null and ap.asin!='' and ap.puid=? and ap.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(param.getStates())) {
            reportSql.append(SqlStringUtil.dealInList("ap.state", param.getStates(), args));
        }
        reportSql.append(SqlStringUtil.dealInList("ap.shop_id", shopIds, args));
        if (adGroupIds.size() < 10000) {
            reportSql.append(SqlStringUtil.dealInList("ap.ad_group_id", adGroupIds, args));
        } else {
            reportSql.append(SqlStringUtil.dealBitMapDorisInList("ap.ad_group_id", adGroupIds, args));
        }
        reportSql.append(SqlStringUtil.dealInList("ap." + field, searchValues, args));

        reportSql.append(" group by ap.shop_id, ap.ad_group_id, ap.asin, ap.sku ");

        StringBuilder sql = new StringBuilder();
        sql.append(" select ")
                .append(" r.shop_id shop_id,")
                .append(" r.ad_group_id ad_group_id,")
                .append(" r.asin asin,")
                .append(" r.sku sku,")
                .append(" sum(ifnull(r.clicks, 0)) clicks,")
                .append(" any(p.title) title,")
                .append(" any(p.main_image) main_image")
                .append(" from (").append(reportSql).append(") r left join ods_t_product p")
                .append(" on r.shop_id=p.shop_id and r.asin=p.asin and r.sku=p.sku")
                .append(" and p.puid=? and p.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("p." + field, searchValues, args));
        sql.append(" group by r.shop_id, r.ad_group_id, r.asin, r.sku ");


        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdProductPageVo vo = new AdProductPageVo();
            vo.setShopId(rs.getInt("shop_id"));
            vo.setAdGroupId(rs.getString("ad_group_id"));
            vo.setAsin(rs.getString("asin"));
            vo.setSku(rs.getString("sku"));
            vo.setClicks(rs.getInt("clicks"));
            vo.setTitle(rs.getString("title"));
            vo.setImgUrl(rs.getString("main_image"));
            return vo;
        }, args.toArray());
    }

    @Override
    public List<AdProductPageVo> getProductClickDataByParent(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup) {
        List<String> searchValues = param.getSearchValue();
        String field = "p.parent_asin";

        List<String> adGroupIds = productGroup.stream().map(OdsAmazonAdProduct::getAdGroupId).distinct().collect(Collectors.toList());
        List<Integer> shopIds = productGroup.stream().map(OdsAmazonAdProduct::getShopId).distinct().collect(Collectors.toList());

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select ")
                .append(" ap.shop_id shop_id,")
                .append(" ap.ad_group_id ad_group_id,")
                .append(" ap.asin asin,")
                .append(" ap.sku sku,")
                .append(" sum(ifnull(adr.clicks, 0)) clicks,")
                .append(" any(p.title) title,")
                .append(" any(p.parent_asin) parent_asin,")
                .append(" any(p.main_image) main_image")
                .append(" from ods_t_amazon_ad_product ap join ods_t_product p")
                .append(" on ap.puid=p.puid and ap.marketplace_id=p.marketplace_id and ap.shop_id=p.shop_id and ap.asin=p.asin and ap.sku=p.sku")
                .append(" and p.parent_asin is not null and p.parent_asin!='' and p.is_variation=2")
                .append(" and p.puid=? and p.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList(field, searchValues, args));

        sql.append(" left join ods_t_amazon_ad_product_report adr")
                .append(" on ap.puid=adr.puid and ap.marketplace_id=adr.marketplace_id and ap.shop_id=adr.shop_id and ap.ad_id=adr.ad_id")
                .append(" and adr.puid=? and adr.marketplace_id=? and adr.count_day>=? and adr.count_day<=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        sql.append(SqlStringUtil.dealInList("adr.shop_id", shopIds, args));
        if (adGroupIds.size() < 10000) {
            sql.append(SqlStringUtil.dealInList("adr.ad_group_id", adGroupIds, args));
        } else {
            sql.append(SqlStringUtil.dealBitMapDorisInList("adr.ad_group_id", adGroupIds, args));
        }

        sql.append(" where ap.asin is not null and ap.asin!='' and ap.puid=? and ap.marketplace_id=? ");
        args.add(param.getPuid());
        args.add(param.getMarketplaceId());
        sql.append(SqlStringUtil.dealInList("ap.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(param.getStates())) {
            sql.append(SqlStringUtil.dealInList("ap.state", param.getStates(), args));
        }
        if (adGroupIds.size() < 10000) {
            sql.append(SqlStringUtil.dealInList("ap.ad_group_id", adGroupIds, args));
        } else {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ap.ad_group_id", adGroupIds, args));
        }

        sql.append(" group by ap.shop_id, ap.ad_group_id, ap.asin, ap.sku ");

        return getJdbcTemplate().query(sql.toString(), (rs, rowNum) -> {
            AdProductPageVo vo = new AdProductPageVo();
            vo.setShopId(rs.getInt("shop_id"));
            vo.setAdGroupId(rs.getString("ad_group_id"));
            vo.setAsin(rs.getString("asin"));
            vo.setSku(rs.getString("sku"));
            vo.setClicks(rs.getInt("clicks"));
            vo.setParentAsin(rs.getString("parent_asin"));
            vo.setTitle(rs.getString("title"));
            vo.setImgUrl(rs.getString("main_image"));
            return vo;
        }, args.toArray());
    }

    @Override
    public int countByGroupIdList(Integer puid, Integer shopId, List<String> adGroupIds) {
        if (CollectionUtils.isEmpty(adGroupIds)) {
            return 0;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) from ods_t_amazon_ad_product where puid = ? and shop_id = ? and state in('enabled', 'paused') ");
        argsList.add(puid);
        argsList.add(shopId);
        if (adGroupIds.size() >= 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", adGroupIds, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        return countPageResult(puid, sql.toString(), argsList.toArray());
    }

    @Override
    public List<AdProductDetailDto> listByGroupIdList(Integer puid, Integer shopId, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select r.puid,r.shop_id as shopId,r.ad_group_id as adGroupId,r.sku,r.asin,p.id,p.marketplace_id as marketplaceId,p.main_image as mainImage,p.title,p.online_status as onlineStatus ")
            .append(" from ods_t_amazon_ad_product r ")
            .append(" left join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
            .append(" and p.puid = ? and p.shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(" where r.puid = ? and r.shop_id = ? and r.state in('enabled', 'paused')");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", adGroupIds, argsList));
        sql.append(" order by r.ad_group_id desc");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AdProductDetailDto.class), argsList.toArray());
    }

    @Override
    public List<AdProductDetailDto> listProductsByGroupIdList(Integer puid, Integer targetShopId, List<String> adGroupIds, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select ")
                .append(" min(r.puid) as puid, ")
                .append(" max(r.ad_group_id) as adGroupId, ")
                .append(" r.sku, ")
                .append(" r.asin, ")
                .append(" min(p.id) as id, ")
                .append(" min(p.marketplace_id) as marketplaceId, ")
                .append(" any_value(p.main_image) as mainImage, ")
                .append(" any_value(p.title) as title, ")
                .append(" any_value(p.online_status) as onlineStatus, ")
                .append(" any_value( otaag.create_time) as createTime")
                .append(" from ods_t_amazon_ad_product r ")
                .append(" join ods_t_amazon_ad_group otaag on r.puid = otaag.puid and r.shop_id = otaag.shop_id and r.ad_group_id = otaag.ad_group_id ")
                .append(" left join ods_t_product p on r.puid = p.puid and r.shop_id = p.shop_id and r.asin = p.asin and r.sku = p.sku ")
                .append(" and p.puid = ?  ")
                .append(" and p.shop_id = ? ");
        argsList.add(puid);
        argsList.add(targetShopId);
        sql.append(" where r.puid = ? and p.shop_id = ? and r.state in('enabled', 'paused')");
        argsList.add(puid);
        argsList.add(targetShopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", adGroupIds, argsList));
        sql.append(" group by r.asin, r.sku  ");
        sql.append(" order by createTime desc");
        sql.append(" limit ? ");
        argsList.add(limit);

        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AdProductDetailDto.class), argsList.toArray());
    }

    @Override
    public List<AsinCampaignNumDto> getAsinCampaignNum(int puid, List<Integer> shopIdList, List<String> asinList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select p.asin, p.shop_id as shopId, count(distinct p.campaign_id) as campaignNum ");
        sql.append(" from ods_t_amazon_ad_product p  ");
        sql.append(" inner join (select puid, shop_id, campaign_id,serving_status from  ods_t_amazon_ad_campaign_all where puid = ? ");
        args.add(puid);
        sql .append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        sql.append(" ) c on p.puid = c.puid and p.shop_id  = c.shop_id and p.campaign_id  = c.campaign_id  ");
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, args));
        sql.append(SqlStringUtil.dealInList("p.asin", asinList, args));
        sql.append(" and c.serving_status in('CAMPAIGN_STATUS_ENABLED', 'RUNNING', 'running','CAMPAIGN_OUT_OF_BUDGET', 'OUT_OF_BUDGET', 'outOfBudget') ");
        sql.append(" group by p.asin, p.shop_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinCampaignNumDto.class), args.toArray());
    }
}

