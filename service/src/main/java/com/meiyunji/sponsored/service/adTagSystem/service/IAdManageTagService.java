package com.meiyunji.sponsored.service.adTagSystem.service;

import com.meiyunji.sponsored.grpc.adTagSystem.TagDetailResponse;
import com.meiyunji.sponsored.grpc.adTagSystem.TagRemoveResponse;
import com.meiyunji.sponsored.grpc.adTagSystem.TagResponse;
import com.meiyunji.sponsored.service.adTagSystem.param.AdTagParam;
import com.meiyunji.sponsored.service.adTagSystem.param.TagsRemoveParam;

public interface IAdManageTagService {

    TagResponse addTag(AdTagParam param);

    TagResponse updateTag(AdTagParam param);

    TagResponse updateTagSort(AdTagParam param);

    TagDetailResponse.TagDetailResponseData getTagDetail(Integer puid, Long id, Boolean isGroup);

    TagRemoveResponse removeTagCampaign(TagsRemoveParam param);

    TagRemoveResponse removeTags(TagsRemoveParam param);

}
