package com.meiyunji.sponsored.service.aadrasGrpcApi;

import com.meiyunji.sellfox.aadras.api.service.AadrasApiGrpc;
import com.meiyunji.sponsored.common.enums.EnvironmentType;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class AadrasGrpcApiConfig {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${services.rule-task.prefix}")
    private String scheduleTaskUrl;

    @Value("${services.rule-task.port}")
    private Integer scheduleTaskPort;

    @Value("${services.rule-task.encrypted}")
    private Boolean encrypted;

    @Value(value = "${spring.profiles.active}")
    private String active;

    @Bean("aadrasApiManagedChannel")
    public ManagedChannel aadrasApiManagedChannel() {
        logger.info("===================start aadrasApi grpc connection===================");
        ManagedChannelBuilder<?> managedChannelBuilder =
                ManagedChannelBuilder.forAddress(scheduleTaskUrl, scheduleTaskPort);
        //未使用https证书
        if (encrypted == null || !encrypted) {
            managedChannelBuilder.usePlaintext();
        }
        if (EnvironmentType.dev.name().equalsIgnoreCase(active) || EnvironmentType.test.name().equalsIgnoreCase(active)) {
            managedChannelBuilder.keepAliveTime(1, TimeUnit.MINUTES);
            managedChannelBuilder.keepAliveWithoutCalls(true);
            managedChannelBuilder.keepAliveTimeout(5, TimeUnit.MINUTES);
        }
        return managedChannelBuilder.build();
    }


}
