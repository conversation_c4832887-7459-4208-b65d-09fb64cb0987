package com.meiyunji.sponsored.service.enums;


import com.meiyunji.sponsored.service.account.po.ShopAuth;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-08-05  16:28
 */
public enum MarketplaceTimeZoneEnum {

    A13V1IB3VIYZZH("eu-west-1", "EUR", "FR", "FR", "+01:00", 1, "Europe/Paris"),
    A17E79C6D8DWNP("eu-west-1", "SAR", "SA", "SA", "+03:00", 3, "Asia/Riyadh"),
    A1805IZSGTT6HS("eu-west-1", "EUR", "NL", "NL", "+01:00", 1, "Europe/Amsterdam"),
    A19VAU5U5O7RUS("us-west-2", "SGD", "SG", "SG", "+08:00", 8, "Asia/Singapore"),
    A1AM78C64UM0Y8("us-east-1", "MXN", "MX", "MX", "-08:00", -8, "US/Pacific"),
    A1C3SOZRARQ6R3("eu-west-1", "PLN", "PL", "PL", "+01:00", 1, "Europe/Warsaw"),
    A1F83G8C2ARO7P("eu-west-1", "GBP", "UK", "UK", "+00:00", 0, "Europe/London"),
    A1PA6795UKMFR9("eu-west-1", "EUR", "DE", "DE", "+01:00", 1, "Europe/Paris"),
    A1RKKUPIHCS9HS("eu-west-1", "EUR", "ES", "ES", "+01:00", 1, "Europe/Paris"),
    A1VC38T7YXB528("us-west-2", "JPY", "JP", "JP", "+09:00", 9, "Asia/Tokyo"),
    A21TJRUUN4KGV("eu-west-1", "INR", "IN", "IN", "+05:00", 5, "Asia/Kolkata"),
    A2EUQ1WTGCTBG2("us-east-1", "CAD", "CA", "CA", "-08:00", -8, "US/Pacific"),
    A2NODRKZP88ZB9("eu-west-1", "SEK", "SE", "SE", "+01:00", 1, "Europe/Stockholm"),
    A2Q3Y263D00KWC("us-east-1", "BRL", "BR", "BR", "-03:00", -3, "Brazil/East"),
    A2VIGQ35RCS4UG("eu-west-1", "AED", "AE", "AE", "+04:00", 4, "Asia/Dubai"),
    A33AVAJ2PDY3EV("eu-west-1", "TRY", "TR", "TR", "+03:00", 3, "Turkey"),
    A39IBJ37TRP1C6("us-west-2", "AUD", "AU", "AU", "+10:00", 10, "Australia/Canberra"),
    AMEN7PMS3EDWL("eu-west-1", "EUR", "BE", "BE", "+01:00", 1, "Europe/Paris"),
    APJ6JRA9NG5V4("eu-west-1", "EUR", "IT", "IT", "+01:00", 1, "Europe/Paris"),
    ATVPDKIKX0DER("us-east-1", "USD", "US", "US", "-08:00", -8, "US/Pacific"),
    ;

    private String region;
    private String currency;
    private String code;
    private String codeExt;
    private String tz;
    private int tz_offset;
    private String zone_id;

    MarketplaceTimeZoneEnum(String region, String currency, String code, String codeExt, String tz, int tz_offset, String zone_id) {
        this.region = region;
        this.currency = currency;
        this.code = code;
        this.codeExt = codeExt;
        this.tz = tz;
        this.tz_offset = tz_offset;
        this.zone_id = zone_id;
    }

    public static Map<String, MarketplaceTimeZoneEnum> map = Arrays.stream(MarketplaceTimeZoneEnum.values())
            .collect(Collectors.toMap(Enum::name, Function.identity(), (a, b) -> a));

    public String getRegion() {
        return region;
    }

    public String getCurrency() {
        return currency;
    }

    public String getCode() {
        return code;
    }

    public String getCodeExt() {
        return codeExt;
    }

    public String getTz() {
        return tz;
    }

    public int getTz_offset() {
        return tz_offset;
    }

    public String getZone_id() {
        return zone_id;
    }
}
