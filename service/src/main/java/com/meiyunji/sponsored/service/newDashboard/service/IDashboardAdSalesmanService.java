package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdProductResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdSalesmanResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdProductReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdSalesmanReqVo;

import java.util.List;


public interface IDashboardAdSalesmanService {
    DashboardAdSalesmanResponseVo queryAdSalesmanCharts(DashboardAdSalesmanReqVo req);


}
