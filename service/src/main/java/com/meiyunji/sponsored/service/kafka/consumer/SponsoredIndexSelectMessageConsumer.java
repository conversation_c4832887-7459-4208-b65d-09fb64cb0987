package com.meiyunji.sponsored.service.kafka.consumer;

import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.index.SponsoredIndexSelectProcessFactory;
import com.meiyunji.sponsored.service.kafka.message.SponsoredIndexSelectMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.sponsored-index-select.enabled", havingValue = "true")
public class SponsoredIndexSelectMessageConsumer {

    @Resource
    private SponsoredIndexSelectProcessFactory sponsoredIndexSelectProcessFactory;

    @KafkaListener(topics = "${kafka.consumers.sponsored-index-select.topic}", containerFactory = "sponsoredIndexSelectConsumerFactory")
    public void consumer(ConsumerRecord<?, byte[]> record) throws Exception {
        try {
            if (record == null || record.value() == null) {
                log.error("sponsored-index-select record is null");
                return;
            }
            String msgStr = GZipUtils.decompressStr(new String(record.value()));
            log.info("sponsored-index-select kafka consume msg {}", msgStr);
            SponsoredIndexSelectMessage message = JSONUtil.jsonToObject(msgStr, SponsoredIndexSelectMessage.class);
            if (message == null) {
                log.error("sponsored-index-select message is null");
                return;
            }
            sponsoredIndexSelectProcessFactory.getSponsoredIndexSelectStrategy(message).processSponsoredIndexSelect(message);
        } catch (Exception e) {
            log.error("SponsoredIndexSelectMessageConsumer error", e);
        }
    }
}
