package com.meiyunji.sponsored.service.config;

import com.meiyunji.sponsored.service.kafka.AdManageOperationLogKafkaProducer;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@Slf4j
public class ManageOperationLogKafkaConfiguration {

    @Value("${spring.kafka.ad-manage-operation-log.bootstrap-servers}")
    private String bootstrapServers;

    private ProducerFactory<String,  byte[]> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }
    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 10);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class);
        return props;
    }

    @Bean
    public KafkaTemplate<String,  byte[]> manageOperationLogKafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }


    @Bean(name = "adManageOperationLogKafkaProducer")
    public AdManageOperationLogKafkaProducer adManageOperationLogKafkaProducer(
            KafkaProperties kafkaProperties, KafkaTemplate<String, byte[]> manageOperationLogKafkaTemplate) {
        KafkaProperties.ProducerProperties producerProperty = kafkaProperties.getProducers().get("ad_manage_operation_log");
        return new AdManageOperationLogKafkaProducer(producerProperty.getTopic(), manageOperationLogKafkaTemplate);
    }

}
