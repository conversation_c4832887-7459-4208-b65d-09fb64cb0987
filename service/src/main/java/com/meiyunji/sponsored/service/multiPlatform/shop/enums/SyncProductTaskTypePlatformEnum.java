package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

import lombok.Getter;

@Getter
public enum SyncProductTaskTypePlatformEnum {
    SHEIN(0, MultiPlatformTypeEnum.SHEIN, "shein"),
    /**
     * 美客多
     */
    MERCADO(1, MultiPlatformTypeEnum.MERCADO, "Mercado"),
    /**
     * 速卖通
     */
    ALIEXPRESS(2, MultiPlatformTypeEnum.ALIEXPRESS, "aliexpress"),
    /**
     * ebay
     */
    EBAY(3, MultiPlatformTypeEnum.EBAY, "ebay"),

    /**
     * 虾皮
     */
    SHOPEE(5, MultiPlatformTypeEnum.SHOPEE, "shopee"),

    ;

    private final Integer code;
    private final MultiPlatformTypeEnum multiPlatformTypeEnum;
    private final String desc;

    SyncProductTaskTypePlatformEnum(Integer code, MultiPlatformTypeEnum multiPlatformTypeEnum, String desc) {
        this.code = code;
        this.multiPlatformTypeEnum = multiPlatformTypeEnum;
        this.desc = desc;
    }

    public static SyncProductTaskTypePlatformEnum getByCode(Integer code) {
        for (SyncProductTaskTypePlatformEnum value : SyncProductTaskTypePlatformEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
