package com.meiyunji.sponsored.service.log.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.vo.SdBatchNeTargetingVo;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.log.vo.AdManageLogDayVo;
import com.meiyunji.sponsored.service.log.vo.AdManageLogVo;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/5/31 15:29
 */
public interface IAdManageOperationLogService {

    /**
     * 广告组合操作日志
     * @return
     */
    AdManageOperationLog getPortfolioOperationLog(AmazonAdPortfolio old, AmazonAdPortfolio portfolio);
    /**
     * TODO 广告活动增加日志
     * @param oldCampaign 数据库查询的对象（旧对象）
     * @param campaign 待更新的对象
     * @return AdManageOperationLog
     */
    public AdManageOperationLog getAdManageOperationLog(AmazonAdCampaignAll oldCampaign, AmazonAdCampaignAll campaign);

    /**
     * SB广告活动增加日志
     * @param oldCampaign 数据库查询的对象（旧对象）
     * @param campaign 待更新的对象
     * @return AdManageOperationLog
     */
    AdManageOperationLog getSbCampaignOperationLog(AmazonAdCampaignAll oldCampaign, AmazonAdCampaignAll campaign);

    /**
     * TODO 广告组增加日志
     * @param oldAdGroup 数据库查询的对象（旧对象）
     * @param adGroup 待更新的对象
     * @return AdManageOperationLog
     */
    public AdManageOperationLog getAdGroupLog(AmazonAdGroup oldAdGroup, AmazonAdGroup adGroup);

    /**
     * SB广告组增加日志
     * @param oldAdGroup 数据库查询的对象（旧对象）
     * @param adGroup 待更新的对象
     * @return AdManageOperationLog
     */
    AdManageOperationLog getSbAdGroupLog(AmazonSbAdGroup oldAdGroup, AmazonSbAdGroup adGroup);

    /**
     * SB广告创意增加日志
     * @param oldSbAds 数据库查询的对象（旧对象）
     * @param sbAds 待更新的对象
     * @return AdManageOperationLog
     */
    AdManageOperationLog getSbAdsLog(AmazonSbAds oldSbAds, AmazonSbAds sbAds);

    AdManageOperationLog getSbNeKeywordLog(AmazonSbAdNeKeyword oldKeyword, AmazonSbAdNeKeyword keyword);

    AdManageOperationLog getSbKeywordLog(AmazonSbAdKeyword oldKeyword, AmazonSbAdKeyword keyword);

    AdManageOperationLog getSbTargetLog(AmazonSbAdTargeting oldTargeting, AmazonSbAdTargeting targeting);

    AdManageOperationLog getSbNeTargetLog(AmazonSbAdNeTargeting oldNeTargeting, AmazonSbAdNeTargeting neTargeting);

    /**
     * heqiwen 广告活动增加日志-sd
     * @param oldSdCampaign 数据库查询的对象（旧对象）
     * @param sdCampaign 待更新的对象
     * @return AdManageOperationLog
     */
    AdManageOperationLog getSdAdManageOperationLog(AmazonAdCampaignAll oldSdCampaign, AmazonAdCampaignAll sdCampaign);

    /**
     * heqiwen 广告组添加日志-sd
     * @param oldAdGroup
     * @param sdAdGroup
     * @return
     */
    AdManageOperationLog getAdSdGroupLog(AmazonSdAdGroup oldAdGroup, AmazonSdAdGroup sdAdGroup);

    /**
     * heqiwen 广告产品添加日志-sd
     * @param oldProduct
     * @param product
     * @return
     */
    AdManageOperationLog getSdProductLog(AmazonSdAdProduct oldProduct, AmazonSdAdProduct product);

    /**
     * heqiwen 商品投放添加日志-sd
     * @param oldTargeting
     * @param targeting
     * @return
     */
    AdManageOperationLog getSdTargetsLog(AmazonSdAdTargeting oldTargeting, AmazonSdAdTargeting targeting);

    /**
     * 否定投放添加日志-sd
     * @param oldSdNeTargeting
     * @param newSdNeTargeting
     * @return
     */
    AdManageOperationLog getSdNeTargetsLog(AmazonSdAdNeTargeting oldSdNeTargeting, AmazonSdAdNeTargeting newSdNeTargeting);

    List<AdManageOperationLog> getSdCreativeLogs(AmazonAdSdCreative oldSdCreative, AmazonAdSdCreative newSdCreative);

    /**
     * TODO 关键词投放增加日志
     * @param oldKeyword 数据库查询的对象（旧对象）
     * @param keyword 待更新的对象
     * @return AdManageOperationLog
     */
    public AdManageOperationLog getkeywordsLog(AmazonAdKeyword oldKeyword, AmazonAdKeyword keyword);

    /**
     * TODO 商品投放增加日志
     * @param oldTargeting 数据库查询的对象（旧对象）
     * @param targeting 待更新的对象
     * @return AdManageOperationLog
     */
    public AdManageOperationLog getTargetsLog(AmazonAdTargeting oldTargeting, AmazonAdTargeting targeting);

    /**
     * 商品投放添加日志
     * @param oldProduct
     * @param product
     * @return
     */
    AdManageOperationLog getProductLog(AmazonAdProduct oldProduct, AmazonAdProduct product);


    //批量操作日志需根据成功/失败，然后再根据广告活动以及广告组分组
    //目的：根据分组筛选出一条记录
    /**
     * TODO 广告打印日志（广告日志公共方法）
     * @param operationLogs 收集的日志
     * @return AdManageOperationLog
     */
    public void batchLogsMergeByAdGroup(List<AdManageOperationLog> operationLogs);

    /**
     * TODO 广告打印日志（自动化、赛狐手动广告日志公共方法）
     * @param adOperationLogs 每个操作对应的所有日志
     * @return
     */
    public void printAdOperationLog(List<AdManageOperationLog> adOperationLogs);

    /**
     * TODO 广告打印日志（其他操作广告日志公共方法）
     * @param adOperationLogs 每个操作对应的所有日志
     * @return
     */
    public void printAdOtherOperationLog(List<AdManageOperationLog> adOperationLogs);

    Page<AdManageLogVo> pageList(OperationLogQo qo);

    Map<String, AdManageLogDayVo> dayList(OperationLogQo qo);

    /**
     * x
     * @return
     */
    List<String> getLogModules();

    /**
     * 广告日志打印（es收集）
     * @param adManageOperationLog
     */
    void printAmazonAdOperationLog(AdManageOperationLog adManageOperationLog);

    /**
     * TODO 根据定时任务结果Vo生成日志对象(后续需重构代码)
     * @param scheduleTaskVo
     * @return
     */
    void getScheduleTaskFinishedLog(ScheduleTaskFinishedVo scheduleTaskVo);

    /**
     * 自动化规则写入自动化日志
     * @param message
     * @return
     */
    void getAutoRuleTaskFinishedLog(AdvertiseAutoRuleExecuteRecord message);

    void manualSyncOperationLog(JdbcTemplate jdbcTemplate, int batchSize);


    /**
     * TODO 关键词投放增加日志
     * @param oldKeyword 数据库查询的对象（旧对象）
     * @param keyword 待更新的对象
     * @return AdManageOperationLog
     */
    public AdManageOperationLog getNekeywordsLog(AmazonAdNeKeyword oldKeyword, AmazonAdNeKeyword keyword);

    //商品投放添加日志
    AdManageOperationLog getNeTargetsLog(AmazonAdNeTargeting oldTargeting, AmazonAdNeTargeting targeting);

}
