package com.meiyunji.sponsored.service.cpc.dao.mapper;

import com.meiyunji.sponsored.service.cpc.vo.pricing.AmazonAdPricingTargetPageVo;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class AmazonAdPricingTargetPageVoMapper implements RowMapper<AmazonAdPricingTargetPageVo> {

    @Override
    public AmazonAdPricingTargetPageVo mapRow(ResultSet rs, int i) throws SQLException {
        AmazonAdPricingTargetPageVo vo = new AmazonAdPricingTargetPageVo();
        vo.setId(rs.getLong("id"));
        vo.setShopId(rs.getInt("shop_id"));
        vo.setState(rs.getInt("state"));
        vo.setMarketplaceId(rs.getString("marketplace_id"));
        vo.setCampaignId(rs.getString("campaign_id"));
        vo.setGroupId(rs.getString("ad_group_id"));
        vo.setTargetName(rs.getString("target_name"));
        vo.setTargetId(rs.getString("target_id"));
        vo.setTargetState(rs.getString("target_state"));
        vo.setTargetType(rs.getString("target_type"));
        vo.setBiddingOriPrice(rs.getDouble("bidding_ori_price"));
        vo.setCreateId(rs.getInt("create_id"));
        vo.setCreateTime(rs.getTimestamp("create_time"));
        vo.setUpdateTime(rs.getTimestamp("update_time"));
        vo.setUpdateId(rs.getInt("update_id"));
        vo.setMatchType(rs.getString("match_type"));
        return vo;
    }
}
