package com.meiyunji.sponsored.service.strategyTask.helper;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.strategy.enums.BudgetValueEnum;
import com.meiyunji.sponsored.service.strategy.enums.CampaignRuleBudgetTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.vo.CampaignRuleVo;
import com.meiyunji.sponsored.service.strategy.vo.SubmitStrategyVo;
import com.meiyunji.sponsored.service.strategyTask.enums.ChildrenItemType;
import com.meiyunji.sponsored.service.strategyTask.enums.SpecialBudgetValueEnum;
import com.meiyunji.sponsored.service.strategyTask.enums.TaskAdType;

import java.math.BigDecimal;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-15  15:49
 */
public class CampaignStrategyBudgetHelper {

    /**
     * 分时调预算校验逻辑
     * @param submitStrategyVo 提交的受控对象
     * @return
     */
    public static Boolean checkStatusBudgetValue(SubmitStrategyVo submitStrategyVo) {
        String marketplaceCN = AmznEndpoint.getByMarketplaceId(submitStrategyVo.getMarketplaceId()).getMarketplaceCN();
        SpecialBudgetValueEnum specialBudgetValueEnum = SpecialBudgetValueEnum.getSpecialBudgetValueEnum(submitStrategyVo.getMarketplaceId());
        String msg = null;
        //校验每日预算最大最小值
        if (specialBudgetValueEnum != null) {
            //判断是否为空
            if (submitStrategyVo.getOriginBudgetValue().doubleValue() > specialBudgetValueEnum.getMaxValue()) {
                msg = String.format(Constants.STATUS_BUDGET_MAX_MSG, marketplaceCN,specialBudgetValueEnum.getMaxValue());
                return false;
            }
            if (TaskAdType.SP.name().equalsIgnoreCase(submitStrategyVo.getAdType()) || TaskAdType.SD.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                if (submitStrategyVo.getOriginBudgetValue().doubleValue() < specialBudgetValueEnum.getSpAndSdminValue()) {
                    msg = String.format(Constants.SP_SD_STATUS_BUDGET_MIN_MSG, marketplaceCN, specialBudgetValueEnum.getSpAndSdminValue());
                    return false;
                }
            } else {
                if (submitStrategyVo.getOriginBudgetValue().doubleValue() < specialBudgetValueEnum.getSbMinValue()) {
                    msg = String.format(Constants.SB_STATUS_BUDGET_MIN_MSG, marketplaceCN, specialBudgetValueEnum.getSbMinValue());
                    return false;
                }
            }
        } else {
            Double maxValue = BudgetValueEnum.getMaxValue(submitStrategyVo.getMarketplaceId());
            Double minValue = BudgetValueEnum.getMinValue(submitStrategyVo.getMarketplaceId());
            //判断是否为空
            if (maxValue != null) {
                if (submitStrategyVo.getOriginBudgetValue().doubleValue() > maxValue) {
                    msg = String.format(Constants.STATUS_BUDGET_MAX_MSG, marketplaceCN, maxValue);
                    return false;
                }
            }
            if (minValue != null) {
                if (submitStrategyVo.getOriginBudgetValue().doubleValue() < minValue) {
                    msg = String.format(Constants.STATUS_BUDGET_Min_MSG, marketplaceCN, minValue);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 分时调预算校验逻辑
     * @param vo 模板对象
     * @param submitStrategyVo 提交的受控对象
     * @param budgetValue 广告活动预算
     * @param campaignRuleVo 分时策略规则
     * @return
     */
    public static Boolean checkScheduleBudgetValue(AdvertiseStrategyTemplate vo, SubmitStrategyVo submitStrategyVo, BigDecimal budgetValue, CampaignRuleVo campaignRuleVo) {
        SpecialBudgetValueEnum specialBudgetValueEnum = SpecialBudgetValueEnum.getSpecialBudgetValueEnum(submitStrategyVo.getMarketplaceId());
        String msg = null;
        if (ChildrenItemType.proportion.name().equals(vo.getChildrenItemType())) {
            if (campaignRuleVo.getStartTimeSite() == 0) {
                if (specialBudgetValueEnum != null) {
                    if (TaskAdType.SB.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                        if (budgetValue.doubleValue() < specialBudgetValueEnum.getSbMinValue()) {
                            msg = String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, specialBudgetValueEnum.getSbMinValue());
                            return false;
                        }
                    } else {
                        if (budgetValue.doubleValue() < specialBudgetValueEnum.getSpAndSdminValue()) {
                            msg = String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, specialBudgetValueEnum.getSpAndSdminValue());
                            return false;
                        }
                    }
                } else {
                    Double minValue = BudgetValueEnum.getMinValue(submitStrategyVo.getMarketplaceId());
                    if (minValue != null) {
                        if (budgetValue.doubleValue() < minValue) {
                            msg = String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, minValue);
                            return false;
                        }
                    }
                }
            }
        } else {
            //校验百分比调整值
            if (CampaignRuleBudgetTypeEnum.PERCENTAGE_INCREASE.getCode().equals(campaignRuleVo.getBudgetType())
                    || CampaignRuleBudgetTypeEnum.PERCENTAGE_DECREASE.getCode().equals(campaignRuleVo.getBudgetType())) {
                if (campaignRuleVo.getBudgetValue().compareTo(BigDecimal.ONE) < 0 || campaignRuleVo.getBudgetValue().compareTo(new BigDecimal("100")) > 0) {
                    msg = com.meiyunji.sponsored.service.cpc.util.Constants.PERCENTAGE_CHECK_PROMPT;
                    return false;
                }
            }
            if (specialBudgetValueEnum != null) {
                if (TaskAdType.SB.name().equalsIgnoreCase(submitStrategyVo.getAdType())) {
                    if (budgetValue.doubleValue() < specialBudgetValueEnum.getSbMinValue()) {
                        msg =  String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, specialBudgetValueEnum.getSbMinValue());
                        return false;
                    }
                } else {
                    if (budgetValue.doubleValue() < specialBudgetValueEnum.getSpAndSdminValue()) {
                        msg =  String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, specialBudgetValueEnum.getSpAndSdminValue());
                        return false;
                    }
                }
                if (budgetValue.doubleValue() > specialBudgetValueEnum.getMaxValue()) {
                    msg =  String.format(Constants.SCHEDULE_BUDGET_MAX_MSG, specialBudgetValueEnum.getMaxValue());
                    return false;
                }
            } else {
                Double minValue = BudgetValueEnum.getMinValue(submitStrategyVo.getMarketplaceId());
                if (minValue != null) {
                    if (budgetValue.doubleValue() < minValue) {
                        msg =  String.format(Constants.SCHEDULE_BUDGET_MIN_MSG, minValue);
                        return false;
                    }
                }
                Double maxValue = BudgetValueEnum.getMaxValue(submitStrategyVo.getMarketplaceId());
                if (maxValue != null) {
                    if (budgetValue.doubleValue() > maxValue) {
                        msg =  String.format(Constants.SCHEDULE_BUDGET_MAX_MSG, maxValue);
                        return false;
                    }
                }
            }
        }
        return true;
    }


    /**
     * 根据调整类型计算调整后的值
     * @param budgetType    调整类型
     * @param budgetValue   原始预算值
     * @param AdjustedValue  调整值
     * @return
     */
    public static BigDecimal computeNewBudgetValue(Integer budgetType, BigDecimal budgetValue, BigDecimal AdjustedValue) {
        BigDecimal newBudgetValue = BigDecimal.ZERO;
        if (CampaignRuleBudgetTypeEnum.FIXED_VALUE.getCode().equals(budgetType)) {
            newBudgetValue = AdjustedValue;
        } else if (CampaignRuleBudgetTypeEnum.PERCENTAGE_INCREASE.getCode().equals(budgetType)) {
            newBudgetValue = MathUtil.add(budgetValue, MathUtil.multiply(MathUtil.multiply(budgetValue, AdjustedValue), new BigDecimal("0.01"))).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if (CampaignRuleBudgetTypeEnum.PERCENTAGE_DECREASE.getCode().equals(budgetType)) {
            newBudgetValue = MathUtil.subtract(budgetValue, MathUtil.multiply(MathUtil.multiply(budgetValue, AdjustedValue), new BigDecimal("0.01"))).setScale(2, BigDecimal.ROUND_HALF_UP);
        } else if (CampaignRuleBudgetTypeEnum.FIXED_VALUE_INCREASE.getCode().equals(budgetType)) {
            newBudgetValue = MathUtil.add(budgetValue, AdjustedValue);
        } else if (CampaignRuleBudgetTypeEnum.FIXED_VALUE_DECREASE.getCode().equals(budgetType)) {
            newBudgetValue = MathUtil.subtract(budgetValue, AdjustedValue);
        }
        return newBudgetValue;
    }
}
