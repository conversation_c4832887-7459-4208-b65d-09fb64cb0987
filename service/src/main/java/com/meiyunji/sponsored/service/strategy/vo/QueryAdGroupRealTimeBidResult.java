package com.meiyunji.sponsored.service.strategy.vo;

import com.meiyunji.sponsored.service.strategyTask.vo.QueryAdGroupRealTimeBidStateResult;
import lombok.Data;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-02-21  17:16
 */
@Data
public class QueryAdGroupRealTimeBidResult {
    private Long preTaskId;
    private List<QueryAdGroupRealTimeBidStateResult> queryAdGroupRealTimeBidStateResultList;
}
