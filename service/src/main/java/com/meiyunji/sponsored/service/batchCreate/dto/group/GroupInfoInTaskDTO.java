package com.meiyunji.sponsored.service.batchCreate.dto.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.ion.Decimal;

/**
 * @author: ys
 * @date: 2023/11/23 15:57
 * @describe: SP批量信息任务下，产品视图，广告组基本信息DTO
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupInfoInTaskDTO {
    private Long id;

    private String name;

    private String marketplaceId;

    @ApiModelProperty("投放类型")
    private String type;

    private Long campaignId;

    private String campaignName;

    private String taskStatus;

    private Double defaultBid;

    private String errMsg;
}
