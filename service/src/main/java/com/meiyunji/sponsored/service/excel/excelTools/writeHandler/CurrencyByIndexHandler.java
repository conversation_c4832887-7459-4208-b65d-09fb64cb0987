package com.meiyunji.sponsored.service.excel.excelTools.writeHandler;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 冻结表列
 *
 * <AUTHOR>
 * @Date 2021/4/21 20:39
 */
public class CurrencyByIndexHandler implements CellWriteHandler {
    /**
     * 货币新样式的列索引
     */
    private List<Integer> indexList;
    /**
     * 复用单元格样式，workBook创建单元格样式超过64000 会报错
     * (k,v) k: 币种   v: 样式
     */
    private Map<String, CellStyle> styleMap = new HashMap<>();

    public CurrencyByIndexHandler() {
        indexList = Collections.emptyList();
    }

    public CurrencyByIndexHandler(List<Integer> indexList) {
        this.indexList = indexList;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, CellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<CellData> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (!isHead && CollectionUtils.isNotEmpty(indexList)) {
            Integer cellColumnIndex = cell.getColumnIndex();
            if (indexList.contains(cellColumnIndex)) {
                String val = cell.getStringCellValue();
                if (StringUtil.isNotEmpty(val)) {
                    CurrencyUnitEnum unitEnum = CurrencyUnitEnum.getStartsWith(val);
                    if (unitEnum != null) {
                        String valStr = val.trim().substring(unitEnum.getCurrency().length());
                        if (StringUtil.isNumber(valStr) && StringUtils.isNotBlank(valStr)) {
                            CellStyle currencyStyle = styleMap.get(unitEnum.getCurrency());
                            if (currencyStyle == null) {
                                Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                                DataFormat format = workbook.createDataFormat();
                                currencyStyle = workbook.createCellStyle();
                                currencyStyle.setDataFormat(format.getFormat("[$" + unitEnum.getUnit() + "]#,##0.00"));
                                currencyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                                styleMap.put(unitEnum.getCurrency(), currencyStyle);
                            }
                            cell.setCellStyle(currencyStyle);
                            cell.setCellValue(Double.parseDouble(valStr));
                        }
                    }
                }
            }
        }
    }
}
