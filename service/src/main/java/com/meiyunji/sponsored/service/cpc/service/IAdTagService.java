package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.cpc.vo.MakeAdTagBatchVo;
import com.meiyunji.sponsored.service.cpc.vo.MakeAdTagVo;

import java.util.List;

public interface IAdTagService {


    /**
     * 删除标签和已标记的数据
     * @param puid
     * @param type
     * @param adTagName
     * @return
     */
    Result<List<AdTag>> queryAdTagList(int puid, String type, String adTagName);

    /**
     * 删除标签和已标记的数据
     * @param puid
     * @param uid
     * @param loginIp
     * @param adTagId
     * @return
     */
    Result deleteByAdTagId(int puid, int uid, String loginIp, Long adTagId);

    /**
     * 创建标签
     * @param puid
     * @param uid
     * @param loginIp
     * @param adTagVo
     * @return
     */
    Result createAdTag(int puid, int uid, String loginIp, AdTagVo adTagVo);

    /**
     * 更行标签
     * @param puid
     * @param uid
     * @param loginIp
     * @param adTagVo
     * @return
     */
    Result updateAdTag(int puid, int uid, String loginIp, AdTagVo adTagVo);

    /**
     * 获取标签列表
     * @param puid
     * @param type
     * @return
     */
    Result listAdTag(int puid, String type, List<String> ids);

    Result<List<AdMarkupTagVo>> makeAdTag(int puid, int uid, String loginIp, MakeAdTagVo makeAdTagVo);

    Result<List<AdMarkupTagVo>> makeAdTagMultiShop(int puid, int uid, MakeAdTagBatchVo makeAdTagBatchVo);

    Result clearMakeAdTag(int puid, int uid, String loginIp, MakeAdTagVo makeAdTagVo);

    Result<List<AdMarkupTagVo>> clearMakeAdTagMultiShop(int puid, int uid, MakeAdTagBatchVo makeAdTagBatchVo);

    void initializeAdTag(Integer puid, Integer shopId);
}
