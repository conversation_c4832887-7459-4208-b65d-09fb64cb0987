package com.meiyunji.sponsored.service.kafka.consumer;

import com.meiyunji.sellfox.aadas.api.entry.TargetStatusPb;
import com.meiyunji.sellfox.aadas.api.enumeration.AdvertiseTaskTypePb;
import com.meiyunji.sellfox.aadas.api.enumeration.AmazonAdvertiseTypePb;
import com.meiyunji.sellfox.aadas.types.enumeration.CampaignPredicate;
import com.meiyunji.sellfox.aadas.types.enumeration.CampaignStrategy;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sellfox.aadas.types.schedule.CampaignAdjustment;
import com.meiyunji.sellfox.aadas.types.schedule.CampaignStatus;
import com.meiyunji.sellfox.aadas.api.entry.ScheduleTaskFinishedMessagePb;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdTargeting;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.kafka.enums.MessageItemType;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyStatusService;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import com.meiyunji.sponsored.service.util.GZipUtils;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * @author: wade
 * @date: 2022/1/13 10:33
 * @describe: 定时任务结果监听类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "spring.kafka.schedule.enabled", havingValue = "true")
public class ScheduleTaskFinishedMessageConsumer {

    @Autowired
    private IAdManageOperationLogService logService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private AadasApiFactory aadasApiFactory;
    @Autowired
    private IAdvertiseStrategyStatusService advertiseStrategyStatusService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;

    @KafkaListener(topics = "${kafka.consumers.sf-aadas-schedule-task.topic}", containerFactory = "scheduleKafkaContainerFactory")
    public void consumer(ConsumerRecord<?, byte[]> record) throws Exception {
        try {
            byte[] bytes = GZipUtils.decompressData(record.value());
            log.info("message: {}", new String(bytes));
            ScheduleTaskFinishedMessagePb.ScheduleTaskFinishedMessage message = ScheduleTaskFinishedMessagePb.ScheduleTaskFinishedMessage.parseFrom(bytes);

            if (message != null) {
                ScheduleTaskFinishedVo vo = new ScheduleTaskFinishedVo();
                vo.setPuid(message.getPuid());
                vo.setShopId(message.getShopId());
                vo.setMarketplaceId(message.getMarketplaceId());
                vo.setTaskId(message.getTaskId());
                vo.setItemId(message.getItemId());
                vo.setItemType(message.getItemType().name());
                vo.setTaskId(message.getTaskId());
                vo.setPolicyType(message.getPolicyType().name());
                vo.setExecutorType(message.getExecutorType().name());
                vo.setModifiedValue(message.getModifiedValue());
                vo.setOriginValue(message.getOriginValue());
                if (StringUtils.isNotBlank(message.getOriginState())) {
                    vo.setOriginState(message.getOriginState());
                }
                if (StringUtils.isNotBlank(message.getState())) {
                    vo.setState(message.getState());
                }
                vo.setCampaignId(message.getCampaignId());
                vo.setAdGroupId(message.getAdGroupId());
                vo.setCode(message.getCode().name());
                vo.setErrInfo(message.getErrInfo());

                TargetStatusPb.CampaignPlacementStatus placement = message.getPlacement();
                CampaignStatus newPlacement = turnPoToVo(placement);
                vo.setPlacement(newPlacement);

                TargetStatusPb.CampaignPlacementStatus originPlacement = message.getOriginPlacement();
                CampaignStatus oldPlacement = turnPoToVo(originPlacement);
                vo.setOriginPlacement(oldPlacement);

                Marketplace marketplace = Marketplace.fromId(message.getMarketplaceId());
                ZoneId zoneId = marketplace.getTimeZone().toZoneId();

                ZoneId defaultZoneId = TimeZone.getTimeZone("Asia/Shanghai").toZoneId();
                if (message.hasExpectedExecTime()) {
                    vo.setExpectedExecTime(LocalDateTimeUtil.fromPb(message.getExpectedExecTime(), defaultZoneId));
                    vo.setExpectedExecTimeSite(LocalDateTimeUtil.fromPb(message.getExpectedExecTime(), zoneId));
                }
                if (message.hasActualExecTime()) {
                    vo.setActualExecTime(LocalDateTimeUtil.fromPb(message.getActualExecTime(), defaultZoneId));
                    vo.setActualExecTimeSite(LocalDateTimeUtil.fromPb(message.getActualExecTime(), zoneId));
                }
                //version == 2 =>自动化日志写入,存入es
                logService.getScheduleTaskFinishedLog(vo);
                //如果是归档的错误,则删除任务
                if ("Archived entity cannot be modified".equalsIgnoreCase(StringUtils.trim(message.getErrInfo())) ||
                        "Parent entity is archived".equalsIgnoreCase(StringUtils.trim(message.getErrInfo()))) {
                    aadasApiFactory.getStrategyApi(TaskTimeType.valueOf(message.getItemType().name()))
                            .removeSchedule(message.getPuid(), message.getShopId(), message.getTaskId(),
                                    false);

                    //新版分时调价归档记录删除
                    try {
                        advertiseStrategyStatusService.removeArchiveRecord(message.getPuid(),
                                message.getShopId(), message.getTaskId(), MessageItemType.getDesc(message.getItemType().name()));
                    } catch (Exception e) {
                        log.error("Remove v2 task with an error.", e);
                    }
                }
                //sb No response 可能是因为已经归档造成的,查询一下状态,确定归档则移除
                if ("No response.".equalsIgnoreCase(StringUtils.trim(message.getErrInfo())) &&
                        message.getType() == AmazonAdvertiseTypePb.AmazonAdvertiseType.SB) {
                    int puid = message.getPuid();
                    int shopId = message.getShopId();
                    boolean needRemove =  false;

                    AmazonAdCampaignAll campaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, shopId, message.getMarketplaceId(),
                            message.getCampaignId(), "sb");
                    //父对象 活动归档了
                    if (campaignAll != null && campaignAll.getState().equalsIgnoreCase("archived")) {
                        needRemove = true;
                    } else {
                        //keyword
                        AdvertiseTaskTypePb.AdvertiseTaskType itemType = message.getItemType();
                        if (itemType == AdvertiseTaskTypePb.AdvertiseTaskType.keywordBid) {
                            AmazonSbAdKeyword sbAdKeyword =
                                    amazonSbAdKeywordDao.getByKeywordId(puid, shopId, message.getItemId());
                            if (sbAdKeyword != null && sbAdKeyword.getState().equalsIgnoreCase("archived")) {
                                needRemove = true;
                            }
                        }
                        //target
                        if (itemType == AdvertiseTaskTypePb.AdvertiseTaskType.targetBid) {
                            AmazonSbAdTargeting sbAdTargeting =
                                    amazonSbAdTargetingDao.getByTargetId(puid, shopId, message.getItemId());
                            if (sbAdTargeting != null && sbAdTargeting.getState().equalsIgnoreCase("archived")) {
                                needRemove = true;
                            }
                        }
                    }
                    //父对象或对象归档都移除任务
                    if (needRemove) {
                        aadasApiFactory.getStrategyApi(TaskTimeType.valueOf(message.getItemType().name()))
                                .removeSchedule(message.getPuid(), message.getShopId(), message.getTaskId(),
                                        false);
                        try {
                            //新版分时调价归档记录删除
                            advertiseStrategyStatusService.removeArchiveRecord(message.getPuid(),
                                    message.getShopId(), message.getTaskId(), MessageItemType.getDesc(message.getItemType().name()));
                        } catch (Exception e) {
                            log.error("Remove v2 task with an error.", e);
                        }
                    }

                }

            }
        } catch (Exception e) {
            log.info("aadas-schedule-task-finished message decompress fail", e);
            throw e;
        }
    }

    private CampaignStatus turnPoToVo(TargetStatusPb.CampaignPlacementStatus placement) {
        if (placement != null) {
            TargetStatusPb.CampaignPlacementStatus.Strategy strategy = placement.getStrategy();
            List<TargetStatusPb.CampaignPlacementStatus.Adjustment> adjustmentsList = placement.getAdjustmentsList();

            CampaignStatus status = new CampaignStatus();
            status.setStrategy(CampaignStrategy.valueOf(strategy.name()));

            List<CampaignAdjustment> adjustments = new ArrayList<>();
            CampaignAdjustment adjust;
            for (TargetStatusPb.CampaignPlacementStatus.Adjustment adjustment : adjustmentsList) {
                adjust = new CampaignAdjustment();
                adjust.setPredicate(CampaignPredicate.valueOf(adjustment.getPredicate().name()));
                adjust.setPercentage(adjustment.getPercentage());
                adjustments.add(adjust);
            }
            status.setAdjustments(adjustments);
            return status;
        } else {
            return null;
        }
    }
}
