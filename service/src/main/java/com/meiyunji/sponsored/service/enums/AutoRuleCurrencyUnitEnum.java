package com.meiyunji.sponsored.service.enums;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2023-08-31  15:47
 */
public enum AutoRuleCurrencyUnitEnum {
    CNY("CNY", "￥"),
    HKD("HKD", "HK$"),
    USD("USD", "US$"),
    CAD("CAD", "CA$"),
    MXN("MXN", "MX$"),
    GBP("GBP", "£"),
    EUR("EUR", "€"),
    INR("INR", "₲"),
    JPY("JPY", "JP￥"),    //日本
    AUD("AUD", "AU$"),   //澳大利亚
    AED("AED", "د.إ"),
    TRY("TRY", "₺"),
    SGD("SGD", "S$"),   //新加坡
    BRL("BRL", "R$"),   //巴西雷亚尔
    SAR("SAR", "﷼"),
    SEK("SEK", "kr"),
    PLN("PLN", "zł");

    private String currency;
    private String unit;

    AutoRuleCurrencyUnitEnum(String currency,String unit){
        this.currency = currency;
        this.unit = unit;
    }

    public String getCurrency() {
        return currency;
    }

    public String getUnit() {
        return unit;
    }

    public static AutoRuleCurrencyUnitEnum getByCurrency(String currency){
        for (AutoRuleCurrencyUnitEnum unitEnum : AutoRuleCurrencyUnitEnum.values()) {
            if (unitEnum.currency.equalsIgnoreCase(currency)) {
                return unitEnum;
            }
        }
        return null;
    }


    public static AutoRuleCurrencyUnitEnum getStartsWith(Object value) {
        if (value != null) {
            String s = String.valueOf(value).trim();
            for (AutoRuleCurrencyUnitEnum unitEnum : AutoRuleCurrencyUnitEnum.values()) {
                if (s.startsWith(unitEnum.currency)) {
                    return unitEnum;
                }
            }
        }
        return null;
    }
}
