package com.meiyunji.sponsored.service.cpc.vo;

import com.amazon.advertising.sd.mode.creative.CreativeProperties;
import com.meiyunji.sponsored.common.enums.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Created by xp on 2021/4/14.
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SdCreativePageParam {

    @ApiModelProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;
    @ApiModelProperty(value = "puid",required = true)
    private Integer puid;
    @ApiModelProperty("创意id")
    private String creativeId;
    @ApiModelProperty("创意类型(只有IMAGE,没有VIDEO)")
    private String creativeType;
    @ApiModelProperty("创意属性包含标题，品牌logo，以及自定义图片")
    private CreativeProperties properties;
    @ApiModelProperty("状态 APPROVED, PENDING_REVIEW, REJECTED")
    private String state;
    @ApiModelProperty("广告组Id")
    private String adGroupId;
    @ApiModelProperty("广告组名称")
    private String adGroupName;
    @ApiModelProperty("查询字段")
    private String searchField;
    @ApiModelProperty("查询字段值")
    private String searchValue;
    @ApiModelProperty("开始时间")
    private String startDate;
    @ApiModelProperty("结束时间")
    private String endDate;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("类型升序 asc 降序 desc")
    private String orderType;
    @ApiModelProperty("广告类型: sp, sd, sb")
    private String type; // sp, sd, sb

    public enum SearchFieldEnum implements BaseEnum {

        NAME("name","name");
        ;

        private String field;
        private String column;
        ;

        SearchFieldEnum(String field, String column) {
            this.field = field;
            this.column = column;
        }

        public String getColumn() {
            return column;
        }

        public String getField() {
            return field;
        }


        @Override
        public String getCode() {
            return field;
        }

        @Override
        public String getDescription() {
            return column;
        }
    }
}
