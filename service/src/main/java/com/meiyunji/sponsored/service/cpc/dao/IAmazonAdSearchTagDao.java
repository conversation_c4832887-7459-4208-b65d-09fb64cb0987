package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSearchTag;

import java.util.List;

public interface IAmazonAdSearchTagDao extends IBaseShardingDao<AmazonAdSearchTag> {

    List<AmazonAdSearchTag> getListByUserIdAndType(Integer puid, Integer userId, String type);

    /**
     *  根据Id 查找模板
     */
    AmazonAdSearchTag getByIdAndPuidAndUid(Integer puid, Integer userId, Long id);
    /**
     * 子账号是否存在这个标签模板
     * @param puid
     * @param userId
     * @param type
     * @param name
     * @return
     */
    boolean isExistByName(Integer puid, Integer userId, String type, String name);

    /**
     * 删除一个子账号自定义模板
     * @param id
     * @param puid
     * @param userId
     * @return
     */
    int deleteById(Long id, Integer puid, Integer userId);

    /**
     * 子账号模板数量
     * @param puid
     * @param userId
     * @param type
     * @return
             */
    int count(Integer puid, Integer userId, String type);

    /**
     * 获取排序的最大值
     * @param puid
     * @param userId
     * @param type
     * @return
     */
    Integer getMaxColumnSort(Integer puid, Integer userId, String type);

    /**
     * 更新名称
     */
    int updateName(Integer puid, Long id, String name);

    /**
     * 更新属性字段
     */
    int updateFilterField(Integer puid, Long id, String name);

    /**
     * 更新固定字段标识
     */
    int updateFixedColumn(Integer puid, Long id, Boolean bool);

    /**
     * 批量更新排序
     */
    void batchUpdateSort(Integer puid, List<AmazonAdSearchTag> list);


    List<AmazonAdSearchTag> listById(Integer puid, Integer uid, List<Long> ids);


    int countFixedColumn(Integer puid, Integer userId, String type);

    /**
     * 是否存在历史数据
     */
    int countAll(Integer puid, Integer userId, String type, String date);

    /**
     * 批量插入
     */
    void batchInsert(Integer puid, List<AmazonAdSearchTag> searchTags);
}
