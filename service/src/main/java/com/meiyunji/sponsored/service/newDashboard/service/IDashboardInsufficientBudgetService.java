package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardInsufficientBudgetResponse;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardInsufficientBudgetReqVo;

import java.util.List;

/**
 * @author: liwei<PERSON>
 * @email: <EMAIL>
 * @date: 2024-04-15  10:42
 */
public interface IDashboardInsufficientBudgetService {
    /**
     * 预算不足列表页
     * @param reqVo
     * @return
     */
    DashboardInsufficientBudgetResponse.InsufficientBudgetVo.Page queryInsufficientBudgetData(DashboardInsufficientBudgetReqVo reqVo);

    /**
     * 预算不足列表页导出
     * @param reqVo
     * @return
     */
    List<String> exportInsufficientBudgetData(DashboardInsufficientBudgetReqVo reqVo);
}
