package com.meiyunji.sponsored.service.wordFrequency.qo;

import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-07  14:23
 */
@Data
@ApiModel
public class QueryWordTopQo extends CpcQueryWordDto {
    @ApiModelProperty(value = "top数量")
    private Integer top;

    @ApiModelProperty(value = "词根类型（0:显示高频词; 1:仅显示高频单词; 2:仅显示高频词组）")
    private String wordFrequencyType;
}
