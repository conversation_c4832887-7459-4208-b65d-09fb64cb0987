package com.meiyunji.sponsored.service.product.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.product.po.AsinInfo;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

public interface IAsinInfoDao extends IAdBaseDao<AsinInfo> {

    List<AsinInfo> getRatingByAsinList(HashSet<String> list, String marketplace);

    int  insertOrUpdate(List<AsinInfo> list, String marketplaceId);

    int  insertOrUpdateIsNeedSync(List<String> asins, String marketplaceId);

    List<String> getNeedSyncAsin(String marketplaceId, Date updateTime, Integer limit);

    int updateIsNeedSync(String marketplaceId, List<String> asins);

    int updateValid(String marketplaceId, List<String> asins);
}
