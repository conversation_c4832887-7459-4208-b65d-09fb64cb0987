package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.amazon.advertising.changesHistory.Events;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.sellerpartner.base.JSONUtil;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonChangeNonTypeDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonChangeNonType;
import com.meiyunji.sponsored.service.cpc.vo.AdCampaignChangeHistoryParam;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AmazonAdCampaign
 * <AUTHOR>
 */
@Repository

public class AmazonChangeNonTypeDaoImpl extends BaseShardingDaoImpl<AmazonChangeNonType> implements IAmazonChangeNonTypeDao {

    @Override
    public Page<AmazonChangeNonType> pageList(AdCampaignChangeHistoryParam param){
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .equalTo("shop_id", param.getShopId())
                .greaterThan("create_time",param.getStartDate());

        return page(param.getPuid(),param.getPageNo(), param.getPageSize(), null, builder.build());
    }

    @Override
    public void insertOnDuplicateKeyUpdate(ShopAuth shopAuth, List<Events> eventsList) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_change_non_type` (`puid`,`shop_id`,`marketplace_id`,`change_timestamp`,")
                .append("`change_type`,`previous_value`,`new_value`,`entity_type`,`entity_id`,`metadata`,");
        sql.append("`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for(Events e : eventsList){
            sql.append("(?,?,?,?,?,?,?,?,?,?");
            sql.append(",now(),now()),");
            argsList.add(shopAuth.getPuid());
            argsList.add(shopAuth.getId());
            argsList.add(shopAuth.getMarketplaceId());
            argsList.add(e.getTimestamp());
            argsList.add(e.getChangeType());
            argsList.add(e.getPreviousValue());
            argsList.add(e.getNewValue());
            argsList.add(e.getEntityType());
            argsList.add(e.getEntityId());
            argsList.add(e.getMetadata() == null ? "" : JSONUtil.objectToJson(e.getMetadata()));
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `previous_value`=values(previous_value),`new_value`=values(new_value),")
                .append("`update_time`=now()");
        getJdbcTemplate(shopAuth.getPuid()).update(sql.toString(), argsList.toArray());
    }

}