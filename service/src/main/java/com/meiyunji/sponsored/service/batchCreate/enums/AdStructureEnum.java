package com.meiyunji.sponsored.service.batchCreate.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-13  18:27
 */
public enum AdStructureEnum {

    SIMPLE_1("simple_1", AdStructureBeanId.SIMPLE_1, true, "简约版结构1", "新品推广低预算"),
    SIMPLE_2("simple_2", AdStructureBeanId.SIMPLE_2, true, "简约版结构2", "新品推广高预算"),
    SIMPLE_3("simple_3", AdStructureBeanId.SIMPLE_3, true, "简约版结构3", "成熟产品低预算"),
    COMPLEX_1("complex_1", AdStructureBeanId.COMPLEX_1, true, "精细化结构1", "成熟产品高预算"),
    COMPLEX_2("complex_2", AdStructureBeanId.COMPLEX_2, true, "精细化结构2", "主力产品大词高预算"),
    COMPLEX_3("complex_3", AdStructureBeanId.COMPLEX_3, true, "精细化结构3", "单词单组结构"),
    CUSTOM("custom", AdStructureBeanId.CUSTOM, true, "自定义结构", "自定义结构");

    private String code;

    private String beanId;

    private Boolean enable;

    private String desc;

    private String newDesc;

    public static Set<String> structureSet = Arrays.stream(AdStructureEnum.values()).filter(AdStructureEnum::getEnable).map(AdStructureEnum::getCode).collect(Collectors.toSet());

    public static Map<String, AdStructureEnum> structureMap = Arrays.stream(AdStructureEnum.values())
            .filter(AdStructureEnum::getEnable)
            .collect((Collectors.groupingBy(AdStructureEnum::getCode,
            Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    AdStructureEnum(String code, String beanId, Boolean enable, String desc, String newDesc) {
        this.code = code;
        this.beanId = beanId;
        this.enable = enable;
        this.desc = desc;
        this.newDesc = newDesc;
    }

    public String getCode() {
        return code;
    }

    public String getBeanId() {
        return beanId;
    }

    public Boolean getEnable() {
        return enable;
    }

    public String getDesc() {
        return desc;
    }

    public String getNewDesc() {
        return newDesc;
    }
}
