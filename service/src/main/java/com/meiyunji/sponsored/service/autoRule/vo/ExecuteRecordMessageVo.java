package com.meiyunji.sponsored.service.autoRule.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteRecordMessageVo implements Serializable{
    private int puid;
    private int shopId;
    private Long templateId;
    private String templateName;
    private Integer executeRecordCount;
    private String pushMessageType;
    private Integer createId;
    private String itemType;
    private String runTime;
    private String executeType;
}


