package com.meiyunji.sponsored.service.autoRule.vo.rule;

import lombok.Data;

@Data
public class AutoRuleVo {
    //近多少天
    private String day;
    //排除近多少天
    private String excludeDay;
    //数据指标:cost,clicks,adImpressions,budgetSurplus,adOrderNum等
    private String ruleIndex;
    //总和total,均值avg
    private String ruleStatisticalModeType;
    //计算符号:大于等于GE,小于等于LE
    private String ruleOperatorType;
    //(大于等于、小于等于)的值
    private String ruleValue;
    //(计算符为介于时)后序值，即介于ruleValue~afterRuleValue
    private String afterRuleValue;
    //排除按钮状态: excludeCheckStatus 0禁用 1启用 2取消
    private String excludeCheckStatus;
}
