package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 重复投放-右侧列表页汇总数据返回
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepeatTargetingTotalVo {

    private Integer puid;

    private Integer totalNum;

    /**
     * 广告曝光量
     */
    private Integer impressions = 0;

    /**
     * 广告点击量
     */
    private Integer clicks = 0;

    /**
     * 广告点击率 点击量/曝光量 * 100%
     */
    private BigDecimal clickRate = BigDecimal.ZERO;

    /**
     * 广告订单量
     */
    private Integer saleNum = 0;

    /**
     * 广告转化率 广告订单量/点击量 * 100%
     */
    private BigDecimal salesConversionRate = BigDecimal.ZERO;

    /**
     * 广告销售额
     */
    private BigDecimal totalSales = BigDecimal.ZERO;

    /**
     * 广告花费
     */
    private BigDecimal cost = BigDecimal.ZERO;

    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal cpc = BigDecimal.ZERO;

    /**
     * CPA 广告花费/广告订单量
     */
    private BigDecimal cpa = BigDecimal.ZERO;

    /**
     * ACOS 广告花费/销售额*100%
     */
    private BigDecimal acos = BigDecimal.ZERO;

    /**
     * ROAS 销售额/广告费(非百分比数据)
     */
    private BigDecimal roas = BigDecimal.ZERO;

    /**
     * 广告曝光量
     */
    private Integer compareImpressions;

    /**
     * 广告点击量
     */
    private Integer compareClicks;

    /**
     * 广告点击率 点击量/曝光量 * 100%
     */
    private BigDecimal compareClickRate;

    /**
     * 广告订单量
     */
    private Integer compareSaleNum;

    /**
     * 广告转化率 广告订单量/点击量 * 100%
     */
    private BigDecimal compareSalesConversionRate;

    /**
     * 广告销售额
     */
    private BigDecimal compareTotalSales;

    /**
     * 广告花费
     */
    private BigDecimal compareCost;

    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal compareCpc;

    /**
     * CPA 广告花费/广告订单量
     */
    private BigDecimal compareCpa;

    /**
     * ACOS 广告花费/销售额*100%
     */
    private BigDecimal compareAcos;

    /**
     * ROAS 销售额/广告费(非百分比数据)
     */
    private BigDecimal compareRoas;


    private String compareImpressionsRate;

    /**
     * 广告点击量对比增长率
     */
    private String compareClicksRate;

    /**
     * 广告点击率对比增长率 点击量/曝光量 * 100%
     */
    private String compareClickRateRate;

    /**
     * 广告订单量对比增长率
     */
    private String compareSaleNumRate;

    /**
     * 广告转化率对比增长率 广告订单量/点击量 * 100%
     */
    private String compareSalesConversionRateRate;

    /**
     * 广告销售额对比增长率
     */
    private String compareTotalSalesRate;

    /**
     * 广告花费对比增长率
     */
    private String compareCostRate;

    /**
     * CPC对比增长率 广告花费/广告点击量(非百分比数据)
     */
    private String compareCpcRate;

    /**
     * CPA对比增长率 广告花费/广告订单量
     */
    private String compareCpaRate;

    /**
     * ACOS对比增长率 广告花费/销售额*100%
     */
    private String compareAcosRate;

    /**
     * ROAS对比增长率 销售额/广告费(非百分比数据)
     */
    private String compareRoasRate;

}
