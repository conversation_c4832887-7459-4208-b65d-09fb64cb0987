package com.meiyunji.sponsored.service.cpc.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.enums.SourceEnum;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@Data
@ApiModel
public class KeywordLibsListExcelVo {

    @ExcelProperty(value = "关键词")
    private String keywordText;
    @ExcelProperty(value = "标签")
    private String adTagName;
    @ExcelProperty(value = "ASIN标签")
    private String asinTagName;
    @ExcelProperty(value = "投放预览")
    private Integer targetNum;
    @ExcelProperty(value = "否定投放预览")
    private Integer negateTargetNum;
    @ExcelProperty(value = "ABA搜索词排名")
    private String displaySearchFrequencyRank;
    @ExcelProperty(value = "广告曝光量")
    private Long impressions;
    @ExcelProperty(value = "广告点击量")
    private Long clicks;
    @ExcelProperty(value = "广告点击率")
    private String clickRate;
    @ExcelProperty(value = "广告花费")
    private String cost;
    @ExcelProperty(value = "CPC")
    private String cpc;
    @ExcelProperty(value = "广告订单量")
    private Integer saleNum;
    @ExcelProperty(value = "广告销售额")
    private String totalSales;
    @ExcelProperty(value = "广告转化率")
    private String salesConversionRate;
    @ExcelProperty(value = "CPA")
    private String cpa;
    @ExcelProperty(value = "ACOS")
    private String acos;
    @ExcelProperty(value = "ROAS")
    private String roas;
    @ExcelProperty(value = "关键词监控")
    private Integer keywordMonitor;
    @ExcelProperty(value = "备注")
    private String remark;
    @ExcelProperty(value = "添加人")
    private String addUser;
    @ExcelProperty(value = "添加时间")
    private String createTime;
    @ExcelProperty(value = "来源")
    private String source;

    public void setSource(String source) {
        this.source = source;
        if(StringUtils.isNotBlank(source)){
            SourceEnum byCode = UCommonUtil.getByCode(source, SourceEnum.class);
            this.source = byCode.getDescription();
        }
    }
}
