package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleObjectParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import org.springframework.beans.PropertyValues;

import java.util.List;

/**
 * CpcQueryKeywordReport
 * <AUTHOR>
 */
public interface ICpcSbQueryKeywordReportDao extends IBaseShardingDao<CpcSbQueryKeywordReport> {
    /**
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<CpcSbQueryKeywordReport> list);

    /**
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 获取指标总数
     * @param puid
     * @param dto
     * @return
     */
    AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto);

    List<AdHomePerformancedto> getReportKeywordByDate(Integer puid, CpcQueryWordDto dto);

    Page pageManageExportList(Integer puid, SearchVo searchVo, Page page);

    List<AdHomePerformancedto> getReportKeywordByKeywordIdList(Integer puid, Integer shopId, String startDate, String endDate, List<String> keywordIdList, CpcQueryWordDto dto);


    /**
     * 详情页汇总
     * @param puid
     * @param dto
     * @return
     */
    CpcSbQueryKeywordReport sumDetailReport(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 详情页列表
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page);


    /**
     * 详情页图表
     * @param puid
     * @param dto
     * @return
     */
    List<CpcSbQueryKeywordReport> detailListChart(Integer puid, CpcQueryWordDetailDto dto);


    List<CpcSbQueryKeywordReport>  getNeedsLocalizationKeywords(Integer puid, Integer shopId, Integer limit);

    int updateQueryCnById(Integer puid, CpcSbQueryKeywordReport report);

    List<AdQueryAutoRuleVo> getAutoRuleDataList(Integer puid, AutoRuleQueryWordDto dto);

    List<Long> getAllIdByPuidLimit(Integer puid, int start, int shopLimit);

    void updateQueryId(Integer puid, Long startId, Long endId);

    List<CpcSbQueryKeywordReport> listSbByKeywordRule(AutoRuleObjectParam param, List<String> itemIdList);

    List<CpcSbQueryKeywordReport> listSbKeywordReport(Integer puid, Integer shopId, Integer page);

    /**
     * 获取最近一段时间内的搜索词数据
     * @param puid
     * @param shopId
     * @param timeRange
     * @return
     */
    List<CpcSbQueryKeywordReport> listSbKeywordReportByTimeRange(Integer puid, Integer shopId, Integer timeRange);

    /**
     * 获取搜索词列表所有的搜索词id
     * @param dto
     * @return
     */
    List<String> listQueryIdByQueryWordDto(CpcQueryWordDto dto);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<String> listQueryId(ProcessTaskParam param);

    List<SearchQueryTagParam> listAdGroupIdByQueryWordDto(CpcQueryWordDto dto);

    List<CpcSbQueryKeywordReport> getDetailList(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 获取计算全量词根的词
     */
    List<WordBo> listWordBo(Integer puid, Integer id, Integer page);

    /**
     * 获取计算增量词根的词
     */
    List<WordBo> listWordBoTimeRange(Integer puid, Integer id, Integer timeRange);

    List<CpcSbQueryKeywordReport> wordListByIds(int puid, Integer shopId, List<Long> ids);
}