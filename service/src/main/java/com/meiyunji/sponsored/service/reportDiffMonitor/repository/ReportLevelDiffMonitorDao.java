package com.meiyunji.sponsored.service.reportDiffMonitor.repository;

import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.service.reportDiffMonitor.repository.po.ReportLevelDiffMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报告数据层级差异监控dao
 *
 * @Author: hejh
 * @Date: 2024/5/30 11:09
 */
@Slf4j
@Repository
public class ReportLevelDiffMonitorDao extends AdBaseDaoImpl<ReportLevelDiffMonitor> {

    /**
     * 批量保存
     *
     * @param monitorList monitorList
     */
    public void saveList(List<ReportLevelDiffMonitor> monitorList) {
        Set<ReportLevelDiffMonitor> collect = monitorList.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (ad_type, level_type, count_date, experiment_json, metric_json, diff, diff_percent) values ");
        List<Object> argsList = new ArrayList<>(collect.size());
        for (ReportLevelDiffMonitor task : collect) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?),");
            argsList.add(task.getAdType());
            argsList.add(task.getLevelType());
            argsList.add(task.getCountDate());
            argsList.add(task.getExperimentJson());
            argsList.add(task.getMetricJson());
            argsList.add(task.getDiff());
            argsList.add(task.getDiffPercent());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

}
