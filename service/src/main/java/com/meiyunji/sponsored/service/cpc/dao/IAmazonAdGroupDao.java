package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.AdGroupAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdGroupTypeBO;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterGroupBaseDataBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.gps.vo.GpsAdGroup;
import com.meiyunji.sponsored.service.strategy.vo.AdStrategyGroupParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * AmazonAdGroup
 * <AUTHOR>
 */
public interface IAmazonAdGroupDao extends IBaseShardingDao<AmazonAdGroup> {
    /**
     * 检查是否重名
     * @param puid
     * @param shopId
     * @Param campainId
     * @param name
     * @return
     */
    boolean exist(Integer puid, Integer shopId,String campainId,String name);

    boolean exist(Integer puid, Integer shopId, String adGroupId);

    /**
     * 根据campainId获取广告组
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param campaignIds
     * @param orderField
     * @param orderValue
     * @param countDays
     * @return
     */
    List<AmazonAdGroup> getListByCampaignId(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String orderField, String orderValue, String countDays);

    /**
     * 获取广告组信息
     * @param puid
     * @param shopId
     * @param adGroupIds
     * @return
     */
    List<AmazonAdGroup> getListByAdGroupIds(Integer puid, Integer shopId, String adGroupIds);

    /**
     * 获取广告组信息
     * @param puid
     * @param shopId
     * @param adGroupIds
     * @return
     */
    List<AmazonAdGroup> getListByAdGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    List<AmazonAdGroup> getListByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds);

    List<AmazonAdGroup> getGroupsByCampaignIdGroupId(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds);

    /**
     * 获取广告组
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param adGroupId
     * @return
     */
    List<String> getAdGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId);


    /**
     * 获取广告组
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param adGroupId
     * @return
     */
    List<AmazonAdGroup> getAdGroupByIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId);

    /**
     * 跨店铺获取广告组
     * @param puid
     * @param shopIdList
     * @param adGroupId
     * @return
     */
    List<AmazonAdGroup> listByGroupIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupId);
    List<AmazonAdGroup> getAdGroupByIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupId);

    /**
     * 获取广告组
     * @param puid
     * @param adGroupId
     * @return
     */
    List<AmazonAdGroup> getAdGroupByIds(Integer puid,  List<String> adGroupId);

    /**
     * 批量新增
     * @param puid
     * @param list
     */
    void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdGroup> list);

    /**
     * 批量新增(包含adGroupType字段)
     * @param puid
     * @param list
     */
    void batchInsert(Integer puid, List<AmazonAdGroup> list);

    /**
     *批量更新
     * @param puid
     * @param list
     */
    void updateList(Integer puid, List<AmazonAdGroup> list);

    /**
     * 修改广告组的类型
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param targetingTypeIds
     * @param groupTypeTargeting
     */
    void updateAdGroupType(Integer puid, Integer shopId, String marketPlaceId, Set<String> targetingTypeIds, String groupTypeTargeting);

    /**
     * 指定活动是否有发布中的广告组
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    boolean existPublishing(Integer puid, Integer shopId, String campaignId);

    /**
     * @param updateId
     * @param shopId
     * @param marketPlaceId
     * @param adGroupId
     * @return
     */
    String getProfileId(int updateId, Integer shopId, String marketPlaceId, String adGroupId);

    /**
     * 修改状态
     * @param puid
     * @param shopId
     * @param adGroupId
     * @param state
     * @param updateId
     */
    void updateState(int puid, Integer shopId, String adGroupId, String state, int updateId);

    /**
     * 修改默认竞价
     * @param puid
     * @param shopId
     * @param adGroupId
     * @param defaultBid
     * @param updateId
     */
    void updateDefaultBid(int puid, Integer shopId, String adGroupId, Double defaultBid, int updateId);

    /**
     * 记录修改人信息
     * @param id
     * @param puid
     * @param updateId
     */
    void updateInfo(Integer id, Integer puid, Integer updateId);

    /**
     * 获取所有草稿状态的广告组走队列创建
     *
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @return
     */
    List<Long> getIdByDraft(Integer puid,Integer shopId, String marketPlaceId);

    /**
     * 修改发布状态
     * @param puid
     * @param id
     * @param publishState
     * @param msg
     */
    void updatePublishState(Integer puid,Integer id, String publishState, String msg);

    /**
     * 将发布成功获得的广告组id插入数据
     * @param id
     * @param adGroupId
     * @param uniqueKey
     */
    void updateAdGroupId(Integer puid,Integer id, String adGroupId, String uniqueKey);

    /**
     *  @param id
     * @param msg
     */
    void updateMsg(Integer puid,Integer id, String msg);

    /**
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @param adGroupId
     * @return
     */
    AmazonAdGroup getByAdGroupId(int puid, Integer shopId, String marketPlaceId, String adGroupId);

    /**
     * @param puid
     * @param shopId
     * @param adGroupId
     * @return
     */
    AmazonAdGroup getByAdGroupId(int puid, Integer shopId, String adGroupId);

    /**
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param name
     * @return
     */
    List<String> getCampaignIds(int puid, Integer shopId, String marketplaceId, String name);

    /**
     * 清空报错信息
     * @param puid
     * @param updateId
     * @param dxmGroupId
     * @return
     */
    int updateMsgNull(int puid, Integer updateId, Integer dxmGroupId);

    /**
     * 获取广告组名称
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param adGroupId
     * @return
     */
    String getNameByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String adGroupId);

    /**
     * 获取广告组类型
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param adGroupId
     * @return
     */
    String getGroupTypeByAdGroupId(Integer puid, Integer shopId, String marketplaceId, String adGroupId);

    /**
     * 获取广告组信息用于创建定时任务
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page);

    /**
     * 获取广告组信息用下拉
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param campaignId
     * @return
     */
    List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId, String searchValue, Integer pageNo, Integer pageSize);

    Integer getAdGroupNamesTotalSize(int puid, Integer shopId, String campaignId, String searchValue);

    /**
     * 获取广告组信息用下拉
     * @param puid
     * @param dto
     * @return
     */
    List<Map<String, Object>> getAdGroupNames(Integer puid, GetNamesDto dto);

    AmazonAdGroup getById(Integer dxmGroupId, int puid, List<Integer> shopList);

    /**
     * 新版列表页查询
     * @param puid
     * @param param
     * @return
     */
    Page<AmazonAdGroup> pageList(Integer puid, GroupPageParam param);

    List<AmazonAdGroup> listByCondition(Integer puid, GroupPageParam param);

    /**
     * 查指定广告活动下、某些类型的广告组列表
     * @param puid
     * @param shopId
     * @param campaignId
     * @param adGroupTypes
     * @return
     */
    List<AmazonAdGroup> getListByCampaignId(int puid, Integer shopId, String campaignId, List<String> adGroupTypes);

    /**
     * 查指定类型的广告组列表
     * @param puid
     * @param shopId
     * @param adGroupTypes
     * @return
     */
    List<AmazonAdGroup> getListByType(int puid, Integer shopId, List<String> adGroupTypes);

    List<AmazonAdGroup> listNoAdGroupType(Integer puid, Integer shopId, String campaignId, String groupId);

    List<AmazonAdGroup> listNoAdGroupType(Integer puid, Integer shopId, Set<String> campaignIdSet, Set<String> groupIdSet);

    List<String> getGroupIdsByGroupType(Integer puid, Integer shopId, String groupType);

    void batchUpdateAdGrpupType(Integer puid,List<AmazonAdGroup> amazonAdGroups);


    String getGroupNameByGroupId(int puid, String groupId, Integer shopId);

    /**
     * 查询全部类型所有广告组
     * @param puid
     * @param param
     * @return
     */
    List<AmazonAdGroupDto> ListAllGroupByIdsAndDate(Integer puid, GroupPageParam param);

    /**
     * 查询所有广告组
     * @param puid
     * @param shopId
     * @param type
     * @param groupType
     * @return
     */
    List<AdGroupOptionVo> getAllGroupsByType(Integer puid, Integer shopId, List<String> type ,String groupType,String name,String campaignIds);

    /**
     * 查询所有广告组
     * @param puid
     * @param shopId
     * @param type
     * @param groupType
     * @return
     */
    List<AdGroupOptionVo> getSpGroupsByType(Integer puid, Integer shopId, String campaignIds, String name);

    /**
     * 查询所有广告组
     * @param puid
     * @param shopId
     * @param type
     * @param groupType
     * @return
     */
    List<AdGroupOptionVo> getAllGroupsByType(Integer puid, Integer shopId, List<String> type ,String groupType,String name,String campaignIds,String groupIds, boolean queryAuto);


    /**
     * 多站点查询所有广告组
     * @param puid
     * @param shopId
     * @param type
     * @param groupType
     * @return
     */
    List<AdGroupOptionVo> getAllGroupsByType(Integer puid, String shopId, List<String> type ,String groupType,String name,String campaignIds,String groupIds);

    /**
     * 获取广告组信息用下拉
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param campaignId
     * @return
     */
    List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId);

    /**
     * 获取广告组名称
     * @param puid
     * @param shopId
     * @param groupIds
     * @return
     */
    List<Map<String, Object>>  getGroupNameByGroupIdsList(int puid, Integer shopId, List<String> groupIds);

    Page getPageList(Integer puid, GroupPageParam param, Page page);

    List<AmazonAdGroup> getList(Integer puid, GroupPageParam param);

    /**
     * 修改报告数据最新更新时间
     * @param puid
     * @param shopId
     * @param groupId
     * @param localDate
     */
    void updateDataUpdateTime(Integer puid, Integer shopId, String groupId, LocalDate localDate);

    void batchUpdateAmazonAdGroup(Integer puid, List<AmazonAdGroup> list, String type);

    List<Map<String, Object>> getGroupNameByShopIdsAndGroupIdsList(int puid, List<Integer> shopIds, List<String> groupIds);

    List<String> getArchivedItems(Integer puid, Integer shopId);

    List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt);

    List<String> getAdGroupIdsByGroup(int puid, GroupPageParam param);

    /**
     * 关联关键词库查询
     * @param puid
     * @param shopIds
     * @param groupIds
     * @return
     */
    List<AmazonAdGroup> getListByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> groupIds);

    List<AmazonAdGroup> getNameByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> groupIds,
                                                    String groupName);

    List<AmazonAdGroup> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds);

    /**
     * 自动化规则广告组分页查询
     *
     * @param param
     * @return
     */
    Page<AmazonAdGroup> listByAutoRule(AdGroupAutoRuleParam param,List<String> itemIds,List<String> similarRuleItemIdList);

    /**
     * 自动化规则受控对象查询
     *
     * @param puid
     * @param shopId
     * @param campaignIds
     * @param adGroupIds
     * @param state
     * @param searchValue
     * @return
     */
    List<AmazonAdGroup> autoRuleAdGroup(int puid,int shopId,List<String> campaignIds,List<String>adGroupIds,String state,String searchValue,List<String> servingStatus);

    /**
     * 自动化规则受控对象查询
     *
     * @param puid
     * @param shopId
     * @return
     */
    Page<AmazonAdGroup> queryGroup(Integer puid, Integer shopId, List<String> types, String groupName,List<String> campaignIds, List<String> adGroupTypes,Integer pageSize, Integer pageNo, List<String> statusList);

    /**
     * 自动化规则广告组查询
     *
     * @param param
     * @return
     */
    List<AmazonAdGroup> queryGroupList(AllUpdateAutoRuleParam param);

    List<AmazonAdGroup> listByPuidAndCampaignIds(int puid, List<String> campaignIds);

    /**
     * @param puid
     * @param shopId
     * @param name
     * @return
     */
    AmazonAdGroup getByAdGroupName(int puid, Integer shopId, String campaignId,String name);

    List<String> getAdGroupByIds(Integer puid, Integer shopId);

    List<AmazonAdGroup> listByPuidAndCampaignId(int puid,int shopId ,String campaignId);

    List<AmazonAdGroupTypeBO> getTAdGroupTypeByAdGroupIdList(Integer puid, Integer shopId, List<String> adGroupIdList);

    /**
     * 策略同步修改
     *
     * @param message
     * @return
     */
    void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message);

    void insertList4BatchCreate(Integer puid, List<AmazonAdGroup> successAdGroupList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    Page<AmazonAdGroup> getPageListByStrategy(AdStrategyGroupParam param);

    List<String> queryAdGroupIdList(ControlledObjectParam param);

    void updatePricing(Integer puid, Integer shopId, String adGroupId, Integer isPricing, Integer pricingState, int updateId);

    Page<GroupInfoPageVo> getAllGroupPage(Integer puid, GroupPageParam param);

    List<String> getGroupIdListByParamAndIds(Integer puid, GroupPageParam param, List<String> adGroupIdList);

    List<AllGroupOrderBo> getGroupIdAndOrderFieldList(Integer puid, GroupPageParam param, List<String> adGroupIdList, String orderField);

    List<GroupInfoPageVo> getGroupPageVoListByGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList);

    List<AllGroupOrderBo> getGroupIdAndIndexList(Integer puid, GroupPageParam param);

    AmazonAdGroup getByCampaignIdAndAdGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId);
    List<AmazonAdGroup> getInfoByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId);

    List<String> queryAdGroupIdList(ProcessTaskParam param);

    List<String> queryAutoRuleAdGroupIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param);

    List<GpsAdGroup> getGpsAdGroupByName(int puid, List<String> campaignIdList, List<String> nameList);

    Page<MultiShopGroupListVo> getMultiShopGroupList(Integer puid, MultiShopGroupListParam param);

    List<MultiShopGroupListVo> getByShopGroupIdPair(Integer puid, List<MultiShopGroupListParam> paramList);

    List<AmazonAdGroup> listInfoByGroupInfoAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> adGroupIds);

    List<String> queryArchivedByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList);

    /**
     * 根据活动id获取广告组投放类型
     */
    List<String> getAdGroupTypeByCampaignId(Integer puid, Integer shopId, String campaignId);

    /**
     * 根据活动id获取广告组列表，限制个数
     */
    List<AmazonAdGroup> listByCampaignIdLimit(Integer puid, Integer shopId, String campaignId, Integer limit);

    List<AmazonAdGroup> getNameAndTypeAndStateByShopIdsAndGroupIds(Integer puid, List<Integer> shopIdList, List<String> spGroupIdList, String groupName);

    Collection<String> getByShopIdsAndCampaignIds(Integer puid, List<Integer> shopIdList, List<String> searchCampaignList);

    List<DownloadCenterGroupBaseDataBO> queryBaseData4DownloadByShopAdGroup(Integer puid, Integer shopId, List<String> adGroupIdList);
}