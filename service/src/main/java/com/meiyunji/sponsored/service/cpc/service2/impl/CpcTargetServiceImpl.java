package com.meiyunji.sponsored.service.cpc.service2.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleSubmitAdTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.ICpcTargetService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.ResolvedExpressionParseHelper;
import com.meiyunji.sponsored.service.cpc.vo.AdTargetInfoParam;
import com.meiyunji.sponsored.service.cpc.vo.AdTargetInfoVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.enums.AdTargetingTypeEnum;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: zhangzenghuang
 * @date: 2024/1/20 17:26
 * @describe: 广告投放 实现类
 */
@Service
@Slf4j
public class CpcTargetServiceImpl implements ICpcTargetService {

    @Resource
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Resource
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Resource
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Resource
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Resource
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;

    @Override
    public AdTargetInfoVo getAdTargetInfo(AdTargetInfoParam param) {
        AdTargetInfoVo vo = new AdTargetInfoVo();
        vo.setShopId(param.getShopId());
        vo.setTargetId(param.getTargetId());
        vo.setAdType(param.getAdType());
        vo.setTargetType(param.getTargetType());
        if (AutoRuleSubmitAdTypeEnum.SD.getCodeEx().equals(param.getAdType())) {
            // SD商品+受众
            buildSdTargetVo(param, vo);
        } else if (AutoRuleSubmitAdTypeEnum.SB.getCodeEx().equals(param.getAdType()) && AdTargetingTypeEnum.keyword.getTargetingType().equals(param.getTargetType())) {
            // SB关键词
            buildSbKeywordVo(param, vo);
        } else if (AutoRuleSubmitAdTypeEnum.SB.getCodeEx().equals(param.getAdType()) && AdTargetingTypeEnum.product.getTargetingType().equals(param.getTargetType())) {
            // SB商品
            buildSbTargetVo(param, vo);
        } else if (AutoRuleSubmitAdTypeEnum.SP.getCodeEx().equals(param.getAdType()) && AdTargetingTypeEnum.keyword.getTargetingType().equals(param.getTargetType())) {
            // SP关键词
            buildSpKeywordVo(param, vo);
        } else if (AutoRuleSubmitAdTypeEnum.SP.getCodeEx().equals(param.getAdType()) && (AdTargetingTypeEnum.targeting.getTargetingType().equals(param.getTargetType())) ||
                AdTargetingTypeEnum.auto.getTargetingType().equals(param.getTargetType())) {
            // SP商品+自动
            buildSpTargetVo(param, vo);
        }
        // 获取默认竞价
        if (AutoRuleSubmitAdTypeEnum.SP.getCodeEx().equals(param.getAdType())){
            AmazonAdGroup group = amazonAdGroupDao.getByAdGroupId(param.getPuid(), param.getShopId(), vo.getAdGroupId());
            if(vo.getBid() == null && group.getDefaultBid() != null){
                vo.setBid(String.valueOf(group.getDefaultBid()));
            }
        }
        return vo;
    }

    private void buildSdTargetVo(AdTargetInfoParam param, AdTargetInfoVo vo) {
        AmazonSdAdTargeting targeting = amazonSdAdTargetingDao.getbyTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
        vo.setMarketplaceId(targeting.getMarketplaceId());
        vo.setCampaignId(targeting.getCampaignId());
        vo.setAdGroupId(targeting.getAdGroupId());
        vo.setServingStatus(targeting.getServingStatus());
        vo.setState(targeting.getState());
        if(targeting.getBid() != null){
            vo.setBid(String.valueOf(targeting.getBid()));
        }
        vo.setTargetName(targeting.getTargetText());
        if(AdTargetingTypeEnum.audience.getTargetingType().equals(param.getTargetType())){
            // 受众投放类型映射
            if(SDTargetingTargetTypeEnum.VIEWS.getType().equals(targeting.getTargetType()) ||
                    SDTargetingTargetTypeEnum.PURCHASES.getType().equals(targeting.getTargetType())){
                vo.setProductTargetType(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode());
            }
            if(AudienceCategoryTypeEnum.IN_MARKET.getType().equals(targeting.getType()) ){
                vo.setProductTargetType(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode());
            }
            List<String> typeList = new ArrayList<>();
            typeList.add(AudienceCategoryTypeEnum.DEFAULT.getType());
            typeList.add(AudienceCategoryTypeEnum.INTEREST.getType());
            typeList.add(AudienceCategoryTypeEnum.LIFESTYLE.getType());
            typeList.add(AudienceCategoryTypeEnum.LIFE_EVENT.getType());
            typeList.add(SdTargetTypeNewEnum.AUDIENCE_SAME_AS.getCode());
            typeList.add(SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode());
            if(typeList.contains(targeting.getType())){
                vo.setProductTargetType(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
            }
        }else{
            // 商品投放类型
            vo.setProductTargetType(targeting.getType());
            // 商品定位填充商品信息
            if (TargetTypeEnum.asin.name().equals(targeting.getType()) && StringUtils.isNotBlank(targeting.getTargetText())) {
                buildAsinInfo(vo, targeting.getTargetText(), targeting.getMarketplaceId(), targeting.getPuid());
            }
            if (TargetTypeEnum.category.name().equals(targeting.getType())) {
                // 类目定位填充类目信息
                buildCategoryInfo(vo, targeting.getResolvedExpression());
            }
        }
    }

    private void buildAsinInfo(AdTargetInfoVo vo, String asin, String marketplaceId, Integer puid){
        vo.setAsin(asin);
        List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(puid, marketplaceId, CollectionUtil.toList(asin));
        if(CollectionUtil.isNotEmpty(listByAsins)){
            AsinImage image = listByAsins.get(0);
            vo.setTitle(image.getTitle());
            vo.setImageUrl(image.getImage());
        }
    }

    private void buildCategoryInfo(AdTargetInfoVo vo, String resolvedExpression){
        //如果为数字ID,表明类目或品牌已经被amazon删除
        if (StringUtils.isNumeric(vo.getTargetName())) {
            vo.setTargetName("此类目亚马逊已删除");
        }
        // 类目名称
        vo.setCategory(vo.getTargetName());
        if (StringUtils.isNotBlank(resolvedExpression)) {
            JSONArray jsonArray = JSONArray.parseArray(resolvedExpression);
            if (jsonArray != null && !jsonArray.isEmpty()) {
                fillBrandMessage(vo, jsonArray);
            }
        }
        vo.setBrandName(StringUtils.isNotBlank(vo.getBrandName()) ? vo.getBrandName() : "所有品牌");
        vo.setCommodityPriceRange(StringUtils.isNotBlank(vo.getCommodityPriceRange()) ? vo.getCommodityPriceRange() : "无限制");
        vo.setRating(StringUtils.isNotBlank(vo.getRating()) ? vo.getRating() : "1-5星");
        vo.setDistribution(StringUtils.isNotBlank(vo.getDistribution()) ? vo.getDistribution() : "所有");
    }

    /**
     * 填充品牌细节信息
     */
    private void fillBrandMessage(AdTargetInfoVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookback(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    private void buildSbKeywordVo(AdTargetInfoParam param, AdTargetInfoVo vo) {
        AmazonSbAdKeyword keyword = amazonSbAdKeywordDao.getByKeywordId(param.getPuid(), param.getShopId(), param.getTargetId());
        vo.setMarketplaceId(keyword.getMarketplaceId());
        vo.setCampaignId(keyword.getCampaignId());
        vo.setAdGroupId(keyword.getAdGroupId());
        vo.setState(keyword.getState());
        if(keyword.getBid() != null){
            vo.setBid(String.valueOf(keyword.getBid()));
        }
        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keyword.getKeywordText())) {
            vo.setTargetName(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keyword.getKeywordText())) {
            vo.setTargetName(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
        }else{
            vo.setTargetName(keyword.getKeywordText());
        }
        vo.setMatchType(keyword.getMatchType());
    }

    private void buildSbTargetVo(AdTargetInfoParam param, AdTargetInfoVo vo) {
        AmazonSbAdTargeting targeting = amazonSbAdTargetingDao.getByTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
        vo.setMarketplaceId(targeting.getMarketplaceId());
        vo.setCampaignId(targeting.getCampaignId());
        vo.setAdGroupId(targeting.getAdGroupId());
        vo.setState(targeting.getState());
        if(targeting.getBid() != null){
            vo.setBid(String.valueOf(targeting.getBid()));
        }
        vo.setTargetName(targeting.getTargetText());
        vo.setProductTargetType(targeting.getType());
        // 商品定位填充商品信息
        if (TargetTypeEnum.asin.name().equals(targeting.getType()) && StringUtils.isNotBlank(targeting.getTargetText())) {
            buildAsinInfo(vo, targeting.getTargetText(), targeting.getMarketplaceId(), targeting.getPuid());
        }
        if (TargetTypeEnum.category.name().equals(targeting.getType())) {
            // 类目定位填充类目信息
            buildCategoryInfo(vo, targeting.getResolvedExpression());
        }
    }

    private void buildSpKeywordVo(AdTargetInfoParam param, AdTargetInfoVo vo) {
        AmazonAdKeyword keyword = amazonAdKeywordShardingDao.getByKeywordId(param.getPuid(), param.getShopId(), param.getTargetId());
        vo.setMarketplaceId(keyword.getMarketplaceId());
        vo.setCampaignId(keyword.getCampaignId());
        vo.setAdGroupId(keyword.getAdGroupId());
        vo.setServingStatus(keyword.getServingStatus());
        vo.setState(keyword.getState());
        if(keyword.getBid() != null){
            vo.setBid(String.valueOf(keyword.getBid()));
        }
        SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(keyword.getKeywordText());
        if(keywordGroupValueEnumByTextCn != null){
            vo.setTargetName(keywordGroupValueEnumByTextCn.getTextCn());
        }else{
            vo.setTargetName(keyword.getKeywordText());
        }
        vo.setMatchType(keyword.getMatchType());
    }

    private void buildSpTargetVo(AdTargetInfoParam param, AdTargetInfoVo vo) {
        AmazonAdTargeting targeting = amazonAdTargetingShardingDao.getByAdTargetId(param.getPuid(), param.getShopId(), param.getTargetId());
        vo.setMarketplaceId(targeting.getMarketplaceId());
        vo.setCampaignId(targeting.getCampaignId());
        vo.setAdGroupId(targeting.getAdGroupId());
        vo.setServingStatus(targeting.getServingStatus());
        vo.setState(targeting.getState());
        if(targeting.getBid() != null){
            vo.setBid(String.valueOf(targeting.getBid()));
        }
        if(AdTargetingTypeEnum.auto.getTargetingType().equals(param.getTargetType())){
            vo.setTargetName(AutoTargetTypeEnum.getAutoTargetValue(targeting.getTargetingValue()));
            vo.setMatchType(targeting.getTargetingValue());
        }else{
            vo.setSelectType(targeting.getSelectType());
            // 与列表页逻辑保持一致
            vo.setTargetName(targeting.getTargetingValue());
            vo.setProductTargetType(targeting.getType());
            if (TargetTypeEnum.category.name().equals(targeting.getType())) {
                if (StringUtils.isNotBlank(targeting.getCategoryPath())) {
                    vo.setTargetName(targeting.getCategoryPath());
                }
            }
            // 商品定位填充商品信息
            if (TargetTypeEnum.asin.name().equals(targeting.getType()) && StringUtils.isNotBlank(targeting.getTargetingValue())) {
                buildAsinInfo(vo, targeting.getTargetingValue(), targeting.getMarketplaceId(), targeting.getPuid());
            }
            if (TargetTypeEnum.category.name().equals(targeting.getType())) {
                // 类目定位填充类目信息
                buildCategoryInfo(vo, targeting.getResolvedExpression());
            }
        }
    }
}
