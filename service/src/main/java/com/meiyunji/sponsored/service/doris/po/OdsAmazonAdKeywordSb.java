package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * amazon SB 关键词表(OdsAmazonAdKeywordSb)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Data
@DbTable("ods_t_amazon_ad_keyword_sb")
public class OdsAmazonAdKeywordSb  implements Serializable {
	/**
     * 商户uid
     */    
	@DbColumn(value = "puid")
    private Integer puid;

	/**
     * 店铺ID
     */    
	@DbColumn(value = "shop_id")
    private Integer shopId;

	/**
     * 关键词id
     */    
	@DbColumn(value = "keyword_id")
    private String keywordId;

	/**
     * 站点
     */    
	@DbColumn(value = "marketplace_id")
    private String marketplaceId;

	/**
     * 配置ID
     */    
	@DbColumn(value = "profile_id")
    private String profileId;

	/**
     * 广告组id
     */    
	@DbColumn(value = "ad_group_id")
    private String adGroupId;

	/**
     * 活动id
     */    
	@DbColumn(value = "campaign_id")
    private String campaignId;

	/**
     * 关键词
     */    
	@DbColumn(value = "keyword_text")
    private String keywordText;

	/**
     * 本地化关键字
     */    
	@DbColumn(value = "native_language_keyword")
    private String nativeLanguageKeyword;

	/**
     * 本地化关键字语言
     */    
	@DbColumn(value = "native_language_locale")
    private String nativeLanguageLocale;

	/**
     * 匹配类型  broad, exact, phrase
     */    
	@DbColumn(value = "match_type")
    private String matchType;

	/**
     * 关键词状态 enabled, paused, pending, archived, draft
     */    
	@DbColumn(value = "state")
    private String state;

	/**
     * 关键词竞价
     */    
	@DbColumn(value = "bid")
    private BigDecimal bid;

	/**
     * 竞价建议范围最小值，用于刚进编辑页面展示
     */    
	@DbColumn(value = "range_start")
    private BigDecimal rangeStart;

	/**
     * 竞价建议范围最大值，用于刚进编辑页面展示
     */    
	@DbColumn(value = "range_end")
    private BigDecimal rangeEnd;

	/**
     * 竞价建议值，用于刚进编辑页面展示
     */    
	@DbColumn(value = "suggested")
    private BigDecimal suggested;

	/**
	 * 关键词词组数量
	 */
	@DbColumn(value = "keyword_size")
	private Integer keywordSize;

	/**
     * 创建人id
     */    
	@DbColumn(value = "create_id")
    private Integer createId;

	/**
     * 更新人id
     */    
	@DbColumn(value = "update_id")
    private Integer updateId;

	/**
     * 报告数据最新更新时间 yyyy-MM-dd
     */    
	@DbColumn(value = "data_update_time")
    private LocalDate dataUpdateTime;

	/**
     * 1在amzup创建，0从amazon同步
     */    
	@DbColumn(value = "create_in_amzup")
    private Integer createInAmzup;

	/**
     * 创建时间
     */    
	@DbColumn(value = "create_time")
    private Date createTime;

	/**
     * 更新的时间
     */    
	@DbColumn(value = "update_time")
    private Date updateTime;

	/**
     * 是否应用分时调价 0,1
     */    
	@DbColumn(value = "is_pricing")
    private Integer isPricing;

	/**
     * 分时调价状态 0,1 关闭，开启
     */    
	@DbColumn(value = "pricing_state")
    private Integer pricingState;

}

