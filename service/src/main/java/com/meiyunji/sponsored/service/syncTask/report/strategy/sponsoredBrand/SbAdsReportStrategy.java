package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredBrand;

import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.sb.mode.report.SbReportAds;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportType;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbAdsReport;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sb 关键词报告处理类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SbAdsReportStrategy extends AbstractReportProcessStrategy {


    private final RedisService redisService;
    private final Producer<ReportReadyNotification> reportReadyProducer;
    private final IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao;
    private final IAmazonSbAdsDao amazonSbAdsDao;
    private final PartitionSqlUtil partitionSqlUtil;

    public SbAdsReportStrategy(
            CosBucketClient dataBucketClient,
            IAmazonAdSbAdsReportDao amazonAdSbAdsReportDao,
            IAmazonSbAdsDao amazonSbAdsDao,
            RedisService redisService, Producer<ReportReadyNotification> reportReadyProducer, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdSbAdsReportDao = amazonAdSbAdsReportDao;
        this.redisService = redisService;
        this.reportReadyProducer = reportReadyProducer;
        this.amazonSbAdsDao = amazonSbAdsDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 2 && (AmazonReportType.sb_ads_all == notification.getType());
    }


    @Override
    public void processReport(ReportReadyNotification notification) throws IOException {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(
                dataBucketClient.getObjectToBytes(notification.getPath())))); JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SbReportAds> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SbReportAds report = new SbReportAds();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= 500) {
                    dealReport(notification, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(notification, reports);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }


    private void dealReport(ReportReadyNotification notification, List<SbReportAds> reports) {
        List<SbReportAds> validReports = reports.stream()
                .filter(item -> BigDecimal.valueOf(item.getImpressions())
                        .compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }
        List<AmazonAdSbAdsReport> poList = getPoBySbReportAds(notification, validReports);
        List<List<AmazonAdSbAdsReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSbAdsReport> amazonAdSbAdsReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdSbAdsReports, 0, amazonAdSbAdsReportDao::insertOrUpdateList);
            if (DateUtil.checkDateRange(notification.getDate(), 2L)) {
                amazonAdSbAdsReportDao.insertDorisList(notification.getSellerIdentifier(), amazonAdSbAdsReports);
            }
        }
    }


    private List<AmazonAdSbAdsReport> getPoBySbReportAds(ReportReadyNotification notification, List<SbReportAds> reports) {
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum
                .getByMarketplaceId(notification.getMarketplace().getId()).getCurrencyCode();
        List<AmazonAdSbAdsReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSbAdsReport sbAdsReport;
        boolean needRetry = false;
        for (SbReportAds report : reports) {
            //查询sb_format
            String adFormat = amazonSbAdsDao.getSbFormatByGroupId(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier(), report.getAdGroupId());
            if (StringUtils.isBlank(adFormat)) {
                log.info("SB广告ADS管理数据未成功匹配成功,30分钟后再解析报告. shopId : {} adGroupId: {}",
                        notification.getMarketplaceIdentifier(), report.getAdGroupId());
                needRetry = true;
                continue;
            }
            sbAdsReport = new AmazonAdSbAdsReport();
            sbAdsReport.setPuid(notification.getSellerIdentifier());
            sbAdsReport.setShopId(notification.getMarketplaceIdentifier());
            sbAdsReport.setMarketplaceId(notification.getMarketplace().getId());
            sbAdsReport.setCountDate(notification.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sbAdsReport.setCurrency(currencyCode);
            sbAdsReport.setAdFormat(adFormat);
            sbAdsReport.setCampaignName(report.getCampaignName());
            sbAdsReport.setCampaignId(report.getCampaignId());
            sbAdsReport.setCampaignStatus(report.getCampaignStatus());
            sbAdsReport.setCampaignBudget(report.getCampaignBudget());
            sbAdsReport.setCampaignBudgetType(report.getCampaignBudgetType());
            sbAdsReport.setCampaignRuleBasedBudget(report.getCampaignRuleBasedBudget());
            sbAdsReport.setApplicableBudgetRuleId(report.getApplicableBudgetRuleId());
            sbAdsReport.setApplicableBudgetRuleName(report.getApplicableBudgetRuleName());
            sbAdsReport.setAdGroupName(report.getAdGroupName());
            sbAdsReport.setAdGroupId(report.getAdGroupId());
            sbAdsReport.setAdId(report.getAdId() == null ? "" : report.getAdId());
            sbAdsReport.setImpressions(report.getImpressions());
            sbAdsReport.setClicks(report.getClicks());
            sbAdsReport.setCost(report.getCost() != null ? BigDecimal.valueOf(report.getCost()) : null);
            sbAdsReport.setSales14d(report.getAttributedSales14d() != null ? BigDecimal.valueOf(report.getAttributedSales14d()) : null);
            sbAdsReport.setSales14dSameSKU(report.getAttributedSales14dSameSKU() != null ? BigDecimal.valueOf(report.getAttributedSales14dSameSKU()) : null);
            sbAdsReport.setConversions14d(report.getAttributedConversions14d());
            sbAdsReport.setConversions14dSameSKU(report.getAttributedConversions14dSameSKU());
            sbAdsReport.setDetailPageViewsClicks14d(report.getAttributedDetailPageViewsClicks14d());
            sbAdsReport.setOrdersNewToBrand14d(report.getAttributedOrdersNewToBrand14d());
            sbAdsReport.setOrdersNewToBrandPercentage14d(report.getAttributedOrdersNewToBrandPercentage14d());
            sbAdsReport.setOrderRateNewToBrand14d(report.getAttributedOrderRateNewToBrand14d());
            sbAdsReport.setSalesNewToBrand14d(report.getAttributedSalesNewToBrand14d() != null ? BigDecimal.valueOf(report.getAttributedSalesNewToBrand14d()) : null);
            sbAdsReport.setSalesNewToBrandPercentage14d(report.getAttributedSalesNewToBrandPercentage14d());
            sbAdsReport.setUnitsOrderedNewToBrand14d(report.getAttributedUnitsOrderedNewToBrand14d());
            sbAdsReport.setUnitsOrderedNewToBrandPercentage14d(report.getAttributedUnitsOrderedNewToBrandPercentage14d());
            sbAdsReport.setUnitsSold14d(report.getUnitsSold14d());
            sbAdsReport.setDpv14d(report.getDpv14d());
            sbAdsReport.setVctr(report.getVctr());
            sbAdsReport.setVideo5SecondViewRate(report.getVideo5SecondViewRate());
            sbAdsReport.setVideo5SecondViews(report.getVideo5SecondViews());
            sbAdsReport.setVideoFirstQuartileViews(report.getVideoFirstQuartileViews());
            sbAdsReport.setVideoMidpointViews(report.getVideoMidpointViews());
            sbAdsReport.setVideoThirdQuartileViews(report.getVideoThirdQuartileViews());
            sbAdsReport.setVideoUnmutes(report.getVideoUnmutes());
            sbAdsReport.setViewableImpressions(report.getViewableImpressions());
            sbAdsReport.setVideoCompleteViews(report.getVideoCompleteViews());
            sbAdsReport.setVtr(report.getVtr());
            sbAdsReport.setBrandedSearches14d(report.getAttributedBrandedSearches14d());
            if (StringUtils.isBlank(report.getAdId())) {
                sbAdsReport.setQueryId(report.getAdGroupId());
            } else {
                sbAdsReport.setQueryId(report.getAdId());
            }
            list.add(sbAdsReport);
        }
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_DAILY_REPORT, notification.getSellerIdentifier(),
                notification.getMarketplaceIdentifier(), notification.getType().name(),
                notification.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (needRetry) {
            Long value = redisService.incr(cacheKey, 1L);
            if (value <= 1) {
                redisService.expire(cacheKey, 3600);
                try {
                    reportReadyProducer.newMessage().value(notification)
                            .deliverAfter(30, TimeUnit.MINUTES).send();
                } catch (PulsarClientException e) {
                    log.error("Pulsar send message with an error.", e);
                }
            }
        }
        return list;
    }
}
