package com.meiyunji.sponsored.service.localization.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@DbTable(value = "t_amazon_keyword_localization_schedule")
public class AmazonKeywordLocalizationSchedule implements Serializable {
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    @DbColumn(value = "profile_id")
    private String profileId;

    @DbColumn(value = "sp_next_sync_at")
    private LocalDateTime spNextSyncAt;

    @DbColumn(value = "sb_next_sync_at")
    private LocalDateTime sbNextSyncAt;

    @DbColumn(value = "create_at")
    private LocalDateTime createAt;

    @DbColumn(value = "last_update_at")
    private LocalDateTime lastUpdateAt;
}
