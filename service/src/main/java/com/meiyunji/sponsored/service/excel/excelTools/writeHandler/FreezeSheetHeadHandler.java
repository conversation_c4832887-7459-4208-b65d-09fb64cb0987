package com.meiyunji.sponsored.service.excel.excelTools.writeHandler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * <AUTHOR>
 * @Date 2021/4/21 20:39
 */
public class FreezeSheetHeadHandler implements SheetWriteHandler {
    /**
     * 要冻结的行数
     */
    private int freezeRow;

    public FreezeSheetHeadHandler(){
        this.freezeRow = 1;
    }


    public FreezeSheetHeadHandler(int freezeRow){
        this.freezeRow = freezeRow;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 冻结表头n行
        writeSheetHolder.getSheet().createFreezePane(0, freezeRow, 0, 1);
    }
}
