package com.meiyunji.sponsored.service.post.response;

import com.meiyunji.sponsored.service.post.vo.PostChartVo;
import com.meiyunji.sponsored.service.post.vo.PostsAggregateVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 列表页图表数据返回
 * @author: he<PERSON><PERSON>
 * @date: 2025-04-07  17:16
 */
@Data
public class GetPostsChartResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    PostsAggregateVo aggregateVo;

    List<PostChartVo> day;

    List<PostChartVo> week;

    List<PostChartVo> month;

}
