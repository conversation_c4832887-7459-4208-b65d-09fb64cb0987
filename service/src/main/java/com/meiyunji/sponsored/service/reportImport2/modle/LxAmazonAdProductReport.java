package com.meiyunji.sponsored.service.reportImport2.modle;

import com.alibaba.fastjson.JSONReader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LxAmazonAdProductReport extends BaseAmazonAdLxReport {

    /**
     * profileId
     */
    @JsonProperty("profile_id")
    private String profileId;

    /**
     * 广告活动id
     */
    @JsonProperty("campaign_id")
    private String campaignId;

    /**
     * 广告活动名称
     */
    @JsonProperty("campaign_name")
    private String campaignName;


    @JsonProperty("asin")
    private String asin;

    @JsonProperty("sku")
    private String sku;

    @JsonProperty("ad_group_id")
    private String adGroupId;

    @JsonProperty("ad_group_name")
    private String adGroupName;

    @JsonProperty("ad_id")
    private String adId;

    public BaseAmazonAdLxReport readFromJsonReader(JSONReader jsonReader) {
        while (jsonReader.hasNext()) {
            String key = jsonReader.readString();
            switch (key) {
                case "profile_id":
                    this.setProfileId(jsonReader.readString());
                    break;
                case "campaign_id":
                    this.setCampaignId(jsonReader.readString());
                    break;
                case "ad_group_name":
                    this.setAdGroupName(jsonReader.readString());
                    break;
                case "ad_group_id":
                    this.setAdGroupId(jsonReader.readString());
                    break;
                case "campaign_name":
                    this.setCampaignName(jsonReader.readString());
                    break;
                case "asin":
                    this.setAsin(jsonReader.readString());
                    break;
                case "sku":
                    this.setSku(jsonReader.readString());
                    break;
                case "ad_id":
                    this.setAdId(jsonReader.readString());
                    break;
                default:
                    super.readFromJsonReader(key, jsonReader, this);
                    break;

            }
        }
        return this;
    }

}
