package com.meiyunji.sponsored.service.batchCreate.dto.productView;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2023/11/22 16:44
 * @describe: 批量新建SP-产品视图列表页DTO
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
    public class AsinViewBatchSpListDTO {
    private Long id;
    private String campaignName;
    private Long taskId;
    private String taskName;
    private String marketplaceId;
    private Integer shopId;
    private String shopName;
    private String createTime;
    private Integer creatorId;
    private Integer taskStatus;
    private String asin;
    private Integer status;
    private String amazonCampaignId;
}
