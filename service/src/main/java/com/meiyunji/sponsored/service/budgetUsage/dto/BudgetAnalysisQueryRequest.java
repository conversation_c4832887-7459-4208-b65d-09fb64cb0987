package com.meiyunji.sponsored.service.budgetUsage.dto;


import com.meiyunji.sponsored.common.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 预算分析的请求
 */
@Data
public class BudgetAnalysisQueryRequest {

    private Integer pageNo;

    private Integer pageSize;

    private List<Integer> shopIdList;

    private Integer puid;
    /**
     * 忽略 主服务统一处理为shopId 这里是为了站点今日
     */
    private List<String> marketplaceIdList;
    /**
     * 排序字段
     */
    private String orderField;
    /**
     * 排序类型
     */
    private String orderType;
    /**
     * 活动运行状态
     */
    private String status;
    /**
     * 服务状态 用于判断是否超预算
     */
    private String severStatus;

    private String startDate;

    private String endDate;

    private String uuid;

    /**
     * 类型
     */
    private String type; // sp, sd, sb
    /**
     * 组合Id 统一处理为 campaignIdList
     */
    private String portfolioIds;
    /**
     *  活动Id 统一处理为 campaignIdList
     */
    private String campaignIds;
    /**
     * 是否开启高级搜索
     */
    private Boolean useAdvanced;
    /**
     * 是否站点今天
     */
    private Boolean siteToday;
    /**
     * 站点今天的日期
     */
    private List<String> siteTodayDate;

    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    /**
     * 预算活跃时常
     */
    private Integer budgetActiveTime;

    /**
     * 查找类型 (asin,msku,父asin) 统一处理为 campaignIdList
     */
    private String productType;
    /**
     * 查找值 统一处理为 campaignIdList
     */
    private String productValue;


    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;

    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;

    @ApiModelProperty(value = "高级搜索点击率小值")
    private BigDecimal clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private BigDecimal clickRateMax;

    @ApiModelProperty(value = "高级搜索花费小值")
    private BigDecimal costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private BigDecimal costMax;

    @ApiModelProperty(value = "高级搜索cpc小值")
    private BigDecimal cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private BigDecimal cpcMax;

    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;

    @ApiModelProperty(value = "高级搜索sales小值")
    private BigDecimal salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private BigDecimal salesMax;


    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private BigDecimal salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private BigDecimal salesConversionRateMax;


    @ApiModelProperty(value = "高级搜索acos小值")
    private BigDecimal acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private BigDecimal acosMax;

    @ApiModelProperty(value = "高级搜索roas小值")
    private BigDecimal roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private BigDecimal roasMax;


    @ApiModelProperty(value = "高级搜索预算最小")
    private BigDecimal budgetMin;
    @ApiModelProperty(value = "高级搜索预算最大")
    private BigDecimal budgetMax;

    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;
    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty(value = "cpa最小值")
    private BigDecimal cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private BigDecimal cpaMax;

    @ApiModelProperty("广告笔单价最小值")
    private BigDecimal advertisingUnitPriceMin;
    @ApiModelProperty("广告笔单价最大值")
    private BigDecimal advertisingUnitPriceMax;

    private BigDecimal adCostPerClickMin;
    private BigDecimal adCostPerClickMax;

    private Integer budgetAdjustmentNumMin;
    private Integer budgetAdjustmentNumMax;
    /**
     *  已花费
     */
    private BigDecimal spentMin;
    private BigDecimal spentMax;
    /**
     * 估算点击率
     */
    private BigDecimal estimateClicksMin;
    private BigDecimal estimateClicksMax;

    @ApiModelProperty(value = "预算剩余最小值，百分比")
    private BigDecimal budgetSurplusMin;
    @ApiModelProperty(value = "预算剩余最大值，百分比")
    private BigDecimal budgetSurplusMax;

    @ApiModelProperty(value = "剩余预算最小值")
    private BigDecimal residualBudgetMin;
    @ApiModelProperty(value = "剩余预算最大值")
    private BigDecimal residualBudgetMax;

    public List<String> getListProductValues(){
        if (StringUtils.isNotBlank(this.productValue)) {
            return StringUtil.stringToList(this.productValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }

    /**
     * 只查数量
     */
    private Boolean onlyCount;
    /**
     * 是否超预算
     */
    private Boolean outBudget;
}
