package com.meiyunji.sponsored.service.strategy.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleProcessTaskVo;
import com.meiyunji.sponsored.service.cpc.po.pricing.AmazonAdPricingCampaign;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.vo.*;

import java.util.List;

public interface AdvertiseStrategyStatusDao extends IBaseShardingDao<AdvertiseStrategyStatus> {

    AdvertiseStrategyStatus getByTaskId(int puid,int shopId ,Long taskId,String itemType);

    List<AdvertiseStrategyStatus> getListByTaskIds(int puid, List<Long> taskId, String itemType);

    List<TaskTemplateIdVo> getTaskTemplateMapping(int puid, List<Long> taskId);

    AdvertiseStrategyStatus getByStatusId(int puid, Long statusId);

    List<AdvertiseStrategyStatus> getByStatusIds(int puid, List<Long> statusIds);

    List<AdvertiseStrategyStatus> getByItemIds(int puid, int shopId, String itemType, List<String> itemIds);

    List<AdvertiseStrategyStatus> getByItemIdWayType(int puid, int shopId, String itemType, List<String> itemIds);

    List<AdvertiseStrategyStatus> getRepeatByItemIds(int puid, int shopId, String itemType, List<String> itemIds);

    List<AdvertiseStrategyStatus> getByItemIdsEffectiveStatus(int puid, int shopId, String itemType, List<String> itemIds);

    List<AdvertiseStrategyStatus> getByItemIdsShopIdListEffectiveStatus(int puid, List<Integer> shopIdList, String itemType, List<String> itemIds);

    int updateByPrimaryKey(int puid, AdvertiseStrategyStatus record);

    int batchInsert(int puid, List<AdvertiseStrategyStatus> list);

    Integer insetStrategyStatus(int puid,AdvertiseStrategyStatus advertiseStrategyStatus);

    Integer updateStrategyStatus(int puid,AdvertiseStrategyStatus advertiseStrategyStatus);

    Integer deleteStrategyStatus(int puid,Long statusId,int shopId);

    Integer deleteStrategyByTaskId(int puid,int shopId,Long taskId);

    Page<AdvertiseStrategyStatus> pageAdControlledCampaign(ControlledObjectParam param);

    Page<AdvertiseStrategyStatus> pageAdControlledStartStop(ControlledObjectParam param);

    Page<AdvertiseStrategyStatus> pageAdControlledCampaignSpace(ControlledObjectParam param);

    Page<AdvertiseStrategyStatus> pageAdControlledTarget(ControlledObjectParam param);

    Page<AdvertiseStrategyStatus> pageAdControlledPortfolio(ControlledObjectParam param);

    List<AdvertiseStrategyStatus> getListByTemplateId(ExecuteParam param);

    Integer existListByTemplateId(Integer puid,Long templateId);

    List<Long> getTemplateUsageAmount(int puid,  List<Long> templateId,String itemType);

    void updateStatus(int puid,Long templateId,String type,Long statusId,String rule,String status,Integer version,String originValue,String returnValue);

    void updateHourStatus(int puid,Long templateId,String type,Long statusId,String rule,String status,Integer version,String originValue,String returnValue, String childrenItemType);

    void updateStatusAddWayType(int puid,Long templateId,String type,Long statusId,String rule,String status,Integer version,String originValue,String returnValue, String addWayType);

    void updateRule(int puid,Integer shopId,String type,Long statusId,String rule);

    void updateRuleAndReturnValue(int puid,Integer shopId,String type,Long statusId,String rule,String returnValue);

    void updateRuleAndReturnValueAndChildrenItemType(int puid,Integer shopId,String type,Long statusId,String rule,String returnValue, String childrenItemType);

    void updateStrategyStatusById(int puid,Long statusId,String status);

    void updateStatusTemplateById(int puid,Long statusId,Long templateId);

    void updateStatusAddWayType(int puid,Long statusId, Long templateId, String addWayType);

    void updateStatusTemplateByIdList(int puid, List<Long> statusIdList, Long templateId);

    List<AdvertiseStrategyStatus> getListByPuidAndShopIds(int puid, Integer shopId, Long templateId, String itemType, List<StatusVo> statusVoList);

    AdvertiseStrategyStatus getObjectByPuidAndShopId(int puid,int shopId,String itemId,String itemType);

    List<String> getListByCampaignId(int puid,Integer shopId,Long templateId,List<String> campaignIds);

    List<String> getListByCampaignId(int puid, List<Integer> shopIdList, Long templateId, List<String> campaignIds);

    List<QueryTargetTypeVo> getListByAdGroupId(int puid,Integer shopId,Long templateId,List<String> campaignIds);

    List<AdvertiseStrategyStatus> getLisByItemIds(Integer puid, Integer shopId, String itemType,List<String> itemIds);

    List<QueryTargetTypeVo> queryListItemIdAndTargetType(Integer puid, Integer shopId, Long templateId, List<String> adTypeList, List<String> targetTypeList);

    List<Long> getIdByPuidAndShopIds(int puid, Integer shopId, Long templateId, String itemType, List<StatusVo> statusVoList, List<Long> includeStatusIdList);

    List<AdvertiseStrategyStatus> getListByStrategyAdGroupId(int puid, int shopId, Long id, Long strategyAdGroupId);

    List<AdvertiseStrategyStatus> getListByStrategyAdGroupIds(int puid, int shopId, Long id, List<Long> strategyAdGroupId);

    void batchUpdateStatusTemplateById(int puid,List<Long> statusIds,Long templateId);

    Integer batchDeleteStrategyStatus(int puid, List<Long> statusId, int shopId);

    Page<AdvertiseStrategyStatus> pageListByCheckItem(CheckItemParam param, List<String> itemIdList);

    List<Long> getIdByPuidAndShopIdsAdGroup(int puid, Integer shopId, Long templateId, String itemType,  List<QueryAdGroupRealTimeBidStateParam> strategyStatusVoList);

    List<AdvertiseStrategyStatus> getByStatusIds(int puid, Long templateId, List<Long> statusIds);

    void updateEffectiveStatus(int puid, String itemType, List<String> itemId, String addWayType, Integer effectiveStatus);

    List<Long> getListTemplateIds(int puid, List<Integer> shopIdList, String addWayType);

    List<String> queryIsTargetItem(int puid, List<String> adGroupIdList);

    Page<AdvertiseStrategyStatus> pageAdGroupTargetStrategy(QueryAdGroupTargetStrategyParam param);

    Integer getCount(int puid, Long templateId);

    Integer getCount(int puid, Long templateId, List<Long> includeStatusIdList);

    List<AdvertiseStrategyStatus> getAdvertiseStrategyStatusList(int puid, Long templateId, Long statusId, List<Long> includeStatusIdList);

    List<AdvertiseStrategyStatusTaskVo> getAdvertiseStrategyStatusTaskVoList(int puid, Long templateId, Long statusId, List<Long> includeStatusIdList);

    List<AdvertiseStrategyStatusTaskVo> getAdvertiseStrategyStatusTaskVoList4ProductStartStop(ProcessTaskParam param);

    List<String> queryListItemIdAndTargetId(Integer puid, Integer shopId, Long templateId, String adType, String targetType);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<String> getItemIdListBytAdGroupId(int puid, int shopId, List<String> adGroupIdList);

    List<Long> getTemplateCount(int puid);

    AdvertiseStrategyStatus independentPageTarget(int puid,int shopId,String itemId,Integer effectiveStatus);

    List<String> getItemIdList(int puid, int shopId, String adGroupId);

    List<String> getItemIdList(int puid, int shopId, List<String> adGroupId);

    List<Long> getIdListByStrategyAdGroupIds(int puid, int shopId, List<Long> strategyAdGroupId);

    List<Long> getStatusIdsByProcessTaskParam(ProcessTaskParam param, List<String> itemIdList);

    List<Long> getTargetStatusIdsByProcessTaskParam(ProcessTaskParam param, List<String> itemIdList);

    List<ProcessTaskVo> getStatusIdsByProcessTaskParam(ProcessTaskParam param);

    AdvertiseStrategyStatus getStatusByItemId(int puid, int shopId, String itemId, String itemType, Long taskId);

    void updateStatusTemplateByIdBatch(Integer puid, List<Long> statusIdList, Long templateId);

    List<AdvertiseStrategyStatus> getListByTemplateIdLimit(Integer puid, Integer shopId, Long templateId, int limit);

    List<AdvertiseStrategyStatus> getByShopIdAndItemIds(int puid, List<Integer> shopId, String itemType, List<String> itemIds, String targetType, String adType);

    AdvertiseStrategyStatus getStatusByItemIdAndStatus(int puid, int shopId, String itemId, String itemType, Long taskId, String status);

    List<String> listAllItemId(int puid, List<Integer> shopId, String itemType, String targetType, String adType);
}