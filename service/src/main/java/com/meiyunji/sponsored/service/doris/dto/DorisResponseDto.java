package com.meiyunji.sponsored.service.doris.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * doris stream load response
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class DorisResponseDto {

    @JsonProperty("TxnId")
    private Integer txnId;
    @JsonProperty("Label")
    private String label;
    @JsonProperty("TwoPhaseCommit")
    private String twoPhaseCommit;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("Message")
    private String message;
    @JsonProperty("NumberTotalRows")
    private Integer numberTotalRows;
    @JsonProperty("NumberLoadedRows")
    private Integer numberLoadedRows;
    @JsonProperty("NumberFilteredRows")
    private Integer numberFilteredRows;
    @JsonProperty("NumberUnselectedRows")
    private Integer numberUnselectedRows;
    @JsonProperty("LoadBytes")
    private Integer loadBytes;
    @JsonProperty("LoadTimeMs")
    private Integer loadTimeMs;
    @JsonProperty("BeginTxnTimeMs")
    private Integer beginTxnTimeMs;
    @JsonProperty("StreamLoadPutTimeMs")
    private Integer streamLoadPutTimeMs;
    @JsonProperty("ReadDataTimeMs")
    private Integer readDataTimeMs;
    @JsonProperty("WriteDataTimeMs")
    private Integer writeDataTimeMs;
    @JsonProperty("CommitAndPublishTimeMs")
    private Integer commitAndPublishTimeMs;
    @JsonProperty("ErrorURL")
    private String errorURL;
}
