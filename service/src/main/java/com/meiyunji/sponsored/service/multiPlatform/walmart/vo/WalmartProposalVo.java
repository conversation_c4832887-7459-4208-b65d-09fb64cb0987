package com.meiyunji.sponsored.service.multiPlatform.walmart.vo;

import java.util.List;
import java.util.Map;

public class WalmartProposalVo {
    private String id;

    private String walmartSku;                  //产品sku

    private String walmartName;                 //产品名称

    private String walmartAttr;                 //产品属性

    private String walmartImgUrl;               //产品图

    private String shopName;                    //店铺名

    private String dxmProductSku;               //小秘商品sku

    private String dxmProductName;              //小秘商品名

    private String dxmProductImgUrl;            //小秘商品图

    private Integer availableQuantity;          //可用库存

    private Integer onPassageStock;             //在途库存

    private Integer lastThreeSale;              //3天销量

    private Integer lastSevenSale;              //7天销量

    private Integer lastFifteenSale;            //15天销量

    private Integer lastThirtySale;             //30天销量

    private Double dailySales;                  //日均销量

    private Integer waitShipNum;                //待发货

    private Integer proposalNum;                //建议备货数 系统计算

    private Integer stockUpNum;                 //手动更改建议备货数

    private int stockingTime;                   // 备货时长

    private Integer markNoStockUp;              //暂不备货，0 正常备货；1 暂不备货

    private List<Map> attrList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWalmartSku() {
        return walmartSku;
    }

    public void setWalmartSku(String walmartSku) {
        this.walmartSku = walmartSku;
    }

    public String getWalmartName() {
        return walmartName;
    }

    public void setWalmartName(String walmartName) {
        this.walmartName = walmartName;
    }

    public String getWalmartAttr() {
        return walmartAttr;
    }

    public void setWalmartAttr(String walmartAttr) {
        this.walmartAttr = walmartAttr;
    }

    public String getWalmartImgUrl() {
        return walmartImgUrl;
    }

    public void setWalmartImgUrl(String walmartImgUrl) {
        this.walmartImgUrl = walmartImgUrl;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getDxmProductSku() {
        return dxmProductSku;
    }

    public void setDxmProductSku(String dxmProductSku) {
        this.dxmProductSku = dxmProductSku;
    }

    public String getDxmProductName() {
        return dxmProductName;
    }

    public void setDxmProductName(String dxmProductName) {
        this.dxmProductName = dxmProductName;
    }

    public String getDxmProductImgUrl() {
        return dxmProductImgUrl;
    }

    public void setDxmProductImgUrl(String dxmProductImgUrl) {
        this.dxmProductImgUrl = dxmProductImgUrl;
    }

    public Integer getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(Integer availableQuantity) {
        this.availableQuantity = availableQuantity;
    }

    public Integer getOnPassageStock() {
        return onPassageStock;
    }

    public void setOnPassageStock(Integer onPassageStock) {
        this.onPassageStock = onPassageStock;
    }

    public Integer getLastThreeSale() {
        return lastThreeSale;
    }

    public void setLastThreeSale(Integer lastThreeSale) {
        this.lastThreeSale = lastThreeSale;
    }

    public Integer getLastSevenSale() {
        return lastSevenSale;
    }

    public void setLastSevenSale(Integer lastSevenSale) {
        this.lastSevenSale = lastSevenSale;
    }

    public Integer getLastFifteenSale() {
        return lastFifteenSale;
    }

    public void setLastFifteenSale(Integer lastFifteenSale) {
        this.lastFifteenSale = lastFifteenSale;
    }

    public Integer getLastThirtySale() {
        return lastThirtySale;
    }

    public void setLastThirtySale(Integer lastThirtySale) {
        this.lastThirtySale = lastThirtySale;
    }

    public Double getDailySales() {
        return dailySales;
    }

    public void setDailySales(Double dailySales) {
        this.dailySales = dailySales;
    }

    public Integer getWaitShipNum() {
        return waitShipNum;
    }

    public void setWaitShipNum(Integer waitShipNum) {
        this.waitShipNum = waitShipNum;
    }

    public Integer getProposalNum() {
        return proposalNum;
    }

    public void setProposalNum(Integer proposalNum) {
        this.proposalNum = proposalNum;
    }

    public Integer getStockUpNum() {
        return stockUpNum;
    }

    public void setStockUpNum(Integer stockUpNum) {
        this.stockUpNum = stockUpNum;
    }


    public int getStockingTime() {
        return stockingTime;
    }

    public void setStockingTime(int stockingTime) {
        this.stockingTime = stockingTime;
    }

    public Integer getMarkNoStockUp() {
        return markNoStockUp;
    }

    public void setMarkNoStockUp(Integer markNoStockUp) {
        this.markNoStockUp = markNoStockUp;
    }

    public List<Map> getAttrList() {
        return attrList;
    }

    public void setAttrList(List<Map> attrList) {
        this.attrList = attrList;
    }
}
