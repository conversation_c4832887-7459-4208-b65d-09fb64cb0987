package com.meiyunji.sponsored.service.batchCreate.adStructure;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.*;
import com.meiyunji.sponsored.service.batchCreate.dto.adstructure.AdStructureCampaignTypeDto;
import com.meiyunji.sponsored.service.batchCreate.dto.adstructure.AdStructureGroupTypeDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureLevelEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-11-13  18:38
 */
public abstract class AdStructure {

    //广告结构JSON
    protected String STRUCTURE_JSON;

    //用于存储预设的广告结构
    protected List<AdStructureCampaignTypeDto> structureCampaignTypeDtoList;

    //用于校验前端传入的广告结构是否符合预设结构，使用双键Map，参考check方法思路
    protected Table<String, String, Integer> structureCheckTable;


    /**
     * 初始化方法
     */
    public void init() {

        //初始化结构
        structureCampaignTypeDtoList = JSON.parseArray(STRUCTURE_JSON, AdStructureCampaignTypeDto.class);

        //初始校验Map
        structureCheckTable = HashBasedTable.create();
        for (AdStructureCampaignTypeDto campaign : structureCampaignTypeDtoList) {
            //广告活动层
            String targetingType = campaign.getTargetingType();
            Integer campaignCount = structureCheckTable.get(AdStructureLevelEnum.LEVEL_CAMPAIGN.getCode(), targetingType);
            structureCheckTable.put(AdStructureLevelEnum.LEVEL_CAMPAIGN.getCode(), targetingType, Objects.isNull(campaignCount) ? 1 : campaignCount + 1);
            //广告组层
            for (AdStructureGroupTypeDto group : campaign.getGroupTypeList()) {
                String groupType = targetingType + "-" + group.getType();
                Integer groupCount = structureCheckTable.get(AdStructureLevelEnum.LEVEL_GROUP.getCode(), groupType);
                structureCheckTable.put(AdStructureLevelEnum.LEVEL_GROUP.getCode(), groupType, Objects.isNull(groupCount) ? 1 : groupCount + 1);

                //投放层
                if (StringUtils.isNotBlank(group.getMatchType())) {
                    String matchType = groupType + "-" + group.getMatchType();
                    Integer matchCount = structureCheckTable.get(AdStructureLevelEnum.LEVEL_TARGETING.getCode(), matchType);
                    structureCheckTable.put(AdStructureLevelEnum.LEVEL_TARGETING.getCode(), matchType, Objects.isNull(matchCount) ? 1 : matchCount + 1);
                }
            }
        }
    }

    /**
     * 创建广告结构预览数据
     * @param batchDataRequestList
     * @return
     */
    public List<SpBatchCreatePreviewVo> generatePreview(List<SpBatchCreatePreviewBatchDataRequest> batchDataRequestList) {
        List<SpBatchCreatePreviewVo> previewVoList = new ArrayList<>(batchDataRequestList.size());
        //各个店铺广告结构是一样的
        for (SpBatchCreatePreviewBatchDataRequest batchDataRequest : batchDataRequestList) {
            SpBatchCreatePreviewBaseInfoRequest baseInfoRequest = batchDataRequest.getBaseInfo();
            SpBatchCreatePreviewVo.Builder previewBuilder = SpBatchCreatePreviewVo.newBuilder();
            previewBuilder.setShopId(baseInfoRequest.getShopId());
            //批量组装广告活动
            List<SpBatchCreatePreviewCampaignVo> previewCampaignList = AdStructureCampaignFactory.assembleCustomCampaignInfo(baseInfoRequest, structureCampaignTypeDtoList);
            previewBuilder.addAllCampaigns(previewCampaignList);
            previewVoList.add(previewBuilder.build());
        }

        return previewVoList;
    }


    /**
     * 检查前端传入的数据是否符合预设的广告结构数据
     * 例如广告活动可以减少，但不能超出
     * 预设的有一个手动-关键词-精准，那么提交的数据可以没有动-关键词-精准，如果有则最多为1个
     *
     * 比较的思路：
     * 把预设的广告结构，组装出一个字符串集合A，集合元素为: 活动类型-组类型-投放匹配类型
     * 把前端传入的数据，组装出一个字符串集合B，集合元素为: 活动类型-组类型-投放匹配类型
     * 然后判断集合B是否为集合A的子集即可（需要考虑重复）
     * 可以使用map来比较，效率更高，map的key为活动类型-组类型-投放匹配类型，value为出现频率
     *
     * 另外还需要考虑只有活动，没有组的情况，或者是有活动和组，没有投放的情况。
     * 所以我们把活动类型，活动类型-组类型，活动类型-组类型-投放匹配类型都要作为map的key，去跑一下子集比较，即我们把预设结构的所有情况都穷举。
     * 如果预设结构中投放没有设定类型，那么我们只需要计入活动类型-组类型即可
     * 穷举时有3层，我们把3层都穷举出来，比较时对应地判断每一层是否符合即可，使用双键map。
     *
     * @param request
     * @return
     */
    public boolean check(SpBatchCreateSubmitRequest request) {
        //将前端传入的数据也转成双键map
        List<SpBatchCreateSubmitTaskRequest> batchDataList = request.getBatchDataList();
        for (SpBatchCreateSubmitTaskRequest taskRequest : batchDataList) {
            Table<String, String, Integer> table = HashBasedTable.create();
            //计算Map
            for (SpBatchCreatePreviewCampaignVo campaign : taskRequest.getCampaignsList()) {
                //广告活动层
                String targetingType = campaign.getTargetingType();
                Integer campaignCount = table.get(AdStructureLevelEnum.LEVEL_CAMPAIGN.getCode(), targetingType);
                table.put(AdStructureLevelEnum.LEVEL_CAMPAIGN.getCode(), targetingType, Objects.isNull(campaignCount) ? 1 : campaignCount + 1);
                //广告组层
                for (SpBatchCreatePreviewGroupVo group : campaign.getGroupsList()) {
                    String groupType = targetingType + "-" + group.getType();
                    Integer groupCount = table.get(AdStructureLevelEnum.LEVEL_GROUP.getCode(), groupType);
                    table.put(AdStructureLevelEnum.LEVEL_GROUP.getCode(), groupType, Objects.isNull(groupCount) ? 1 : groupCount + 1);

                    //投放层
                    if (StringUtils.isNotBlank(group.getMatchType())) {
                        String matchType = groupType + "-" + group.getMatchType();
                        Integer matchCount = table.get(AdStructureLevelEnum.LEVEL_TARGETING.getCode(), matchType);
                        table.put(AdStructureLevelEnum.LEVEL_TARGETING.getCode(), matchType, Objects.isNull(matchCount) ? 1 : matchCount + 1);
                    }
                }
            }
            //比较2个双键map
            for (Table.Cell cell : table.cellSet()) {
                Integer count = structureCheckTable.get(cell.getRowKey(), cell.getColumnKey());
                if (Objects.isNull(count) || Integer.parseInt(cell.getValue().toString()) > count) {
                    return false;
                }
            }
        }
        return true;
    }

}
