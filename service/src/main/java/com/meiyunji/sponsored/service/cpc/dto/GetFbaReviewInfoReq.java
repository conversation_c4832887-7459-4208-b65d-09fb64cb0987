package com.meiyunji.sponsored.service.cpc.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 查询fba可售数据req
 *
 * @Author: hejh
 * @Date: 2024/9/2 16:12
 */
@Data
public class GetFbaReviewInfoReq implements Serializable {

    @NotNull
    @JsonProperty("puid")
    private int puid;

    @NotEmpty
    @JsonProperty("mskuShopIds")
    private List<MskuShopId> mskuShopIds;

    @Data
    public static class MskuShopId {

        @NotEmpty
        @JsonProperty("msku")
        private String msku;

        @NotNull
        @JsonProperty("shopId")
        private int shopId;
    }
}
