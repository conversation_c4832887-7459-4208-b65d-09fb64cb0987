<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.1975-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
    <artifactId>service</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-generator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.amzup.api</groupId>
            <artifactId>sponsored-grpc-api-sdk</artifactId>
            <version>${sponsored-grpc-api-sdk.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.sellfox.aadas</groupId>
            <artifactId>sellfox-aadas-api</artifactId>
            <version>${sellfox-aadas-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.sellfox.aadas</groupId>
            <artifactId>types</artifactId>
            <version>${sellfox-aadas-type.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.sellfox.aadras</groupId>
            <artifactId>sellfox-aadras-api</artifactId>
            <version>${sellfox-aadras-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.sellfox.aadras</groupId>
            <artifactId>types</artifactId>
            <version>${sellfox-aadras-type.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meiyunji.sellfox.cpc</groupId>
            <artifactId>api</artifactId>
            <version>${cpc.api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-client</artifactId>
            <version>${pulsar.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pulsar</groupId>
            <artifactId>pulsar-jackson-jsr310-shade</artifactId>
            <version>${pulsar.version}</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>${amazon.awssdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>${org.apache.commons.csv.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>${protobuf.version}</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redission.version}</version>
        </dependency>
        <dependency>
            <groupId>com.meiyunji.sellfox.ams</groupId>
            <artifactId>sellfox-ams-api</artifactId>
            <version>${sellfox-ams-api.version}</version>
        </dependency>
        <!--用于webp图片转换-->
        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>${webp-imageio.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.walmart.api</groupId>
            <artifactId>walmart-api-sdk</artifactId>
            <version>${walmart-api-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-log4j2</artifactId>
            <version>${sentry-log4j2.version}</version>
        </dependency>
    </dependencies>

</project>
