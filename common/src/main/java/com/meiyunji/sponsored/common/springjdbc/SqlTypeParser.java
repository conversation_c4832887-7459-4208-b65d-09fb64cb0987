package com.meiyunji.sponsored.common.springjdbc;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.*;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.ast.statement.SQLTableSource;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlDeleteStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlUpdateStatement;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.alibaba.druid.util.JdbcConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class SqlTypeParser {

    /**
     * 判断SQL语句是否为UPDATE类型
     *
     * @param sql SQL语句
     * @return 是否为UPDATE语句
     */
    public static boolean isUpdateStatement(String sql) {
        List<SQLStatement> statements = parseSql(sql, JdbcConstants.MYSQL.name());
        if (CollectionUtils.isEmpty(statements)) {
            return false;
        }
        // 只判断第一条语句
        return statements.get(0) instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlUpdateStatement;
    }

    /**
     * 判断SQL语句是否为DELETE类型
     *
     * @param sql SQL语句
     * @return 是否为DELETE语句
     */
    public static boolean isDeleteStatement(String sql) {
        List<SQLStatement> statements = parseSql(sql, JdbcConstants.MYSQL.name());
        if (CollectionUtils.isEmpty(statements)) {
            return false;
        }
        // 只判断第一条语句
        return statements.get(0) instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlDeleteStatement;
    }

    /**
     * 判断是否为更新或者删除语句
     */
    public static boolean isUpdateOrDeleteSql(SQLStatement statement) {
        return statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlDeleteStatement || statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlUpdateStatement;
    }

    /**
     * 判断SQL语句是否为SELECT类型
     *
     * @param sql SQL语句
     * @return 是否为SELECT语句
     */
    public static boolean isSelectStatement(String sql) {
        List<SQLStatement> statements = parseSql(sql, JdbcConstants.MYSQL.name());
        if (CollectionUtils.isEmpty(statements)) {
            return false;
        }
        // 只判断第一条语句
        return statements.get(0) instanceof SQLSelectStatement ;
    }

    /**
     * 判断是否为查询语句
     */
    public static boolean isSelectSql(SQLStatement statement) {
        return statement instanceof SQLSelectStatement ;
    }

    /**
     * 解析SQL语句
     *
     * @param sql    SQL语句
     * @param dbType 数据库类型
     * @return 解析后的SQL语句列表
     */
    public static List<SQLStatement> parseSql(String sql, String dbType) {
        // 创建SQL语句解析器
        try {
            return SQLUtils.parseStatements(sql, dbType);
        } catch (Exception e) {
            log.error("解析sql出错", e);
            return null;
        }
    }

    /**
     * 检查SQL WHERE条件中是否包含指定字段
     *
     * @param targetField 目标字段名
     * @return 是否包含指定字段
     */
    public static boolean containsFieldInWhere(SQLStatement statement, String targetField) {
        WhereConditionVisitor visitor = new WhereConditionVisitor(targetField);
        if (statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlDeleteStatement) {
            MySqlDeleteStatement mySqlDeleteStatement = (MySqlDeleteStatement) statement;
            SQLExpr sqlExpr = mySqlDeleteStatement.getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.isFieldFound();
        }

        if (statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlUpdateStatement) {
            MySqlUpdateStatement mySqlUpdateStatement = (MySqlUpdateStatement) statement;
            SQLExpr sqlExpr = mySqlUpdateStatement.getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.isFieldFound();
        }

        if (statement instanceof SQLSelectStatement) {
            SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) statement;
            SQLExpr sqlExpr = sqlSelectStatement.getSelect().getQueryBlock().getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.isFieldFound();
        }
        return false;
    }

    /**
     * 提取SQL语句中涉及的表名
     *
     * @param statement SQL语句对象
     * @return 表名集合
     */
    public static Set<String> extractTableNames(SQLStatement statement) {
        TableNameVisitor visitor = new TableNameVisitor();
        statement.accept(visitor);
        return visitor.getTableNames();
    }

    /**
     * 检查SQL是否涉及指定的表，并且WHERE条件中是否包含指定字段
     *
     * @param statement SQL语句对象
     * @param targetTables 目标表名集合
     * @param targetField 目标字段名
     * @return 如果涉及目标表但WHERE条件中不包含目标字段，返回true
     */
    public static boolean needsFieldInWhere(SQLStatement statement, Set<String> targetTables, String targetField) {
        if (targetTables == null || targetTables.isEmpty()) {
            return false;
        }

        Set<String> tableNames = extractTableNames(statement);
        // 检查是否涉及目标表
        boolean involvesTargetTable = tableNames.stream()
                .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));

        if (!involvesTargetTable) {
            return false;
        }

        // 如果涉及目标表，检查WHERE条件中是否包含目标字段
        return !containsFieldInWhere(statement, targetField);
    }

    /**
     * 检查SQL WHERE条件中指定字段是否有有效值（不为null或空字符串）
     *
     * @param statement SQL语句对象
     * @param targetField 目标字段名
     * @return 如果字段存在且有有效值返回true，如果字段不存在或值为null/空字符串返回false
     */
    public static boolean containsValidFieldInWhere(SQLStatement statement, String targetField) {
        ValidFieldValueVisitor visitor = new ValidFieldValueVisitor(targetField);
        if (statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlDeleteStatement) {
            MySqlDeleteStatement mySqlDeleteStatement = (MySqlDeleteStatement) statement;
            SQLExpr sqlExpr = mySqlDeleteStatement.getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.hasValidValue();
        }

        if (statement instanceof com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlUpdateStatement) {
            MySqlUpdateStatement mySqlUpdateStatement = (MySqlUpdateStatement) statement;
            SQLExpr sqlExpr = mySqlUpdateStatement.getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.hasValidValue();
        }

        if (statement instanceof SQLSelectStatement) {
            SQLSelectStatement sqlSelectStatement = (SQLSelectStatement) statement;
            SQLExpr sqlExpr = sqlSelectStatement.getSelect().getQueryBlock().getWhere();
            if (sqlExpr != null) {
                sqlExpr.accept(visitor);
            }
            return visitor.hasValidValue();
        }
        return false;
    }

    /**
     * 检查SQL是否涉及指定的表，并且WHERE条件中是否包含有效的指定字段值
     *
     * @param statement SQL语句对象
     * @param targetTables 目标表名集合
     * @param targetField 目标字段名
     * @return 如果涉及目标表但WHERE条件中不包含有效的目标字段值，返回true
     */
    public static boolean needsValidFieldInWhere(SQLStatement statement, Set<String> targetTables, String targetField) {
        if (targetTables == null || targetTables.isEmpty()) {
            return false;
        }

        Set<String> tableNames = extractTableNames(statement);
        // 检查是否涉及目标表
        boolean involvesTargetTable = tableNames.stream()
                .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));

        if (!involvesTargetTable) {
            return false;
        }

        // 如果涉及目标表，检查WHERE条件中是否包含有效的目标字段值
        return !containsValidFieldInWhere(statement, targetField);
    }

    /**
     * 表名访问器 - 用于提取SQL中的表名
     */
    private static class TableNameVisitor extends MySqlASTVisitorAdapter {
        private final Set<String> tableNames = new HashSet<>();

        public Set<String> getTableNames() {
            return tableNames;
        }

        @Override
        public boolean visit(SQLExprTableSource x) {
            String tableName = x.getTableName();
            if (StringUtils.isNotBlank(tableName)) {
                tableNames.add(tableName.toLowerCase());
            }
            return true;
        }
    }

    /**
     * WHERE条件访问器 - 用于检查特定字段
     */
    private static class WhereConditionVisitor extends MySqlASTVisitorAdapter {
        private final String targetField;
        private boolean fieldFound = false;

        public WhereConditionVisitor(String targetField) {
            this.targetField = targetField;
        }

        public boolean isFieldFound() {
            return fieldFound;
        }

        @Override
        public boolean visit(SQLIdentifierExpr x) {
            // 处理普通字段名（如 age）
            checkField(x.getName());
            return true;
        }

        @Override
        public boolean visit(SQLPropertyExpr x) {
            // 处理带表别名或前缀的字段（如 user.name）
            checkField(x.getName());
            return true;
        }

        private void checkField(String fieldName) {
            // 这里是考虑别名的情况 i.puid
            if (StringUtils.isNotBlank(fieldName)) {
                // 包含也算
                if (fieldName.contains(targetField)) {
                    fieldFound = true;
                }
            }
            if (targetField.equalsIgnoreCase(fieldName)) {
                fieldFound = true;
            }
        }
    }

    /**
     * 有效字段值访问器 - 用于检查特定字段是否有有效值（不为null或空字符串）
     */
    private static class ValidFieldValueVisitor extends MySqlASTVisitorAdapter {
        private final String targetField;
        private boolean hasValidValue = false;

        public ValidFieldValueVisitor(String targetField) {
            this.targetField = targetField;
        }

        public boolean hasValidValue() {
            return hasValidValue;
        }

        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            // 处理二元操作表达式，如 seller_id = 'value' 或 seller_id IS NULL
            SQLExpr left = x.getLeft();
            SQLExpr right = x.getRight();
            SQLBinaryOperator operator = x.getOperator();

            // 检查左侧是否是目标字段
            if (isTargetField(left)) {
                // 检查操作符和右侧值
                if (operator == SQLBinaryOperator.Equality) {
                    // seller_id = value
                    if (isValidValue(right)) {
                        hasValidValue = true;
                    }
                } else if (operator == SQLBinaryOperator.Is) {
                    // seller_id IS NULL 或 seller_id IS NOT NULL
                    if (right instanceof SQLNullExpr) {
                        // seller_id IS NULL - 这是无效的
                        hasValidValue = false;
                    }
                } else if (operator == SQLBinaryOperator.IsNot) {
                    // seller_id IS NOT NULL - 这可能是有效的，但我们需要更严格的检查
                    if (right instanceof SQLNullExpr) {
                        // seller_id IS NOT NULL - 仅仅不为null还不够，还需要检查是否为空字符串
                        // 这里我们认为 IS NOT NULL 不足以保证有效值
                        hasValidValue = false;
                    }
                } else if (operator == SQLBinaryOperator.In) {
                    // seller_id IN (values)
                    if (isValidInValues(right)) {
                        hasValidValue = true;
                    }
                } else if (operator == SQLBinaryOperator.NotEqual ||
                          operator == SQLBinaryOperator.LessThanOrGreater) {
                    // seller_id != value 或 seller_id <> value
                    if (isNullOrEmptyValue(right)) {
                        // seller_id != null 或 seller_id != '' 表示有有效值
                        hasValidValue = true;
                    }
                }
            }
            return true;
        }

        /**
         * 检查表达式是否是目标字段
         */
        private boolean isTargetField(SQLExpr expr) {
            if (expr instanceof SQLIdentifierExpr) {
                String fieldName = ((SQLIdentifierExpr) expr).getName();
                return isTargetFieldName(fieldName);
            } else if (expr instanceof SQLPropertyExpr) {
                String fieldName = ((SQLPropertyExpr) expr).getName();
                return isTargetFieldName(fieldName);
            }
            return false;
        }

        /**
         * 检查字段名是否匹配目标字段
         */
        private boolean isTargetFieldName(String fieldName) {
            if (StringUtils.isBlank(fieldName)) {
                return false;
            }
            return targetField.equalsIgnoreCase(fieldName) || fieldName.contains(targetField);
        }

        /**
         * 检查值是否有效（不为null且不为空字符串）
         */
        private boolean isValidValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return false; // NULL值无效
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isNotBlank(value); // 空字符串无效
            }
            if (expr instanceof SQLVariantRefExpr) {
                // 参数化查询的占位符，我们无法在编译时确定值，假设有效
                return true;
            }
            // 其他类型的值（数字、日期等）认为有效
            return true;
        }

        /**
         * 检查值是否为null或空字符串
         */
        private boolean isNullOrEmptyValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return true;
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isBlank(value);
            }
            return false;
        }

        /**
         * 检查IN子句中的值是否有效
         */
        private boolean isValidInValues(SQLExpr expr) {
            if (expr instanceof SQLListExpr) {
                SQLListExpr listExpr = (SQLListExpr) expr;
                // 检查IN列表中是否有至少一个有效值
                for (SQLExpr item : listExpr.getItems()) {
                    if (isValidValue(item)) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

}