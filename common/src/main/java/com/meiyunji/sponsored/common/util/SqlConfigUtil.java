package com.meiyunji.sponsored.common.util;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * sql相关配置
 */
@Component
@Data
@RefreshScope
public class SqlConfigUtil {

    /**
     * 是否打印sql日志(打印所有sql日志)
     */
    @Value(value = "${db.config.log.enable:false}")
    private Boolean logEnable;

    /**
     * 是否进行统计 update 和 delete 语句 (测试环境默认开启 生产环境关闭)
     */
    @Value(value = "${db.config.stat.enable:false}")
    private Boolean statEnable;

    /**
     * 是否抛出异常 (上一个为true 此配置才有用 决定是否抛出异常 中断程序)
     */
    @Value(value = "${db.config.stat.throwError:false}")
    private Boolean throwError;

    /**
     * 是否启用sellerid字段检查 (针对特定表的SELECT语句)
     */
    @Value(value = "${db.config.sellerId.check.enable:true}")
    private Boolean sellerIdCheckEnable;

    /**
     * 需要检查sellerid字段的表名列表 (逗号分隔)
     */
    @Value(value = "${db.config.sellerId.check.tables:}")
    private String sellerIdCheckTables;

    /**
     * 获取需要检查sellerid字段的表名集合
     *
     * @return 表名集合
     */
    public Set<String> getSellerIdCheckTablesSet() {
        if (StringUtils.isBlank(sellerIdCheckTables)) {
            return new HashSet<>();
        }
        return Arrays.stream(sellerIdCheckTables.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }
}
