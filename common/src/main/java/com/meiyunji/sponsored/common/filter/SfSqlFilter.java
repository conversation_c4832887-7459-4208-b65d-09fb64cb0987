package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Set;

/**
 * sql拦截器 sql安全
 */
@Slf4j
@Component
public class SfSqlFilter  extends FilterEventAdapter {


    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 白名单sql
     */
//    private static final Set<String> WHITE_SQL = new HashSet<>(Lists.newArrayList("sql"));

    @Override
    public PreparedStatementProxy connection_prepareStatement(
            FilterChain chain, ConnectionProxy connection, String sql) throws SQLException {
        // 拦截 SQL
        if (sqlConfigUtil.getLogEnable()) {
            log.info("sql = {}", sql);
        }
        if (sqlConfigUtil.getStatEnable()) {
            this.interceptPuidSql(sql, sqlConfigUtil.getThrowError());
        }
        // 检查sellerid字段
        if (sqlConfigUtil.getSellerIdCheckEnable()) {
            this.interceptSellerIdSql(sql, sqlConfigUtil.getSellerIdThrowError());
        }
        return super.connection_prepareStatement(chain, connection, sql);
    }

    /**
     * SQL拦截策略枚举
     */
    private enum SqlInterceptStrategy {
        /**
         * PUID字段检查策略（针对UPDATE/DELETE语句）
         */
        PUID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isUpdateOrDeleteSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                List<String> targetFields = Lists.newArrayList("puid","id");
                for (String targetField : targetFields) {
                    if (SqlTypeParser.containsFieldInWhere(statement, targetField)) {
                        return true;
                    }
                }
                return false;
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：增删改需增加puid或id条件!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptPuidSql";
            }
        },

        /**
         * SellerID字段检查策略（针对SELECT语句）
         */
        SELLER_ID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isSelectSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                Set<String> targetTables = filter.sqlConfigUtil.getSellerIdCheckTablesSet();
                if (targetTables.isEmpty()) {
                    return true; // 如果没有配置目标表，则跳过检查
                }
                // 返回true表示检查通过，false表示检查失败
                return !SqlTypeParser.needsFieldInWhere(statement, targetTables, "seller_id");
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：查询特定表时必须包含sellerId条件!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptSellerIdSql";
            }
        };

        /**
         * 检查SQL语句类型是否需要处理
         */
        public abstract boolean shouldProcess(SQLStatement statement);

        /**
         * 执行具体的检查逻辑
         */
        public abstract boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter);

        /**
         * 获取错误消息
         */
        public abstract String getErrorMessage();

        /**
         * 获取日志前缀
         */
        public abstract String getLogPrefix();
    }

    /**
     * 通用SQL拦截模板方法
     */
    private void interceptSqlTemplate(String sql, boolean throwError, SqlInterceptStrategy strategy) {
        try {
            // 白名单sql检查
            if (dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls().contains(sql)) {
                return;
            }

            // 解析 SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return;
            }

            // 只取第一个
            SQLStatement statement = sqlStatements.get(0);

            // 检查是否需要处理此类型的SQL
            if (!strategy.shouldProcess(statement)) {
                return;
            }

            log.info("{} = {}", strategy.getLogPrefix(), sql);

            // 执行具体的检查逻辑
            boolean checkPassed = strategy.checkSql(statement, sql, this);
            if (!checkPassed) {
                BizServiceException bizServiceException = new BizServiceException(strategy.getErrorMessage());
                // 检查不包含sellerId时，记录警告日志
                log.warn("SQL拦截记录 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(bizServiceException));
                if (throwError) {
                    throw bizServiceException;
                }
            }
        } catch (Exception e) {
            // 解析失败时跳过（例如复杂 SQL）
            log.error("解析SQL错误 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(e));
            if (e instanceof BizServiceException) {
                if (throwError) {
                    throw new BizServiceException(strategy.getErrorMessage());
                }
            }
        }
    }

    private void interceptPuidSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.PUID_CHECK);
    }

    /**
     * 拦截针对特定表的查询，检查是否包含sellerid字段
     *
     * @param sql SQL语句
     * @param throwError 是否抛出异常
     */
    private void interceptSellerIdSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.SELLER_ID_CHECK);
    }
}
