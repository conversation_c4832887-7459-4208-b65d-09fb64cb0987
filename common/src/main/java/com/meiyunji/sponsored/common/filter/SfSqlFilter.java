package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.proxy.jdbc.ResultSetProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Set;

/**
 * sql拦截器 sql安全
 */
@Slf4j
@Component
public class SfSqlFilter  extends FilterEventAdapter {


    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 白名单sql
     */
//    private static final Set<String> WHITE_SQL = new HashSet<>(Lists.newArrayList("sql"));

    @Override
    public PreparedStatementProxy connection_prepareStatement(
            FilterChain chain, ConnectionProxy connection, String sql) throws SQLException {
        // 拦截 SQL
        if (sqlConfigUtil.getLogEnable()) {
            log.info("sql = {}", sql);
        }
        if (sqlConfigUtil.getStatEnable()) {
            this.interceptPuidSql(sql, sqlConfigUtil.getThrowError());
        }
        // 检查sellerid字段
//        if (sqlConfigUtil.getSellerIdCheckEnable()) {
//            this.interceptSellerIdSql(sql, sqlConfigUtil.getSellerIdThrowError());
//        }
        return super.connection_prepareStatement(chain, connection, sql);
    }

    /**
     * SQL拦截策略枚举
     */
    private enum SqlInterceptStrategy {
        /**
         * PUID字段检查策略（针对UPDATE/DELETE语句）
         */
        PUID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isUpdateOrDeleteSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                List<String> targetFields = Lists.newArrayList("puid","id");
                for (String targetField : targetFields) {
                    if (SqlTypeParser.containsFieldInWhere(statement, targetField)) {
                        return true;
                    }
                }
                return false;
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：增删改需增加puid或id条件!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptPuidSql";
            }
        },

        /**
         * SellerID字段检查策略（针对SELECT语句）
         * 增强版：不仅检查字段是否存在，还检查字段值是否有效（不为null或空字符串）
         */
        SELLER_ID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isSelectSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                Set<String> targetTables = filter.sqlConfigUtil.getSellerIdCheckTablesSet();
                if (targetTables.isEmpty()) {
                    return true; // 如果没有配置目标表，则跳过检查
                }

                // 校验查询是否带有seller_id且值有效
                boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");

                if (needsValidField) {
                    // 记录详细的错误信息
                    log.warn("SQL seller_id 检查失败: sql={}, targetTables={}, 原因: seller_id字段缺失或值无效(null/空字符串)",
                            sql, targetTables);
                }

                // 返回true表示检查通过，false表示检查失败
                return !needsValidField;
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：查询特定表时必须包含有效的sellerId条件(不能为null或空字符串)!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptSellerIdSql";
            }
        };

        /**
         * 检查SQL语句类型是否需要处理
         */
        public abstract boolean shouldProcess(SQLStatement statement);

        /**
         * 执行具体的检查逻辑
         */
        public abstract boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter);

        /**
         * 获取错误消息
         */
        public abstract String getErrorMessage();

        /**
         * 获取日志前缀
         */
        public abstract String getLogPrefix();
    }

    /**
     * 通用SQL拦截模板方法
     */
    private void interceptSqlTemplate(String sql, boolean throwError, SqlInterceptStrategy strategy) {
        try {
            // 白名单sql检查
            if (dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls().contains(sql)) {
                return;
            }

            // 解析 SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return;
            }

            // 只取第一个
            SQLStatement statement = sqlStatements.get(0);

            // 检查是否需要处理此类型的SQL
            if (!strategy.shouldProcess(statement)) {
                return;
            }

            log.info("{} = {}", strategy.getLogPrefix(), sql);

            // 执行具体的检查逻辑
            boolean checkPassed = strategy.checkSql(statement, sql, this);
            if (!checkPassed) {
                BizServiceException bizServiceException = new BizServiceException(strategy.getErrorMessage());
                // 检查不包含sellerId时，记录警告日志
                log.warn("SQL拦截记录 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(bizServiceException));
                if (throwError) {
                    throw bizServiceException;
                }
            }
        } catch (Exception e) {
            // 解析失败时跳过（例如复杂 SQL）
            log.error("解析SQL错误 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(e));
            if (e instanceof BizServiceException) {
                if (throwError) {
                    throw new BizServiceException(strategy.getErrorMessage());
                }
            }
        }
    }

    private void interceptPuidSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.PUID_CHECK);
    }

    /**
     * 拦截针对特定表的查询，检查是否包含sellerid字段
     *
     * @param sql SQL语句
     * @param throwError 是否抛出异常
     */
    private void interceptSellerIdSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.SELLER_ID_CHECK);
    }

    // ==================== 执行阶段拦截（包含实际参数值） ====================

    @Override
    public boolean preparedStatement_execute(FilterChain chain, PreparedStatementProxy statement) throws SQLException {
        // 在执行阶段拦截，此时可以获取到实际的参数值
        if (sqlConfigUtil.getSellerIdCheckEnable() && sqlConfigUtil.getSellerIdExecutionCheckEnable()) {
            interceptExecutionSql(statement);
        }
        return super.preparedStatement_execute(chain, statement);
    }

    @Override
    public ResultSetProxy preparedStatement_executeQuery(FilterChain chain, PreparedStatementProxy statement) throws SQLException {
        // 在查询执行阶段拦截
        if (sqlConfigUtil.getSellerIdCheckEnable() && sqlConfigUtil.getSellerIdExecutionCheckEnable()) {
            interceptExecutionSql(statement);
        }
        return super.preparedStatement_executeQuery(chain, statement);
    }

    @Override
    public int preparedStatement_executeUpdate(FilterChain chain, PreparedStatementProxy statement) throws SQLException {
        // 在更新执行阶段拦截
        if (sqlConfigUtil.getSellerIdCheckEnable() && sqlConfigUtil.getSellerIdExecutionCheckEnable()) {
            interceptExecutionSql(statement);
        }
        return super.preparedStatement_executeUpdate(chain, statement);
    }

    /**
     * 拦截执行阶段的SQL，包含实际参数值
     * 方案1：构建完整SQL进行检查
     * 方案2：直接验证参数值（推荐）
     */
    private void interceptExecutionSql(PreparedStatementProxy statement) {
        try {
            Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
            if (targetTables.isEmpty()) {
                return;
            }

            // 方案2：直接验证参数值（推荐，性能更好）
            boolean hasValidSellerIdValue = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);

            if (!hasValidSellerIdValue) {
                String errorMsg = "SQL执行阶段检查失败：seller_id参数值无效(null或空字符串)";
                log.warn("执行阶段seller_id检查失败: sql={}, parameters={}",
                        statement.getSql(), statement.getParameters());

                if (sqlConfigUtil.getSellerIdThrowError()) {
                    throw new BizServiceException(errorMsg);
                }
            }

            if (log.isDebugEnabled()) {
                String executionSql = buildExecutionSql(statement.getSql(), statement.getParameters());
                log.debug("执行阶段SQL检查: originalSql={}, parameters={}, executionSql={}",
                         statement.getSql(), statement.getParameters(), executionSql);
            }

        } catch (Exception e) {
            log.error("执行阶段SQL拦截失败", e);
            if (sqlConfigUtil.getSellerIdThrowError()) {
                throw new BizServiceException("SQL执行阶段检查失败: " + e.getMessage());
            }
        }
    }

    /**
     * 构建包含实际参数值的执行SQL
     */
    private String buildExecutionSql(String originalSql, List<Object> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return originalSql;
        }

        String executionSql = originalSql;
        int paramIndex = 0;

        // 替换 ? 占位符为实际参数值
        while (executionSql.contains("?") && paramIndex < parameters.size()) {
            Object param = parameters.get(paramIndex);
            String paramValue = convertParameterToString(param);

            // 替换第一个 ? 为参数值
            executionSql = executionSql.replaceFirst("\\?", paramValue);
            paramIndex++;
        }

        return executionSql;
    }

    /**
     * 将参数值转换为SQL字符串表示
     */
    private String convertParameterToString(Object param) {
        if (param == null) {
            return "NULL";
        }

        if (param instanceof String) {
            String strValue = (String) param;
            // 转义单引号并用单引号包围
            return "'" + strValue.replace("'", "''") + "'";
        }

        if (param instanceof Number) {
            return param.toString();
        }

        if (param instanceof Boolean) {
            return param.toString();
        }

        // 其他类型转换为字符串并用单引号包围
        return "'" + param.toString().replace("'", "''") + "'";
    }
}
