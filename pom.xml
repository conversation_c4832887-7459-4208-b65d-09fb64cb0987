<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meiyunji.base</groupId>
        <artifactId>myj-parent</artifactId>
        <version>1.1.12</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <scm>
        <developerConnection>scm:git:https://git.meiyunji.net/amzup/sellfox-sponsored/</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <groupId>com.meiyunji.amzup.sellfox-sponsored</groupId>
    <artifactId>parent</artifactId>
    <version>1.0.1975-SNAPSHOT</version>

    <modules>
        <module>common</module>
        <module>service</module>
        <module>grpc</module>
        <module>task</module>
        <module>api</module>
    </modules>
    <packaging>pom</packaging>


    <properties>
        <!-- JDK 版本不可改动，只能使用1.8 -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <druid.version>1.2.6</druid.version>
        <amazon.advertisingApis.version>3.1.130</amazon.advertisingApis.version>
        <commons.lang3.version>3.9</commons.lang3.version>
        <commons.text.version>1.8</commons.text.version>
        <commons.io.version>2.4</commons.io.version>
        <commons.codec.version>1.10</commons.codec.version>
        <commons.httpclient.version>3.1</commons.httpclient.version>
        <commons-collections4>4.4</commons-collections4>
        <easy-excel.version>2.2.6</easy-excel.version>
        <mail-sendcloud.version>1.0.0</mail-sendcloud.version>
        <amazon-sellerpartner-sdk.version>1.2.23</amazon-sellerpartner-sdk.version>
        <amazon.mws.version>1.1.70</amazon.mws.version>
        <poi.version>3.17</poi.version>
        <commons.CSV.version>1.8</commons.CSV.version>
        <qcloud.sdk.version>1.1.1</qcloud.sdk.version>
        <okhttp.version>3.13.1</okhttp.version>
        <google.guava.version>22.0</google.guava.version>
        <google.findbugs.version>1.3.9</google.findbugs.version>
        <maven.surefire.version>2.19</maven.surefire.version>
        <sponsored-grpc-api-sdk.version>1.0.375</sponsored-grpc-api-sdk.version>
        <xxl-job.version>2.2.0</xxl-job.version>
        <grpc-spring-boot.version>4.5.8</grpc-spring-boot.version>
        <pulsar.version>2.9.1</pulsar.version>
        <amazon.awssdk.version>2.17.100</amazon.awssdk.version>
        <sellfox-aadas-api.version>1.0.47</sellfox-aadas-api.version>
        <sellfox-aadas-type.version>1.0.57</sellfox-aadas-type.version>
        <sellfox-aadras-api.version>1.0.18</sellfox-aadras-api.version>
        <sellfox-aadras-type.version>1.0.21</sellfox-aadras-type.version>
        <fastjson.version>1.2.83</fastjson.version>
        <redission.version>3.17.5</redission.version>
        <shardingsphere.version>5.1.2</shardingsphere.version>
        <sellfox-ams-api.version>1.0.19</sellfox-ams-api.version>
        <org.apache.commons.csv.version>1.10.0</org.apache.commons.csv.version>
        <guava.version>32.1.2-jre</guava.version>
        <hutool.version>5.8.15</hutool.version>
        <webp-imageio.version>0.1.6</webp-imageio.version>
        <protobuf.version>3.20.1</protobuf.version>
        <cpc.api.version>1.0.1</cpc.api.version>
        <walmart-api-sdk.version>3.0.14</walmart-api-sdk.version>
        <hibernate-validator.version>6.2.5.Final</hibernate-validator.version>
        <sentry-log4j2.version>8.1.0</sentry-log4j2.version>
        <sellfox-common-right.version>1.6.13</sellfox-common-right.version>
        <amazon.tiktok.sdk.version>1.0.15</amazon.tiktok.sdk.version>
    </properties>

    <build>
            <plugins>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-release-plugin</artifactId>
			<version>2.5.3</version>
			<configuration>
				<autoVersionSubmodules>true</autoVersionSubmodules>
				<tagNameFormat>@{project.version}</tagNameFormat>
				<arguments>-DskipTests</arguments>
			</configuration>
		</plugin>
            </plugins>
    </build>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <name>dianxiaomi</name>
            <url>http://maven.dev.dianxiaomi.com/repository/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
